import {
  IGeocoderR<PERSON>ult,
  IWhat3wordsControllerState,
  IWhat3WordsMapCleoAddress
} from "@/what3words/what3words-cleo-models";
import { What3WordsThreeWords } from "@/typings/unknown-lib";
import * as CallDetailService from "@/calls/details/call-detail-service";
import { ICallDetail, ICleoAddress } from "@/calls/details/call-details-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
// import GeocoderResult = google.maps.GeocoderResult;

// const callDetailService = new CallDetailService();

export class What3wordsCleoService {
  public factoryWhat3wordsControllerState(): IWhat3wordsControllerState {

    return {
      apiKey: window.MyGlobalSession.WHAT3WORDS_API_KEY,
      version: "4.0.3",
      map: {
        apiKey: window.MyGlobalSession.GOOGLE_API_KEY,
        gridOverlayData: null,
        isZoomOkForOverlay: false,
        debounceApplyOverlay: null,
        uiOptions: {
          mapStyle: "map"
        }
      },
      isLoading: false,
      userInput: {
        divSuffix: Math.random()
          .toString(36)
          .substring(2),
        coordinates: {
          lat: 0,
          lng: 0
        },
        words: "",
        autoSuggest: "",
        pafPostCode: "",
        mobileNumber: "",
        what3WordsConfirmed: false,
        addressConfirmed: false,
        googleAutoPredictionQuery: "",
        autoSuggestSearchType: "w3w",
        defaultLocation: {
          addressString: ""
        },
        callDetail: CallDetailService.factoryCallDetail(),
        userPermissions: {}
      },
      results: {
        what3WordsResponse: null,
        suggestions: null,
        suggestion: null,
        geocoderResults: [],
        geocoderResult: null,
        geocoderResultsPostCodes: [],
        pafResults: [],
        pafSelected: null,
        mapMarkers: [],
        nearestAddress: null,
        smsCount: 0,
        googleAutoPredictions: [],
        googleAutoPrediction: null,
        googlePlaceResult: null,
        vehicles: [],
        tomTomVehicles: []
      },
      display: {
        pafPicker: false,
        pafIsLoading: false,
        smsIsLoading: false
      }
    };
  }

  public isWordLongEnoughForSearch(words: What3WordsThreeWords): boolean {
    if (words.length === 0) {
      return false;
    }
    return words.split(".").length === 3;
  }

  public isMobileNumber(phoneNumber: string): boolean {
    // phoneNumber = phoneNumber.replaceAll(" ", "");
    const phoneNumberReplaced = phoneNumber.replace(/ /g, "");

    const pattern = /^(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}$/;
    return pattern.test(phoneNumberReplaced);
  }

  public mapGoogleNearestAddressToCleoAddress(
    nearest: any
  ): IWhat3WordsMapCleoAddress {
    const cleoAddress: IWhat3WordsMapCleoAddress = {
      address1: "",
      address2: "",
      address3: "",
      address4: "",
      postCode: "",
      town: "",
      GPS: ""
    };

    const formatted_address = (nearest as any).formatted_address.replace(
      "UK",
      ""
    );
    const formattedAddresses: string[] = formatted_address.split(",");

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const address_components = nearest.address_components.reduce<
      Record<string, string>
    >((accum: Record<string, string>, addressComponent: unknown) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      (addressComponent.types as string[]).forEach((addressType: string) => {
        if (addressType === "postal_town" || addressType === "postal_code") {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          accum[addressType] = addressComponent.long_name;
        }
      });
      return accum;
    }, {});

    Object.keys(address_components).forEach(key => {
      if (key === "postal_town") {
        cleoAddress.town = address_components[key];
      }
      if (key === "postal_code") {
        cleoAddress.postCode = address_components[key];
      }
    });

    const townAndPostCode = cleoAddress.town + " " + cleoAddress.postCode;

    formattedAddresses.forEach((addressLine, index) => {
      const lineNumber = index + 1;

      addressLine = addressLine.trim();

      if (addressLine.length > 0) {
        const isMatch =
          addressLine === cleoAddress.town ||
          addressLine === cleoAddress.postCode ||
          addressLine === townAndPostCode
            ? true
            : false;

        if (!isMatch) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          cleoAddress["address" + lineNumber] = addressLine;
        }
      }
    });

    cleoAddress.GPS =
      nearest.geometry.location.lat.toString() +
      "," +
      nearest.geometry.location.lng.toString();

    return cleoAddress;
  }

  public mapCallDetailAddressToCleoAddress(
    callDetail: ICallDetail,
    whichAddress: "CURRENT" | "HOME"
  ): IWhat3WordsMapCleoAddress {
    const cleoAddress: ICleoAddress =
      whichAddress === "CURRENT"
        ? callDetail.callAddress
        : callDetail.homeAddress;

    return {
      address1: cleoAddress.line1,
      address2: cleoAddress.line2,
      address3: cleoAddress.line3,
      address4: cleoAddress.line4,
      postCode: cleoAddress.postCode,
      town: cleoAddress.town,
      GPS: "" //  Do NOT set GPS as neither Google, W3W or PAF has house specific Lat/Long for an address
    };
  }

  public mapCallSummaryAddressToCleoAddress(
    cleoCallSummary: ICleoCallSummary
  ): IWhat3WordsMapCleoAddress {
    return {
      address1: cleoCallSummary.CallAddress1,
      address2: cleoCallSummary.CallAddress2,
      address3: cleoCallSummary.CallAddress3,
      address4: cleoCallSummary.CallAddress4,
      postCode: cleoCallSummary.CallPostCode,
      town: cleoCallSummary.CallTown,
      GPS: "" //  Do NOT set GPS as neither Google, W3W or PAF has house specific Lat/Long for an address
    };
  }

  public getWhat3WordsMapCleoAddressFromCallData(
    callData: ICallDetail | ICleoCallSummary
  ): IWhat3WordsMapCleoAddress {
    if ((callData as ICallDetail).callAddress) {
      return this.mapCallDetailAddressToCleoAddress(
        callData as ICallDetail,
        "CURRENT"
      );
    }
    return this.mapCallSummaryAddressToCleoAddress(
      callData as ICleoCallSummary
    );
  }

  /**
   * Returns address in format: St Clements, King St, Colyton, EX24 6LF
   * @param what3WordsMapCleoAddress
   */
  public getQueryStringFromAddress(
    what3WordsMapCleoAddress: IWhat3WordsMapCleoAddress
  ): any {
    return [...new Set(Object.values(what3WordsMapCleoAddress))]
      .filter(addressValue => {
        return addressValue.length > 0;
      })
      .join(", ");
  }
}
