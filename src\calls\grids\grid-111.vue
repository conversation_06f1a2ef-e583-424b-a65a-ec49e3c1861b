<template>
  <div id="grid--content">
    <div class="grid--header">
      <GridRouteToolbar
        view-title="CAS Validation & Speak To Queue (O)"
        :call-count="gridController1.state.gridData.length"
        :call-filter-count="gridController1.state.callFilterCount"
        :is-loading="gridController1.state.isLoading"
        :socket-loading-trigger="socketLoadingTrigger"
        :socket-status="cleoSocketWrapperController.status"
        :last-reloaded="gridController1.state.lastLongPollHumanReadTime"
        v-on:getData="gridController1.getData()"
      >
        <div slot="toolbar-content">
          <ToolbarWidgetNewCall
            style="display: inline"
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
          ></ToolbarWidgetNewCall>

          <ToolbarWidgetActions
            style="display: inline"
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
          ></ToolbarWidgetActions>

          <ButtonSupport
            v-if="
              !permissionStoreState.userAppPermsShort[
                permissionNames.NO_SUPPORT
              ]
            "
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
            :permission-names="permissionNames"
          ></ButtonSupport>

          <div
            style="display: inline"
            :class="
              gridController1.state.filters.length > 0
                ? 'grid-route-toolbar--filters-warn'
                : ''
            "
          >
            <input
              placeholder="Enter search term..."
              class="grid-route-toolbar--filters-sep"
              :class="
                quickFilterText1.length > 0
                  ? 'grid-route-toolbar--filters-warn-value'
                  : ''
              "
              v-model="quickFilterText1"
              v-on:keyup="quickFilterTextChanged"
            />
            <!--            <CleoContextMenu></CleoContextMenu>-->
            <GridFilterCov19
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              v-on:change="onCov19Filter"
            ></GridFilterCov19>
            <GridFilterClassn
              v-show="
                permissionStoreState.userAppPermsShort
                  .GRID_FILTER_CLASSIFICATION
              "
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              :classifications="getClassificationFilterOptions"
              v-on:change="onClassificationFilter"
            >
            </GridFilterClassn>

            <select
              v-on:change="socketGroupNameChanged"
              v-model="socketGroupNameSelected"
            >
              <option
                v-for="socketGroup in socketGroupNames"
                :key="socketGroup"
                :value="socketGroup"
                v-text="socketGroup"
              ></option>
            </select>

            <!--            <button v-on:click="showIdentityLogin = true">-->
            <!--              ID-->
            <!--            </button>-->
            <!--            <CleoModal v-if="showIdentityLogin">-->
            <!--              <LoginIdentityForm slot="body"></LoginIdentityForm>-->
            <!--              <div slot="buttons"></div>-->
            <!--            </CleoModal>-->

            <!--            <button-->
            <!--              v-on:click="toggleSocket"-->
            <!--              :disabled="cleoSocketWrapperController.isTransitioning"-->
            <!--            >-->
            <!--              <span-->
            <!--                v-text="-->
            <!--                  cleoSocketWrapperController.isConnected ? 'Stop' : 'Start'-->
            <!--                "-->
            <!--              ></span>-->
            <!--            </button>-->
            <!--            <span v-text="cleoSocketWrapperController.status"></span>-->
            <!--            <span v-text="cleoSocketWrapperController.socket.state"></span>-->
            <!--            <span v-text="cleoSocketWrapperController.isTransitioning"></span>-->
          </div>
        </div>
      </GridRouteToolbar>
    </div>
    <!--    <div style="width: 0;height: 0;visibility: hidden;" :id="PREFIX">-->
    <!--      window.setTimeout() looking for this div id: {{ PREFIX }}-->
    <!--    </div>-->
    <!--    Breach{{ gridController1.state.simpleTriggerBreach }} Polling{{-->
    <!--      gridController1.state.simpleGetData-->
    <!--    }}-->
    <!--    <input v-if="gridController1.config.allowMultiSelect" type="checkbox" v-model="selectAll1" v-on:change="onSelectAllChanged"/>-->
    <div class="grid--content-pane">
      <splitpanes
        class="default-theme"
        v-on:ready="paneReady"
        v-on:pane-click="paneClick"
        v-on:resized="paneResized"
        horizontal
      >
        <pane
          key="grid"
          :size="configStoreState.splitPanes.gridContentDefault.size"
          id="grid--top-pane"
        >
          <GridDefault
            oncontextmenu="javascript:return false;"
            :grid-data="gridController1.state.gridData"
            :new-grid-data="gridController1.state.newCalls"
            :updated-grid-data="gridController1.state.updatedCalls"
            :recalc-breach-trigger="gridController1.state.simpleTriggerBreach"
            :show-multi-select="gridController1.config.allowMultiSelect"
            :select-all-trigger="selectAllTrigger"
            :quick-filter-trigger="quickFilterTrigger1"
            :external-filters="gridController1.state.filters"
            v-on:onRowClicked="onRowClicked"
            v-on:onRowDoubleClicked="onRowDoubleClicked"
            v-on:onCellContextMenu="onCellContextMenu"
            v-on:onCurrentSelectedDocs="onCurrentSelectedDocs"
            v-on:onFilterChangedRowCount="onFilterChangedRowCount"
          ></GridDefault>
          <CleoModal
            v-if="showConfirmOpeningCall"
            header-message="Open Call"
            :body-message="getOpenCallMessage"
            v-on:closeSecondary="showConfirmOpeningCall = false"
            v-on:closePrimary="proceedToOpeningLockedCall"
          ></CleoModal>
          <CleoContextMenu2
            contain-with-dom-id="grid--top-pane"
          ></CleoContextMenu2>
        </pane>

        <pane
          key="summary"
          :size="configStoreState.splitPanes.callSummaryDefault.size"
          :min-size="configStoreState.splitPanes.callSummaryDefault.minSize"
          :max-size="configStoreState.splitPanes.callSummaryDefault.maxSize"
        >
          <CallSummaryToolbar
            class="grid--call-summary-toolbar"
            :cleo-call-summary="cleoCallSummarySelected"
            :is-loading="isLoadingPerms"
            :perms-for-call="cleoCallSummarySelectedPerms"
          ></CallSummaryToolbar>

          <CallSummaryDisplay
            class="grid--call-summary-display"
            :cleo-call-summary="cleoCallSummarySelected"
          ></CallSummaryDisplay>
        </pane>
      </splitpanes>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { GridCallsData } from "./grid-calls-data";
import {
  ICleoCallSummary,
  ISimpleTrigger
} from "../summary/call-summarry-models";
import { CallSummaryService } from "../summary/call-summary-service";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import GridDefault from "@/calls/grids/grid-default.vue";

import { debounce } from "@/common/debounce";
import { CommonService } from "@/common/common-service";
import { loggerInstance } from "@/common/Logger";

import { mapState } from "vuex";
import SocketLoadingSpinner from "@/socket/socket-loading-spinner.vue";
import CallSummaryDisplay from "@/calls/summary/call-summary-display.vue";
import { GridController } from "@/calls/grids/grid-controller/grid-controller";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import CallSummaryToolbar from "@/calls/summary/call-summary-toolbar/call-summary-toolbar.vue";
import {
  ICleoPermission,
  PERMISSION_NAMES
} from "@/permissions/permission-models";
import { PermissionData } from "@/permissions/permission-data";

import Splitpanes from "../../../node_modules/splitpanes/src/components/splitpanes/splitpanes.vue";
import Pane from "../../../node_modules/splitpanes/src/components/splitpanes/pane.vue";
import GridRouteToolbar from "@/calls/grids/grid-route-toolbar.vue";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import ToolbarWidgetNewCall from "@/calls/grids/toolbar-widgets/toolbar-widget-newcall.vue";
import GridFilterCov19 from "@/calls/grids/grid-filter/grid-filter-cov19.vue";
import {
  factoryGridFilters,
  IGridFilter
} from "@/calls/grids/grid-filter/grid-filter-models";
import GridFilterClassn from "@/calls/grids/grid-filter/grid-filter-classn.vue";
import {
  CALL_CLASSIFICATION,
  IAdapterPagedResponse
} from "@/common/common-models";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import GridFilterQuick from "@/calls/grids/grid-filter/grid-filter-quick.vue";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";

import {
  CleoSocketWrapperController,
  SOCKET_ACTIONS
} from "@/socket/socket-controller";
import ToolbarWidgetActions from "@/calls/grids/toolbar-widgets/toolbar-widget-actions.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
  ISplitPane
} from "@/common/config/config-store";
import CleoContextMenu2 from "@/calls/grids/contextmenu/cleo-context-menu2.vue";
import { CONTEXT_MENU_STORE_CONST } from "@/calls/grids/contextmenu/context-menu-store";
import LoginIdentityForm from "@/login/identity/login-identity-form.vue";
import { ILoginStoreState, LOGIN_STORE_STORE_CONST } from "@/login/login-store";
import ButtonSupport from "@/calls/grids/toolbar-widgets/buttons/button-support.vue";
import { Watch } from "vue-property-decorator";
import { SocketGroup, SocketGroupNames } from "@/calls/grids/grid-models";

@Component({
  name: "grid-111",
  components: {
    ButtonSupport,
    LoginIdentityForm,
    CleoContextMenu2,
    ToolbarWidgetActions,
    GridFilterQuick,
    CleoModal,
    GridFilterClassn,
    GridFilterCov19,
    ToolbarWidgetNewCall,
    GridRouteToolbar,
    CallSummaryToolbar,
    CallSummaryDisplay,
    SocketLoadingSpinner,
    GridDefault,
    LoadingSpinner,
    Splitpanes,
    Pane
  },
  computed: {
    ...mapState<IUserStoreState>(USER_STORE_CONST.USER__CONST_MODULE_NAME, {
      userStoreState: (state: IUserStoreState) => state
    }),
    ...mapState<IPermissionStoreState>(
      PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME,
      {
        permissionStoreState: (state: IPermissionStoreState) => state
      }
    ),
    ...mapState<IConfigStoreState>(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME,
      {
        configStoreState: (state: IConfigStoreState) => state
      }
    ),
    ...mapState<ILoginStoreState>(
      LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME,
      {
        loginStoreState: (state: ILoginStoreState) => state
      }
    )
  }
})
export default class Grid111 extends Vue {
  public readonly permissionStoreState!: IPermissionStoreState;
  public readonly userStoreState!: IUserStoreState;
  public readonly configStoreState!: IConfigStoreState;
  public readonly loginStoreState!: ILoginStoreState;

  public cleoCommonService: CleoCommonService = new CleoCommonService();
  public commonService: CommonService = new CommonService();
  public callSummaryController: CallSummaryController = new CallSummaryController();

  public gridCallsData: GridCallsData = new GridCallsData();
  public callSummaryService: CallSummaryService = new CallSummaryService();

  public cleoCallSummarySelected: ICleoCallSummary = this.callSummaryService.factoryCleoCallSummary();
  public cleoCallSummarySelectedPerms: Record<string, ICleoPermission> = {};
  public debounceGetSelectedPerms: any;
  public isLoadingPerms = false;

  public permissionData: PermissionData = new PermissionData();
  public permissionNames = PERMISSION_NAMES;

  public isLoading = false;

  public socketLoading = false;
  public socketLoadingTrigger: ISimpleTrigger<unknown> = { timeIso: "" };
  public breachRecalcTimer: any;
  public selectAllTrigger: ISimpleTrigger<boolean> = {
    timeIso: "",
    data: false
  };
  public selectAll1 = false;
  public quickFilterText1 = "";
  public quickFilterTrigger1: ISimpleTrigger<string> = {
    timeIso: "",
    data: ""
  };

  public filterQuick = "";
  public filterValueClassification = "";
  public filterValueCov19 = "";

  public showConfirmOpeningCall = false;

  public cleoSocketWrapperController: CleoSocketWrapperController | null = null;
  public socketGroupName: SocketGroup = "111calls";
  public socketGroupNames = SocketGroupNames;
  public socketGroupNameSelected: SocketGroup = "111calls";
  // public socket1: HubConnection | undefined;
  // public showIdentityLogin = false;

  public PREFIX = Math.random()
    .toString(36)
    .substring(2);

  public gridController1 = new GridController();

  public created(): void {
    loggerInstance.log("Grid111.created()...a...");

    this.debounceGetSelectedPerms = debounce(() => {
      loggerInstance.log("Grid111.debounceGetSelectedPerms()...");
      this.getPermsForSelectedCall();
    }, 1000);

    this.gridController1.config = {
      viewTitle: "CAS Validation & Speak To Queue",
      divTimer: this.PREFIX,
      adapterQueueParams: {
        PageNumber: 1,
        RecordsPerPage: 1_000,
        /**
         * SELECT Form = "Call" &
         * CallServiceType = "111" &
         * @Text(CallStatusValue)="1" &
         * ALL_VIEW_INCLUDE = "" &
         * BreachPriorityQueueRestrict!="CAS_ONLY" &
         * IsDX_PA!="1" &
         * ALL_VIEW_EXCLUDE != "1"
         *
         */
        QueryName: "111calls",
        Criteria: {}
      },
      /**
       * breachRefreshRateMs and longPollingRefreshRateMs should actually match, else
       * odd ui as breach will read "5m" but the long polling hasn't happened, so in case
       * off Dx333, the row color should also have changed colour.
       */
      breachRefreshRateMs: 15_000,
      longPollingRefreshRateMs: 120_000,
      allowMultiSelect: false,
      stackCollectionMessagesCount: 100
    };

    const headers = {
      Authorization:
        "Bearer " + this.loginStoreState.tokenResponse.access_token,
      HeaderAuthorization:
        "Bearer " + this.loginStoreState.tokenResponse.access_token
    };

    this.cleoSocketWrapperController = new CleoSocketWrapperController(headers);
    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_UPDATED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(">>>>>>>>>>>>> socket1 OnCaseUpdated()", {
          ...cleoCallSummary
        });
        this.handleUpdated(cleoCallSummary);
      }
    );
    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_LOCKED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(">>>>>>>>>>>>> socket1 OnCaseUpdated()", {
          ...cleoCallSummary
        });
        this.handleUpdated(cleoCallSummary);
      }
    );
    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_UNLOCKED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(">>>>>>>>>>>>> socket1 OnCaseUpdated()", {
          ...cleoCallSummary
        });
        this.handleUpdated(cleoCallSummary);
      }
    );

    this.cleoSocketWrapperController.socket.onreconnecting((response: any) => {
      loggerInstance.log(
        ">>>>>>>>>>socket1  connection.onreconnecting():",
        response
      );
    });

    this.cleoSocketWrapperController.socket.onreconnected((response: any) => {
      loggerInstance.log(
        ">>>>>>>>>> socket1  connection.onreconnected():",
        response
      );
    });

    this.cleoSocketWrapperController.socket.onclose((response: any) => {
      loggerInstance.log(">>>>>>>>>> socket1 connection.onclose():", response);
    });

    this.cleoSocketWrapperController.startSocket(
      "registerForEpisodeOfCareQueue",
      "111calls"
    );
  }

  public mounted(): void {
    this.gridController1.init();
    this.configStoreState.splitPanes.panes;
  }

  public toggleSocket(): void {
    if (this.cleoSocketWrapperController?.isConnected) {
      this.cleoSocketWrapperController.stopSocket();
    } else {
      this.cleoSocketWrapperController?.startSocket(
        "registerForEpisodeOfCareQueue",
        this.socketGroupName
      );
    }
  }

  @Watch("cleoSocketWrapperController.status")
  public onSocketStatusChanged(newValue: string, oldValue: string): void {
    console.log(
      "onSocketStatusChanged>>>>>> newValue: " +
        newValue +
        ", oldValue: " +
        oldValue
    );
    this.joinSocketGroup();
  }

  public joinSocketGroup(): void {
    if (
      !this.cleoSocketWrapperController ||
      this.cleoSocketWrapperController.status !== "Connected"
    ) {
      return;
    }
    this.cleoSocketWrapperController.socket
      .invoke("RegisterForEpisodeOfCareQueue", this.socketGroupName)
      .then((invokeResponse: IAdapterPagedResponse<ICleoCallSummary>) => {
        console.log(
          "onSocketStatusChanged>>>>>> invoke() invokeResponse",
          invokeResponse
        );
        // this.gridController1.state.gridData = invokeResponse;
        this.gridController1.setData(invokeResponse);
      })
      .catch((err: Error) => {
        console.error(err);
      });
  }

  public socketGroupNameChanged() {
    this.socketGroupName = this.socketGroupNameSelected;
    this.joinSocketGroup();
  }

  public handleUpdated(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("Grid111.handleUpdated()...");
    this.socketLoading = true;
    this.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    //  Until we can map the grid to a specific socket channel, every call comes up.
    if (cleoCallSummary.CallService.Type !== "111") {
      return;
    }
    const updatedCalls = [cleoCallSummary];

    this.gridController1.updateCalls(updatedCalls);

    // TODO update summary display if required or remove if not in view any more.
    this.checkIfSummaryDisplayNeedUpdating(updatedCalls);
  }

  public checkIfSummaryDisplayNeedUpdating(
    updatedCalls: ICleoCallSummary[]
  ): void {
    const cleoCallSummarySelected = this.cleoCallSummarySelected;

    const pred = (callSummary: ICleoCallSummary) => {
      return callSummary.CallNo === cleoCallSummarySelected.CallNo;
    };

    const callMatchUpdate: ICleoCallSummary | null = this.commonService.findFirst(
      pred,
      updatedCalls
    );
    if (callMatchUpdate) {
      this.cleoCallSummarySelected = this.commonService.simpleObjectClone(
        callMatchUpdate
      );
      this.getPermsForSelectedCall();
    }
  }

  public onCellContextMenu(payload: {
    data: ICleoCallSummary;
    coords: { x: number; y: number };
  }): void {
    loggerInstance.log("Grid111.onCellContextMenu();", payload.data);
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_SHOW_MENU,
      true
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_COORDS,
      payload.coords
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_CLEO_CALL_SUMMARY,
      payload.data
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS,
      {}
    );

    this.onRowClicked(payload.data);
  }

  public onRowClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("Grid111.onRowClicked();", cleoCallSummary);
    this.cleoCallSummarySelected = this.commonService.simpleObjectClone(
      cleoCallSummary
    );
    this.debounceGetSelectedPerms();
  }

  public getPermsForSelectedCall(): void {
    this.isLoadingPerms = true;
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS_LOADING,
      true
    );
    this.permissionData
      .getUserPermissions(
        this.userStoreState.user.userRole,
        this.cleoCallSummarySelected.CallNo
      )
      .then(perms => {
        this.cleoCallSummarySelectedPerms = this.commonService.simpleObjectClone(
          perms
        );

        this.$store.commit(
          CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
            "/" +
            CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS,
          this.cleoCallSummarySelectedPerms
        );
      })
      .finally(() => {
        this.isLoadingPerms = false;
        this.$store.commit(
          CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
            "/" +
            CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS_LOADING,
          false
        );
      });
  }

  public onRowDoubleClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("Grid111.onRowDoubleClicked();", cleoCallSummary);

    if (this.cleoCallSummarySelected.CallNo !== cleoCallSummary.CallNo) {
      this.onRowClicked(cleoCallSummary);
    }

    if (this.isCallLockedByAnotherUser()) {
      this.showConfirmOpeningCall = true;
      return;
    }
    this.proceedToOpeningCall(cleoCallSummary);
  }

  public isCallLockedByAnotherUser(): boolean {
    if (!this.cleoCallSummarySelected) {
      return false;
    }
    if (!this.userStoreState.user.userName) {
      return false;
    }

    return this.callSummaryService.isCallLockedByAnotherUser(
      this.cleoCallSummarySelected,
      this.userStoreState.user.userName
    );
  }

  public proceedToOpeningLockedCall(): void {
    this.showConfirmOpeningCall = false;
    this.proceedToOpeningCall(this.cleoCallSummarySelected);
  }

  public get getOpenCallMessage(): string {
    if (
      this.cleoCallSummarySelected &&
      this.cleoCallSummarySelected.CallNo > 0
    ) {
      return (
        "Call " +
        this.cleoCallSummarySelected.CallNo +
        (this.isCallLockedByAnotherUser()
          ? " is locked by: " +
            this.cleoCommonService.formatUserDominoName(
              this.cleoCallSummarySelected.IsLocked
            ) +
            ", continue to open?"
          : "")
      );
    }
    return "";
  }

  public proceedToOpeningCall(cleoCallSummary: ICleoCallSummary): void {
    this.callSummaryController.openCall(
      cleoCallSummary,
      this.userStoreState.user
    );
  }

  public onCurrentSelectedDocs(cleoCallSummaries: ICleoCallSummary[]): void {
    this.gridController1.state.selectedCalls = this.commonService.simpleObjectClone(
      cleoCallSummaries
    );
  }

  public selectAll(doSelectAll: boolean): void {
    this.selectAllTrigger = {
      timeIso: new Date().toISOString(),
      data: doSelectAll
    };
  }

  public onSelectAllChanged(): void {
    this.selectAll(this.selectAll1);
  }

  public quickFilterTextChanged(): void {
    this.gridController1.state.quickFilterText = {
      timeIso: new Date().toISOString(),
      data: this.quickFilterText1
    };
    this.quickFilterTrigger1 = {
      timeIso: new Date().toISOString(),
      data: this.quickFilterText1
    };
  }

  public get getClassificationFilterOptions(): CALL_CLASSIFICATION[] {
    return ["CH Advice", "Message"];
  }

  public onQuickFilter(filterValue: string): void {
    this.filterQuick = filterValue;
    this.setFilters();
  }

  public onClassificationFilter(filterValue: string): void {
    this.filterValueClassification = filterValue;
    this.setFilters();
  }

  public onCov19Filter(filterName: string): void {
    this.filterValueCov19 = filterName;
    this.setFilters();
  }

  public setFilters(): void {
    const filters: IGridFilter[] = [];

    if (this.filterQuick.length > 0) {
      const quickFilter = factoryGridFilters.QUICK;
      quickFilter.filterValue = this.filterQuick;
      filters.push(quickFilter);
    }

    if (this.filterValueClassification.length > 0) {
      const cleoFilter = factoryGridFilters.CLASSIFICATION;
      cleoFilter.filterValue = this.filterValueClassification;
      filters.push(cleoFilter);
    }

    if (this.filterValueCov19 === "COV19") {
      filters.push(factoryGridFilters.COV19);
    }
    if (this.filterValueCov19 === "NOT_COV19") {
      filters.push(factoryGridFilters.NOT_COV19);
    }

    this.gridController1.state.filters = filters;
  }

  public onFilterChangedRowCount(callFilterCount: number): void {
    this.gridController1.state.callFilterCount = callFilterCount;
  }

  public paneReady(): void {
    console.log("...paneReady");
  }

  public paneResized(panes: ISplitPane[]): void {
    console.log("...paneResized");
    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG__MUTATION_SET_SPLIT_PANES,
      panes
    );
  }

  public paneClick(a: any): void {
    loggerInstance.log("...paneClickSummary");
  }

  public beforeDestroy(): void {
    loggerInstance.log("Grid111.beforeDestroy();");
    this.gridController1.destroy();
    if (this.cleoSocketWrapperController) {
      this.cleoSocketWrapperController.stopSocket();
    }
  }

  public destroyed(): void {
    loggerInstance.log("Grid111.destroyed();");
  }
}
</script>
