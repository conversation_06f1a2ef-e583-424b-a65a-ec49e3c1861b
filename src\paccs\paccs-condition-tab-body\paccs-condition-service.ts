import {
  IPaccsCondition,
  IPaccsConditionOrder,
  IPaccsConditionSupportingInformation
} from "@/paccs/paccs-condition-tab-body/paccs-condition-models";
import { CommonService } from "@/common/common-service";

const commonService: CommonService = new CommonService();

export class PaccsConditionService {
  public factoryPaccsCondition(): IPaccsCondition {
    return {
      conditionId: "",
      conditionName: "",
      importance: 0,
      // symptomGroup: null,
      ambJump: "",
      homeMgmtJump: "",
      servicesJump: "",
      etcJump: "",
      gender: "male",
      age: "adult",
      conditionSupportingInformation: []
    };
  }

  public factoryPaccsConditionSupportingInformation(): IPaccsConditionSupportingInformation {
    return {
      conditionId: "",
      categoryTitle: "",
      categoryId: "",
      orderNo: 10,
      text: ""
    };
  }

  public sortPaccsConditionOrder(
    paccsConditionOrders: IPaccsConditionOrder[]
  ): IPaccsConditionOrder[] {

    const withAndWithoutOrder = paccsConditionOrders.reduce(
      (accum, paccsConditionOrder: IPaccsConditionOrder) => {
        if (paccsConditionOrder.orderNo === 0) {
          accum.withZeroOrder.push(paccsConditionOrder);
        } else {
          accum.withOrder.push(paccsConditionOrder);
        }
        return accum;
      },
      {
        withOrder: [] as IPaccsConditionOrder[],
        withZeroOrder: [] as IPaccsConditionOrder[]
      }
    );

    return [
      ...commonService.sortArray("orderNo", withAndWithoutOrder.withOrder),
      ...withAndWithoutOrder.withZeroOrder
    ];
  }
}
