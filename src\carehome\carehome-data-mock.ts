import { ICareHomeCleo } from "@/carehome/carehome-data";

export let mockCareHomesCleo: ICareHomeCleo[] = [];

if (process.env.NODE_ENV === "development") {
  mockCareHomesCleo = [
    {
      id: 109,
      careHomeName: "CareHome 109",
      isEnabled: true,
      email: "<EMAIL>"
    },
    {
      id: 110,
      careHomeName: "CareHome 110",
      isEnabled: true,
      email: "<EMAIL>"
    },
    // {
    //   id: 111,
    //   careHomeName: "CareHome 111",
    //   isEnabled: false,
    //   email: "<EMAIL>"
    // },
    {
      id: 112,
      careHomeName: "CareHome 112",
      isEnabled: true,
      email: "<EMAIL>"
    }
  ];
}
