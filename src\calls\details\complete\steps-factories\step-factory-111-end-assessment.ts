import {
  CompleteStepName,
  CompleteStepName111,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IStep,
  IValidationMessage
} from "@/calls/details/complete/complete-models";
import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import {
  factoryStep,
  factorySteps,
  validationMapDefault
} from "@/calls/details/complete/complete-service";
import * as CompleteService from "@/calls/details/complete/complete-service";

export function factoryUserStepsEndAssessment111(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<CompleteStepName111> {
  const stepsDefault: Record<
    CompleteStepName111,
    IStep<CompleteStepName>
  > = factorySteps();

  const steps: Record<CompleteStepName111, IStep<CompleteStepName>> = {
    END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
    CONTACT_MADE: stepsDefault.CONTACT_MADE,
    OUTCOMES: stepsDefault.OUTCOMES,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    INSUFFICIENT_CONTACT_ATTEMPTS: stepsDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
    FAILED_CONTACT_RISK_ASSESSMENT: stepsDefault.FAILED_CONTACT_RISK_ASSESSMENT,
    CLINICAL_VALIDATION: stepsDefault.CLINICAL_VALIDATION,
    TAXI: stepsDefault.TAXI,
    VULNERABILITY: stepsDefault.VULNERABILITY,
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
  };

  const validationDefault = validationMapDefault(
    state,
    completeControllerInput
  );

  const validateMap: Record<CompleteStepName111, () => IValidationMessage[]> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      return [];
    },
    CONTACT_MADE: validationDefault.CONTACT_MADE,
    OUTCOMES: validationDefault.OUTCOMES,
    FAILED_CONTACT_REASON: validationDefault.FAILED_CONTACT_REASON,
    INSUFFICIENT_CONTACT_ATTEMPTS:
      validationDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
    CLINICAL_VALIDATION: validationDefault.CLINICAL_VALIDATION,
    FAILED_CONTACT_RISK_ASSESSMENT:
      validationDefault.FAILED_CONTACT_RISK_ASSESSMENT,
    TAXI: validationDefault.TAXI,
    VULNERABILITY: validationDefault.VULNERABILITY,
    UNKNOWN: validationDefault.UNKNOWN
  };

  const gotoNextMap: Record<CompleteStepName111, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "CONTACT_MADE";
    },
    CONTACT_MADE: () => {
      if (state.userResponse.contactMade.id === "CONTACT_FAILURE") {
        if (state.dx.isDxValidationRequired) {
          state.currentStep = "CLINICAL_VALIDATION";
          return;
        }
        state.currentStep = "FAILED_CONTACT_REASON";
        return;
      }

      state.currentStep = "OUTCOMES";
    },
    CLINICAL_VALIDATION: () => {
      if (
        state.userResponse.clinicalValidation.id ===
        "ATTEMPTED_AND_FAILED_CONTACT"
      ) {
        state.currentStep = "FAILED_CONTACT_REASON";
        return;
      }
      state.currentStep = "OUTCOMES";
    },
    FAILED_CONTACT_REASON: () => {
      // state.currentStep = "OUTCOMES";
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
      state.ui.disableNext = true;
      state.ui.disableComplete = true;
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
    },
    OUTCOMES: () => {
      //  TODO left this comment in case of rollback.
      // if (
      //   !CompleteService.doesPassFailedContactValidation(
      //     state,
      //     completeControllerInput.callDetail.failedContacts,
      //     state.failedContactConfig.config.seedTime
      //   )
      // ) {
      //   state.currentStep = "INSUFFICIENT_CONTACT_ATTEMPTS";
      //   state.ui.disableNext = true;
      //   state.ui.disableComplete = true;
      //   return;
      // }
      if (
        completeControllerInput.callDetail.Service.id === 14 ||
        completeControllerInput.callDetail.Contract.Id === 14
      ) {
        state.currentStep = "TAXI";
        return;
      }
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    TAXI: () => {
      //  Should only have got here from READ_CODES when service is FCMS (14)
      state.currentStep = "VULNERABILITY";
    },
    VULNERABILITY: () => {
      //  Should only have got here from READ_CODES when service is FCMS (14)
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      // TODO remove this step, left here in case of rollback.
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  const gotoBackMap: Record<CompleteStepName111, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    CONTACT_MADE: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    OUTCOMES: () => {
      if (state.dx.isDxValidationRequired) {
        if (state.userResponse.failedContactReason.id !== "") {
          state.currentStep = "FAILED_CONTACT_REASON";
          return;
        }
        if (state.userResponse.clinicalValidation.id !== "") {
          state.currentStep = "CLINICAL_VALIDATION";
          return;
        }
      } else {
        if (state.userResponse.failedContactReason.id !== "") {
          state.currentStep = "FAILED_CONTACT_REASON";
          return;
        }
      }

      state.currentStep = "CONTACT_MADE";
    },
    TAXI: () => {
      state.currentStep = "OUTCOMES";
    },
    VULNERABILITY: () => {
      state.currentStep = "TAXI";
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      state.currentStep = "OUTCOMES";
    },
    CLINICAL_VALIDATION: () => {
      state.currentStep = "CONTACT_MADE";
    },
    FAILED_CONTACT_REASON: () => {
      if (state.dx.isDxValidationRequired) {
        state.currentStep = "CLINICAL_VALIDATION";
        return;
      }
      state.currentStep = "CONTACT_MADE";
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    UNKNOWN: () => {
      console.error("useCompleteController.cancel under construction.");
    }
  };

  return {
    steps: steps,
    validateMap: validateMap,
    gotoNextMap: gotoNextMap,
    gotoBackMap: gotoBackMap
  } as ICompleteUserStepsConfig<CompleteStepName111>;
}
