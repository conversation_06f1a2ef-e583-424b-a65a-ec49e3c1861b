import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { useDynamicQuestions } from "@/common/ui/dynamic-question/models/useDynamicQuestions";
import {
  mockCasOtherQuestions,
  mockCasOtherQuestionsParsed
} from "@/common/ui/dynamic-question/test/cas-other/mockCasOtherQuestions";

describe("casOtherQuestions", () => {
  it("convertFailedContacts", () => {
    const mockQuestions =
      mockCasOtherQuestions.AUDIT_QUESTION_DATA_OTHER.codeID1;
    const questions: Question[] = JSON.parse(mockQuestions);
    // const questions = mockCasOtherQuestionsParsed;

    const dynamicQuestions = useDynamicQuestions();
    dynamicQuestions.init(questions);

    expect(dynamicQuestions.state.questions.length).toEqual(4);

    expect(dynamicQuestions.state.questions[0].id).toEqual(
      "verificationOfDeath"
    );
    expect(dynamicQuestions.state.questions[1].id).toEqual("supportGmc");
    expect(dynamicQuestions.state.questions[2].id).toEqual(
      "safeguardingConcerns"
    );
    expect(dynamicQuestions.state.questions[3].id).toEqual(
      "safeguardingReferral"
    );

    expect(typeof dynamicQuestions.state.questions[1].condition).toEqual(
      "function"
    );
  });
});
