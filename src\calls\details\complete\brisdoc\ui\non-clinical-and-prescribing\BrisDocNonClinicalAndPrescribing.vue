<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <!--    <div class="complete-step&#45;&#45;subheader">-->
    <!--      Non-Clinical support to complete case required-->
    <!--    </div>-->

    <div class="ic24-flex-row ic24-flex-gap">
      <label class="outcomes--label">
        Non-Clinical support to complete case required?
      </label>
      <div class="ic24-flex-row ic24-flex-gap">
        <RadioButton
          class="ic24-flex-row ic24-flex-gap ic24-justify-fle-row-vert-center"
          label="No"
          :option-value="false"
          v-model="valueInternal.nonClinicalSupportToCompleteCaseRequired"
          @onChanged="onInput"
        />
        <RadioButton
          :label="'Yes' + getFollowUpInputStateMessage"
          :option-value="true"
          :isDisabled="followUpInputState.userConfirmed"
          v-model="valueInternal.nonClinicalSupportToCompleteCaseRequired"
          @onChanged="onNonClinicalSupportToCompleteCaseRequired"
        />
      </div>
    </div>

    <!--    followUpInputState: {{ followUpInputState }}-->

    <div
      class="ic24-flex-row ic24-flex-gap"
      v-if="valueInternal.nonClinicalSupportToCompleteCaseRequired"
    >
      <label class="outcomes--label">Type Required</label>
      <div class="ic24-flex-row ic24-flex-gap">
        <select v-model="valueInternal.supportTypeRequired" @change="onInput">
          <option value=""></option>
          <option value="TAKE_LIST">Take List</option>
          <option value="CHILDREN_ED_REFERRAL">Children’s ED Referral</option>
          <option value="OTHER">Other</option>
        </select>
      </div>
    </div>

    <div
      class="ic24-flex-column ic24-flex-gap"
      v-if="valueInternal.supportTypeRequired === 'OTHER'"
    >
      <label class="outcomes--label">Additional Comments</label>
      <textarea
        v-model="valueInternal.supportTypeRequiredComments"
        class="non-clinical-reason--textarea"
        placeholder="Enter any additional comments..."
        @change="onInput"
        rows="5"
        style="resize: vertical;"
      ></textarea>
    </div>

    <!--CAS_MentalHealth-->
    <div class="ic24-flex-row ic24-flex-gap" v-if="isCasCliniMentalHealth">
      <label class="outcomes--label">
        Registered MH Clinician Sign off required
      </label>
      <div class="ic24-flex-row ic24-flex-gap">
        <RadioButton
          label="No"
          :option-value="false"
          v-model="valueInternal.mhClinicianSignOffRequired"
          @onChanged="onInput"
        />
        <RadioButton
          label="Yes"
          :option-value="true"
          v-model="valueInternal.mhClinicianSignOffRequired"
          @onChanged="onMhClinicianSignOffRequired"
        />
      </div>
    </div>
    <!--/CAS_MentalHealth-->

    <!--Prescribing-->
    <div class="ic24-flex-row ic24-flex-gap" v-if="isOohBaseOrVisit">
      <label class="outcomes--label">
        Was the medication issued from stock
      </label>
      <div class="ic24-flex-row ic24-flex-gap">
        <RadioButton
          label="No"
          :option-value="false"
          v-model="valueInternal.medicationIssuedFromStock"
          @onChanged="onInput"
        />
        <RadioButton
          label="Yes"
          :option-value="true"
          v-model="valueInternal.medicationIssuedFromStock"
          @onChanged="onInput"
        />
      </div>
    </div>
    <!--/Prescribing-->

    <div class="ic24-vertical-spacer-large"></div>
  </div>
</template>

<script lang="ts">
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext
} from "@vue/composition-api";
import { IStep } from "@/calls/details/complete/complete-models";
import RadioButton from "@/common/ui/fields/RadioButton.vue";
import { BrisDocNonClinicalAndPrescribingState } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";
import { simpleObjectClone } from "@/common/common-utils";
import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { FollowUpInputState } from "@/calls/details/complete/components/followup/models/follow-up-models";
import { CompleteUserRoleTypes } from "@/calls/details/complete/useCompleteController";
import { IService } from "@/common/services/services-models";

export default defineComponent({
  name: "BrisDocNonClinicalAndPrescribing",
  components: { RadioButton, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"BRISDOC_NON_CLINICAL_AND_PRESCRIBING">>,
      required: true
    },
    value: {
      type: Object as PropType<BrisDocNonClinicalAndPrescribingState>,
      required: true
    },
    service: {
      type: Object as PropType<IService>,
      required: true
    },
    cleoClientService: {
      type: String as PropType<CLEO_CLIENT_SERVICE>,
      required: true
    },
    classification: {
      type: String as PropType<CALL_CLASSIFICATION>,
      required: true
    },
    completeUserRoleTypes: {
      type: Object as PropType<CompleteUserRoleTypes>,
      required: true
    },
    followUpInputState: {
      type: Object as PropType<FollowUpInputState>,
      required: true
    }
  },
  setup(
    props: {
      step: IStep<"BRISDOC_NON_CLINICAL_AND_PRESCRIBING">;
      value: BrisDocNonClinicalAndPrescribingState;
      service: IService;
      cleoClientService: CLEO_CLIENT_SERVICE;
      classification: CALL_CLASSIFICATION;
      completeUserRoleTypes: CompleteUserRoleTypes;
      followUpInputState: FollowUpInputState;
    },
    context: SetupContext
  ) {
    const valueInternal = reactive<BrisDocNonClinicalAndPrescribingState>(
      simpleObjectClone(props.value)
    );

    if (props.followUpInputState.userConfirmed) {
      valueInternal.nonClinicalSupportToCompleteCaseRequired = false;
      context.emit("input", simpleObjectClone(valueInternal));
    }

    function onMhClinicianSignOffRequired() {
      if (valueInternal.mhClinicianSignOffRequired) {
        valueInternal.nonClinicalSupportToCompleteCaseRequired = false;
        valueInternal.supportTypeRequired = "";
      }
      onInput();
    }

    function onNonClinicalSupportToCompleteCaseRequired() {
      if (valueInternal.nonClinicalSupportToCompleteCaseRequired) {
        valueInternal.mhClinicianSignOffRequired = false;
        valueInternal.supportTypeRequired = "";
      }
      onInput();
    }

    function onInput() {
      context.emit("input", simpleObjectClone(valueInternal));
    }

    const isCasCliniMentalHealth = computed(() => {
      if (!props.completeUserRoleTypes.isCasClinician) {
        return false;
      }
      return props.cleoClientService === "MENTAL_HEALTH";
    });

    /**
     * but...what happens if an OOH get's classification switched to Advice...
     * it shouldn't , but it might.
     */
    const isOohBaseOrVisit = computed(() => {
      // return (
      //   (props.serviceType === "OOH") &
      //   (props.classification.toUpperCase() === "BASE" ||
      //     props.classification.toUpperCase() === "VISIT")
      // );
      return props.service.serviceType === "OOH";
    });

    // const isCasCliniOrOohBase = computed(() => {
    //   return (
    //     props.completeUserRoleTypes.isCasClinician ||
    //     props.classification.toUpperCase() === "BASE"
    //   );
    // });

    // const isCasOrOohBase = computed(() => {
    //   return (
    //     props.service.serviceType === "CAS" ||
    //     (props.service.serviceType === "OOH" &&
    //       props.classification.toUpperCase() === "BASE")
    //   );
    // });

    /**
     * If user has confirmed the follow up, then disable the "Yes" option.
     * If user did select this option:  a "follow up" case would be cloned
     * and placed in the oversight queue BUT this call would NOT be completed and
     * placed in the oversight queue as well!!!
     */
    const getFollowUpInputStateMessage = computed(() => {
      const followUpInputState = props.followUpInputState;

      if (followUpInputState.userConfirmed) {
        return (
          " (Disabled - " +
          followUpInputState.dxCode!.description +
          " " +
          followUpInputState.followUpType!.classification +
          " follow up requested)"
        );
      }
      return "";
    });

    return {
      valueInternal,

      // isCasCliniOrOohBase,
      // isCasOrOohBase,
      isCasCliniMentalHealth,
      isOohBaseOrVisit,
      getFollowUpInputStateMessage,

      onInput,
      onNonClinicalSupportToCompleteCaseRequired,
      onMhClinicianSignOffRequired
    };
  }
});
</script>
