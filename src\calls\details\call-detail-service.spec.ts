import { convertFailedContacts } from "@/calls/details/call-detail-service";
import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";

describe("CallDetailService", () => {
  it("convertFailedContacts", () => {
    const legacyCallData = ({
      PatientContactCode_Events: {
        VALUE: [
          "CN=<PERSON>/O=sehnp~Answerphone - Message left~2024-06-28T01:49:00",
          "CN=<PERSON>/O=sehnp~No Answer~2024-06-28T02:29:46",
          "CN=<PERSON>/O=sehnp~Answerphone - Message left~2024-06-28T02:48:21",
          "CN=<PERSON>/O=sehnp~Answerphone - Message left~2024-06-28T04:03:52",
          "CN=<PERSON>/O=sehnp~Answerphone - Message left~2024-06-28T04:07:21"
        ]
      }
    } as any) as ICallDetailLegacy;

    const result = convertFailedContacts(legacyCallData);
    expect(result.length).toEqual(5);
    expect(result[0].userName).toEqual("CN=<PERSON>/O=sehnp");
    expect(result[0].type).toEqual("Answerphone - Message left");
    expect(result[0].time).toEqual("2024-06-28T01:49:00");
  });
});
