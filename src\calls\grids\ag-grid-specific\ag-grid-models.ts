import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";

export type filterKeys = Pick<
  ICleoCallSummary,
  "CallClassification" | "CallSubClassification"
>;

export type IAgGridFilter = Partial<
  Record<keyof ICleoCallSummary, IAgGridFilterModel>
>;

export interface IAgGridFilterModel {
  type: "startsWith" | "contains";
  filterType: "text" | "number" | "date";
  filter: string;
}
