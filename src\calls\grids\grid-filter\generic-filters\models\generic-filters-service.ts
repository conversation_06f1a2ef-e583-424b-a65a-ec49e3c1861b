import { ButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";

const gridFilterService = new GridFilterService();

export function factoryButtonFiltersControllerState(): ButtonFiltersControllerState {
  return {
    isLoading: false,
    gridFilterUserInput: gridFilterService.factoryGridFilterUserInput(),
    dxGroupTypes: {
      "Ambulance Validation": [],
      "Toxic Ingestion": [],
      Other: []
    }
  };
}
