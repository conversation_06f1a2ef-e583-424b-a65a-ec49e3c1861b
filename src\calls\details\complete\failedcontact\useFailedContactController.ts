import { UnwrapRef } from "@vue/reactivity";
import * as FailedContactService from "./failed-contact-service";
import {
  BaseStepController,
  IStep,
  IValidationMessage,
  StepAction,
} from "../complete-models";

import { simpleObjectClone } from "@/common/common-utils";
import { validationMessageNotSelected } from "../complete-service";
import { ISimpleButtonInputValue } from "../simple-button-selector-models";
import { HowManaged, HowManagedValue } from "./how-managed/how-managed-models";
import { FailedContactReason } from "./failed-contact-reasons/failed-contact-models";
import { IFailedContactControllerState } from "./IFailedContactControllerState";

export type FaildContactStepName =
  | "END_ASSESSMENT_CONFIRMATION"
  | "HOW_WAS_CASE_MANAGED"
  | "CONTACT_MADE"
  | "FAILED_CONTACT_SAFEGUARDING"
  | "UNKNOWN";

export type FailedContactController = BaseStepController<
  IFailedContactControllerState
> & {
  onHowMangedSelected: (
    output: ISimpleButtonInputValue<HowManaged, HowManagedValue>
  ) => void;
  contactMade: (wasContacted: boolean) => void;
  onFailedContactReasonSelected: (
    output: ISimpleButtonInputValue<FailedContactReason, FailedContactReason>
  ) => void;
  validate: () => void;
};

export function useFailedContactController(
  stateInject?: UnwrapRef<IFailedContactControllerState>
): FailedContactController {
  const state:
    | IFailedContactControllerState
    | UnwrapRef<IFailedContactControllerState> = stateInject
    ? stateInject
    : FailedContactService.factoryFailedContactControllerState();

  function goto(stepName: StepAction) {
    stepName === "NEXT" ? gotoNext() : gotoBack();
  }

  function simpleLogger(message: string, data?: unknown) {
    if (state.debug) {
      console.warn(
        "useFailedContactController.simpleLogger >>> " + message,
        data
      );
    }
  }

  function gotoNext(): void {
    const currentStepName = state.currentStep;
    const currentStep: IStep<FaildContactStepName> =
      state.steps[currentStepName];

    simpleLogger("gotoNext currentStepName: " + currentStepName);

    simpleLogger(
      "gotoNext validationMessages A: " + state.validationMessages.length
    );
    validate();
    simpleLogger(
      "gotoNext validationMessages B: " + state.validationMessages.length
    );

    if (state.validationMessages.length > 0 && currentStep.requiresValidation) {
      return;
    }

    const nextStepMap: Record<FaildContactStepName, () => void> = {
      END_ASSESSMENT_CONFIRMATION: () => {
        state.currentStep = "HOW_WAS_CASE_MANAGED";
      },
      HOW_WAS_CASE_MANAGED: () => {
        state.currentStep = "CONTACT_MADE";
      },
      CONTACT_MADE: () => {
        if (state.userResponse.contactMade === null) {
          //  should not have got through validation, but checking.
          return;
        }

        if (state.userResponse.contactMade) {
          state.currentStep = "UNKNOWN";
          return;
        }
        state.currentStep = "FAILED_CONTACT_SAFEGUARDING";
      },
      FAILED_CONTACT_SAFEGUARDING: () => {
        // state.currentStep = "CONTACT_MADE";
      },
      UNKNOWN: () => {
        // state.currentStep = "CONTACT_MADE";
      },
    };

    if (nextStepMap[currentStepName]) {
      nextStepMap[currentStepName]();
    }
  }

  function validate() {
    const currentStepName = state.currentStep;

    state.validationMessages = [];

    //  Validate step.
    const validationMap: Record<
      FaildContactStepName,
      () => IValidationMessage[]
    > = {
      END_ASSESSMENT_CONFIRMATION: () => {
        return [];
      },
      HOW_WAS_CASE_MANAGED: () => {
        return state.userResponse.howManaged.id === ""
          ? [validationMessageNotSelected()]
          : [];
      },
      CONTACT_MADE: () => {
        if (state.userResponse.contactMade === null) {
          return [validationMessageNotSelected()];
        }
        if (state.userResponse.contactMade) {
          return [];
        }
        if (state.userResponse.failedContactReason.id === "") {
          return [
            {
              id: "FAILED_CONTACT_REASON",
              message: "Please select a failed contact reason",
            },
          ];
        }
        return [];
      },
      FAILED_CONTACT_SAFEGUARDING: () => {
        return [
          {
            id: "VALIDATION_UNDER_CONSTRUCTION__FAILED_CONTACT_SAFEGUARDING",
            message:
              "VALIDATION_UNDER_CONSTRUCTION__FAILED_CONTACT_SAFEGUARDING",
          },
        ];
      },
      UNKNOWN: () => {
        return [
          {
            id: "VALIDATION_UNDER_CONSTRUCTION__UNKNOWN",
            message: "VALIDATION_UNDER_CONSTRUCTION__UNKNOWN",
          },
        ];
      },
    };

    const messages = validationMap[currentStepName]
      ? validationMap[currentStepName]()
      : [
          {
            id: "COULD_NOT_VALIDATE",
            message: "COULD_NOT_VALIDATE",
          },
        ];
    state.validationMessages = messages;
  }

  function gotoBack() {
    console.error("useFailedContactController.gotoBack under construction, ");
  }

  function cancel() {
    console.error("useFailedContactController.cancel under construction, ");
  }

  function onHowMangedSelected(
    output: ISimpleButtonInputValue<HowManaged, HowManagedValue>
  ): void {
    state.userResponse.howManaged = simpleObjectClone(output);
    autoProgressStep();
  }

  function contactMade(wasContacted: boolean) {
    state.userResponse.contactMade = wasContacted;
  }

  function onFailedContactReasonSelected(
    output: ISimpleButtonInputValue<FailedContactReason, FailedContactReason>
  ): void {
    state.userResponse.failedContactReason = simpleObjectClone(output);
    autoProgressStep();
  }

  function autoProgressStep() {
    if (state.autoProgress) {
      goto("NEXT");
    }
  }

  return {
    state,
    goto,
    validate,
    cancel,
    onHowMangedSelected,
    contactMade,
    onFailedContactReasonSelected,
  };
}
