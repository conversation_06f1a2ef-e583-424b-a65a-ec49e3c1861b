<template>
  <transition name="modal">
    <div class="e4s-modal-mask">
      <div
        class="e4s-modal-wrapper"
        :class="isFullSizeForm ? 'e4s-modal-wrapper--full-size' : ''"
      >
        <div
          class="e4s-modal-container"
          :class="getCssClass"
          :style="getCssStyle"
        >
          <slot name="header">
            <div class="e4s-modal-header">
              <div class="e4s-modal-header" v-text="headerMessage"></div>
            </div>
          </slot>

          <!--          <slot name="body">-->
          <!--            <div class="e4s-modal-body">-->
          <!--              <div class="e4s-modal-body" v-html="bodyMessage"></div>-->
          <!--            </div>-->
          <!--          </slot>-->

          <div class="e4s-modal-body">
            <slot name="body">
              <div class="e4s-modal-body" v-html="bodyMessage"></div>
            </slot>
          </div>

          <slot name="footer">
            <div class="e4s-modal-footer">
              <slot name="buttons">
                <slot name="button-close-secondary">
                  <button
                    class="adapter-button adapter-width-5 adapter-button--red"
                    v-on:click.stop="closeSecondary"
                  >
                    <span v-text="buttonSecondaryText"></span>
                  </button>
                </slot>

                <slot name="button-close-primary">
                  <button
                    class="adapter-button adapter-width-5 adapter-button--green"
                    v-on:click.stop="closePrimary"
                  >
                    <span v-text="buttonPrimaryText"></span>
                  </button>
                </slot>

                <div v-if="isLoading" class="e4s-force-inline-block">
                  <LoadingSpinner></LoadingSpinner>
                </div>
              </slot>
            </div>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { loggerInstance } from "@/common/Logger";

@Component({
  name: "cleo-modal",
  components: {
    LoadingSpinner
  }
})
export default class CleoModal extends Vue {
  @Prop({ default: "" }) public headerMessage!: string;
  @Prop({ default: "" }) public bodyMessage!: string;
  @Prop({ default: "Cancel" }) public buttonSecondaryText!: string;
  @Prop({ default: "OK" }) public buttonPrimaryText!: string;
  @Prop({ default: "" }) public cssClass!: string;
  @Prop({ default: "" }) public cssDialogStyle!: string;
  @Prop({ default: false }) public isLoading!: boolean;
  @Prop({ default: false }) public isFullSizeForm!: boolean;
  @Prop({ default: false }) public removeBodyScroll!: boolean;

  public created(): void {
    loggerInstance.log("......created");
    if (this.removeBodyScroll) {
      document.body.style.overflow = "hidden";
    }
  }

  public get getCssClass(): string {
    if (this.isFullSizeForm) {
      return "e4s-modal-container--full-size" + " " + this.cssClass;
    }
    return this.cssClass.length === 0
      ? "e4s-modal-container-size"
      : this.cssClass;
  }

  public get getCssStyle(): string {
    return this.cssDialogStyle ? this.cssDialogStyle : "";
  }

  public closeSecondary(): void {
    loggerInstance.log("......closeSecondary");
    this.$emit("closeSecondary");
  }

  public closePrimary(): void {
    loggerInstance.log("......closePrimary");
    this.$emit("closePrimary");
  }

  public beforeDestroy(): void {
    loggerInstance.log("......beforeDestroy");
    if (this.removeBodyScroll) {
      document.body.style.overflow = "auto";
    }
  }
}
</script>

<style scoped>
.e4s-modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: table;
  transition: opacity 0.3s ease;
}

.e4s-modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.e4s-modal-container {
  /*width: 300px;*/
  /*width: 100%;*/
  /*height: 100%;*/
  margin: 0px auto;
  /*padding: 20px 30px;*/
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  transition: all 0.3s ease;
  /*font-family: Helvetica, Arial, sans-serif;*/
}

.e4s-modal-container-size {
  width: fit-content;
  max-width: 100vw;
  /*height: 50%;*/
}

.e4s-modal-container--full-size {
  width: 100% !important;
  padding: 0 !important;
  /*height: 50%;*/
}

.e4s-modal-header {
  /*font-size: 1.5rem;*/
  background: transparent
    linear-gradient(180deg, #e9effd 0%, #cbdff6 64%, #bcd5f0 65%, #bcd5f0 100%)
    0 0 no-repeat padding-box;
  padding: 5px;
}

.e4s-modal-body {
  /*margin: 20px 0;*/
  padding: 5px;
}

.e4s-modal-footer {
  padding: 5px;
}

.e4s-modal-footer button {
  /*float: right;*/
  margin-right: 5px;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.modal-enter {
  opacity: 0;
}

.modal-leave-active {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.e4s-modal-wrapper--full-size {
  padding: 0 !important;
}

@media only screen and (max-height: 700px) {
  .e4s-modal-container-size {
    width: 100%;
    /*height: 50%;*/
  }
}
</style>
