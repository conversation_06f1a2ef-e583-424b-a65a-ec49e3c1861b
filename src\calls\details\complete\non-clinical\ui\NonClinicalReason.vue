<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">Select Non Clinical Reason</div>

    <div class="ic24-vertical-spacer-large"></div>

    <div class="ic24-flex-column ic24-flex-gap">
      <div class="ic24-flex-row ic24-flex-gap">
        <div class="ic24-flex-column ic24-flex-gap">
          <!--          <RadioButton-->
          <!--            v-for="option in options"-->
          <!--            :key="option.id"-->
          <!--            :label="option.description"-->
          <!--            :option-value="option.description"-->
          <!--            v-model="valueInternal.reason"-->
          <!--            @onChanged="onInput"-->
          <!--          />-->
          <RadioButtonObj
            v-for="option in options"
            :key="option.id"
            v-model="valueInternal.reason"
            :option-value="option"
            :label="option.description"
            @onChanged="onInput"
          />
        </div>
      </div>

      <div
        class="ic24-flex-column ic24-flex-gap"
        v-if="valueInternal.reason && valueInternal.reason.id === 'Other'"
      >
        <label class="outcomes--label">Additional Comments</label>
        <textarea
          v-model="valueInternal.comment"
          class="non-clinical-reason--textarea"
          placeholder="Enter any additional comments..."
          @change="onInput"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  ref,
  onMounted
} from "@vue/composition-api";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
// import RadioButton from "@/common/ui/fields/RadioButton.vue";
import {
  IStep,
  NonClinicalReasonType
} from "@/calls/details/complete/complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import { INonClinicalReason } from "@/calls/details/complete/complete-models";
import { ISimpleButtonInputValue } from "@/calls/details/complete/simple-button-selector-models";
import { getNonClinicalReasons } from "@/calls/details/complete/non-clinical/api/non-clinical-reason-api";
import RadioButtonObj from "@/common/ui/fields/RadioButtonObj.vue";

export default defineComponent({
  name: "NonClinicalReason",
  components: { RadioButtonObj, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"NON_CLINICAL_REASON">>,
      required: true
    },
    value: {
      type: Object as PropType<INonClinicalReason>,
      required: true
    }
  },
  setup(props, context) {
    const valueInternal = reactive<INonClinicalReason>(
      simpleObjectClone(props.value)
    );

    const options = ref<
      ISimpleButtonInputValue<NonClinicalReasonType, NonClinicalReasonType>[]
    >([]);

    onMounted(async () => {
      getNonClinicalReasons().then(response => {
        const responseOptions = Object.values(response);

        options.value = responseOptions.map<
          ISimpleButtonInputValue<NonClinicalReasonType, NonClinicalReasonType>
        >(item => ({
          id: item.description as NonClinicalReasonType,
          description: item.description,
          value: item.description as NonClinicalReasonType
        }));
      });
    });

    function onInput() {
      context.emit("input", simpleObjectClone(valueInternal));
    }

    return {
      valueInternal,
      options,
      onInput
    };
  }
});
</script>

<style scoped>
.non-clinical-reason--textarea {
  padding: 4px;
  height: 80px;
  min-height: 80px;
  resize: vertical;
  width: 95%;
}
</style>
