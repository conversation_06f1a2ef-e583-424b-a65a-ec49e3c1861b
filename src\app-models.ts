import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";
import { ICleoPermission } from "@/permissions/permission-models";

export interface IAdapterMenuMapper {
  id: string;
}

export type LEGACY_ACTION_TYPE =
  | ""
  | "ROUTE"
  | "REFRESH_GRID"
  | "OPEN_CALL"
  | "CALL_DETAIL_RETRIEVED"
  | "START_CONSULT"
  | "SHOW_ADDRESS_MAP"
  | "SAVE_AND_RETURN"
  | "END_ASSESSMENT";

export interface ILegacyAction {
  actionType: LEGACY_ACTION_TYPE;
  data?: any;
}

// export interface ILegacyMenuMapper {
//   path: string;
// }

export interface ILegacyRouteTo extends ILegacyAction {
  actionType: "ROUTE";
  data: {
    path: string;
  };
}

export interface ILegacyOpenCall extends ILegacyAction {
  actionType: "OPEN_CALL";
  data: {
    callNumber: number;
  };
}

export interface ILegacyActionCallDetail extends ILegacyAction {
  actionType: "CALL_DETAIL_RETRIEVED";
  data: {
    callDetail: ICallDetailLegacy;
    userPermissions: Record<string, ICleoPermission>;
  };
}

export interface IAdapterAction {
  payload: ILegacyAction | ILegacyRouteTo | ILegacyOpenCall;
}
