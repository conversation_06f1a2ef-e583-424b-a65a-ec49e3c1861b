import { computed } from "@vue/composition-api";
import { appStore } from "@/store/store";
import { IPaccsStoreState, PACCS_STORE_CONST } from "@/paccs/paccs-store";

const store = appStore;

const paccsStoreState = computed<IPaccsStoreState>(() => {
  return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
});

export const isPaccsReady = computed<boolean>(() => {
  return paccsStoreState.value.addCaseRecordResponse.caseId.length > 0;
});

// export function useCallDetailController() {
//   function hasPermission(permName: string) {
//     window.CallControllerClient.hasPermission(permName);
//   }
//
//   return {
//     hasPermission
//   };
// }
