import { BrisDocNonClinicalAndPrescribingState } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";

export function factoryBrisDocNonClinicalAndPrescribingState(): BrisDocNonClinicalAndPrescribingState {
  return {
    nonClinicalSupportToCompleteCaseRequired: null,
    supportTypeRequired: "",
    supportTypeRequiredComments: "",
    mhClinicianSignOffRequired: null,
    medicationIssuedFromStock: null
  };
}
