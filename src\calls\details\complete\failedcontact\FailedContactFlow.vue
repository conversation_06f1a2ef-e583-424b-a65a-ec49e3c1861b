<template>
  <div>
    FailedContactFlow

    <Timeline :value="testEvents" layout="horizontal">
      <template #content="slotProps">
        {{ slotProps.item.status }}
      </template>
    </Timeline>

    <!-- <exit-reasons></exit-reasons> -->

    <EndAssessmentConfirmation
      v-if="controller.state.currentStep === 'END_ASSESSMENT_CONFIRMATION'"
    />

    <HowManaged
      :value="controller.state.userResponse.howManaged"
      v-if="controller.state.currentStep === 'HOW_WAS_CASE_MANAGED'"
      @input="controller.onHowMangedSelected"
    />

    <div class="ic24-flex-row ic24-flex-gap">
      <div class="ic24-flex-row ">
        <Ic24Button
          title="Back"
          @click="controller.goto('BACK')"
          :disabled="
            controller.state.currentStep === 'END_ASSESSMENT_CONFIRMATION'
          "
        />
        <Ic24Button
          title="Cancel & Return to Queue"
          @click="controller.cancel"
        />
      </div>
      <div class="ic24-flex-row ">
        <Ic24Button
          title="Next"
          @click="controller.goto('NEXT')"
          :disabled="controller.state.isProcessComplete"
        />
        <Ic24Button
          title="Complete"
          @click="complete"
          :disabled="!controller.state.isProcessComplete"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  reactive,
  computed
} from "@vue/composition-api";
import {
  FaildContactStepName,
  useFailedContactController
} from "./useFailedContactController";
// import ExitReasons from "./exitreasons/ExitReasons.vue";
import * as FailedContactService from "./failed-contact-service";
import EndAssessmentConfirmation from "../components/EndAssessmentConfirmation.vue";
import { simpleObjectClone } from "@/common/common-utils";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { IStep } from "../complete-models";
import HowManaged from "./how-managed/HowManaged.vue";
// import Timeline from "primevue/timeline/Timeline";

export default defineComponent({
  name: "FailedContactFlow",
  components: { EndAssessmentConfirmation, Ic24Button, HowManaged },
  props: {},
  setup(props: any, context: SetupContext) {
    const controller = useFailedContactController(
      reactive(FailedContactService.factoryFailedContactControllerState())
    );

    function complete() {
      if (!controller.state.isProcessComplete) {
        console.error(
          "SaveAndReturnFlow isProcessComplete: " +
            controller.state.isProcessComplete +
            ", not ready for complete."
        );
      }

      context.emit("onComplete", simpleObjectClone(controller.state));
    }

    const getTitle = computed(() => {
      const step: IStep<FaildContactStepName> =
        controller.state.steps[controller.state.currentStep];
      return step ? step.title : "Unknown Step";
    });

    const testEvents = [
      {
        status: "Ordered",
        date: "15/10/2020 10:30",
        icon: "pi pi-shopping-cart",
        color: "#9C27B0",
        image: "game-controller.jpg"
      },
      {
        status: "Processing",
        date: "15/10/2020 14:00",
        icon: "pi pi-cog",
        color: "#673AB7"
      },
      {
        status: "Shipped",
        date: "15/10/2020 16:15",
        icon: "pi pi-shopping-cart",
        color: "#FF9800"
      },
      {
        status: "Delivered",
        date: "16/10/2020 10:00",
        icon: "pi pi-check",
        color: "#607D8B"
      }
    ];

    return { controller, complete, getTitle, testEvents };
  }
});
</script>

<style scoped></style>, computedFaildContactStepName, import { IStep } from
"../complete-models";
