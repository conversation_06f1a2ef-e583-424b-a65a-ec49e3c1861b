import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import {
  CompleteStepName,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IStep,
  IValidationMessage,
  SaveAndReturnStepName111,
  SaveAndReturnStepNameCas
} from "@/calls/details/complete/complete-models";
import {
  factoryStep,
  factorySteps,
  validationMapDefault
} from "@/calls/details/complete/complete-service";

export function factoryUserStepsConfigSaveAndReturnCas(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<SaveAndReturnStepNameCas> {
  const stepsDefault: Record<
    CompleteStepName,
    IStep<CompleteStepName>
  > = factorySteps();

  const steps: Record<SaveAndReturnStepNameCas, IStep<CompleteStepName>> = {
    EXIT_REASON: stepsDefault.EXIT_REASON,
    HOW_WAS_CASE_MANAGED: stepsDefault.HOW_WAS_CASE_MANAGED,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    FAILED_CONTACT_WARNING: stepsDefault.FAILED_CONTACT_WARNING,
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
  };

  const validationDefault = validationMapDefault(
    state,
    completeControllerInput
  );

  const validateMap: Record<
    SaveAndReturnStepNameCas,
    () => IValidationMessage[]
  > = {
    EXIT_REASON: validationDefault.EXIT_REASON,
    HOW_WAS_CASE_MANAGED: validationDefault.HOW_WAS_CASE_MANAGED,
    FAILED_CONTACT_REASON: validationDefault.FAILED_CONTACT_REASON,
    FAILED_CONTACT_WARNING: validationDefault.FAILED_CONTACT_WARNING,
    UNKNOWN: validationDefault.UNKNOWN
  };

  const gotoNextMap: Record<SaveAndReturnStepNameCas, () => void> = {
    EXIT_REASON: () => {
      // state.isProcessComplete = state.steps[state.currentStep].isValid;
      if (state.userResponse.exitReason.id === "NO_ACTION_TAKEN") {
        //  Case will save and close, unassign clini and reapply breach.
        state.isProcessComplete = state.steps[state.currentStep].isValid;
        if (state.isProcessComplete) {
          state.finalAction = "SAVE_AND_RETURN_TO_QUEUE";
        }
        return;
      }
      if (state.userResponse.exitReason.id === "FURTHER_ACTION_REQUIRED") {
        state.isProcessComplete = state.steps[state.currentStep].isValid;
        if (state.isProcessComplete) {
          state.finalAction = "FURTHER_ACTION_REQUIRED";
        }
        return;
      }

      //  Failed contact
      state.currentStep = "HOW_WAS_CASE_MANAGED";
    },
    HOW_WAS_CASE_MANAGED: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "FAILED_CONTACT_WARNING";

      state.ui.disableNext = true;
      state.ui.disableComplete = true;
    },
    FAILED_CONTACT_WARNING: () => {
      state.currentStep = "FAILED_CONTACT_WARNING";
      state.isProcessComplete = state.steps[state.currentStep].isValid;
      if (state.isProcessComplete) {
        state.finalAction =
          state.userResponse.failedContactWarning.id === "RETURN_TO_OPEN_CASE"
            ? "RETURN_TO_OPEN_CASE"
            : "SAVE_AND_RETURN_TO_QUEUE";
      }
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  const gotoBackMap: Record<SaveAndReturnStepNameCas, () => void> = {
    EXIT_REASON: () => {
      state.currentStep = "EXIT_REASON";
    },
    HOW_WAS_CASE_MANAGED: () => {
      state.currentStep = "EXIT_REASON";
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "HOW_WAS_CASE_MANAGED";
    },
    FAILED_CONTACT_WARNING: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  return {
    steps: steps,
    validateMap: validateMap,
    gotoNextMap: gotoNextMap,
    gotoBackMap: gotoBackMap
  } as ICompleteUserStepsConfig<SaveAndReturnStepName111>;
}

export function validationMessageNotSelected(): IValidationMessage {
  return {
    id: "NOT_SELECTED",
    message: "Please select an option"
  };
}
