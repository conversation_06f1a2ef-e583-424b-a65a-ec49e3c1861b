import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import {
  ICompleteUserStepsConfig,
  NonClinicalCompleteStepName,
  CompleteStepName,
  IStep,
  ICompleteControllerState
} from "../complete-models";
import { factorySteps, validationMapDefault } from "../complete-service";
import { factoryStep } from "../complete-service";

export function factoryUserStepsConfigNonClinical(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<NonClinicalCompleteStepName> {
  const stepsDefault: Record<
    CompleteStepName,
    IStep<CompleteStepName>
  > = factorySteps();

  const steps: Record<NonClinicalCompleteStepName, IStep<CompleteStepName>> = {
    END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
    NON_CLINICAL_REASON: stepsDefault.NON_CLINICAL_REASON,
    OUTCOMES: stepsDefault.OUTCOMES,
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
  };

  const gotoNextMap: Record<NonClinicalCompleteStepName, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "NON_CLINICAL_REASON";
    },
    NON_CLINICAL_REASON: () => {
      if (
        state.userResponse.nonClinicalReason.reason.id.toUpperCase() === "OTHER"
      ) {
        state.isProcessComplete = state.steps[state.currentStep].isValid;
        return;
      }
      state.currentStep = "OUTCOMES";
    },
    OUTCOMES: () => {
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    UNKNOWN: () => {
      console.error("Unknown step");
    }
  };

  const gotoBackMap: Record<NonClinicalCompleteStepName, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    NON_CLINICAL_REASON: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    OUTCOMES: () => {
      state.currentStep = "NON_CLINICAL_REASON";
    },
    UNKNOWN: () => {
      console.error("Unknown step");
    }
  };

  return {
    steps,
    validateMap: validationMapDefault(state, completeControllerInput),
    gotoNextMap,
    gotoBackMap
  } as ICompleteUserStepsConfig<NonClinicalCompleteStepName>;
}
