import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { ICleoPermission } from "@/permissions/permission-models";
import { IContextMenuStoreState } from "@/calls/grids/contextmenu/context-menu-store";
import { CommonService } from "@/common/common-service";
import {
  CLEO_ACTION_NAME,
  CLEO_CALL_ACTIONS,
  ICleoAction
} from "@/common/cleo-common-models";
import { PermissionService } from "@/permissions/permission-service";
const permissionService: PermissionService = new PermissionService();

export class CleoContextMenuService {
  public factoryContextMenuStoreState(): IContextMenuStoreState {
    return {
      showMenu: false,
      permsLoading: false,
      cleoCallSummary: new CallSummaryService().factoryCleoCallSummary(),
      permsForCall: {},
      permsForCallSimple: {},
      permsForMenuSimple: {},
      clickedElementCoords: {
        x: 0,
        y: 0
      }
    };
  }

  public getActionsForRightClick(): ICleoAction[] {
    const menuActionNames: CLEO_ACTION_NAME[] = [
      "MAKE_APPOINTMENT",
      "ARRIVED",
      "ACKNOWLEDGE_RECEIPT_BASE",
      "COMFORT_COURTESY_CALL",
      "EDIT CLASSIFICATION",
      "DOWNGRADE",
      "ASSIGN",
      "ASSIGN_BASE",
      "SECONDARY_ASSIGN",
      "PRIORITY",
      "DISPATCH_VEHICLE",
      "RETRIEVE_VEHICLE",
      "PRINT",
      "UNLOCK CALL"
    ];

    return new CommonService()
      .convertObjectToArray(CLEO_CALL_ACTIONS)
      .filter((cleoAction: ICleoAction) => {
        return menuActionNames.indexOf(cleoAction.id) > -1;
      });
  }

  public filterPermsForRightClick(
    permsForCall: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    return Object.keys(permsForCall).reduce((accum, key) => {
      if (permsForCall[key].PermissionForm === "WEBUI_DOCVIEW") {
        accum[key] = { ...permsForCall[key] };
      }
      return accum;
    }, {} as Record<string, ICleoPermission>);
  }

  public getActionsWithPermissionsApplied(
    cleoActions: ICleoAction[],
    permsForCall: Record<string, ICleoPermission>
  ): ICleoAction[] {
    return cleoActions.filter(cleoAction => {
      const hasPerm = permissionService.getUserPermission(
        permsForCall,
        cleoAction.permissionName,
        "WEBUI_DOCVIEW"
      );
      return hasPerm;
    });
  }
}
