/**
 * Usage:
 * import { testLoggerInstance } from "@/common/Logger";
 *  testLoggerInstance.setLogLevel(3);
    testLoggerInstance.log(
      "HelloWorld.....",
      testLoggerInstance.LOG_LEVELS.DEBUG
    );
 */
import { simpleObjectClone } from "@/common/common-utils";

type logLevelLevels = 0 | 1 | 2 | 3 | 4;

class Logger {
  private static _instance: Logger;

  public LOG_LEVELS = {
    DEBUG: 4,
    WARN: 3,
    INFO: 2,
    ERROR: 1,
    NONE: 0
  };

  private logLevel: logLevelLevels = 1;

  private constructor() {
    this.logLevel = process.env.NODE_ENV === "development" ? 4 : 1;
  }

  public static get Instance() {
    // Do you need arguments? Make it a regular static method instead.
    return this._instance || (this._instance = new this());
  }

  public setLogLevel(logLevel: logLevelLevels) {
    this.logLevel = logLevel;
  }

  public log(message: string, obj?: any, logLevel = 4) {
    if (logLevel > this.logLevel) {
      return;
    }

    if (obj) {
      if (process.env.NODE_ENV === "development") {
        console.warn(
          "Logger.log() logLevel: " +
            logLevel +
            ", message: " +
            message +
            ", >>> DEBUG, printing Obj",
          obj
        );
      } else {
        console.warn(
          "Logger.log() logLevel: " +
            logLevel +
            ", message: " +
            message +
            ", obj passed BUT WILL NOT BE LOGGED."
        );
      }

      const logPayload = process.env.NODE_ENV === "development";
      if (logPayload && obj) {
        //  TODO Memory leak...DO NOT DO THIS IN PRODUCTION!!!!
        console.error(
          "Logger.log() logLevel: " + logLevel + ", message: " + message,
          obj
        );
        console.error(
          "Logger.log() logLevel: " + logLevel + ", message: " + message
        );
      }
    } else {
      console.log(
        "Logger.log() logLevel: " + logLevel + ", message: " + message
      );
    }
  }

  public error(message: string) {
    console.error("Logger.log() " + message);
  }
}

export const loggerInstance: Logger = Logger.Instance;
