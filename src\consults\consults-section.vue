<template>
  <div>
<!--    Consults Section-->
    <div v-for="consult in consults" :key="consult.Id">
      <consult :consult="consult" class="consults-section--separate"></consult>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  watch,
  SetupContext
} from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IConsult } from "@/consults/consult-models";
import Consult from "@/consults/consult.vue";

const commonService: CommonService = new CommonService();

export default defineComponent({
  // type inference enabled
  name: "consults-section",
  components: { Consult },
  props: {
    consults: {
      default: () => {
        return [];
      }
    }
  },
  setup(props: { consults: IConsult[] }, context: SetupContext) {
    let consultsInternal: IConsult[] = reactive(
      commonService.simpleObjectClone(props.consults)
    );

    onBeforeMount(() => {
      console.log("consults-section>>>>>>>>>>>>>>>>>>mounted!");
    });

    watch(
      () => props.consults,
      (newValue: IConsult[], oldValue: IConsult[]) => {
        console.log("consults-section>>>>>>>>>>>>>>>>>>watch!", {
          newValue,
          oldValue
        });
        Object.assign(consultsInternal, newValue);
      }
    );

    return {
      consultsInternal
    };
  }
});
</script>

<style scoped>
.consults-section--separate {
  margin-top: 1px;
}
</style>
