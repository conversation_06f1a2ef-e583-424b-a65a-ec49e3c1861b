<template>
  <div>
    <LoadingSpinnerLarge v-if="!isReady" />
    <div v-if="isReady">
      <What3WordsCleo
        :what3words-controller-state="what3wordsControllerState"
        :call-detail="state.callDetail"
        v-on:close="what3wordsMapConfirmed(null)"
        v-on:confirmed="what3wordsMapConfirmed"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  ref
} from "@vue/composition-api";
import What3WordsCleo from "@/what3words/What3WordsCleo.vue";
import {
  IWhat3wordsControllerState,
  IWhat3WordsMapResponse
} from "@/what3words/what3words-cleo-models";
import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
import { CommonService } from "@/common/common-service";
import { useRoute } from "@/router/migrateRouterVue3";
import { loggerInstance } from "@/common/Logger";
import { appStore } from "@/store/store";
import { USER_STORE_CONST } from "@/user/user-store";
import { IUser } from "@/user/user-models";
import { CLeoPermissionServerResponse } from "@/permissions/permission-models";
import { PermissionService } from "@/permissions/permission-service";
import * as CallDetailService from "@/calls/details/call-detail-service";
import * as CallDetailData from "@/calls/details/call-detail-data";
import LoadingSpinnerLarge from "@/common/ui/loading-spinner-large.vue";

const commonService = new CommonService();

export default defineComponent({
  name: "what3words-route",
  props: {
    test: String
  },
  components: {
    LoadingSpinnerLarge,
    What3WordsCleo
  },
  setup() {
    const what3wordsControllerState: IWhat3wordsControllerState = new What3wordsCleoService().factoryWhat3wordsControllerState();
    const route = useRoute();
    let callId = "";

    const state = reactive({
      callDetail: CallDetailService.factoryCallDetail(),
      userPermissions: {}
    });

    if (process.env.NODE_ENV === "development") {
      what3wordsControllerState.userInput.coordinates = {
        lat: 0,
        lng: 0
      };
      what3wordsControllerState.userInput.words = "remembers.crumples.couch";
      what3wordsControllerState.userInput.autoSuggest =
        "remembers.crumples.couc";
      what3wordsControllerState.userInput.mobileNumber = "07912626865";
    }

    if (route.query) {
      let mobileNumber = "";

      if (route.query.mob) {
        mobileNumber = route.query.mob.toString();
      }
      what3wordsControllerState.userInput.mobileNumber = mobileNumber;

      let words = "";
      if (route.query.words) {
        words = route.query.words.toString();
      }
      what3wordsControllerState.userInput.words = words;

      if (route.query.callid) {
        callId = route.query.callid.toString();
      }
    }

    const isReady = ref(false);

    onMounted(() => {
      loggerInstance.log("what3words-route.mounted!");

      let what3WordScript = document.createElement("script");
      what3WordScript.setAttribute(
        "src",
        "https://cdn.what3words.com/javascript-components@" +
          what3wordsControllerState.version +
          "/dist/what3words/what3words.js?key=" +
          what3wordsControllerState.apiKey
      );
      document.head.appendChild(what3WordScript);

      if (callId.length > 0) {
        CallDetailData.getCallDetail(callId, false).then(resp => {
          const callDetail = CallDetailService.mapLegacyCall(resp);
          const callDetailNew = Object.assign(
            {},
            {
              ...state.callDetail,
              ...callDetail
            }
          );
          state.callDetail = callDetailNew;
          appStore
            .dispatch(
              USER_STORE_CONST.USER__CONST_MODULE_NAME +
                "/" +
                USER_STORE_CONST.USER__ACTIONS_GET_USER
            )
            .then((user: IUser) => {
              const allUserPermissions = (resp.USER_CONFIG as any) as CLeoPermissionServerResponse;
              const userPermissions =
                allUserPermissions.Permissions[user.userRole];
              const userPermissionsSimple = new PermissionService().simpleKeyPerms(
                userPermissions
              );
              what3wordsControllerState.userInput.userPermissions = userPermissionsSimple;
              state.userPermissions = userPermissionsSimple;
              isReady.value = true;
            });
        });
      } else {
        isReady.value = true;
      }

      /*
      if (window.parent) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //  @ts-ignore
        const legacyCallDetail = window.parent.CallControllerClient;

        if (
          legacyCallDetail &&
          legacyCallDetail.JSONFields &&
          Object.keys(legacyCallDetail.JSONFields).length > 0
        ) {
          // console.log("what3words-route.onMounted() userPermissions:", commonService.simpleObjectClone(legacyCallDetail));
          console.log("what3words-route.onMounted() found window.parent call:");

          const callDetail = callDetailService.mapLegacyCall(legacyCallDetail);
          const callDetailNew = Object.assign(
            {},
            {
              ...state.callDetail,
              ...callDetail
            }
          );
          state.callDetail = callDetailNew;
          appStore
            .dispatch(
              USER_STORE_CONST.USER__CONST_MODULE_NAME +
                "/" +
                USER_STORE_CONST.USER__ACTIONS_GET_USER
            )
            .then((user: IUser) => {
              console.log(
                "what3words-route.onMounted() user:",
                commonService.simpleObjectClone(user)
              );

              const allUserPermissions = legacyCallDetail.userPermissions as CLeoPermissionServerResponse;

              console.log(
                "what3words-route.onMounted() allUserPermissions:",
                commonService.simpleObjectClone(allUserPermissions)
              );

              const userPermissions =
                allUserPermissions.Permissions[user.userRole];

              console.log(
                "what3words-route.onMounted() userPermissions:",
                commonService.simpleObjectClone(userPermissions)
              );

              const userPermissionsSimple = new PermissionService().simpleKeyPerms(
                userPermissions
              );

              console.log(
                "what3words-route.onMounted() userPermissionsSimple:",
                commonService.simpleObjectClone(userPermissionsSimple)
              );

              what3wordsControllerState.userInput.userPermissions = userPermissionsSimple;
              state.userPermissions = userPermissionsSimple;
              isReady.value = true;
            });
        } else {
          isReady.value = true;
        }
      } else {
        isReady.value = true;
      }
      */

      // isReady.value = true;
    });

    function what3wordsMapConfirmed(
      what3WordsMapResponse: IWhat3WordsMapResponse
    ) {
      console.log(
        "what3words-route.what3wordsMapConfirmed()",
        what3WordsMapResponse
      );
      loggerInstance.log(
        "what3words-route.what3wordsMapConfirmed()",
        what3WordsMapResponse
      );

      if (window.parent) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //  @ts-ignore
        window.parent.adapterMenu.addressMapResponse(
          commonService.simpleObjectClone(what3WordsMapResponse)
        );
      }

      if (window.opener) {
        window.opener.adapterMenu.addressMapResponse(what3WordsMapResponse);
      }
    }

    return {
      state,
      isReady,
      what3wordsControllerState,
      what3wordsMapConfirmed
    };
  }
});
</script>
