<template>
  <div>
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">
      Please select your reason for exiting the case:
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <div>
      <SimpleButtonSelecter
        class="ic24-flex-column ic24-flex-gap-large complete-step--simple-buttons"
        :options="options"
        :value="value"
        @input="onInput"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, PropType } from "@vue/composition-api";
import SimpleButtonSelecter, {
  ISimpleButtonInputValue
} from "../../SimpleButtonSelecter.vue";
import { ExitReason } from "./exit-reasons-models";
import { IStep } from "@/calls/details/complete/complete-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "ExitReasons",
  components: { CompleteStepHeader, SimpleButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"EXIT_REASON">>,
      required: true
    },
    value: {
      type: Object as PropType<ISimpleButtonInputValue<ExitReason, ExitReason>>,
      required: true
    },
    options: {
      type: Array as PropType<
        ISimpleButtonInputValue<ExitReason, ExitReason>[]
      >,
      default: () => {
        const options: ISimpleButtonInputValue<ExitReason, ExitReason>[] = [
          {
            id: "FAILED_CONTACT",
            description: "Failed Contact",
            value: "FAILED_CONTACT"
          },
          {
            id: "NO_ACTION_TAKEN",
            description: "No Action Taken",
            value: "NO_ACTION_TAKEN"
          },
          {
            id: "FURTHER_ACTION_REQUIRED",
            description: "Further Action Required to consultation",
            value: "FURTHER_ACTION_REQUIRED"
          }
        ];
        return options;
      }
    }
  },
  setup(
    props: {
      step: IStep<"EXIT_REASON">;
      value: ISimpleButtonInputValue<ExitReason, ExitReason>;
      options: ISimpleButtonInputValue<ExitReason, ExitReason>[];
    },
    context: SetupContext
  ) {
    function onInput(
      simpleButtonInputValue: ISimpleButtonInputValue<ExitReason, ExitReason>
    ) {
      console.log("ExitReasons.onInput()", simpleButtonInputValue);
      context.emit("input", simpleButtonInputValue);
      if (simpleButtonInputValue.id === "NO_ACTION_TAKEN") {
        context.emit("saveAndReturnToQueue", simpleButtonInputValue);
      }
      if (simpleButtonInputValue.id === "FURTHER_ACTION_REQUIRED") {
        context.emit("furtherActionRequired", simpleButtonInputValue);
      }
    }

    return { onInput };
  }
});
</script>

<style scoped></style>
