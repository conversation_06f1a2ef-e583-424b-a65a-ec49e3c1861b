<template>
  <div style="height: 80vh;width: 80vw;">
    Map page
    <div class="map--full" id="map" style="height: 100%;width: 100%;"></div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  SetupContext
} from "@vue/composition-api";

import { Loader } from "@googlemaps/js-api-loader";
import { BaseData } from "@/bases/base-data";
import { BaseService } from "@/bases/base-service";
import { IBaseSummary } from "@/bases/base-models";
declare const google: any;

const baseService = new BaseService();

export default defineComponent({
  // type inference enabled
  name: "map-test",
  components: {},
  props: {},
  setup(props: Record<string, any>, context: SetupContext) {
    // const mapApiKey = "AIzaSyD0zdCViFaubv3fu3TxFNgv_5zoxWSo37M";
    const mapApiKey = "AIzaSyCA9S5winwBlVSpUA0CQQdyOzOUIzA1jAw";

    console.log("map-test ....a");

    let mapCleo: any;

    const state = reactive({
      bases: [] as IBaseSummary[]
    });

    (window as any).initMap = function() {
      // JS API is loaded and available
      console.log("........map loaded");
      // map = new google.maps.Map(document.getElementById("map_canvas"), {
      //   center: {lat: -34.397, lng: 150.644},
      //   zoom: 8
      // });

      console.log("map-test initMap....a");
    };

    onMounted(() => {
      // Attach your callback function to the `window` object

      let loader = new Loader({
        apiKey: mapApiKey
      });
      loader.load().then(() => {
        console.log("map-test loader.load....a");
        const mapDiv = document.getElementById("map");
        if (mapDiv) {
          console.log("map-test loader.load....b");
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //  @ts-ignore
          mapCleo = new google.maps.Map(mapDiv, {
            center: {
              lat: 51.43333,
              lng: 0.55
            },
            zoom: 9,
            zoomControl: true,
            scaleControl: true
          });

          console.log("map-test loader.load....c");

          const myLatLng = { lat: 51.43333, lng: 0.55 };
          new google.maps.Marker({
            position: myLatLng,
            mapCleo,
            title: "Hello World!"
          });

          console.log("map-test loader.load....d");

          const infowindow = new google.maps.InfoWindow({
            content: "<div>What's up!?!?</div>",
          });

          var marker = new google.maps.Marker({
            position: { lat: 51.43333, lng: 0.33 },
            title: "Hello World!"
          });

          marker.addListener("click", () => {
            infowindow.open({
              anchor: marker,
              mapCleo,
              shouldFocus: false,
            });
          });


          console.log("map-test loader.load....e");

          // To add the marker to the map, call setMap();
          marker.setMap(mapCleo);

          console.log("map-test loader.load....f");

          const hospitalIcon: string = require("../../assets/hospital.png");
          new BaseData().getCleoBases().then(resp => {
            console.log("map-test loader.load....g", resp);
            const validBases = baseService.getValidBases(
              Object.values(resp.items)
            );
            state.bases = validBases;

            console.log("map-test loader.load....h");

            validBases.forEach(base => {
              var marker = new google.maps.Marker({
                position: {
                  lat: base.lat,
                  lng: base.long
                },
                title: "Base: " + base.Name,
                icon: hospitalIcon
              });

              var infowindow = new google.maps.InfoWindow({
                content: " "
              });
              google.maps.event.addListener(marker, "click", function() {
                console.log(".................click");
                infowindow.setContent("testddddd: " + base.Name);
                infowindow.open(mapCleo);
              });
              // google.maps.event.addListener(marker, "mouseout", function() {
              //   infowindow.close();
              // });

              marker.setMap(mapCleo);
            });
          });
        }
      });

      // Create the script tag, set the appropriate attributes
      // var script = document.createElement("script");
      // script.src =
      //   "https://maps.googleapis.com/maps/api/js?key=" + mapApiKey + "&callback=initMap";
      // script.async = true;
      // Append the 'script' element to 'head'
      // document.head.appendChild(script);
    });

    return {
      state
    };
  }
});
</script>

<style scoped>
.map--full {
  width: 100vw;
  height: 100vw;
}
</style>
