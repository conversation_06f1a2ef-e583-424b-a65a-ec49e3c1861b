import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export const mockAuditQuestionsMentalHealthMap: Question[] = [
  {
    id: "safeguardingConcerns",
    type: "radio",
    label:
      "Q1 - Do you have any safeguarding concerns relating to the current consultation?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: true
  },
  {
    id: "safeguardingReferral",
    type: "radio",
    label:
      "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: function(answers) {
      console.log("safeguardingReferral.condition", answers);
      return answers["safeguardingConcerns"] === "Yes";
    }
  },
  //  make a property: primary presenting need of the patient
  {
    id: "primaryPresentingNeed",
    type: "select",
    label: "Q2 - What was the primary presenting need of the patient? ",
    options: [
      "suicidal [thoughts]",
      "self-harm [thoughts]",
      "psychosis",
      "alcohol",
      "anxiety",
      "dementia",
      "depression",
      "illicit drug",
      "low mood",
      "social factors",
      "self-injury [requires medical support]",
      "self-injury [no medical support required]",
      "suicide attempt [requires medical support]",
      "suicide attempt [no medical support required]",
      "emotional dysregulation",
      "medication requested",
      "medication advice",
      "eating disorder",
      "physical health issue",
      "PTSD",
      "overdose"
    ],
    mandatory: true,
    value: "",
    visible: false,
    condition: function(answers) {
      console.log("safeguardingReferral.condition", answers);
      return answers["safeguardingConcerns"] === "Yes";
    }
  },
  // Make a prop:level of intervention was provided
  {
    id: "levelOfIntervention",
    type: "select",
    label: "Q3 - What level of intervention was provided (as per SOP)? ",
    options: [
      "guided to another service for assessment",
      "Assessment of known presentation",
      "Information sharing only",
      "Assessment of new presentation"
    ],
    mandatory: true,
    visible: true
  },
  // make a prop: the onward pathway
  {
    id: "onwardPathway",
    type: "select",
    label: "Q4 - What was the onward pathway? ",
    options: [
      "Referred for non-blue light pool car response",
      "Referred for SWASFT MH RV (blue light)",
      "Referred to rapid engagement workers (REW)",
      "Signposted back to NHS treatment team CCo",
      "Signposted to own MH treatment team [CMHT, Crisis]",
      "Signposted to VCSE or third sector [Safe Haven/San]",
      "Agreement for self-care [no signposting]",
      "Signposted or referred to GP/New referral MH assessment team [PCLS/Bris Triage]",
      "New referral to crisis team",
      "New referral to MH treatment team [CMHT, Recovery]",
      "New referral to drug and alcohol services",
      "Referred to emergency services [999]",
      "Referred for physical health clinician assessment",
      "Referred for meds by physical health clinician",
      "Referred to UAC",
      "Information requested but not shared",
      "Signposted to self-convey to ED",
      "Professional Liaison"
    ],
    mandatory: true,
    visible: true
  },
  //make a prop: Did the call require support and liaison from both physical and mental health expertise in the CAS
  {
    id: "supportAndLiaisonRequired",
    type: "radio",
    label:
      "Did the call require support and liaison from both physical and mental health expertise in the CAS",
    options: ["Yes", "No"],
    mandatory: true,
    visible: true
  }
];

export const mockAuditQuestionsMentalHealthServerResponse = {
  AUDIT_QUESTION_DATA_MENTAL_HEALTH: {
    unid: "784134FD649E06AD80258C50005CD416",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_MENTAL_HEALTH",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "primaryPresentingNeed",\r\n    "type": "select",\r\n    "label": "Q2 - What was the primary presenting need of the patient? ",\r\n    "options": [\r\n      "suicidal [thoughts]",\r\n      "self-harm [thoughts]",\r\n      "psychosis",\r\n      "alcohol",\r\n      "anxiety",\r\n      "dementia",\r\n      "depression",\r\n      "illicit drug",\r\n      "low mood",\r\n      "social factors",\r\n      "self-injury [requires medical support]",\r\n      "self-injury [no medical support required]",\r\n      "suicide attempt [requires medical support]",\r\n      "suicide attempt [no medical support required]",\r\n      "emotional dysregulation",\r\n      "medication requested",\r\n      "medication advice",\r\n      "eating disorder",\r\n      "physical health issue",\r\n      "PTSD",\r\n      "overdose"\r\n    ],\r\n    "mandatory": true,\r\n    "value": "",\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "levelOfIntervention",\r\n    "type": "select",\r\n    "label": "Q3 - What level of intervention was provided (as per SOP)? ",\r\n    "options": [\r\n      "guided to another service for assessment",\r\n      "Assessment of known presentation",\r\n      "Information sharing only",\r\n      "Assessment of new presentation"\r\n    ],\r\n    "mandatory": true,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "onwardPathway",\r\n    "type": "select",\r\n    "label": "Q4 - What was the onward pathway? ",\r\n    "options": [\r\n      "Referred for non-blue light pool car response",\r\n      "Referred for SWASFT MH RV (blue light)",\r\n      "Referred to rapid engagement workers (REW)",\r\n      "Signposted back to NHS treatment team CCo",\r\n      "Signposted to own MH treatment team [CMHT, Crisis]",\r\n      "Signposted to VCSE or third sector [Safe Haven/San]",\r\n      "Agreement for self-care [no signposting]",\r\n      "Signposted or referred to GP/New referral MH assessment team [PCLS/Bris Triage]",\r\n      "New referral to crisis team",\r\n      "New referral to MH treatment team [CMHT, Recovery]",\r\n      "New referral to drug and alcohol services",\r\n      "Referred to emergency services [999]",\r\n      "Referred for physical health clinician assessment",\r\n      "Referred for meds by physical health clinician",\r\n      "Referred to UAC",\r\n      "Information requested but not shared",\r\n      "Signposted to self-convey to ED",\r\n      "Professional Liaison"\r\n    ],\r\n    "mandatory": true,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "supportAndLiaisonRequired",\r\n    "type": "radio",\r\n    "label": "Did the call require support and liaison from both physical and mental health expertise in the CAS",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "visible": true\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA_MENTAL_HEALTH"
  }
};
