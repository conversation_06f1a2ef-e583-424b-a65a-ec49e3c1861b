export const CallStatusHumanReadableTypes = [
  "Unknown",
  "Draft",
  "Open",
  "Completed"
] as const;
export type CallStatusHumanReadable = typeof CallStatusHumanReadableTypes[number];

export function factoryCallsCommonService() {
  function getStatusDisplay(status: number): CallStatusHumanReadable {
    let statusHumanReadable: CallStatusHumanReadable = "Unknown";
    switch (status) {
      case 1:
        statusHumanReadable = "Open";
        break;
      case 2:
        statusHumanReadable = "Completed";
        break;
      default:
        if (status < 1) {
          statusHumanReadable = "Draft";
        } else {
          statusHumanReadable = "Unknown";
        }
    }
    return statusHumanReadable;
  }

  return {
    getStatusDisplay
  };
}
