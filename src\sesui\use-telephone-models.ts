import { ISesuiLoginResult } from "@/sesui/use-sesui-models";

export interface ISesuiTelephoneConfig {
  enabled: boolean;
  url: string;
  user: string; //  master user
  pw: string; //  master user password
}

export interface ISesuiTelephoneState {
  showLogin: boolean;
  socketUrl: string;
  user: string;
  password: string;
  operatorPhoneNumber: string;
  masterUser: string;
  masterPassword: string;
  session_start_result: ISesuiLoginResult | null;
}
