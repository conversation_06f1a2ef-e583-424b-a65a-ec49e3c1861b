import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { patientReferredToFurtherActionButtonOption } from "@/calls/details/complete/components/patient-referred-to/patient-referred-to-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";

const callDetail: ICallDetail = factoryCallDetail();

describe("CAS Clini useCompleteController failure", () => {
  it("CAS Clini contact made flow failure: OVERRIDE_COMPLETE", () => {
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.processName).toBe("COMPLETE_CAS_CLINICAL");
    expect(controller.state.autoProgress).toBe(true);

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  can move forward from HOW_WAS_CASE_MANAGED to CONTACT_MADE as already answered
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_FAILURE",
      description: "Contact Failure",
      value: "CONTACT_FAILURE"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_RISK_ASSESSMENT");

    /*
    expect(controller.state.currentStep).toBe("PATIENT_RISK_ASSESSMENT");
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onPatientRiskAssessmentSelected({
      risk: {
        id: "NO",
        description: "No",
        value: "NO"
      },
      actionTaken: ""
    });
    expect(controller.state.currentStep).toBe("PATIENT_RISK_ASSESSMENT");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(controller.state.ui.disableBack).toBe(false);
    expect(controller.state.ui.disableNext).toBe(true);

    controller.onInsufficientContactAttemptTypeSelected({
      id: "OVERRIDE_COMPLETE",
      description: "Override and Complete",
      value: "OVERRIDE_COMPLETE"
    });

    expect(controller.state.finalAction).toBe("COMPLETE");
    */
  });
});
