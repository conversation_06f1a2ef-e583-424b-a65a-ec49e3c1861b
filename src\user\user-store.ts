import { <PERSON><PERSON><PERSON>x<PERSON>, <PERSON><PERSON><PERSON> } from "vuex";
import { IRootState } from "@/store/store";

import { IUser } from "@/user/user-models";
import { UserService } from "@/user/user-service";
import { CommonService } from "@/common/common-service";
import { UserData } from "@/user/user-data";

const userData: UserData = new UserData();
const userService: UserService = new UserService();
const commonService: CommonService = new CommonService();

export interface IUserStoreState {
  user: IUser;
  isLoadingUser: boolean;
}

export enum USER_STORE_CONST {
  USER__CONST_MODULE_NAME = "USER__CONST_MODULE_NAME",

  USER__ACTIONS_SET_USER = "USER__ACTIONS_SET_USER",
  USER__ACTIONS_SET_USER_LOADING = "USER__ACTIONS_SET_USER_LOADING",

  USER__ACTIONS_GET_USER = "USER__ACTIONS_GET_USER"
}

const mutations = {
  [USER_STORE_CONST.USER__ACTIONS_SET_USER](
    state: IUserStoreState,
    user: IUser
  ): void {
    state.user = commonService.simpleObjectClone(user);
  },

  [USER_STORE_CONST.USER__ACTIONS_SET_USER_LOADING](
    state: IUserStoreState,
    isLoading: boolean
  ): void {
    state.isLoadingUser = isLoading;
  }
};
const getters = {};
const actions = {
  [USER_STORE_CONST.USER__ACTIONS_GET_USER](
    context: ActionContext<IUserStoreState, IRootState>
  ): Promise<IUser> {
    let userInt = userService.factoryUser();
    context.commit(
      USER_STORE_CONST.USER__ACTIONS_SET_USER,
      userService.factoryUser()
    );
    context.commit(USER_STORE_CONST.USER__ACTIONS_SET_USER_LOADING, true);
    return userData
      .getUser()
      .then(user => {
        context.commit(USER_STORE_CONST.USER__ACTIONS_SET_USER, user);
        userInt = user;
        return userInt;
      })
      .finally(() => {
        context.commit(USER_STORE_CONST.USER__ACTIONS_SET_USER_LOADING, false);
        return userInt;
      });
  }
};

export const userStore: Module<IUserStoreState, IRootState> = {
  namespaced: true,
  state: userService.factoryUserStoreState(),
  mutations,
  getters,
  actions
};
