<template>
  <div>
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">
      Please select your failure contact reason:
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <SimpleButtonSelecter
      class="ic24-flex-column ic24-flex-gap-large complete-step--simple-buttons"
      :options="options"
      :value="value"
      @input="onInput"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, PropType } from "@vue/composition-api";
import SimpleButtonSelecter, {
  ISimpleButtonInputValue
} from "../../SimpleButtonSelecter.vue";
import { FailedContactReason } from "./failed-contact-models";
import {
  FailedContactReasonType,
  IStep
} from "@/calls/details/complete/complete-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "FailedContactReasons",
  components: { CompleteStepHeader, <PERSON>ButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"FAILED_CONTACT_REASON">>,
      required: true
    },
    value: {
      type: Object as PropType<FailedContactReasonType>,
      required: true
    },
    options: {
      type: Array as PropType<FailedContactReasonType[]>,
      default: () => {
        const options: FailedContactReasonType[] = [
          {
            id: "ENGAGED",
            description: "Engaged",
            value: "Engaged"
          },
          {
            id: "NO_ANSWER",
            description: "No Answer",
            value: "No Answer"
          },
          {
            id: "NUMBER_UNOBTAINABLE",
            description: "Number Unobtainable",
            value: "Number Unobtainable"
          },
          {
            id: "WRONG_NUMBER",
            description: "Wrong Number",
            value: "Wrong Number"
          },
          {
            id: "ANSWERPHONE_MESSAGE_LEFT",
            description: "Answerphone - Message left",
            value: "Answerphone - Message left"
          },
          {
            id: "ANSWERPHONE_MESSAGE_NOT_LEFT",
            description: "Answerphone - Message not left",
            value: "Answerphone - Message not left"
          },
          {
            id: "OTHER",
            description: "Other",
            value: "Other"
          }
        ];
        return options;
      }
    }
  },
  setup(
    props: {
      step: IStep<"FAILED_CONTACT_REASON">;
      value: FailedContactReasonType;
      options: FailedContactReasonType[];
    },
    context: SetupContext
  ) {
    function onInput(simpleButtonInputValue: FailedContactReasonType) {
      console.log("FailedContactReasons.onInput()", simpleButtonInputValue);
      context.emit("input", simpleButtonInputValue);
    }

    return { onInput };
  }
});
</script>

<style scoped></style>
