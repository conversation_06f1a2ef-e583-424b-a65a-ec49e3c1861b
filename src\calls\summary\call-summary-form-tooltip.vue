<template>
  <div class="custom-tooltip">
    <CallSummaryForm
      :cleo-call-summary="cleoCallSummary"
      class="custom-tooltip"
    ></CallSummaryForm>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { ITooltipParams } from "ag-grid-community";
import CallSummaryForm from "@/calls/summary/call-summary-form.vue";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";

@Component({
  name: "call-summary-form-tooltip",
  components: { CallSummaryForm }
})
export default class CallSummaryFormTooltip extends Vue {
  public params!: ITooltipParams;

  public callSummaryService: CallSummaryService = new CallSummaryService();
  public cleoCallSummary: ICleoCallSummary = this.callSummaryService.factoryCleoCallSummary();

  public created() {
    this.cleoCallSummary = this.params.data;
  }
}
</script>
