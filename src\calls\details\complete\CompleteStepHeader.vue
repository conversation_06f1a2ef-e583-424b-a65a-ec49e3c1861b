<template>
  <div class="complete-step--header ic24-flex-row">
    <span v-text="step.title"></span>
    <slot name="titleEnd">
      <span
        slot="titleEnd"
        class="ic24-flex-row--end complete-step--header-title-end"
        v-text="titleEnd"
      ></span>
    </slot>
  </div>
</template>
<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import { IStep } from "@/calls/details/complete/complete-models";

export default defineComponent({
  name: "CompleteStepHeader",
  props: {
    step: {
      type: Object as PropType<IStep<any>>,
      required: true
    },
    titleEnd: {
      type: String,
      default: ""
    }
  }
});
</script>

<style scoped>
.complete-step--header {
  font-size: 40px;
  margin-bottom: var(--ic24-flex-gap-small);
  padding-bottom: var(--ic24-flex-gap);
  color: #7f7e80;
  border-bottom: 1px solid var(--slate-300);
}

.complete-step--header-title-end {
  font-size: 16px;
  color: #bfbebf;
}
</style>
