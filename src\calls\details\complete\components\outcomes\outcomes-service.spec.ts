import { convertServerResponse } from "@/calls/details/complete/components/outcomes/outcomes-service";
import { completeOutcomesMockOptions } from "@/calls/details/complete/components/api/complete-api-mock";

describe("outcomes-service", () => {
  it("convertServerResponse", () => {
    const res = convertServerResponse(completeOutcomesMockOptions);

    expect(res["999"].length).toBe(0);

    expect(res["Admission Requested"].length).toBe(19);
  });
});
