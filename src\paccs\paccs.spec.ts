import { PaccsService } from "@/paccs/paccs-service";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import {
  IPathwaysDatasetLegacy,
  ITriageRecord,
  ITriageRecordCondition,
  ITriageRecordTemplate
} from "@/paccs/paccs-models";
import { parseISO } from "date-fns";

const paccsService: PaccsService = new PaccsService();

describe("Paccs", () => {
  it("canConditionTabBeRemoved", () => {
    const paccssymptomModel: IPaccsSymptomModel = {
      templateId: "Cs000014",
      version: 9,
      templateName: "Women's Health/Menstrual Concerns",
      synonyms:
        "STD|STI|foreign|perineum|fetal|foetal|menses|vagina|genital|labour|incontinence|period|ectopic|thrush|candida|missed|menstruation",
      symptomGroup: 1146,
      servicesJump: "PW1846.0",
      etcJump: "PW1847.0"
    };

    const triageRecords: ITriageRecord[] = [
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:03+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Failed Contraception/Unprotected Intercourse"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:05+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010537",
        answerNumber: 2,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Domestic Violence or Abuse, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:06+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010526",
        answerNumber: 1,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexual Abuse or Exploitation, Considered"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:07+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010397",
        answerNumber: 2,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexually Active Young People, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:08+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010397",
        answerNumber: 0,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexually Active Young People, Deselected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:09+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010537",
        answerNumber: 0,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Domestic Violence or Abuse, Deselected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T12:40:10+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010526",
        answerNumber: 0,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexual Abuse or Exploitation, Deselected"
      }
    ];

    const conditionTab = paccsService.factoryConditionTab(paccssymptomModel);
    expect(
      paccsService.canConditionTabBeRemoved(conditionTab, triageRecords).isOK
    ).toBe(true);

    const triageRecords2: ITriageRecord[] = [
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:06:21+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Failed Contraception/Unprotected Intercourse"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:06:24+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010537",
        answerNumber: 1,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Domestic Violence or Abuse, Considered"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:06:27+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010526",
        answerNumber: 1,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexual Abuse or Exploitation, Considered"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:06:29+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010537",
        answerNumber: 0,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Domestic Violence or Abuse, Deselected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:06:30+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010078",
        answerNumber: 2,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Sexually Transmitted Infection, Suspected"
      }
    ];

    expect(
      paccsService.canConditionTabBeRemoved(conditionTab, triageRecords2).isOK
    ).toBe(false);

    const triageRecords3: ITriageRecord[] = [
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:39+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Failed Contraception/Unprotected Intercourse"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:41+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 123,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010077",
        answerNumber: 2,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Failed Contraception/Unprotected Intercourse, Female over 12, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:43+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Mental Health"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:47+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010054",
        answerNumber: 2,
        reportText: "Mental Health / Hormonal Changes, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:48+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010041",
        answerNumber: 1,
        reportText: "Mental Health / Drug, Alcohol or Solvent Use, Considered"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:50+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010054",
        answerNumber: 0,
        reportText: "Mental Health / Hormonal Changes, Deselected"
      }
    ];

    expect(
      paccsService.canConditionTabBeRemoved(conditionTab, triageRecords2).isOK
    ).toBe(false);
  });

  it("getLatestTriageRecordCondition", () => {
    const triageRecords: ITriageRecord[] = [
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:39+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 124,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Failed Contraception/Unprotected Intercourse"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:41+01:00",
        pathwayId: "Cs000014",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 124,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010077",
        answerNumber: 2,
        reportText:
          "Failed Contraception/Unprotected Intercourse / Failed Contraception/Unprotected Intercourse, Female over 12, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:43+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Mental Health"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:47+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010054",
        answerNumber: 2,
        reportText: "Mental Health / Hormonal Changes, Suspected"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:48+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010041",
        answerNumber: 1,
        reportText: "Mental Health / Drug, Alcohol or Solvent Use, Considered"
      },
      {
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-13T13:19:50+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        userComment: "",
        quId: "Cn010054",
        answerNumber: 0,
        reportText: "Mental Health / Hormonal Changes, Deselected"
      }
    ];

    expect(
      paccsService.getLatestTriageRecordCondition(triageRecords, {
        pathwayId: "",
        quId: "",
        userComment: ""
      })
    ).toBe(null);

    expect(
      paccsService.getLatestTriageRecordCondition(triageRecords, {
        pathwayId: "Cs000015",
        quId: "",
        userComment: ""
      })
    ).toBe(null);

    const result: ITriageRecordCondition | null = paccsService.getLatestTriageRecordCondition(
      triageRecords,
      {
        pathwayId: "Cs000015",
        quId: "Cn010041",
        userComment: ""
      }
    );

    expect(result!.pathwayId).toBe("Cs000015");
    expect(result!.quId).toBe("Cn010041");
  });

  it("isTriageRecordCondition", () => {
    expect(
      paccsService.isTriageRecordCondition({
        caseId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        userId: "Joe%20Bloggs/sehnp",
        timeIn: "2021-05-14T12:08:38+01:00",
        pathwayId: "Cs000036",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 5,
        symptomGroup: 136,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Tiredness"
      })
    ).toBe(false);
  });

  it("mapPathwaysDatasets", () => {
    const data: IPathwaysDatasetLegacy = {
      InjuryModule: "",
      skillset: 6,
      userId: "Joe Bloggs/sehnp",
      pathwaysCaseId: "",
      cadCaseId: "",
      hostCaseId: "**********",
      setting: "",
      serviceId: "EAST KENT 111",
      hostDocId: "5A89CC56B241176A802586D3002F1057",
      casQ: 0,
      caseStartTime: "2021-02-04T15:08:55+00:00",
      patientId: "DRSM-C2WD4U-012",
      surname: "T1",
      forename: "Test",
      CallerTelephoneNumber: "00000%20000000",
      ageInHours: 130560,
      gender: 1,
      party: 3,
      surgeryId: "Z10000",
      previousEncounters: "1",
      presentingCondition:
        "%7E%20%5BBen%20Smythson%20%28Call%20Back%29%2004-Feb-2021%2015%3A20%5D%20Test%20Callback%201",
      addressBuildingPrefix: "",
      addressBuildingIdentifier: "",
      addressStreetName1: "",
      addressStreetName2: "",
      addressTown: "",
      addressCounty: "",
      postCode: "unk",
      addressDescription: "",
      addressEasting: -1,
      addressNorthing: -1,
      addressKey: "",
      clinicianCallStartTime: "2021-05-18T13:37:37+01:00",
      nhsId: "",
      TraceVerified: false,
      dob: "20210518"
    };

    const res = paccsService.mapPathwaysDatasets(data);

    expect(res.skillSet).toBe(14);
    expect(res.userId).toBe("Joe Bloggs/sehnp");
    expect(res.dob).toBe("20210518");
  });

  it("mapPathwaysDatasets encoded data", () => {
    const data: IPathwaysDatasetLegacy = {
      skillset: 1,
      userId: "Test%20Doctor2/staging",
      pathwaysCaseId: "",
      InjuryModule: "On",
      cadCaseId: "",
      hostCaseId: "**********",
      setting: "",
      serviceId: "Norfolk and Wisbech 111",
      hostDocId: "F3ABC6673B38FA2A802586F6004C556D",
      casQ: 1,
      caseStartTime: "2021-06-16T14:53:43+01:00",
      patientId: "DRSM-C44DQ7-926",
      surname: "TEST%20CALL%20PACCS%202",
      forename: "Test%20Call",
      CallerTelephoneNumber: "01233%20123123",
      ageInHours: 221173,
      gender: 1,
      party: 1,
      surgeryId: "Z10000",
      previousEncounters: 0,
      presentingCondition: "Test%20Call%20for%20PaCCS.",
      addressBuildingPrefix: "",
      addressBuildingIdentifier: "313%20VICTORIA%20ROAD",
      addressStreetName1: "",
      addressStreetName2: "",
      addressTown: "",
      addressCounty: "LOWESTOFT",
      postCode: "NR33%209LS",
      addressDescription: "",
      addressEasting: "65300",
      addressNorthing: "29230",
      addressKey: "",
      clinicianCallStartTime: "2021-06-18T11:04:50+01:00",
      nhsId: "",
      TraceVerified: false,
      dob: "20210616"
    };

    const res = paccsService.mapPathwaysDatasets(data);

    expect(res.skillSet).toBe(14);
    expect(res.userId).toBe("Test Doctor2/staging");
    expect(res.callerTelephoneNumber).toBe("01233123123");
    expect(res.clinicianCallStartTime).toBe("2021-06-18T11:04:50+01:00");
  });

  it("getLatestActiveStateForEachTriageRecord 1", () => {
    const triageRecordTemplate: ITriageRecordTemplate = {
      caseId: "40fe5389-a09e-430d-9bb5-08d92037d5ec",
      userId: "Joe Bloggs/sehnp",
      timeIn: "2021-05-26T12:48:30+01:00",
      pathwayId: "Cs000015",
      actionId: 2,
      includeInReport: true,
      interfaceId: 15,
      skillSet: 15,
      symptomGroup: 125,
      siteId: "WHAT_VALUE_SHOULD_GO_HERE",
      dispositionRationale: "Mental Health"
    };

    expect(
      paccsService.isTriageRecordTemplateActive(triageRecordTemplate)
    ).toBe(true);
    expect(paccsService.isTriageRecordActive(triageRecordTemplate)).toBe(true);

    triageRecordTemplate.actionId = 1;
    expect(
      paccsService.isTriageRecordTemplateActive(triageRecordTemplate)
    ).toBe(false);
    expect(paccsService.isTriageRecordActive(triageRecordTemplate)).toBe(false);
  });

  // it("getLatestActiveStateForEachTriageRecord 1", () => {
  //   const triageRecords: ITriageRecord[] = [{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:40+01:00","pathwayId":"Cs000014","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","dispositionRationale":"Failed Contraception/Unprotected Intercourse"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:41+01:00","pathwayId":"Cs000049","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","dispositionRationale":"Heat Exposure"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:41+01:00","pathwayId":"Cs000061","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","dispositionRationale":"Head Lice"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:41+01:00","pathwayId":"Cs000015","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","dispositionRationale":"Mental Health"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:44+01:00","pathwayId":"Cs000014","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010526","answerNumber":2,"reportText":"Failed Contraception/Unprotected Intercourse / Sexual Abuse or Exploitation, Suspected"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:46+01:00","pathwayId":"Cs000061","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010522","answerNumber":2,"reportText":"Head Lice / Head Lice, Over 2 Years, Suspected"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:46+01:00","pathwayId":"Cs000061","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010522","answerNumber":0,"reportText":"Head Lice / Head Lice, Over 2 Years, Deselected"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:47+01:00","pathwayId":"Cs000061","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010522","answerNumber":1,"reportText":"Head Lice / Head Lice, Over 2 Years, Considered"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:50+01:00","pathwayId":"Cs000049","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010040","answerNumber":2,"reportText":"Heat Exposure / Diabetes Mellitus, Suspected"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:53+01:00","pathwayId":"Cs000049","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010041","answerNumber":1,"reportText":"Heat Exposure / Drug, Alcohol or Solvent Use, Considered"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:17:55+01:00","pathwayId":"Cs000049","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010041","answerNumber":0,"reportText":"Heat Exposure / Drug, Alcohol or Solvent Use, Deselected"},{"caseId":"56c555b4-7879-4a49-9bac-08d92037d5ec","userId":"Joe Bloggs/sehnp","timeIn":"2021-05-26T12:18:03+01:00","pathwayId":"Cs000015","actionId":2,"includeInReport":true,"interfaceId":15,"skillSet":15,"siteId":"WHAT_VALUE_SHOULD_GO_HERE","userComment":"","quId":"Cn010402","answerNumber":1,"reportText":"Mental Health / Crisis or Mental Health Team, Considered"}];
  //
  //   expect(triageRecords.length).toBe(12);
  //
  //   const res = paccsService.getLatestActiveStateForEachTriageRecord(triageRecords);
  //   expect(res.length).toBe(5);
  // });

  it("getLatestActiveStateForEachTriageRecord 2", () => {
    const triageRecords: ITriageRecord[] = [
      {
        caseId: "40fe5389-a09e-430d-9bb5-08d92037d5ec",
        userId: "Joe Bloggs/sehnp",
        timeIn: "2021-05-26T12:48:30+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 15,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Mental Health"
      },
      {
        caseId: "40fe5389-a09e-430d-9bb5-08d92037d5ec",
        userId: "Joe Bloggs/sehnp",
        timeIn: "2021-05-26T12:48:32+01:00",
        pathwayId: "Cs000015",
        actionId: 1,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 15,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Mental Health"
      },
      {
        caseId: "40fe5389-a09e-430d-9bb5-08d92037d5ec",
        userId: "Joe Bloggs/sehnp",
        timeIn: "2021-05-26T12:48:33+01:00",
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        interfaceId: 15,
        skillSet: 15,
        symptomGroup: 125,
        siteId: "WHAT_VALUE_SHOULD_GO_HERE",
        dispositionRationale: "Mental Health"
      }
    ];

    expect(triageRecords.length).toBe(3);

    const res = paccsService.getLatestStateForEachTriageRecord(triageRecords);
    expect(res.length).toBe(1);
  });

  it("getLatestActiveStateForEachTriageRecord 3", () => {
    const triageRecords: ITriageRecord[] = [
      ({
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        dispositionRationale: "Mental Health",
        debug: 1
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000015",
        actionId: 1,
        includeInReport: true,
        dispositionRationale: "Mental Health",
        debug: 2
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000015",
        actionId: 2,
        includeInReport: true,
        dispositionRationale: "Mental Health",
        debug: 3
      } as any) as ITriageRecord
    ];

    expect(triageRecords.length).toBe(3);

    const res = paccsService.getLatestStateForEachTriageRecord(triageRecords);
    expect(res.length).toBe(1);

    expect(
      paccsService.getLatestActiveStateForEachTriageRecord(triageRecords).length
    ).toBe(1);
  });

  it("getLatestActiveStateForEachTriageRecord 4", () => {
    const triageRecords: ITriageRecord[] = [
      ({
        pathwayId: "Cs000015",
        actionId: 2,
        dispositionRationale: "Mental Health"
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000049",
        actionId: 2,
        dispositionRationale: "Heat Exposure"
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000049",
        actionId: 2,
        quId: "Cn010040",
        answerNumber: 2
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000049",
        actionId: 2,
        quId: "Cn010040",
        answerNumber: 0
      } as any) as ITriageRecord,
      ({
        pathwayId: "Cs000049",
        actionId: 2,
        quId: "Cn010041",
        answerNumber: 2
      } as any) as ITriageRecord
    ];

    expect(triageRecords.length).toBe(5);

    const res = paccsService.getLatestStateForEachTriageRecord(triageRecords);
    expect(res.length).toBe(4);

    expect(
      paccsService.getLatestActiveStateForEachTriageRecord(triageRecords).length
    ).toBe(3);
  });

  it("JS test", () => {
    expect(null ?? 1).toBe(1);
    expect(undefined ?? 2).toBe(2);
    expect("" ?? "A").toBe("");
    expect(null || 4).toBe(4);
    expect(undefined || 5).toBe(5);
    expect("" || 6).toBe(6);
  });

  it("getPaccsAgeGroup 3", () => {
    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("1970-03-28"),
        parseISO("2020-09-06")
      )
    ).toBe("Adult");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2004-03-28"),
        parseISO("2020-09-06")
      )
    ).toBe("Adult");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2004-10-06"),
        parseISO("2020-09-06")
      )
    ).toBe("Child");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2015-09-06"),
        parseISO("2020-09-06")
      )
    ).toBe("Child");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2015-10-06"),
        parseISO("2020-09-06")
      )
    ).toBe("Toddler");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2019-09-06"),
        parseISO("2020-09-06")
      )
    ).toBe("Toddler");

    expect(
      paccsService.getPaccsAgeGroup(
        parseISO("2019-10-06"),
        parseISO("2020-09-06")
      )
    ).toBe("Infant");
  });
});
