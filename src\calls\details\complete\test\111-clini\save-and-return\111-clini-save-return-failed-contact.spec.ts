import { ICallDetail } from "@/calls/details/call-details-models";
import { factoryCallDetail } from "@/calls/details/call-detail-service";
import { IStep } from "@/calls/details/complete/complete-models";
import {
  ICompleteControllerInput,
  useCompleteController
} from "@/calls/details/complete/useCompleteController";
import * as CallDetailService from "@/calls/details/call-detail-service";

describe("111 Clini useCompleteController Save-Return", () => {
  /**
   *
   */
  it("111 Clini Failed Contact", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: CallDetailService.factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "SAVE_AND_RETURN_PROCESS"
    );
    expect(controller.state.processName).toBe("SAVE_AND_RETURN_111_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(false);
    expect(controller.state.autoProgress).toBe(true);
    expect(controller.state.userOptions.exitReason.forUser.length).toBe(2);
    expect(controller.state.userOptions.exitReason.forUser[0].id).toBe(
      "FAILED_CONTACT"
    );
    expect(controller.state.userOptions.exitReason.forUser[1].id).toBe(
      "NO_ACTION_TAKEN"
    );

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(3);

    expect(stepNames[0]).toBe("EXIT_REASON");
    expect(stepNames[1]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[2]).toBe("UNKNOWN");

    // controller.onExitReasonSelected();
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("EXIT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "EXIT_REASON__NOT_SELECTED"
    );

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FAILED_CONTACT",
      description: "Failed Contact",
      value: "FAILED_CONTACT"
    });

    expect((controller.state.steps.EXIT_REASON as IStep<any>).isValid).toBe(
      true
    );

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    // controller.goto("NEXT");

    const errorMessages = controller.validateStep("FAILED_CONTACT_REASON");

    expect(errorMessages.length).toBe(1);

    expect(controller.isCurrentStepValid()).toBe(false);

    expect(controller.state.validationMessages.length).toBe(0);
    expect(controller.state.isProcessComplete).toBe(false);

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.validationMessages.length).toBe(0);
    expect(
      (controller.state.steps.FAILED_CONTACT_REASON as IStep<any>).isValid
    ).toBe(true);
    expect(controller.state.isProcessComplete).toBe(true);

    expect(controller.state.finalAction).toBe("SAVE_AND_RETURN_TO_QUEUE");
  });
});
