import {
  DxCodeServerResponse,
  FollowUpClassification,
  FollowUpInputState,
  FollowUpState,
  ILegacyKeywordDx
} from "@/calls/details/complete/components/followup/models/follow-up-models";
import { format, parse, parseISO } from "date-fns";

export function factoryFollowUpInputState(): FollowUpInputState {
  return {
    followUpType: null,
    dxCode: null,
    cleoClientService: null,
    baseQuestionTriageType: "",
    followUpNotes: "",
    dateTimePicker: "",
    userConfirmed: false
  };
}

export function factoryFollowUpState(): FollowUpState {
  return {
    input: factoryFollowUpInputState(),
    data: {
      isReady: false,
      isLoading: false,
      followUpRecord: {
        Advice: {
          classification: "Advice",
          label: "Urgent Care Follow Up"
        },
        Base: {
          classification: "Base",
          label: "Face-to-Face Urgent Care Referral"
        },
        Visit: {
          classification: "Visit",
          label: "Home Visit Urgent Care Referral"
        }
      },
      dxCodes: {},
      dxCodesForClassification: [],
      cleoClientServices: {},
      baseQuestionTriageTypes: [],
      errors: {},
      dateTimePickerOptions: {
        minUtc: "",
        maxUtc: "",
        minHumanDisplay: "",
        maxHumanDisplay: ""
      }
    }
  };
}

export function getDxCodesForClassification(
  dxCodes: DxCodeServerResponse,
  classification: FollowUpClassification
): ILegacyKeywordDx[] {
  // loop across keys of dxCodes, get codeID3 of each key and check if it includes the classification
  // if it does, return the key.  Use reduce to return an array of ILegacyKeywordDx[]

  const classificationInternal = classification.toUpperCase();
  const dxCodesInternal = Object.keys(dxCodes).reduce(
    (acc: ILegacyKeywordDx[], key: string) => {
      const code = dxCodes[key];
      if (code.codeID3.includes(classificationInternal)) {
        acc.push(code);
      }
      return acc;
    },
    []
  );

  // If codeID2 has a value and it is a number then sort the array by codeID2.   If codeID2
  // is not a number then set codeID2 to 0 and sort the array by codeID2.
  return dxCodesInternal.sort((a, b) => {
    const codeID2A = isNaN(Number(a.codeID2)) ? 0 : Number(a.codeID2);
    const codeID2B = isNaN(Number(b.codeID2)) ? 0 : Number(b.codeID2);
    return codeID2A - codeID2B;
  });
}

export function validate(followUpState: FollowUpState): Record<string, string> {
  const errors: Record<string, string> = {};

  if (followUpState.input.followUpType === null) {
    errors.followUpType = "Follow up type is required";
    return errors;
  }

  if (followUpState.input.dxCode === null) {
    errors.dxCode = "Priority is required";
  }

  if (
    followUpState.input.followUpType.classification.toUpperCase() === "ADVICE"
  ) {
    if (followUpState.input.cleoClientService === null) {
      errors.cleoClientService = "Cleo client service is required";
    }

    if (followUpState.input.dateTimePicker === "") {
      errors.dateTimePicker = "Date and time is required";
    } else {
      // Ensure min and max stay as UTC
      const minUtcString =
        followUpState.data.dateTimePickerOptions.minUtc + "Z";
      const minUtc = new Date(minUtcString).getTime();

      const maxUtcString =
        followUpState.data.dateTimePickerOptions.maxUtc + "Z";
      const maxUtc = new Date(maxUtcString).getTime();

      // this is LOCAL TIME!
      const selected = new Date(followUpState.input.dateTimePicker);
      const selectedUtc = selected.getTime();

      if (selectedUtc < minUtc || selectedUtc > maxUtc) {
        errors.dateTimePicker =
          "Date and time must be within the allowed range";
      }
    }
  }

  if (
    ["BASE", "VISIT"].indexOf(
      followUpState.input.followUpType.classification.toUpperCase()
    ) > -1
  ) {
    if (followUpState.input.baseQuestionTriageType === "") {
      errors.cleoClientService = "Triage type is required";
    }
  }

  return errors;
}

export function getConfirmedMessage(followUpState: FollowUpState) {
  if (!followUpState.input.userConfirmed) {
    return "Not yet confirmed.";
  }
  if (followUpState.input.followUpType === null) {
    return "Follow up type is required";
  }
  return (
    "On completion (End Assessment) of this case, a new case will be placed " +
    "in the oversight queue for the selected service."
  );
}

export function getFollowUpStateDisplayText(
  followUpInputState: FollowUpInputState
): string {
  if (followUpInputState.userConfirmed && followUpInputState.followUpType) {
    const classification = followUpInputState.followUpType.classification;

    let displayText =
      "Follow up requested: " +
      followUpInputState.followUpType.label +
      " " +
      (followUpInputState.dxCode ? followUpInputState.dxCode.description : "");

    if (classification.toUpperCase() === "ADVICE") {
      displayText +=
        " at " +
        format(parseISO(followUpInputState.dateTimePicker), "MMM do HH:mm");
    }

    if (
      classification.toUpperCase() === "BASE" ||
      classification.toUpperCase() === "VISIT"
    ) {
      displayText += ", " + followUpInputState.baseQuestionTriageType;
    }

    return displayText;
  }

  return "";
}
