<template>
  <div>
    <div class="consult--header" v-on:click="toggleShowConsultSection">
      <div
        class="standard-icon dijit-arrow-icon"
        :class="
          showConsultSection
            ? 'dijit-arrow-icon--open'
            : 'dijit-arrow-icon--closed'
        "
      ></div>
      <div class="consult--header-title" v-text="getHeaderTitle"></div>
    </div>

    <div
      class="consult--consult-section"
      :class="
        showConsultSection
          ? 'consult--consult-section-show'
          : 'consult--consult-section-hide'
      "
    >
      <div class="consult--consult-large-field-pair">
        <div class="consult--consult-label">Details</div>
        <div
          class="consult--consult-content-large"
          v-text="consultInternal.Details"
        ></div>
      </div>

      <div class="consult--consult-large-field-pair">
        <div class="consult--consult-label">Examination</div>
        <div v-text="consultInternal.Objective"></div>
      </div>

      <div class="consult--consult-large-field-pair">
        <div class="consult--consult-label">Diagnosis</div>
        <div v-text="consultInternal.Assessment"></div>
      </div>

      <div class="consult--consult-large-field-pair">
        <div class="consult--consult-label">Treatment</div>
        <div v-text="consultInternal.Plan"></div>
      </div>

      <div class="consult--consult-large-field-pair" v-if="hasPrescriptions">
        <div class="consult--consult-label">Prescribing</div>
        <consult-prescriptions
          :consult="consultInternal"
        ></consult-prescriptions>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  ref,
  watch,
  SetupContext,
  ComputedRef,
  computed
} from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IConsult } from "@/consults/consult-models";
import { ConsultsService } from "@/consults/consults-service";
import ConsultPrescriptions from "@/consults/prescriptions/consult-prescriptions.vue";

const commonService: CommonService = new CommonService();
const consultsService: ConsultsService = new ConsultsService();

export default defineComponent({
  // type inference enabled
  name: "consult",
  components: { ConsultPrescriptions },
  props: {
    consult: {
      default: () => {
        return consultsService.factoryConsult();
      }
    }
  },
  setup(props: { consult: IConsult }, context: SetupContext) {
    let consultInternal: IConsult = reactive(
      commonService.simpleObjectClone(props.consult)
    );

    let showConsultSection = ref(false);

    onBeforeMount(() => {
      console.log("consult>>>>>>>>>>>>>>>>>>mounted!");
    });

    // const consultsService = new ConsultsService();

    watch(
      () => props.consult,
      (newValue: IConsult, oldValue: IConsult) => {
        console.log("consult>>>>>>>>>>>>>>>>>>watch!", {
          newValue,
          oldValue
        });
        Object.assign(consultInternal, newValue);
      }
    );

    const getHeaderTitle: ComputedRef<string> = computed(() => {
      return consultsService.getHeaderTitle(consultInternal);
    });

    const toggleShowConsultSection = () => {
      showConsultSection.value = !showConsultSection.value;
    };

    const hasPrescriptions = computed(() => {
      return consultInternal.Prescriptions.length > 0;
    });

    return {
      consultInternal,
      consultsService,
      getHeaderTitle,
      showConsultSection,
      toggleShowConsultSection,
      hasPrescriptions
    };
  }
});
</script>

<style scoped>
.consult--header {
  width: 100%;
  border: solid 1px #c5c3c3;
  background: transparent
    linear-gradient(180deg, #e9effd 0%, #cbdff6 64%, #bcd5f0 65%, #bcd5f0 100%)
    0 0 no-repeat padding-box;
}

.consult--header-title {
  color: #243c5f;
  font-weight: 700;
  margin-left: 5px;
  height: 20px;
  padding-top: 5px;
  display: inline-block;
}

.consult--consult-section {
  width: 100%;
}

.consult--consult-section-show {
  opacity: 1;
  height: auto;
}

.consult--consult-section-hide {
  opacity: 0;
  height: 0;
  overflow: hidden;
}

.consult--consult-large-field-pair {
  /*margin: 5px 0;*/
  padding: 5px;
  border-bottom: 1px solid #dcdada;
}

.consult--consult-label {
  font-weight: 700;
  padding: 0 0 5px 0;
}
</style>
