import { Modu<PERSON> } from "vuex";
import { IRootState } from "@/store/store";
import { ICleoPermission } from "@/permissions/permission-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { PermissionService } from "@/permissions/permission-service";
import { CleoContextMenuService } from "@/calls/grids/contextmenu/cleo-context-menu-service";

const permissionService: PermissionService = new PermissionService();
const cleoContextMenuService = new CleoContextMenuService();

export interface IContextMenuStoreState {
  showMenu: boolean;
  permsLoading: boolean;
  cleoCallSummary: ICleoCallSummary;
  permsForCall: Record<string, ICleoPermission>;
  permsForCallSimple: Record<string, ICleoPermission>;
  permsForMenuSimple: Record<string, ICleoPermission>;
  clickedElementCoords: {
    x: number;
    y: number;
  };
}

export enum CONTEXT_MENU_STORE_CONST {
  CONTEXT_MENU__CONST_MODULE_NAME = "CONTEXT_MENU__CONST_MODULE_NAME",

  CONTEXT_MENU__MUTATIONS_SHOW_MENU = "CONTEXT_MENU__MUTATIONS_SHOW_MENU",
  CONTEXT_MENU__MUTATIONS_PERMS_LOADING = "CONTEXT_MENU__MUTATIONS_PERMS_LOADING",
  CONTEXT_MENU__MUTATIONS_COORDS = "CONTEXT_MENU__MUTATIONS_COORDS",
  CONTEXT_MENU__MUTATIONS_PERMS = "CONTEXT_MENU__MUTATIONS_PERMS",
  CONTEXT_MENU__MUTATIONS_CLEO_CALL_SUMMARY = "CONTEXT_MENU__MUTATIONS_CLEO_CALL_SUMMARY"
}

const mutations = {
  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_SHOW_MENU](
    state: IContextMenuStoreState,
    showMenu: boolean
  ): void {
    state.showMenu = showMenu;
  },

  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS_LOADING](
    state: IContextMenuStoreState,
    isLoading: boolean
  ): void {
    state.permsLoading = isLoading;
  },

  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_COORDS](
    state: IContextMenuStoreState,
    coords: {
      x: number;
      y: number;
    }
  ): void {
    state.clickedElementCoords = coords;
  },

  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS](
    state: IContextMenuStoreState,
    permsForCall: Record<string, ICleoPermission>
  ): void {
    state.permsForCall = { ...permsForCall };
    state.permsForCallSimple = permissionService.simpleKeyPerms(permsForCall);
    // state.permsForMenuSimple = cleoContextMenuService.filterPermsForRightClick(
    //   permsForCall
    // );
    state.permsForMenuSimple = permissionService.simpleKeyPerms(permsForCall);
  },

  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_CLEO_CALL_SUMMARY](
    state: IContextMenuStoreState,
    cleoCallSummary: ICleoCallSummary
  ): void {
    state.cleoCallSummary = { ...cleoCallSummary };
  }
};

const getters = {};
const actions = {};

export const contextMenuStore: Module<IContextMenuStoreState, IRootState> = {
  namespaced: true,
  state: cleoContextMenuService.factoryContextMenuStoreState(),
  mutations,
  getters,
  actions
};
