@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap");

:root {
  /*************************************************************************************************************************************
    Colours
    *************************************************************************************************************************************/

  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;

  --grey-50: #f9fafb;
  --grey-100: #f3f4f6;
  --grey-200: #e5e7eb;
  --grey-300: #d1d5db;
  --grey-400: #9ca3af;
  --grey-500: #6b7280;
  --grey-600: #4b5563;
  --grey-700: #374151;
  --grey-800: #1f2937;
  --grey-900: #111827;

  --zinc-50: #fafafa;
  --zinc-100: #f4f4f5;
  --zinc-200: #e4e4e7;
  --zinc-300: #d4d4d8;
  --zinc-400: #a1a1aa;
  --zinc-500: #71717a;
  --zinc-600: #52525b;
  --zinc-700: #3f3f46;
  --zinc-800: #27272a;
  --zinc-900: #18181b;

  --neutral-0: #ffffff;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-1000: #000000;

  --stone-50: #fafaf9;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-300: #d6d3d1;
  --stone-400: #a8a29e;
  --stone-500: #78716c;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;
  --stone-900: #1c1917;

  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;

  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;

  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;

  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;

  --lime-50: #f7fee7;
  --lime-100: #ecfccb;
  --lime-200: #d9f99d;
  --lime-300: #bef264;
  --lime-400: #a3e635;
  --lime-500: #84cc16;
  --lime-600: #65a30d;
  --lime-700: #4d7c0f;
  --lime-800: #3f6212;
  --lime-900: #365314;

  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;

  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;

  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;

  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;

  --sky-50: #f0f9ff;
  --sky-100: #e0f2fe;
  --sky-200: #bae6fd;
  --sky-300: #7dd3fc;
  --sky-400: #38bdf8;
  --sky-500: #0ea5e9;
  --sky-600: #0284c7;
  --sky-700: #0369a1;
  --sky-800: #075985;
  --sky-900: #0c4a6e;

  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;

  --indigo-50: #eef2ff;
  --indigo-100: #e0e7ff;
  --indigo-200: #c7d2fe;
  --indigo-300: #a5b4fc;
  --indigo-400: #818cf8;
  --indigo-500: #6366f1;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --indigo-800: #3730a3;
  --indigo-900: #312e81;

  --violet-50: #f5f3ff;
  --violet-100: #ede9fe;
  --violet-200: #ddd6fe;
  --violet-300: #c4b5fd;
  --violet-400: #a78bfa;
  --violet-500: #8b5cf6;
  --violet-600: #7c3aed;
  --violet-700: #6d28d9;
  --violet-800: #5b21b6;
  --violet-900: #4c1d95;

  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7e22ce;
  --purple-800: #6b21a8;
  --purple-900: #581c87;

  --fuchsia-50: #fdf4ff;
  --fuchsia-100: #fae8ff;
  --fuchsia-200: #f5d0fe;
  --fuchsia-300: #f0abfc;
  --fuchsia-400: #e879f9;
  --fuchsia-500: #d946ef;
  --fuchsia-600: #c026d3;
  --fuchsia-700: #a21caf;
  --fuchsia-800: #86198f;
  --fuchsia-900: #701a75;

  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;

  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;

  /*************************************************************************************************************************************
    Typography (naming convention: ic24-typography-type--hierarchy
    *************************************************************************************************************************************/

  --ic24-font-size--20: 1.667em;
  /* Equivalent to 20px based on base height of 12px */
  --ic24-font-size--18: 1.5em;
  /* Equivalent to 18px based on base height of 12px */
  --ic24-font-size--16: 1.333em;
  /* Equivalent to 16px based on base height of 12px */
  --ic24-font-size--14: 1.167em;
  /* Equivalent to 14px based on base height of 12px */
  --ic24-font-size--12: 1em;
  /* Equivalent to 12px based on base height of 12px */
  --ic24-font-size--10: 0.833em;
  /* Equivalent to 10px based on base height of 12px */

  --ic24-header--100: normal normal 800 var(--ic24-font-size--20) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-header--200: normal normal 800 var(--ic24-font-size--18) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-header--300: normal normal 800 var(--ic24-font-size--16) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-header--400: normal normal 800 var(--ic24-font-size--14) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-header--500: normal normal 800 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;

  --ic24-subheader--100: normal normal 500 var(--ic24-font-size--18) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-subheader--200: normal normal 500 var(--ic24-font-size--16) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-subheader--300: normal normal 500 var(--ic24-font-size--14) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-subheader--400: normal normal 500 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;

  --ic24-body--100: normal normal 500 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-label--100: normal normal 600 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;

  --ic24-hyperlink--100: normal normal 600 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;

  --ic24-caption--100: normal normal 500 var(--ic24-font-size--12) "Inter",
    "Open Sans", "Segoe UI", Arial;
  --ic24-caption--200: normal normal 500 var(--ic24-font-size--10) "Inter",
    "Open Sans", "Segoe UI", Arial;

  /*************************************************************************************************************************************
    Component styles (naming convention: ic24-component-type--hierarchy__property
    *************************************************************************************************************************************/

  --ic24-card-wrapper--padding: 16px;
  --ic24-card-wrapper--background: var(--neutral-0);
  --ic24-card-wrapper--border: 1px solid;
  --ic24-card-wrapper--border-color: var(--slate-300);
  --ic24-card-wrapper--border-radius: 8px;

  --ic24-card--padding: calc(var(--ic24-card-wrapper--padding) / 2);
  --ic24-card--background: var(--neutral-0);
  --ic24-card--border: 1px solid;
  --ic24-card--border-color: var(--slate-200);
  --ic24-card--border-radius: calc(var(--ic24-card-wrapper--border-radius) / 2);
  --ic24-card-title--text-color: var(--slate-800);

  --ic24-card-well--margin: 0 0 8px 0;
  --ic24-card-well--padding: calc(var(--ic24-card-wrapper--padding) / 2);
  --ic24-card-well--background: var(--slate-50);
  --ic24-card-well--border-radius: calc(
    var(--ic24-card-wrapper--border-radius) / 2
  );
  --ic24-card-well--text-color: var(--slate-700);
  --ic24-card-well--tertiary-text-color: var(--slate-400);

  /*--ic24-modal--background: rgba(255, 255, 255, .7);*/
  --ic24-modal--background: rgba(227, 227, 227, 0.7);
  --ic24-modal--padding: 20px;
  --ic24-modal-dialog--max-width: 1140px;
  --ic24-modal-dialog--max-height: 90%;
  --ic24-modal-dialog--padding: 16px;
  --ic24-modal-dialog--background: var(--neutral-0);
  --ic24-modal-dialog--border: 1px solid;
  /*--ic24-modal-dialog--border-color: var(--slate-200);*/
  --ic24-modal-dialog--border-color: var(--slate-300);
  --ic24-modal-dialog--border-radius: 8px;
  --ic24-modal-dialog--box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
    rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
  --ic24-modal-dialog-header--background: var(--sky-100);
  --ic24-modal-dialog-header--padding: calc(
    var(--ic24-modal-dialog--padding) / 2
  );
  --ic24-modal-dialog-header--border-radius: calc(
    var(--ic24-modal-dialog--padding) / 4
  );
  --ic24-modal-dialog-header--text-color: var(--slate-800);
  --ic24-modal-dialog-header_error--background: var(--red-100);
  --ic24-modal-dialog-header_error--border-color: var(--red-700);
  --ic24-modal-dialog-header_success--background: var(--green-100);
  --ic24-modal-dialog-header_success--border-color: var(--green-700);
  --ic24-modal-dialog-header_info--background: var(--slate-200);
  --ic24-modal-dialog-header_info--border-color: var(--blue-700);

  --ic24-pill--padding: 8px 16px;
  --ic24-pill--border-radius: 50px;

  --ic24-button--height: 28px;
  --ic24-button--padding: 4px 8px;
  --ic24-button--border: none;
  --ic24-button--border-radius: 4px;
  --ic24-button_disabled--background: var(--slate-200);
  --ic24-button_disabled--text-color: var(--slate-400);
  --ic24-button-primary--background: var(--blue-800);
  --ic24-button-primary--text-color: var(--neutral-0);
  --ic24-button-primary_hover--background: var(--blue-700);
  --ic24-button-primary_hover--text-color: var(--neutral-0);
  --ic24-button-primary_active--background: var(--blue-900);
  --ic24-button-primary_active--text-color: var(--neutral-0);
  --ic24-button-tertiary--background: var(--slate-200);
  --ic24-button-tertiary--border: 1px solid;
  --ic24-button-tertiary--border-color: var(--slate-300);
  --ic24-button-tertiary--text-color: var(--slate-800);
  --ic24-button-tertiary_hover--background: var(--slate-300);
  --ic24-button-tertiary_hover--text-color: var(--slate-800);
  --ic24-button-tertiary_active--background: var(--slate-400);
  --ic24-button-tertiary_active--text-color: var(--slate-800);

  --ic24-button--destructive__background: var(--red-50);
  --ic24-button--destructive__border: var(--red-100);
  --ic24-button--destructive__text-color: var(--red-600);
  --ic24-button--destructive__hover-background: var(--red-400);

  --ic24-input--padding: 0 4px;
  --ic24-input--border: 1px solid;
  --ic24-input--border-color: var(--slate-300);
  --ic24-input--border-radius: 4px;
  --ic24-input--placeholder-color: var(--slate-500);
  --ic24-input_focus--border-color: var(--ic24-button-primary--background);
  --ic24-input_disabled--background: var(--slate-100);
  --ic24-input_disabled--text-color: var(--slate-400);

  --ic24-checkbox--height: 16px;
  --ic24-checkbox--width: 16px;
  --ic24-checkbox--border: 1px solid;
  --ic24-checkbox--border-color: var(--slate-300);
  --ic24-checkbox--border-radius: 4px;
  --ic24-checkbox--background: var(--neutral-0);
  --ic24-checkbox_focus--border-color: var(--ic24-button-primary--background);
  --ic24-checkbox_checked--background: var(--ic24-button-primary--background);
  --ic24-checkbox_checked--border-color: var(--ic24-button-primary--background);
  --ic24-checkbox_disabled--background: var(--ic24-button_disabled--background);
  --ic24-checkbox_disabled--border-color: var(
    --ic24-button_disabled--background
  );

  --ic24-warning--text-color: var(--red-700);
  --ic24-success--text-color: var(--green-700);

  --ic24-box-shadow--tight: 0 1px 2px rgba(0, 0, 0, 0.08),
    0 2px 5px rgba(0, 0, 0, 0.04);
  --ic24-box-shadow--small: 0 4px 4px rgba(0, 0, 0, 0.03),
    0 5px 10px rgba(0, 0, 0, 0.04);
  --ic24-box-shadow--medium: 0 8px 12px rgba(0, 0, 0, 0.08),
    0 12px 15px rgba(0, 0, 0, 0.03);
  --ic24-box-shadow--large: 0 7px 5px rgba(0, 0, 0, 0.05),
    0 14px 26px rgba(0, 0, 0, 0.15);

  --ic24-flex-gap-tiny: 2px;
  --ic24-flex-gap-small: 4px;
  --ic24-flex-gap: 8px;
  --ic24-flex-gap-large: 16px;
  --ic24-flex-gap-xlarge: 32px;
  --ic24-flex-gap-xxlarge: 64px;

  --ic24-transition--ease-in-out: ease-in-out 0.16s;

  --cleo-dark-blue: #243c5f;

}

/** {*/
/*  margin: 0;*/
/*  padding: 0;*/
/*  box-sizing: border-box;*/
/*  font-size: 12px;*/
/*  font-family: "Inter", sans-serif;*/
/*}*/

/*body {*/
/*  background: var(--slate-50);*/
/*}*/

/*************************************************************************************************************************************
Reusable Components (naming convention: component-type--subsection/hierarchy__descriptor
*************************************************************************************************************************************/

[class^="ic24-header"] {
  margin: 0;
  padding: 0;
  letter-spacing: 0.017em;
}

.ic24-header--100 {
  font: var(--ic24-header--100);
}

.ic24-header--200 {
  font: var(--ic24-header--200);
}

.ic24-header--300 {
  font: var(--ic24-header--300);
}

.ic24-header--400 {
  font: var(--ic24-header--400);
}

.ic24-header--500 {
  font: var(--ic24-header--500);
}

.ic24-subheader--100 {
  font: var(--ic24-subheader--100);
}

.ic24-subheader--200 {
  font: var(--ic24-subheader--200);
}

.ic24-subheader--300 {
  font: var(--ic24-subheader--300);
}

.ic24-subheader--400 {
  font: var(--ic24-subheader--400);
}

.ic24-body--100 {
  font: var(--ic24-body--100);
}

.ic24-caption--100 {
  font: var(--ic24-caption--100);
}

.ic24-caption--200 {
  font: var(--ic24-caption--200);
}

.ic24-hyperlink--100 {
  width: fit-content;
  font: var(--ic24-hyperlink--100);
  color: var(--ic24-button-primary--background);
  text-decoration: underline;
}

.ic24-label--100 {
  font: var(--ic24-label--100);
}

.ic24-warning-text {
  color: var(--ic24-warning--text-color);
}

.ic24-success-text {
  color: var(--ic24-success--text-color);
}

.ic24-warning-icon {
  fill: var(--ic24-warning--text-color);
}

.ic24-success-icon {
  fill: var(--ic24-success--text-color);
}

.ic24-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--ic24-button--height);
  padding: var(--ic24-button--padding);
  border: var(--ic24-button--border);
  border-radius: var(--ic24-button--border-radius);
  cursor: pointer;
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-button.ic24-card-content {
  width: 100%;
  display: flex;
  align-items: unset;
  justify-content: unset;
  padding: unset;
  margin: unset;
  height: fit-content;
  text-align: unset;
}

button > span {
  font: var(--ic24-label--100);
}

.ic24-button--primary {
  background: var(--ic24-button-primary--background);
}

.ic24-button--primary > span {
  color: var(--ic24-button-primary--text-color);
}

.ic24-button--primary:hover {
  background: var(--ic24-button-primary_hover--background);
}

.ic24-button--primary:hover > span {
  color: var(--ic24-button-primary_hover--text-color);
}

.ic24-button--primary:active {
  background: var(--ic24-button-primary_active--background);
}

.ic24-button--primary:active > span {
  color: var(--ic24-button-primary_active--text-color);
}

.ic24-button--tertiary {
  border: var(--ic24-button-tertiary--border);
  border-color: var(--ic24-button-tertiary--border-color);
  background: var(--ic24-button-tertiary--background);
}

.ic24-button--tertiary > span {
  color: var(--ic24-button-tertiary--text-color);
}

.ic24-button--tertiary:hover {
  background: var(--ic24-button-tertiary_hover--background);
}

.ic24-button--tertiary:hover > span {
  color: var(--ic24-button-tertiary_hover--text-color);
}

.ic24-button--tertiary:active {
  background: var(--ic24-button-tertiary_active--background);
}

.ic24-button--tertiary:active > span {
  color: var(--ic24-button-tertiary_active--text-color);
}

button:disabled,
button:disabled:hover,
button:disabled:active,
.ic24-button--primary:disabled,
.ic24-button--primary:disabled:hover,
.ic24-button--primary:disabled:active,
.ic24-button--tertiary:disabled,
.ic24-button--tertiary:disabled:hover,
.ic24-button--tertiary:disabled:active {
  background: var(--ic24-button_disabled--background);
  cursor: default;
}

button:disabled,
button:disabled:hover,
button:disabled:active,
button:disabled > span,
button:disabled:hover > span,
button:disabled:active > span,
.ic24-button--primary:disabled > span,
.ic24-button--primary:disabled:hover > span,
.ic24-button--primary:disabled:active > span,
.ic24-button--tertiary:disabled > span,
.ic24-button--tertiary:disabled:hover > span,
.ic24-button--tertiary:disabled:active > span {
  color: var(--ic24-button_disabled--text-color);
}

.ic24-button--destructive {
  background: var(--ic24-button--destructive__background);
  border: 1px solid var(--ic24-button--destructive__border);
}

.ic24-button--destructive > span {
  color: var(--ic24-button--destructive__text-color);
}

.ic24-button--destructive:hover {
  background: var(--ic24-button--destructive__hover-background);
}

.ic24-input {
  height: var(--ic24-button--height);
  padding: var(--ic24-input--padding);
  border: var(--ic24-input--border);
  border-color: var(--ic24-input--border-color);
  border-radius: var(--ic24-input--border-radius);
  caret-color: var(--ic24-button-primary--background);
  transition: var(--ic24-transition--ease-in-out);
  outline: none;
}

.ic24-input::placeholder {
  color: var(--ic24-input--placeholder-color);
}

.ic24-input:focus {
  border-color: var(--ic24-input_focus--border-color);
  transition: var(--ic24-transition--ease-in-out);
}

textarea.ic24-input {
  padding: 4px;
  resize: none;
}

.ic24-input:disabled,
.ic24-input:disabled:hover,
.ic24-input:disabled:focus,
.ic24-input:disabled::placeholder {
  background: var(--ic24-input_disabled--background);
  color: var(--ic24-input_disabled--text-color);
}

.ic24-input-invalid,
.ic24-input-invalid:focus,
.ic24-input-invalid:active {
  border: 1px solid var(--InputInvalidBorderColor);
  caret-color: var(--InputInvalidCaretColor);
}

.ic24-checkbox-input-container label,
.ic24-radio-input-container label {
  display: block;
  padding: 4px 0 0 24px;
}

.ic24-checkbox-input-container input[type="checkbox"],
.ic24-radio-input-container input[type="radio"] {
  position: absolute;
  top: 0;
  left: 0;
  height: var(--ic24-checkbox--height);
  width: var(--ic24-checkbox--width);
  opacity: 0;
  outline: none;
}

.ic24-checkbox-input-container input[type="checkbox"] + label,
.ic24-radio-input-container input[type="radio"] + label {
  cursor: pointer;
}

.ic24-checkbox-input-container:disabled input[type="checkbox"] + label,
.ic24-radio-input-container:disabled input[type="radio"] + label {
  cursor: default;
}

.ic24-checkbox-input-container input[type="checkbox"] + label::before,
.ic24-radio-input-container input[type="radio"] + label::before {
  position: absolute;
  top: 2px;
  left: 0;
  height: var(--ic24-checkbox--height);
  width: var(--ic24-checkbox--width);
  border: var(--ic24-checkbox--border);
  border-color: var(--ic24-checkbox--border-color);
  background-color: var(--ic24-checkbox--background);
  content: "";
  outline: none;
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-checkbox-input-container input[type="checkbox"] + label::before {
  border-radius: var(--ic24-checkbox--border-radius);
}

.ic24-radio-input-container input[type="radio"] + label::before {
  border-radius: 50%;
}

.ic24-checkbox-input-container input[type="checkbox"]:checked + label::before,
.ic24-radio-input-container input[type="radio"]:checked + label::before {
  border-color: var(--ic24-checkbox_checked--border-color);
  background-color: var(--ic24-checkbox_checked--background);
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-checkbox-input-container input[type="checkbox"] + label::after {
  position: absolute;
  top: 1px;
  left: -1px;
  height: 20px;
  width: 20px;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.293 14.707l-3-3a.999.999 0 111.414-1.414l2.236 2.236 6.298-7.18a.999.999 0 111.518 1.3l-7 8a1 1 0 01-.72.35 1.017 1.017 0 01-.746-.292z' fill='%23FFFFFF'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  opacity: 0;
  content: "";
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-radio-input-container input[type="radio"] + label::after {
  position: absolute;
  top: 1px;
  left: -1px;
  height: 18px;
  width: 18px;
  background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse fill='%23FFFFFF' cx='10' cy='10' id='svg_2' rx='4' ry='4'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  opacity: 0;
  content: "";
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-checkbox-input-container
  input[type="checkbox"]:indeterminate
  + label::before {
  background: var(--ic24-checkbox_checked--background);
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 9H5a1 1 0 100 2h10a1 1 0 100-2z' fill='%23FFFFFF'/%3E%3C/svg%3E");
}

.ic24-checkbox-input-container input[type="checkbox"]:checked + label::after,
.ic24-radio-input-container input[type="radio"]:checked + label::after {
  opacity: 1;
}

.ic24-checkbox-input-container input[type="checkbox"]:focus + label::before,
.ic24-checkbox-input-container
  input[type="checkbox"]:checked:focus
  + label::before,
.ic24-checkbox-input-container
  input[type="checkbox"]:active
  + label::before
  .ic24-checkbox-input-container
  input[type="checkbox"]:checked:active
  + label::before,
.ic24-radio-input-container input[type="radio"]:focus + label::before,
.ic24-radio-input-container input[type="radio"]:checked:focus + label::before,
.ic24-radio-input-container
  input[type="radio"]:active
  + label::before
  .ic24-radio-input-container
  input[type="radio"]:checked:active
  + label::before {
  border-color: var(--ic24-checkbox_focus--border-color);
  outline: none;
  transition: var(--ic24-transition--ease-in-out);
}

.ic24-checkbox-input-container input[type="checkbox"]:disabled + label::before,
.ic24-checkbox-input-container
  input[type="checkbox"]:disabled:checked
  + label::before,
.ic24-radio-input-container input[type="radio"]:disabled + label::before,
.ic24-radio-input-container
  input[type="radio"]:disabled:checked
  + label::before {
  border-color: var(--ic24-checkbox_disabled--border-color);
  background-color: var(--ic24-checkbox_disabled--background);
}

.ic24-checkbox-input-container input[type="checkbox"]:disabled + label::after {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.293 14.707l-3-3a.999.999 0 111.414-1.414l2.236 2.236 6.298-7.18a.999.999 0 111.518 1.3l-7 8a1 1 0 01-.72.35 1.017 1.017 0 01-.746-.292z' fill='%2394a3b8'/%3E%3C/svg%3E");
}

.ic24-radio-input-container input[type="radio"]:disabled + label::after {
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse fill='%2394a3b8' cx='10' cy='10' id='svg_2' rx='4' ry='4'/%3E%3C/svg%3E");
}

.ic24-checkbox-input-container input[type="checkbox"]:disabled + label::before,
.ic24-radio-input-container input[type="radio"]:disabled + label::before {
  cursor: default;
}

.ic24-pill {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: 28px;
  padding: var(--ic24-pill--padding);
  border-radius: var(--ic24-pill--border-radius);
}

.ic24-pill--icon-container {
  position: relative;
  left: -4px;
}

.ic24-pill > span {
  font: var(--ic24-body--100);
}

.ic24-pill--info {
  background: var(--blue-100);
}

.ic24-pill--info > span {
  color: var(--blue-700);
}

.ic24-pill--info svg path {
  fill: var(--blue-700);
}

/*************************************************************************************************************************************
Utilities
*************************************************************************************************************************************/
.ic24-flex {
  display: flex;
}

.ic24-flex-column {
  display: flex;
  flex-direction: column;
}

.ic24-flex-row {
  display: flex;
  flex-direction: row;
}

.ic24-flex-wrap {
  flex-wrap: wrap;
}

.ic24-flex-nowrap {
  flex-wrap: nowrap;
}

.ic24-flex-grow {
  flex-grow: 1;
}

.ic24-flex-start {
  align-items: flex-start;
}

.ic24-flex-center {
  align-items: center;
}

.ic24-flex-end {
  align-items: flex-end;
}

.ic24-align-self-flex-start {
  align-self: flex-start;
}

.ic24-align-self-flex-end {
  align-self: flex-end;
}

.ic24-align-self-flex-center {
  align-self: center;
}

.ic24-justify-flex-start {
  justify-content: flex-start;
}

.ic24-justify-flex-center {
  justify-content: center;
}

.ic24-justify-flex-end {
  justify-content: flex-end;
}

.ic24-flex-row--end {
  margin-left: auto;
}

.ic24-justify-flex-space-between {
  justify-content: space-between;
}

.ic24-justify-flex-space-around {
  justify-content: space-around;
}

.ic24-justify-flex-space-evenly {
  justify-content: space-evenly;
}

.ic24-flex-gap-tiny {
  gap: var(--ic24-flex-gap-tiny);
}

.ic24-flex-gap-small {
  gap: var(--ic24-flex-gap-small);
}

.ic24-flex-gap {
  gap: var(--ic24-flex-gap);
}

.ic24-flex-gap-large {
  gap: var(--ic24-flex-gap-large);
}

.ic24-flex-gap-xlarge {
  gap: var(--ic24-flex-gap-xlarge);
}

.ic24-flex-gap-xxlarge {
  gap: var(--ic24-flex-gap-xxlarge);
}

.ic24-full-width {
  width: 100%;
}

.ic24-vertical-spacer-standard {
  margin-top: var(--ic24-flex-gap);
}

.ic24-vertical-spacer-large {
  margin-top: var(--ic24-flex-gap-large);
}

.ic24-justify-fle-row-vert-center {
  align-items: center;
}

.ic24-full-height {
  height: 100%;
}

.ic24-uppercase {
  text-transform: uppercase;
}

.ic24-section-wrapper {
  margin: 20px 0 0 0;
}

.ic24-icon-text--container {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 4px;
}

.ic24-button--container {
  margin: 0 4px;
  gap: 4px;
}

.ic24-input-container {
  gap: 4px;
}

.ic24-checkbox-input-container,
.ic24-radio-input-container {
  position: relative;
  display: inline-flex;
}

.ic24-container-gradient--white {
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.ic24-list-item {
  list-style-position: inside;
  margin: 4px 0;
}

.ic24-hr {
  border: 0;
  border-top: 1px solid var(--slate-300);
  margin: 0;
}

/*************************************************************************************************************************************
Media queries to target specific screen sizes
*************************************************************************************************************************************/

/*************************************************************************************************************************************
<Card>
*************************************************************************************************************************************/
.ic24-card--wrapper {
  padding: var(--ic24-card-wrapper--padding);
  background: var(--ic24-card-wrapper--background);
  border: var(--ic24-card-wrapper--border);
  border-color: var(--ic24-card-wrapper--border-color);
  border-radius: var(--ic24-card-wrapper--border-radius);
  margin: 4px 0;
}

.ic24-card {
  padding: var(--ic24-card--padding);
  background: var(--ic24-card--background);
  border: var(--ic24-card--border);
  border-color: var(--ic24-card--border-color);
  border-radius: var(--ic24-card--border-radius);
}

.ic24-card-well {
  gap: 4px;
  margin: var(--ic24-card-well--margin);
  padding: var(--ic24-card-well--padding);
  background: var(--ic24-card-well--background);
  border-radius: var(--ic24-card-well--border-radius);
  color: var(--ic24-card-well--text-color);
}

.ic24-card > .ic24-card-well {
  margin: 20px 0 12px 0;
  gap: 4px;
}
/*************************************************************************************************************************************
</Card>
*************************************************************************************************************************************/

/*************************************************************************************************************************************
<CompleteForm>
*************************************************************************************************************************************/

.complete-form--container {
  /*width: 1000px;*/
  /*height: 650px;*/
  width: 95vw;
  height: 70vh;
  font-size: 12px;
  font-family: "Inter", sans-serif;
}


/*If screen is wider than 1100px then set width of complete-form--container to 1000px */
@media (min-width: 1200px) {
  .complete-form--container {
    width: 1000px;
  }
}

/*If screen is taller than 750px then set height of complete-form--container to 650px */
@media (min-height: 800px) {
  .complete-form--container {
    height: 650px;
  }
}

/*@media (max-width: 1010px) {*/
/*  .complete-form--container {*/
/*    width: 95vw;*/
/*  }*/
/*}*/

/*@media (max-height: 640px) {*/
/*  .complete-form--container {*/
/*    height: 90vh;*/
/*  }*/
/*}*/

.complete-form--content {
  flex-grow: 1;
  overflow: auto;
}

.complete-form--bottom-buttons {
  margin-top: auto;
  justify-content: space-between;
}

.complete-form--bottom-button {
  min-width: 100px;
  background: #e7e6e8;
  border: 1px solid #bababc;
}

/*.complete-form--bottom-button:focus {*/
/*  background: #e7e6e8;*/
/*}*/

/*.complete-form--bottom-button:focus span {*/
/*  color: rgba(61.00000016391277, 62.00000010430813, 69.00000348687172, 1);*/
/*}*/

.complete-form--bottom-button:hover span {
  color: rgba(61.00000016391277, 62.00000010430813, 69.00000348687172, 1);
}

.complete-form--bottom-button:hover {
  background: #d9d9d9;
}

.complete-form--bottom-button:disabled {
  background-color: rgba(
    231.00000143051147,
    230.00000149011612,
    232.00000137090683,
    1
  );
}

.complete-form--bottom-button span {
  color: rgba(61.00000016391277, 62.00000010430813, 69.00000348687172, 1);
}
.complete-form--bottom-button:disabled span {
  color: rgba(158.00000578165054, 159.0000057220459, 158.00000578165054, 1);
}

.complete-step--subheader {
  font-size: 18px;
  color: #000000;
}

.complete-step--big-button {
  width: 250px;
  height: 70px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--ic24-transition--ease-in-out);
}

.complete-step--big-button:hover {
  /*background: #D9D9D9;*/
  text-decoration: underline;
  text-decoration-color: white;
  text-decoration-thickness: 2px;
}

.complete-step--big-button-selected {
  background: #d9d9d9;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  text-decoration: underline;
  text-decoration-color: white;
  text-decoration-thickness: 2px;
}

.complete-step--big-button-green {
  background: var(--green-600);
}
.complete-step--big-button-green:hover {
  background: var(--green-600);
}

.complete-step--big-button-green > span {
  color: white;
}

.complete-step--big-button-red {
  background: var(--red-600);
}

.complete-step--big-button-red:hover {
  background: var(--red-600);
}

.complete-step--big-button-red > span {
  color: white;
}

.complete-form--validation-error {
  color: var(--red-700);
  font-weight: bold;
}

.complete-step--simple-buttons button {
  width: 350px;
}

.complete-step--select-button {
  background: #e2ebfc;
  border: 1px solid #b1badf;
}

/*.complete-step--select-button:focus {*/
/*  background: #e2ebfc;*/
/*}*/

.complete-step--select-button:hover {
  background: #d9d9d9;
}

.complete-step--select-button-selected {
  background-color: #d9d9d9 !important;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.complete-step--select-button span {
  color: black !important;
}

/*************************************************************************************************************************************
</CompleteForm>
*************************************************************************************************************************************/
