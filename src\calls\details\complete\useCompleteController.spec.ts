import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import {
  ICompleteControllerInput,
  useCompleteController
} from "@/calls/details/complete/useCompleteController";

describe("useCompleteController", () => {
  it("cacheStepState TRUE", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);

    //  set controller to cache step state
    controller.state.cacheStepState = true;

    expect(controller.state.processName).toBe("SAVE_AND_RETURN_CAS_CLINICAL");
    expect(controller.state.cacheStepState).toBe(true);

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FAILED_CONTACT",
      description: "Failed Contact",
      value: "FAILED_CONTACT"
    });

    // expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");

    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.onHowMangedSelected({
      id: "VIRTUAL_CONSULT_TEL_AND_VIDEO",
      description: "Virtual Consult Tel and Video",
      value: "2-VirtualConsultTelVideo"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    //  Data has been cached for all steps.
    expect(controller.state.userResponse.howManaged.id).toBe(
      "VIRTUAL_CONSULT_TEL_AND_VIDEO"
    );
    expect(controller.state.userResponse.failedContactReason.id).toBe(
      "NO_ANSWER"
    );
    expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");
  });

  it("cacheStepState FALSE", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);

    //  set controller to NOT cache step state
    controller.state.cacheStepState = false;

    expect(controller.state.processName).toBe("SAVE_AND_RETURN_CAS_CLINICAL");
    expect(controller.state.cacheStepState).toBe(false);

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FAILED_CONTACT",
      description: "Failed Contact",
      value: "FAILED_CONTACT"
    });

    // expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");

    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.onHowMangedSelected({
      id: "VIRTUAL_CONSULT_TEL_AND_VIDEO",
      description: "Virtual Consult Tel and Video",
      value: "2-VirtualConsultTelVideo"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    //  Data has NOT been cached for all steps.
    expect(controller.state.userResponse.howManaged.id).toBe("");
    expect(controller.state.userResponse.failedContactReason.id).toBe("");

    //  We;ve goe back to this step, so the data should not be cleared.
    expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");
  });
});
