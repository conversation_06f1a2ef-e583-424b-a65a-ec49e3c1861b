<template>
  <div>
    Class
    <select
      v-model="selectedValue"
      v-on:change="onChange"
      :class="
        selectedValue === '' ? '' : 'grid-route-toolbar--filters-warn-value'
      "
    >
      <option value="">All</option>
      <option
        v-for="(classn, index) in classifications"
        :value="classn"
        :key="index"
      >
        <span v-text="classn"></span>
      </option>
    </select>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";

@Component({
  name: "grid-filter-classn"
})
export default class GridFilterClassn extends Vue {
  @Prop({
    default: () => {
      return [];
    }
  })
  public readonly classifications!: string[];

  public selectedValue = "";

  public onChange() {
    this.$emit("change", this.selectedValue);
  }
}
</script>

<style scoped>
.grid-filter-classn--value-selected {
  background-color: red;
}
</style>
