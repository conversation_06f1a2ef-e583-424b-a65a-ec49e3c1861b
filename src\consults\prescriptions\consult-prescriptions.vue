<template>
  <div>
    <div v-for="prescription in consult.Prescriptions" :key="prescription.Id">
      <consult-prescription
        :consult-prescription="prescription"
      ></consult-prescription>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";

import { IConsult } from "@/consults/consult-models";
import ConsultPrescription from "@/consults/prescriptions/consult-prescription.vue";

export default defineComponent({
  name: "consult-prescriptions",
  components: { ConsultPrescription },
  props: {
    consult: {
      type: Object as PropType<IConsult>,
      required: true
    }
  }
});
</script>
