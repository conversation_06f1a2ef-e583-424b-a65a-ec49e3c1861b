<template>
  <div class="ic24-modal">
    <div
      class="ic24-modal-dialog"
      :class="isFullWidth ? 'ic24-full-width' : ''"
    >
      <slot name="header">
        <header
          class="ic24-modal-dialog--header"
          :class="headerType ? 'ic24-modal-dialog--header-' + headerType : ''"
        >
          <h5 class="ic24-header--400" v-text="title"></h5>
        </header>
      </slot>

      <slot name="content">
        <section class="ic24-modal-dialog--content">
          <!--        <p class="ic24-body&#45;&#45;100">We were unable to determine the endpoint for this message.</p>-->
          <!--        <br>-->
          <!--        <p class="ic24-body&#45;&#45;100">Please retry or call <strong>01603 286 286</strong>.</p>-->
          <!--        <br>-->
          <!--        <p class="ic24-header&#45;&#45;500">Attempt 1</p>-->
          <p class="ic24-body--100" v-text="simpleContent"></p>
        </section>
      </slot>

      <section class="ic24-modal-dialog--footer">
        <div class="ic24-modal-dialog--button-container">
          <slot name="buttons">
            <div
              class="ic24-modal-dialog--button-group-container"
              data-button-group-priority="secondary"
            >
              <slot name="buttons-cancel-group">
                <button
                  class="ic24-button--tertiary"
                  v-on:click="clickedCancel"
                >
                  <span v-text="buttonCancelText"></span>
                </button>
              </slot>
            </div>
            <div
              class="ic24-modal-dialog--button-group-container"
              data-button-group-priority="primary"
            >
              <slot name="buttons-ok-group">
                <!--            <button class="ic24-button&#45;&#45;primary">-->
                <!--              <span>Retry</span>-->
                <!--            </button>-->
                <button class="ic24-button--primary" v-on:click="clickedOk">
                  <span v-text="buttonOkText"></span>
                </button>
              </slot>
            </div>
          </slot>
        </div>
      </section>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeUnmount,
  PropType,
  SetupContext
} from "@vue/composition-api";

export default defineComponent({
  // type inference enabled
  name: "cleo-modal-v2",
  components: {},
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      }
    },
    simpleContent: {
      type: String,
      default: () => {
        return "";
      }
    },
    buttonCancelText: {
      type: String,
      default: () => {
        return "Cancel";
      }
    },
    buttonOkText: {
      type: String,
      default: () => {
        return "OK";
      }
    },
    isFullWidth: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    isError: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    headerType: {
      type: String as PropType<"" | "success" | "info" | "error">,
      default: () => {
        return "";
      }
    }
  },
  setup(
    props: {
      title: string;
      simpleContent: string;
      buttonCancelText: string;
      buttonOkText: string;
      isFullWidth: boolean;
      isError: boolean;
    },
    context: SetupContext
  ) {
    setScrollBar(false);

    function clickedOk() {
      setScrollBar(true);
      context.emit("clickedOk");
    }
    function clickedCancel() {
      setScrollBar(true);
      context.emit("clickedCancel");
    }

    function setScrollBar(showIt: boolean) {
      window.document.getElementsByTagName("body")[0].style.overflow = showIt
        ? ""
        : "hidden";
    }

    onBeforeUnmount(() => {
      setScrollBar(true);
    });

    return {
      clickedOk,
      clickedCancel
    };
  }
});
</script>

<style>
.ic24-modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ic24-modal--background);
  padding: var(--ic24-modal--padding);
  z-index: 100;
}

.ic24-modal-dialog {
  display: flex;
  flex-direction: column;
  //gap: 12px;
  max-width: var(--ic24-modal-dialog--max-width);
  /*width: 100%;*/
  max-height: var(--ic24-modal-dialog--max-height);
  //padding: var(--ic24-modal-dialog--padding);
  border: var(--ic24-modal-dialog--border);
  border-color: var(--ic24-modal-dialog--border-color);
  border-radius: var(--ic24-modal-dialog--border-radius);
  background: var(--ic24-modal-dialog--background);
  box-shadow: var(--ic24-modal-dialog--box-shadow);
}

.ic24-modal-dialog--header {
  padding: var(--ic24-modal-dialog-header--padding);
  border-radius: var(--ic24-modal-dialog-header--border-radius);
  background: var(--ic24-modal-dialog-header--background);
  color: var(--ic24-modal-dialog-header--text-color);
}

.ic24-modal-dialog--header-info {
  background: var(--ic24-modal-dialog-header_info--background);
  border: 1px solid;
  border-color: var(--ic24-modal-dialog-header_info--border-color);
}

.ic24-modal-dialog--header-success {
  background: var(--ic24-modal-dialog-header_success--background);
  border: 1px solid;
  border-color: var(--ic24-modal-dialog-header_success--border-color);
}

.ic24-modal-dialog--header-error {
  background: var(--ic24-modal-dialog-header_error--background);
  border: 1px solid;
  border-color: var(--ic24-modal-dialog-header_error--border-color);
}

.ic24-modal-dialog--content {
  max-height: 100%;
  color: var(--slate-700);
  overflow: auto;
  padding: 0 var(--ic24-modal-dialog--padding);
}

.ic24-modal-section-wrapper {
  margin: 20px 0;
}

.ic24-modal-dialog--button-container {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0 0 0;
  gap: 8px;
}

.ic24-modal-dialog--button-container
  .ic24-modal-dialog--button-group-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.ic24-modal-dialog--button-group-container[data-button-group-priority="primary"] {
  justify-content: flex-end;
  order: 2;
}

.ic24-modal-dialog--button-group-container[data-button-group-priority="secondary"] {
  justify-content: flex-start;
  order: 1;
}

/*************************************************************************************************************************************
Media queries to target specific screen sizes
*************************************************************************************************************************************/

@media screen and (max-width: 499px) {
  .ic24-modal-dialog {
    max-width: 90%;
    max-height: 90%;
  }

  .ic24-modal-dialog--button-container {
    flex-direction: column;
    flex-wrap: unset;
    justify-content: unset;
    width: 100%;
  }

  .ic24-modal-dialog--button-container
    .ic24-modal-dialog--button-group-container {
    width: 100%;
    flex-direction: column;
    flex-wrap: unset;
  }

  .ic24-modal-dialog--button-group-container[data-button-group-priority="primary"] {
    justify-content: unset;
    order: 1;
  }

  .ic24-modal-dialog--button-group-container[data-button-group-priority="secondary"] {
    justify-content: unset;
    order: 2;
  }
}
</style>
