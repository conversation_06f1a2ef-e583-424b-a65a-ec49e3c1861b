<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <complete-step-header :step="step" />

    <div class="complete-step--subheader">
      Is a taxi required.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <div class="ic24-flex-row ic24-flex-gap-large">
      <RadioButtonObj
        v-model="valueInternal"
        :option-value="options.YES"
        :label="options.YES.description"
        @onChanged="onSelected"
      />

      <RadioButtonObj
        v-model="valueInternal"
        :option-value="options.NO"
        :label="options.NO.description"
        @onChanged="onSelected"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import { IStep, TaxiResponse } from "../../complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import RadioButtonObj from "@/common/ui/fields/RadioButtonObj.vue";

export default defineComponent({
  name: "Taxi",
  components: { RadioButtonObj, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"TAXI">>,
      required: true
    },
    value: {
      type: Object as PropType<TaxiResponse>,
      required: true
    }
  },
  setup(
    props: {
      step: IStep<"TAXI">;
      value: TaxiResponse;
    },
    context: SetupContext
  ) {
    const valueInternal = ref<TaxiResponse>(simpleObjectClone(props.value));

    const options = {
      YES: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      NO: {
        id: "NO",
        description: "No",
        value: "NO"
      }
    };

    function onSelected() {
      console.log("Taxi.onSelected: ", valueInternal.value);
      context.emit("input", simpleObjectClone(valueInternal.value));
    }

    return { valueInternal, onSelected, options };
  }
});
</script>
