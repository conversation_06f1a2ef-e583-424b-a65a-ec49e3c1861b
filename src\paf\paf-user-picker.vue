<template>
  <div class="paf-user-picker">
    <div
      v-for="address in pafAddresses"
      :key="address.id"
      class="paf-user-picker--address"
      v-on:click="addressSelected(address)"
    >
      <span v-text="address.address"></span>,
      <span v-text="address.postcode"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IPafAddress } from "@/paf/paf-models";
export default defineComponent({
  name: "paf-user-picker",
  components: {},
  props: {
    pafAddresses: {
      type: Array as PropType<IPafAddress[]>,
      default: () => {
        return [];
      }
    }
  },
  setup(props: { pafAddresses: string }, context: SetupContext) {
    function addressSelected(pafAddress: IPafAddress) {
      context.emit("addressSelected", pafAddress);
    }
    return {
      addressSelected
    };
  }
});
</script>

<style>
.paf-user-picker {
  width: 100%;
  border-left: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}

.paf-user-picker--addresses {
  border-left: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}

.paf-user-picker--address {
  line-height: 2;
  font-size: 15px;
  padding-left: 5px;
  border-top: 1px solid #e1e1e1;
}

.paf-user-picker--address:hover {
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
