<template>
  <div
    :title="'Socket Status: ' + socketStatus"
    class="standard-icon"
    v-on:click.prevent.stop="showSocketMessages"
    :class="
      getIsConnected
        ? 'standard-icon--socket-connected'
        : 'standard-icon--socket-not-connected'
    "
  ></div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { SOCKET_STATUS } from "@/socket/socket-controller";

@Component({
  name: "socket-ui-status"
})
export default class SocketUiStatus extends Vue {
  @Prop({
    default: () => {
      const socketStatus: SOCKET_STATUS = "Not Connected";
      return socketStatus;
    }
  })
  public readonly socketStatus!: SOCKET_STATUS;

  public get getIsConnected(): boolean {
    return this.socketStatus === "Connected";
  }

  public showSocketMessages(): void {
    this.$emit("showSocketMessages");
  }
}
</script>
