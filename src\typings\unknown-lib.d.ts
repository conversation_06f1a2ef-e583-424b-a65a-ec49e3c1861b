import { I<PERSON><PERSON><PERSON>erA<PERSON>, IAdapterMenuMapper } from "@/app-models";
import { CLEO_CLIENT_SERVICE, MyGlobalSession } from "@/common/common-models";
import { ISimpleTrigger } from "@/calls/summary/call-summarry-models";
import {
  ICleoServerResponse,
  PEM_MESSAGE_TYPE
} from "@/common/cleo-common-models";
import {
  ILegacyCleoCallSummary,
  ILegacyDojoResponse,
  ILegacyKeywordFullService,
  ILegacyKeywordSimple
} from "@/common/cleo-legacy-models";
import { SERVICE_NAME, SERVICE_TYPE } from "@/common/services/services-models";
import { ITokenResponse } from "@/login/login-models";
import { IPathwaysDatasetLegacy } from "@/paccs/paccs-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import { IConsultCallLegacy } from "@/consults/consult-models-legacy";
import { IWhat3WordsMapResponse } from "@/what3words/what3words-cleo-models";
import { IVehicleLegacy } from "@/vehicles/vehicles-models";
import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";
import { CLeoPermissionServerResponse } from "@/permissions/permission-models";
import { IFailedContactComplete } from "@/calls/details/complete/complete-models";
import { FollowUpInputState } from "@/calls/details/complete/components/followup/models/follow-up-models";

export type Opaque<K, T> = T & { __TYPE__: K };

declare global {
  var ApplicationControllerClient: {
    getUserConfigRole: () => string;
    getSsoJwt: () => ITokenResponse | null;
    requestGenericJwt: () => Promise<ITokenResponse | null>;
    createActionEventLog: (
      category: string,
      action: string,
      data?: Record<string, string | string[]>[]
    ) => void;
    moveCaseToOversightState?: FollowUpInputState | undefined;
  };
}

declare global {
  var CallControllerClient: {
    m_call_id: string;
    isNewCall: boolean;
    serviceDoc: ICleoServiceDoc;
    OpenDocumentNewWindow: (
      callNo: string,
      objCallController_IGNORE: null,
      obj_json_IGNORE: null,
      callStatusValue: string,
      serviceName: string,
      row: ILegacyCleoCallSummary
    ) => void;
    printCall: () => void;
    submitDocumentPromise: (
      callIdentifier: string | number,
      closeTheCall: boolean,
      fieldMapOverride?: Record<string, any>
    ) => Promise<ICleoServerResponse>;
    saveNewCall: () => Promise<void>;
    submitSimpleFields: (
      payload: Partial<Record<keyof ICallDetail, unknown>>
    ) => Promise<ICleoServerResponse>;
    downgradeDialog: (callBack: () => void, callNumbers: string[]) => void;
    priorityDialog: (
      callBack: () => void,
      callNumbers: string[],
      cleoCallSummary: ILegacyCleoCallSummary
    ) => void;
    assignCallDialog: (
      isCallOpen: boolean,
      callBack: () => void,
      callNumbers: string[]
    ) => void;
    assignBaseDialog: (
      callBack: () => void,
      unusedVar: boolean,
      callNumbers: string[]
    ) => void;
    selectSecondaryAssignee: (
      callBack: () => void,
      callNumbers: string[]
    ) => void;
    dispatchCarSelect: (callBack: () => void, callNumbers: string[]) => void;
    dispatchCarRetrieveSelect: (
      callBack: () => void,
      callNumbers: string[]
    ) => void;
    getKeywordsPromise: (
      key: string,
      serviceName?: string
    ) => Promise<Record<string, ILegacyKeywordSimple>>;
    getFullKeywordsPromise: (
      key: string,
      serviceName?: string
    ) => Promise<Record<string, ILegacyKeywordFullService>>;
    loadNewCallDocument: (
      serviceName: SERVICE_NAME,
      callServiceAlt?: string,
      cleoClientService?: CLEO_CLIENT_SERVICE
    ) => void;
    getUserConfigRole: () => string;
    showSupport: () => void;
    JSONFields: ICallDetailLegacy;
    createNHSPathwaysDataSet: () => IPathwaysDatasetLegacy;
    _onPathwaysCompleted: (pathwaysResponse: string) => void; // IPathwaysReturnData
    hasPermission: (permissionName: string) => boolean;
    StartedConsult: boolean;
    UserNameAbbrev: string;
    paccs: {
      isReady: boolean; // Has PACCS Loaded properly
      hasStarted: boolean; //  Has user performed any PACCS tasks
    };
    userPermissions: CLeoPermissionServerResponse;
    getFieldValue: (fieldName: keyof ICallDetailLegacy) => unknown | "";
    setFieldValue: (fieldName: keyof ICallDetailLegacy, value: unknown) => void;
    getValueFromCallJson: (
      fieldName: keyof ICallDetailLegacy
    ) => unknown | null;
    createPaccsConsultDataForPathways: () => string;
    getCallDob: () => Date;
    createConsultFromCurrentHubCallForPathways: () => IConsultCallLegacy;
    getConsultPathwaysOutput: (consult: IConsultCallLegacy) => string;
    initPathwaysPermissions: () => void;
    userLookupController: typeof userLookupController;
    getDispatchCarDataStore: (
      callBack: (resp: ILegacyDojoResponse<IVehicleLegacy[]>) => void
    ) => Promise<void>;
    showConsultSection: (showSection: "CONSULTS" | "PACCS") => void;
    areWeInViewGrid: () => boolean;
    jqueryConfirmationDialog: (
      title: string,
      message: string,
      okButtonText?: string,
      cancelButtonText?: string
    ) => Promise<boolean>;
    jqueryAlertDialog: (title: string, message: string) => void;
    addFieldToModel: (
      fieldName: keyof ICallDetailLegacy,
      value: unknown
    ) => void;
    ACTION_COMPLETE: boolean;
    PathCleoBeanInterface: string;
  };
}

declare global {
  var userLookupController: {
    what3WordsMapResponse: (
      what3WordsMapResponse: IWhat3WordsMapResponse
    ) => void;
  };
}

declare global {
  var CGC: {
    clickItem: ILegacyCleoCallSummary | null;
  };
  var NHSPathwaysSEHUrl: string; //  E.g. https://staging.pathways.sehnp.nhs.uk
  var NHSPathwaysSEHReportUrl: string; //  E.g. https://staging.pathways.sehnp.nhs.uk/WebReports/PathwaysReport.aspx?PathwaysCaseId=
}

declare global {
  var ContextMenuController: {
    makeAppointment: () => void;
    setArrivedTime: () => void;
    acknowledgeReceiptBase: () => void;
    setComfortCourtesy: () => void;
    editClassification: () => void;
    onUnlockCallMenu: () => void;
    resendEmailDialog: (
      callBack: () => void,
      callNumbers: string[],
      sendType: PEM_MESSAGE_TYPE
    ) => void;
    resolveMessagingDialog: (
      callBack: () => void,
      callNumbers: string[],
      sendType: "Resend Resolved"
    ) => void;
    printCall: () => void;
  };
}

declare global {
  var localCache: {
    getUrlDataWithCache: (
      url: string,
      cache: boolean,
      contents?: Record<string, any>,
      responseType?: "json"
    ) => Promise<unknown>;
  };
}

export interface ICleoServiceDoc {
  serviceId: number;
  serviceName: SERVICE_NAME;
  serviceType: SERVICE_TYPE;
  eps: {
    enabled: boolean;
    launchLink: string;
    pbcLinks: {
      enabled: boolean;
    };
  };
  //  ...etc.
}

export interface ICallSummaryDisplay {
  new (CallControllerClient);
  selectDraft111Call(): void;
}

declare global {
  function callSummaryDisplay(CallControllerClient): ICallSummaryDisplay;
}

declare global {
  interface Window {
    cleoGlobalMenuName: string;
    ADAPTER_MENU: IAdapterMenuMapper;
    ADAPTER_CLEO_ACTION: IAdapterAction;
    MyGlobalSession: MyGlobalSession;
    adapterMenu: {
      state: {
        showAdapter: boolean;
        triggerResize: ISimpleTrigger<any>;
      };
    };
    callSummaryDisplay: (CallControllerClient) => ICallSummaryDisplay;
    dxValidation: string | undefined;
    failedContactConfig: IFailedContactComplete | undefined;
  }
}

declare global {
  var $: any;
  var dijit: any;
}

export type What3WordsThreeWords = string;
export type What3WordsThreeWordsPartial = string; //  At least 2 words and 1st letter of 3rd, "some.word.a"

export type What3WordsCoordinates = {
  lng: number;
  lat: number;
};

export type What3WordsCountry = "GB";
export type What3WordsLanguage = "en";

export type What3WordsResponse = {
  country: What3WordsCountry;
  language: What3WordsLanguage;
  coordinates: What3WordsCoordinates;
  map: string;
  nearestPlace: string;
  square: {
    southwest: What3WordsCoordinates;
    northeast: What3WordsCoordinates;
  };
  words: What3WordsThreeWords;
};

export type What3WordsSuggestion = {
  country: What3WordsCountry;
  nearestPlace: string;
  words: What3WordsThreeWords;
  rank: number;
  language: What3WordsLanguage;
};

export type What3WordsSuggestionResponse = {
  suggestions: What3WordsSuggestion[];
};

declare global {
  const what3words: {
    api: {
      convertTo3wa: (
        coords: What3WordsCoordinates,
        language: What3WordsLanguage
      ) => Promise<What3WordsResponse>;
      convertToCoordinates: (
        words: What3WordsThreeWords
      ) => Promise<What3WordsResponse>;
      autosuggest: (
        wordsPartial: What3WordsThreeWordsPartial,
        options: {
          clipToCountry?: [typeof What3WordsCountry];
          clipToCircle?: { center: What3WordsCoordinates };
          focus?: What3WordsCoordinates;
        }
      ) => Promise<any>;
      gridSection: (options: {
        southwest: What3WordsCoordinates;
        northeast: What3WordsCoordinates;
      }) => Promise<any>;
      gridSectionGeoJson: (options: {
        southwest: What3WordsCoordinates;
        northeast: What3WordsCoordinates;
      }) => Promise<any>;
    };
  };
}

declare global {
  const google: google;
}
