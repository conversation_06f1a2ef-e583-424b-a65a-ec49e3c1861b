<template>
  <div
    class="paccs-triage-record-report--triage-record"
    :class="getTriageRecordAnswerCss(triageRecord)"
  >
    <div class="paccs-triage-record-report--triage-record-title">
      <a
        v-if="isTriageRecordActive && showLinks"
        href="#"
        class="paccs-triage-record-report--triage-record-open-link"
        v-on:click.prevent="openTriageRecord"
      >
        <span v-text="getTriageRecordTitle(triageRecord)"></span>
      </a>

      <span
        v-text="getTriageRecordTitle(triageRecord)"
        v-if="!(isTriageRecordActive && showLinks)"
      ></span>

      <!--      <span v-text="getTriageRecordTitle(triageRecord)"></span>-->
      <a
        href="#"
        class="paccs-triage-record-report--triage-record-more-info-link"
        v-on:click.prevent="state.showMoreInfo = !state.showMoreInfo"
        v-if="hasMoreInfo"
        ><span v-text="state.showMoreInfo ? 'Hide' : 'More'"></span
      ></a>
    </div>
    <div
      class="paccs-triage-record-report--triage-record-more-info"
      v-if="state.showMoreInfo"
      v-text="triageRecord.userComment"
    ></div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext
} from "@vue/composition-api";
import {
  ITriageRecord,
  ITriageRecordCondition,
  ITriageRecordTemplate
} from "@/paccs/paccs-models";
import { PaccsService } from "@/paccs/paccs-service";

export default defineComponent({
  name: "paccs-triage-record-report",
  props: {
    triageRecord: {
      type: Object as PropType<ITriageRecord>
    },
    showLinks: {
      type: Boolean,
      default: () => {
        return true;
      }
    }
  },
  components: {},
  setup(
    props: {
      triageRecord: ITriageRecord;
      showLinks: boolean;
    },
    context: SetupContext
  ) {
    const paccsService: PaccsService = new PaccsService();

    const state = reactive({
      showMoreInfo: false
    });

    function getTriageRecordTitle(triageRecord: ITriageRecord): string {
      return paccsService.getTriageRecordTitle(triageRecord);
    }

    function getTriageRecordAnswerCss(triageRecord: ITriageRecord): string {
      if (paccsService.isTriageRecordTemplate(triageRecord)) {
        return getTriageRecordTemplateCss(
          triageRecord as ITriageRecordTemplate
        );
      }

      if (paccsService.isTriageRecordCondition(triageRecord)) {
        return getTriageRecordConditionAnswerCss(
          triageRecord as ITriageRecordCondition
        );
      }
      return "";
    }

    function getTriageRecordTemplateCss(
      triageRecord: ITriageRecordTemplate
    ): string {
      const defaultCss = "paccs-triage-record-report--triage-record-template";

      const map: Record<string, string> = {
        "1": "paccs-triage-record-report--triage-record-template-1",
        "2": "paccs-triage-record-report--triage-record-template-2"
      };
      const key = triageRecord.actionId.toString();
      return defaultCss + (map[key] ? " " + map[key] : "");
    }

    function getTriageRecordConditionAnswerCss(
      triageRecord: ITriageRecordCondition
    ): string {
      const map: Record<string, string> = {
        "0": "paccs-triage-record-report--triage-record-answer-0",
        "1": "paccs-triage-record-report--triage-record-answer-1",
        "2": "paccs-triage-record-report--triage-record-answer-2"
      };
      const key = triageRecord.answerNumber.toString();
      if (map[key]) {
        return map[key];
      }
      return "";
    }

    function getKey(triageRecord: ITriageRecord): string {
      let key = triageRecord.timeIn + "-" + triageRecord.pathwayId;
      key =
        key +
        "-" +
        (paccsService.isTriageRecordCondition(triageRecord)
          ? "C-" + triageRecord.quId
          : "T");
      console.log("paccs-triage-record-report getKey(): " + key);
      return key;
    }

    const hasMoreInfo = computed<boolean>(() => {
      if (paccsService.isTriageRecordCondition(props.triageRecord)) {
        return props.triageRecord.userComment!.length > 0;
      }
      return false;
    });

    function openTriageRecord() {
      context.emit("openTriageRecord", props.triageRecord);
    }

    const isTriageRecordActive = computed<boolean>(() => {
      return paccsService.isTriageRecordActive(props.triageRecord);
    });

    return {
      getTriageRecordTitle,
      getTriageRecordAnswerCss,
      getKey,
      openTriageRecord,
      isTriageRecordActive,
      hasMoreInfo,
      state
    };
  }
});
</script>

<style>
.paccs-triage-record-report--triage-record {
  border: 1px solid lightgrey;
  margin-top: 0.25em;
  padding: 0.25em;
}

.paccs-triage-record-report--triage-record-template {
  font-weight: 600;
}

.paccs-triage-record-report--triage-record-template-1 {
  background-color: #f1f1f1;
  border-left: 0.5em solid #9a9a9a;
}

.paccs-triage-record-report--triage-record-template-2 {
  background-color: #d7ffd4;
  border-left: 0.5em solid #52c552;
}

.paccs-triage-record-report--triage-record-answer-0 {
  background-color: #f1f1f1;
  border-left: 0.5em solid #9a9a9a;
}

.paccs-triage-record-report--triage-record-answer-1 {
  background-color: #fafdea;
  border-left: 0.5em solid yellow;
}

.paccs-triage-record-report--triage-record-answer-2 {
  background-color: #f5e9e9;
  border-left: 0.5em solid red;
}

.paccs-triage-record-report--triage-record-title {
}

.paccs-triage-record-report--triage-record-open-link {
  text-decoration: none;
}

.paccs-triage-record-report--triage-record-more-info-link {
  margin-left: 0.5em;
  text-decoration: none;
  float: right;
}

.paccs-triage-record-report--triage-record-more-info {
  padding-top: 0.25em;
  border-top: 1px solid #f1efef;
}
</style>
