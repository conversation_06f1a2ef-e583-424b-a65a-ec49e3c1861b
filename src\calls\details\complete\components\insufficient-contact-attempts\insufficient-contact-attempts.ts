import { ISimpleButtonInputValue } from "../../SimpleButtonSelecter.vue";

export type InsufficientContactAttemptType =
  | "SAVE_AND_RETURN"
  | "OVERRIDE_COMPLETE";

export const insufficientContactAttemptSaveReturnOption: ISimpleButtonInputValue<
  InsufficientContactAttemptType,
  InsufficientContactAttemptType
> = {
  id: "SAVE_AND_RETURN",
  description: "Save and Return to Queue",
  value: "SAVE_AND_RETURN"
};

export const insufficientContactAttemptCompleteOption: ISimpleButtonInputValue<
  InsufficientContactAttemptType,
  InsufficientContactAttemptType
> = {
  id: "OVERRIDE_COMPLETE",
  description: "Override and Complete",
  value: "OVERRIDE_COMPLETE"
};

export const insufficientContactAttemptMap: Record<
  InsufficientContactAttemptType,
  ISimpleButtonInputValue<
    InsufficientContactAttemptType,
    InsufficientContactAttemptType
  >
> = {
  SAVE_AND_RETURN: insufficientContactAttemptSaveReturnOption,
  OVERRIDE_COMPLETE: insufficientContactAttemptCompleteOption
};
