console.log("Current PUBLIC_PATH:", process.env.VUE_APP_PUBLIC_PATH);
console.log("Current NODE_ENV:", process.env.NODE_ENV);

module.exports = {
  chainWebpack: config => {
    // remove the prefetch plugin
    config.plugins.delete("prefetch");
    if (config.plugins.has("named-chunks")) {
      config.plugins.delete("named-chunks");
    }
  },

  filenameHashing: true,
  publicPath: process.env.NODE_ENV === "production" ? "/appbrisdoc/" : ""
  // publicPath: process.env.VUE_APP_PUBLIC_PATH || ""

  // configureWebpack: {
  //   output: {
  //     filename: "[name][hash].js"
  //   }
  // }
  // configureWebpack: {
  //   output: {
  //     filename: '[name].[hash].js',
  //     chunkFilename: '[name].[hash].js',
  //     publicPath: process.env.NODE_ENV === "production" ? "/app/" : ""
  //   }
  // }
};
// "production" ? "/app/" : ""
// publicPath: process.env.NODE_ENV === "production" ? `/app/${new Date().toISOString().slice(0, 16).replace(/:/g, "-")}` : ""

// publicPath: process.env.NODE_ENV === "production" ? `/app/${new Date().toISOString()}/` : ""
// publicPath: process.env.NODE_ENV === "production" ? "/app/" : ""

// publicPath:
//   process.env.NODE_ENV === "production"
//     ? `/app/${new Date()
//       .toISOString()
//       .replaceAll("-", "")
//       .replaceAll(":", "")
//       .replaceAll(".", "")}/`
//     : ""
