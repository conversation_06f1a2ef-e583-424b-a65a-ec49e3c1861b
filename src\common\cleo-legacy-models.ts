import { CLEO_CLIENT_SERVICE } from "@/common/common-models";
import { BrisDocSupportType } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";

export interface ILegacyCleoServerResponse<DataObject> {
  DATA: DataObject;
  RESULT: "SUCCESS" | "FAILURE";
  MESSAGE: string;
}

export interface ILegacyDojoResponse<DataObject> {
  identifier: "unid";
  label: "unid";
  items: DataObject;
}

export interface ILegacyKeywordSimple {
  unid: string;
  keyType: string; //  E.g. Service
  description: string; //  E.g. Norfolk and Wisbech 111
  KeywordService: string; //  E.g. East Kent 111
  codeID1: string; //  E.g. 111
  codeID2: string;
  codeID3: string;
}

// E.g. /xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=CLEO_CLIENT_SERVICE~BrisDoc
export type LegacyKeywordServerResponse = Record<string, ILegacyKeywordSimple>;

export interface ILegacyKeywordFullService extends ILegacyKeywordSimple {
  ServiceId: string;
}

export interface ILegacyCleoCallSummary {
  CallCreatedBy: string;
  CallNo: string;
  CallID: string; //  Put CallNo in it's place, it's only used as unique ref to a call.
  CallService: string;
  IUC_Contract: string;
  CallClassification: string;
  CC: string;
  CallSubClassification: string;
  CSC: string;
  CallUrgentYN: "Yes" | "No";
  CallSurname: string;
  CallForename: string;
  CallDoctorNameCN: string;
  CallDoctorName: string;
  CallAppointmentTime: string; //  ? E.g. 13:50
  DutyBase: string;
  BreachActualTime: string;
  StartConsultationPerformed: "" | "1";
  CliniHighPriority: "1" | "";
  Linked_Call_ID: "";
  CallArrivedTime: string;
  CallStatusValue: "9" | "1" | "2" | string;
  CallAddress1: string;
  CallAddress2: string;
  CallAddress3: string;
  CallAddress4: string;
  CallTown: string;
  CallPostCode: string;
  CHFinalDispositionCode: string;
  FinalDispositionCode: string;
  cleoClientService: CLEO_CLIENT_SERVICE;

  OVERSIGHT_BASE_TRIAGE_TYPE: string | "Isolation" | "Does patient need PPE";
  Cpl_supportTypeRequired: BrisDocSupportType | "";
  CallTelNo: string;
  CallTelNo_R: string;
}

export interface ILegacyServerResponse<Data> {
  Count: number;
  Returned: number;
  identifier: string;
  label: string;
  Limit: number;
  items: Data[];
}
