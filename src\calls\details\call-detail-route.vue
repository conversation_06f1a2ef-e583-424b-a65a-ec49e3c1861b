<template>
  <div>
    <!--    Call Detail Route ID: <span v-text="state.id"></span> Route-->
    <!--    state.callDetail.id <input v-model="state.callDetail.id" />-->

    <!--    startedConsult MF: {{ startedConsult }}-->
    <!--    UserNameAbbrev: {{UserNameAbbrev}}-->

    <div v-if="configStoreState.isLocalDevServer" class="dat-local-dev-server">
      This section is only visible on local dev
      <button v-on:click="callDetailRetrieved">1. CALL_DETAIL_RETRIEVED</button>
      <button v-on:click="startConsult">2. Start Consult</button>
      <button v-on:click="saveAndReturn">3. Save and Return</button>
      <button v-on:click="endAssessment">4. End Assessment</button>

      <div id="adapter--sesui-call-form">
        <portal-target name="adapter--return-call-number"></portal-target>
        Return Tel number
      </div>

      CallTelNo_R:
      <span id="CallTelNo_R"></span
      ><button v-on:click="testChange('CallTelNo_R')">change tel</button>

      Forename:
      <span id="CallForename"></span
      ><button v-on:click="testChange('CallForename')">change</button>

      CallSurname:
      <input id="CallSurname" />

      <div class="ic24-flex-column">
        <div class="ic24-flex-row ic24-flex-gap">
          <span>Forename:</span>
          <span v-text="state.callDetail.Forename"></span>
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <span>Surname:</span>
          <span v-text="state.callDetail.Surname"></span>
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <span>FailedContact:</span>
          <span v-text="state.callDetail.failedContacts.length"></span>
        </div>
      </div>

      <div class="ic24-flex-column">
        <div class="ic24-flex-row ic24-flex-gap">
          <span>CH Dx:</span>
          <span v-text="state.callDetail.dx.ch.dxCode"></span>
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <span>Clini Dx:</span>
          <span v-text="state.callDetail.dx.clini.dxCode"></span>
        </div>
      </div>
    </div>

    <CallDetail
      v-if="state.isCallReady"
      :is-new-call="state.isNewCall"
      :started-consult="state.startedConsult"
      :call-detail="state.callDetail"
      :user-permissions="state.userPermissions"
      :case-config="state.caseConfig"
    />
  </div>
</template>

<script lang="ts">
import {
  reactive,
  computed,
  ComputedRef,
  defineComponent,
  onBeforeMount,
  watch
} from "@vue/composition-api";
import CallDetail from "@/calls/details/call-detail.vue";
import * as CallDetailService from "@/calls/details/call-detail-service";
import { ConsultsService } from "@/consults/consults-service";
import { appStore } from "@/store/store";
import { KEYWORD_STORE_STORE_CONST } from "@/keywords/keywords-store";
import { IKeywordsRefDataKey } from "@/keywords/keywords-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState
} from "@/common/config/config-store";
import { IAdapterAction, ILegacyAction, ILegacyOpenCall } from "@/app-models";
import { loggerInstance } from "@/common/Logger";
import { ICallDetail } from "@/calls/details/call-details-models";
import { PermissionService } from "@/permissions/permission-service";
import { userPermissionServerResponseMockData } from "@/permissions/permission-mock-data";
import { useRoute } from "@/router/migrateRouterVue3";
import * as CommonUtils from "@/common/common-utils";

const consultsService: ConsultsService = new ConsultsService();

export default defineComponent({
  name: "call-detail-route",
  components: {
    CallDetail
  },
  setup() {
    const route = useRoute();
    const id: number = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);

    const state = reactive({
      isCallReady: false,
      id: id,
      callDetail: CallDetailService.factoryCallDetail(),
      userPermissions: {},
      isNewCall: true,
      startedConsult: false,
      caseConfig: CallDetailService.factoryCaseConfig()
    });

    const store = appStore;

    const callDetail: ICallDetail = CallDetailService.factoryCallDetail();
    callDetail.Id = state.id;
    callDetail.CallNo = state.id;
    state.callDetail = callDetail;

    const configStoreState = computed<IConfigStoreState>(() => {
      return store.state[CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME];
    });

    watch(
      () => configStoreState.value.adapterCleoAction,
      (newValue: IAdapterAction, oldValue: IAdapterAction) => {
        console.log(
          "adapterCleoAction newValue.payload.actionType: " +
            newValue.payload.actionType,
          JSON.parse(
            JSON.stringify({
              newValue,
              oldValue
            })
          )
        );
        loggerInstance.log("call-detail>>>>>>>>>>>>>>>>>>watch!", {
          newValue,
          oldValue
        });
        if (newValue.payload.actionType === "START_CONSULT") {
          loggerInstance.log("call-detail-route.vue actionType: START_CONSULT");
          state.startedConsult = true;
        }

        if (newValue.payload.actionType === "CALL_DETAIL_RETRIEVED") {
          loggerInstance.log(
            "call-detail-route.vue actionType: CALL_DETAIL_RETRIEVED"
          );

          //  In a New Call...unless...
          if (window.CallControllerClient.JSONFields) {
            console.warn("<<<<<<<<<<< A existing call");
            //  In an existing call
            state.callDetail = CallDetailService.mapLegacyCall(
              window.CallControllerClient.JSONFields
            );
            state.isNewCall = false;
          } else {
            console.warn("<<<<<<<<<<< B new call");
            const callDetailNew = CommonUtils.simpleObjectClone(
              state.callDetail
            );
            callDetailNew.Service = {
              id: window.CallControllerClient.serviceDoc.serviceId,
              name: window.CallControllerClient.serviceDoc.serviceName,
              serviceType: window.CallControllerClient.serviceDoc.serviceType
            };

            state.callDetail = callDetailNew;
          }

          const userPermissions =
            window.CallControllerClient.userPermissions.Permissions[
              window.ApplicationControllerClient.getUserConfigRole()
            ];

          state.userPermissions = new PermissionService().simpleKeyPerms(
            userPermissions
          );

          //  Dig some config off legacy document..
          console.log("window.dxValidation: ", window.dxValidation);
          if (typeof window.dxValidation !== "undefined") {
            //  E.g. 'Dx333, Dx334, Dx337, Dx338'
            const dxCodesThatRequireValidation: string = window.dxValidation as string;
            if (dxCodesThatRequireValidation.length > 0) {
              state.caseConfig.dx.validation = dxCodesThatRequireValidation
                .split(",")
                .map((dxCode: string) => {
                  return dxCode.trim();
                });
            }
          }

          if (typeof window.failedContactConfig !== "undefined") {
            state.caseConfig.failedContactConfig = window.failedContactConfig;
          } else {
            //  Defaults...
            state.caseConfig.failedContactConfig.config.attemptsRequired = 2;
            state.caseConfig.failedContactConfig.config.minsInterval = 20;
          }

          console.log("state.caseConfig: ", state.caseConfig);

          state.isCallReady = true;
        }
      }
    );

    function testChange(domId: string) {
      const target: HTMLElement | null = document.getElementById(domId);
      if (target) {
        if (domId === "CallTelNo_R") {
          target.innerText = "01112223333";
        } else {
          target.innerText = new Date().toISOString();
        }
      }
    }

    onBeforeMount(() => {
      let linkedCallNumbers = [state.id];
      if (
        window.CallControllerClient &&
        window.CallControllerClient.JSONFields &&
        window.CallControllerClient.JSONFields["CallNo"]
      ) {
        linkedCallNumbers.push(
          Number(window.CallControllerClient.JSONFields["CallNo"].VALUE[0])
        );
      }

      /*
      //  [state.id, 2000245755]
      new ConsultsData().getConsultsApi(linkedCallNumbers).then(resp => {
        const callDetail = callDetailService.factoryCallDetail();
        callDetail.Id = state.id;
        callDetail.Callno = state.id;

        const refData: ComputedRef<IKeywordsRefDataKey> = computed(() => {
          return store.state[
            KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME
            ].keywordsRefDataKey;
        }) as ComputedRef<IKeywordsRefDataKey>;

        // const refData =
        //   store.state[
        //     KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME
        //   ].keywordsRefDataKey;

        let consults = resp.Records.map(consultServer => {
          return consultsService.mapConsultFromServer(consultServer, refData.value);
        });

        consults = commonService.sortArray("TimeStart", consults);
        callDetail.Consults = consults;

        state.callDetail = callDetail;
      });
      */

      // const callDetail = callDetailService.factoryCallDetail();
      // callDetail.Id = state.id;
      // callDetail.Callno = state.id;
      // state.callDetail = callDetail;

      const refData: ComputedRef<IKeywordsRefDataKey> = computed(() => {
        return store.state[
          KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME
        ].keywordsRefDataKey;
      }) as ComputedRef<IKeywordsRefDataKey>;
    });

    function startConsult() {
      state.startedConsult = true;
    }

    function callDetailRetrieved() {
      const payload: ILegacyAction = {
        actionType: "CALL_DETAIL_RETRIEVED",
        data: {
          callDetail: {
            userPermissions: userPermissionServerResponseMockData,
            JSONFields: {
              CallNo: { VALUE: [1234567] },
              CallForename: { VALUE: ["Joe"] },
              CallSurname: { VALUE: ["CallRouteBloggs"] },
              CallAddress1: { VALUE: ["St Clements"] },
              CallAddress2: { VALUE: ["King st"] },
              CallAddress3: { VALUE: ["Colyton"] },
              CallAddress4: { VALUE: [""] },
              CallTown: { VALUE: ["Colyton"] },
              CallPostCode: { VALUE: ["EX24 6LF"] },
              CallClassification: { VALUE: ["Advice"] },
              CallUrgentYn: { VALUE: ["No"] },
              CallAge: { VALUE: ["33"] },
              CallAgeClass: { VALUE: ["Yrs"] },
              CallSymptoms: {
                VALUE: ["Patient feels sick and needs to blah, blah"]
              },
              DispatchVehicle: { VALUE: [""] },
              PatientContactCode_Events: {
                VALUE: [
                  "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T01:49:00"
                ]
              }
            }
          }
        }
      };

      //  TODO failed contacts above...need to fix...but it's in config.ts when locally.  So confusing!!!

      console.log("callDetailRetrieved payload: ", payload);
      window.ADAPTER_CLEO_ACTION.payload = payload;
    }

    function saveAndReturn() {
      const payload: ILegacyAction = {
        actionType: "SAVE_AND_RETURN"
      };
      console.log("CallDetailRoute.saveAndReturn payload: ", payload);
      window.ADAPTER_CLEO_ACTION.payload = payload;
    }

    function endAssessment() {
      const payload: ILegacyAction = {
        actionType: "END_ASSESSMENT"
      };
      console.log("CallDetailRoute.saveAndReturn payload: ", payload);
      window.ADAPTER_CLEO_ACTION.payload = payload;
    }

    // VALUE: [
    //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T01:49:00",
    //   "CN=Hannah Spicer/O=sehnp~No Answer~2024-06-28T02:29:46",
    //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T02:48:21",
    //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:03:52",
    //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:07:21"
    // ]

    return {
      state,
      configStoreState,
      startConsult,
      callDetailRetrieved,
      testChange,
      saveAndReturn,
      endAssessment
    };
  }
});
</script>
