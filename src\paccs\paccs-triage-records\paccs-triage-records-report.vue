<template>
  <div class="paccs-triage-records-report--wrapper">
    <div class="paccs-triage-records-report--header">
      <a
        href="#"
        v-on:click.prevent="showAllHistory = !showAllHistory"
        class="paccs-triage-records-report--link"
      >
        PACCS Call Report: <span v-text="showAllHistory === true ? 'All' : 'Active'"></span>
      </a>

      <!--      <a-->
      <!--        class="paccs-triage-records-report&#45;&#45;back-button"-->
      <!--        href="#"-->
      <!--        v-on:click.prevent="backToPaccs"-->
      <!--        >Back</a-->
    </div>
    <div class="paccs-triage-records-report--triage-records">
      <div v-for="triageRecord in getTriageRecords" :key="getKey(triageRecord)">
        <div class="paccs-triage-records-report--triage-record-sep"></div>
        <PaccsTriageRecord
          :show-links="showLinks"
          :triage-record="triageRecord"
          v-on:openTriageRecord="openTriageRecord"
        ></PaccsTriageRecord>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, ref } from "@vue/composition-api";
import { ITriageRecord } from "@/paccs/paccs-models";
import { PaccsService } from "@/paccs/paccs-service";
import PaccsTriageRecord from "@/paccs/paccs-triage-records/paccs-triage-record.vue";
import { PACCS_STORE_CONST } from "@/paccs/paccs-store";
import { appStore } from "@/store/store";

export default defineComponent({
  name: "paccs-triage-records-report",
  props: {
    triageRecords: {
      type: Array as PropType<ITriageRecord[]>,
      default: () => {
        return [];
      }
    },
    showLinks: {
      type: Boolean,
      default: () => {
        return true;
      }
    }
  },
  components: { PaccsTriageRecord },
  setup(props: { triageRecords: ITriageRecord[]; showLinks: boolean }) {
    const paccsService: PaccsService = new PaccsService();
    const store = appStore;
    const showAllHistory = ref(false);

    function getKey(triageRecord: ITriageRecord): string {
      let key = triageRecord.timeIn + "-" + triageRecord.pathwayId;
      key =
        key +
        "-" +
        (paccsService.isTriageRecordCondition(triageRecord)
          ? "C-" + triageRecord.quId
          : "T");
      return key;
    }

    const getTriageRecords = computed<ITriageRecord[]>(() => {
      return showAllHistory.value
        ? props.triageRecords.slice().reverse()
        : paccsService.getLatestActiveStateForEachTriageRecord(
            props.triageRecords
          );
    });

    function backToPaccs() {
      store.commit(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
        "PACCS"
      );
    }

    function openTriageRecord(triageRecord: ITriageRecord) {
      store.commit(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__MUTATION_SET_REPORT_TRIAGE_CLICKED,
        triageRecord
      );
    }

    return {
      getTriageRecords,
      showAllHistory,
      getKey,
      backToPaccs,
      openTriageRecord
    };
  }
});
</script>

<style>
.paccs-triage-records-report--wrapper {
}

.paccs-triage-records-report--header {
  background-color: #2980b9;
  padding: 0.5em;
  color: white;
}

.paccs-triage-records-report--triage-records {
  max-height: 150px;
  overflow: auto;
}

.paccs-triage-records-report--link {
  color: white;
  text-decoration: none;
}

.paccs-triage-records-report--back-button {
  float: right;
}
</style>
