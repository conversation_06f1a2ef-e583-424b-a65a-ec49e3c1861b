import {
  broadcastChannelNameForSocketController,
  IBroadcastAction,
  ISesuiCommand,
  ISesuiCommandPayload,
  ISesuiCommandPayloadCallTerminate,
  ISesuiCommandPayloadLogin,
  ISesuiCommandPayloadMakeCall,
  ISesuiCommandPayloadSessionEnd,
  ISesuiConfig,
  ISesuiEvent,
  ISesuiEventCallSpecificMessage,
  ISesuiEventMessageNewCallState,
  ISesuiEventOperatorSpecificMessage,
  ISesuiLoginResult,
  ISesuiState
} from "@/sesui/use-sesui-models";

export const broadcastChannelNameSocketMessages = "sesui-socket-channel";

export const broadcastChannelNameForClient =
  "sesui-telephone-channel-for-call-client";

export function useSesuiClient(config: ISesuiConfig, socketName?: string) {
  const state: ISesuiState = {
    eventTimes: {
      onopen: "",
      onclose: "",
      onerror: ""
    },
    session_start_result: null,
    call_new_state: null,
    user: 0
  };

  let ping: number | null = null;
  const sockName = socketName ? socketName : "Master";
  const broadcastChannelSocket = new BroadcastChannel(
    broadcastChannelNameSocketMessages
  );
  // const socket: WebSocket = new WebSocket(config.url);
  let socket: null | WebSocket = null;

  /**
   * Messages sent to this controller.  E.g from "top" window, the call form.
   */
  const broadcastChannelForSocketController = new BroadcastChannel(
    broadcastChannelNameForSocketController
  );

  broadcastChannelForSocketController.onmessage = function(event) {
    console.log("broadcastChannelForController onmessage", event);

    const broadcastAction: IBroadcastAction = event.data as IBroadcastAction;
    if (broadcastAction.action === "MAKE_CALL") {
      alert("QAZWSSX Deprecated...this should not be called!!!");
      // makeCall(
      //   (broadcastAction as IBroadcastActionMakeCall).payload.telephoneNumber,
      //   "55721608"
      // );
      return;
    }

    if (broadcastAction.action === "END_CALL") {
      terminateCall();
    }
  };

  if (config.user.length === 0) {
    console.log("useSesuiClient() no user supplied, exiting.");
    return;
  }

  // makeSocket();

  // function restart() {
  //   socket = makeSocket();
  // }

  function initSocket(userId: string) {
    socket = new WebSocket(config.url);
    state.user = userId;

    /**
     *
     * @param event
     */
    socket.onopen = function(event) {
      console.warn(
        sockName + " sesuiClient [onopen] Connection established",
        event
      );
      state.eventTimes.onopen = new Date().toString();
      login();
    };

    /**
     * @param event
     */
    socket.onmessage = (event: MessageEvent<ISesuiEvent>) => {
      // console.warn(
      //   sockName + " sesuiClient [onmessage] Data received from server:",
      //   event
      // );

      const eventData: ISesuiEvent =
        typeof event.data === "string" ? JSON.parse(event.data) : event.data;

      const eventType = eventData.event.event_type;

      if ((eventData.event as ISesuiEventOperatorSpecificMessage).operator_id) {
        //  let's filter out for current operator...

        const sesuiEventOperatorSpecificPayload = eventData.event as ISesuiEventOperatorSpecificMessage;

        const operatorIdOnMessage: number | undefined =
          sesuiEventOperatorSpecificPayload.operator_id;

        const currentOperatorId = state.user;

        console.log(
          "Sesui socket.onmessage Operator match >>> " +
            operatorIdOnMessage +
            " ??? " +
            state.user +
            " === " +
            (operatorIdOnMessage.toString() === currentOperatorId)
        );

        const opertatorIdMatch =
          operatorIdOnMessage &&
          operatorIdOnMessage.toString().length > 0 &&
          operatorIdOnMessage.toString() === currentOperatorId;

        let callIdMatch = false;
        if (
          ((eventData as unknown) as ISesuiEventCallSpecificMessage).call_id
        ) {
          const callIdOnMessage = ((eventData as unknown) as ISesuiEventCallSpecificMessage)
            .call_id;

          console.log("CAllId match>>>" + callIdOnMessage.toString());

          if (
            state.call_new_state &&
            state.call_new_state.call_id === callIdOnMessage
          ) {
            console.log(
              "CAllId match>>>" +
                callIdOnMessage.toString() +
                " ??? " +
                state.call_new_state.call_id.toString() +
                " === " +
                (callIdOnMessage.toString() ===
                  state.call_new_state.call_id.toString())
            );
            callIdMatch = true;
          }
        }

        if (opertatorIdMatch || callIdMatch) {
          console.warn(
            sockName +
              " sesuiClient eventType ===  " +
              eventType +
              ", for operator: " +
              operatorIdOnMessage +
              " match, broadcast message to client"
          );
          broadcastChannelSocket.postMessage(eventData);

          if (eventType === "session_start_result") {
            //  TODO  what is the matching "id", since connected via master connection, see all connections for everyone.
            state.session_start_result = eventData.event as ISesuiLoginResult;
          }

          if (eventType === "call_new_state") {
            const sesuiNewCallEvent = eventData.event as ISesuiEventMessageNewCallState;
            console.log(
              "call_new_state call_id: " + sesuiNewCallEvent.call_id,
              eventData
            );
            state.call_new_state = sesuiNewCallEvent;
          }
        }
      }
    };

    /**
     *
     * @param event
     */
    socket.onclose = function(event) {
      state.eventTimes.onclose = new Date().toString();
      console.warn(sockName + " sesuiClient [onclose] Connection died", event);
    };

    /**
     *
     * @param error
     */
    socket.onerror = function(error) {
      state.eventTimes.onerror = new Date().toString();
      console.warn(sockName + " sesuiClient [error]", error);
    };
  }

  function doPing() {
    if (socket && socket.readyState === 3) {
      //	so polling stops.
      return;
    }
    ping = window.setTimeout(function() {
      const command = {
        command_type: "session_ping"
      } as ISesuiCommandPayload;

      sendCommand(command);
      doPing();
    }, 5000);
  }

  function stopPing() {
    console.log(sockName + " sesuiClient.stopPing()");
    if (ping) {
      clearTimeout(ping);
    }
  }

  function login() {
    const commandPayload: ISesuiCommandPayloadLogin = {
      command_type: "session_start",
      user: config.user,
      pass: config.password,
      config_id: config.configId
    };

    console.log(sockName + " sesuiClient.login() command:", commandPayload);
    sendCommand(commandPayload);
    doPing();
  }

  /**
   * Not used right now, should take a param, but used
   * if you would like to switch and "make" calls from
   * the number provided here.  E.g. use a mobile phone.
   * @return
   */
  // function addNumber() {
  //   console.log(sockName + " sesuiClient.addNumber()");
  //   sendCommand({
  //     command: {
  //       command_type: "op_update_target",
  //       operator_id: state.user
  //         ? state.user.session_start_result?.authority_id
  //         : "",
  //       target: config.operatorPhoneNumber
  //     }
  //   });
  // }

  function sessionEnd(reason: string) {
    const commandPayload: ISesuiCommandPayloadSessionEnd = {
      command_type: "session_end",
      reason: reason
    };
    console.log(sockName + " sesuiClient.sessionEnd()", commandPayload);
    stopPing();
    sendCommand(commandPayload);
    if (socket) {
      socket.close();
    }
  }

  // function getServices() {
  //   console.log(sockName + " sesuiClient.getServices()");
  //   sendCommand({
  //     command: {
  //       command_type: "provisioning_request_telephony_services",
  //       request_id: "",
  //       specific_service_id: 0,
  //       only_active: true,
  //       external_id_type: ""
  //     }
  //   });
  // }

  function makeCall(phoneNumberToCall: string, authorityId: string) {
    console.log(
      "useTelephone.makeCall() a phoneNumberToCall: " +
        phoneNumberToCall +
        ", authorityId: " +
        authorityId
    );
    const command: ISesuiCommandPayloadMakeCall = {
      command_type: "call_start_outdialler",
      operator_id: authorityId, //  E.g. 55728327
      phone_number_to_call: phoneNumberToCall.replace(/ /g, "")
    };

    console.log(
      sockName +
        " sesuiClient.makeCall() tel: " +
        phoneNumberToCall +
        "command: ",
      command
    );

    console.log("useTelephone.makeCall() c");
    sendCommand(command);
    console.log("useTelephone.makeCall() z");
  }

  function terminateCall() {
    console.log(sockName + " sesuiClient.terminateCall()");

    if (state.call_new_state && state.call_new_state.call_id) {
      const command: ISesuiCommandPayloadCallTerminate = {
        command_type: "call_terminate",
        call_id: state.call_new_state.call_id,
        reason: ""
      };
      sendCommand(command);
    }
  }

  function sendCommand(payload: ISesuiCommandPayload): void {
    console.log(
      sockName +
        " sesuiClient.sendCommand() socket status: " +
        getSocketStatus() +
        ", payload: ",
      payload
    );
    const command: ISesuiCommand = {
      command: payload
    };
    if (socket) {
      socket.send(JSON.stringify(command));
    }
  }

  function getSocketStatus() {
    if (!socket) {
      return "NA";
    }
    const status = socket.readyState;
    const socketMap: Record<number, string> = {
      0: "Connecting",
      1: "Open",
      2: "Closing",
      3: "Closed"
    };
    return socketMap[status] ? socketMap[status] : "Unknown";
  }

  function isLoggedIn(): boolean {
    if (getSocketStatus().toUpperCase() === "OPEN") {
      //		  return state.user.session_start_result && state.user.session_start_result.result_text.toUpperCase() === "LOGON SUCCESS";
      if (state.session_start_result) {
        return (
          state.session_start_result.result_text.toUpperCase() ===
          "LOGON SUCCESS"
        );
      }
      return false;
    }
    return false;
  }

  function stopSocket() {
    stopPing();
    if (!socket) {
      return;
    }
    socket.close();
    setTimeout(function() {
      if (socket) {
        if ([socket.OPEN, socket.CLOSING].indexOf(socket.readyState) > -1) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //  @ts-ignore
          socket.terminate();
        }
      }
    }, 10000);
  }

  return {
    state,
    initSocket,
    login,
    isLoggedIn,
    sessionEnd,
    makeCall,
    terminateCall,
    stopPing,
    stopSocket,
    getSocketStatus
  };
}
