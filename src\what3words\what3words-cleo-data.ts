import { ILegacyCleoServerResponse } from "@/common/cleo-legacy-models";

export class What3wordsCleoData {
  public sendSMS(phoneNumber: string): Promise<ILegacyCleoServerResponse<any>> {
    const url =
      window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
      "/" +
      window.MyGlobalSession.Global_DB_Paths.PATH_CALL +
      "/(what3words)?Openagent&action=SEND_SMS&phone=" +
      phoneNumber;

    if (process.env.NODE_ENV === "development") {
      return Promise.resolve(({} as any) as ILegacyCleoServerResponse<any>);
    } else {
      return window.localCache
        .getUrlDataWithCache(url, false, {})
        .then(resp => {
          return (resp as any) as ILegacyCleoServerResponse<any>;
        });
    }
  }
}
