import { VehicleService } from "@/vehicles/vehicle-service";
import { VehiclesData } from "@/vehicles/vehicles-data";
import { ref } from "@vue/composition-api";
import { useHttpResponseController } from "@/common/useHttpController";
import {
  ILegacyCleoServerResponse,
  ILegacyDojoResponse
} from "@/common/cleo-legacy-models";
import {
  ITomTomVehicle,
  IVehicle,
  IVehicleLegacy
} from "@/vehicles/vehicles-models";
import { CommonService } from "@/common/common-service";

const vehiclesData = new VehiclesData();
const vehiclesService = new VehicleService();
const commonService = new CommonService();

export function useVehicleController() {
  const cleoVehicles = ref<IVehicleLegacy[]>([]);
  const vehicles = ref<IVehicle[]>([]);
  const tomTomehicles = ref<ITomTomVehicle[]>([]);
  const cleoVehiclesHttpResponseController = useHttpResponseController<
    ILegacyDojoResponse<IVehicleLegacy[]>
  >();

  const tomTomVehiclesHttpResponseController = useHttpResponseController<
    ILegacyDojoResponse<IVehicleLegacy[]>
  >();

  const dispatchResponseController = useHttpResponseController<
    ILegacyCleoServerResponse<unknown>
  >();

  function initDispatch(forceReload = false): Promise<void> {
    console.log("useVehicleController.initDispatch()...");
    if (forceReload || cleoVehicles.value.length === 0) {
      console.log("useVehicleController.initDispatch()...load data...");

      return cleoVehiclesHttpResponseController
        .getData(vehiclesData.getCleoVehicles())
        .then(resp => {
          console.log(
            "useVehicleController.getCleoVehicles()...data: ",
            commonService.simpleObjectClone(
              cleoVehiclesHttpResponseController.data
            )
          );
          if (cleoVehiclesHttpResponseController.data) {
            cleoVehicles.value = cleoVehiclesHttpResponseController.data.items;
            vehicles.value = cleoVehiclesHttpResponseController.data.items.map(
              vehicleLegacy => {
                return vehiclesService.mapVehicleLegacyToVehicle(vehicleLegacy);
              }
            );
          }
        });
    }
    return Promise.resolve();
  }

  function dispatchToVehicle(
    callId: number | string,
    vehicleId: string,
    lat: string,
    lng: string
  ): Promise<any> {
    return dispatchResponseController.getData(
      vehiclesData.dispatchToVehicle(callId, vehicleId, lat, lng)
    );
  }

  return {
    initDispatch,
    cleoVehicles,
    vehicles,
    dispatchResponseController,
    dispatchToVehicle
  };
}
