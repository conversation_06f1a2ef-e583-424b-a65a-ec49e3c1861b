import { UnwrapRef } from "@vue/composition-api";
import {
  CompleteFinalAction,
  CompleteProcess,
  CompleteStepName,
  FailedContactReasonType,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IFailedContactComplete,
  IOutcomes,
  IPatientReferredTo,
  IPatientRiskAssessment,
  IStep,
  IValidationMessage,
  IVulnerability,
  INonClinicalReason,
  ProcessName,
  StepAction,
  UserOptionsOutcomesOverrideKey
} from "./complete-models";
import * as CompleteService from "./complete-service";
import {
  HowManaged,
  HowManagedValue
} from "./failedcontact/how-managed/how-managed-models";
import {
  ISimpleButtonInputValue,
  SimpleButtonTypeBoolean
} from "./simple-button-selector-models";
import { simpleObjectClone } from "@/common/common-utils";

import { ContactType } from "./components/contactmade/contactmade-models";
import {
  ICallDetail,
  ICallDetailState
} from "@/calls/details/call-details-models";
import { CleoPermissionsForRole } from "@/permissions/permission-models";
import { InsufficientContactAttemptType } from "@/calls/details/complete/components/insufficient-contact-attempts/insufficient-contact-attempts";
import {
  ClinicalValidationType,
  ClinicalValidationValue
} from "@/calls/details/complete/components/clinical-validation/clinical-validation-models";
import { ExitReason } from "@/calls/details/complete/failedcontact/exitreasons/exit-reasons-models";
import { IApiReadCode } from "@/calls/details/complete/components/readcodes/readcodes-models";
import { failedContactRiskAssesmentAction } from "@/calls/details/complete/components/failed-contact-risk-assesment/failed-contact-rosk-assesment-models";
import { BrisDocNonClinicalAndPrescribingState } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";
import { factoryBrisDocStepFactoryCasEndAssessment } from "@/calls/details/complete/brisdoc/models/brisdoc-step-factory-cas-end-assessment";
import { DynamicQuestionsOutput } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { factoryUserStepsConfigNonClinical } from "@/calls/details/complete/steps-factories/step-factory-non-clinical";

export interface CompleteUserRoleTypes {
  isCasClinician: boolean;
  isClinician: boolean;
}

export interface ICompleteControllerInput {
  completeProcess: CompleteProcess;
  callDetail: ICallDetail;
  callDetailState: ICallDetailState;
  userRole: string;
  userPermissions: CleoPermissionsForRole;
  dx: {
    requiringValidation: string[];
  };
  failedContactConfig: IFailedContactComplete;
  userRoleTypes: CompleteUserRoleTypes;
}

export function useCompleteController(
  completeControllerInput: ICompleteControllerInput,
  stateInject?: UnwrapRef<ICompleteControllerState>
) {
  const state:
    | ICompleteControllerState
    | UnwrapRef<ICompleteControllerState> = stateInject
    ? stateInject
    : CompleteService.factoryCompleteControllerState();

  init();

  function init() {
    //  This is permission that currently drives the 2 user "type" flows.
    // const isCasTypeClinician = !!completeControllerInput.userPermissions
    //   .COMPLETE_USE_ROTA_CLINICIAN;

    //  TODO fix any.
    let completeUserStepsConfig: ICompleteUserStepsConfig<any> = {
      steps: {},
      gotoNextMap: {},
      gotoBackMap: {},
      validateMap: {}
    };
    let processName: ProcessName = "" as ProcessName;

    state.failedContactConfig = simpleObjectClone(
      completeControllerInput.failedContactConfig
    );

    //  since user will be on first step...
    state.ui.disableBack = true;

    /**
     * SAVE_AND_RETURN_PROCESS  this block builds the actual steps dependant on the user role.
     */
    if (completeControllerInput.completeProcess === "SAVE_AND_RETURN_PROCESS") {
      //  This is the default action.
      state.finalAction = "SAVE_AND_RETURN_TO_QUEUE";
      state.ui.buttons.complete.text = "Save";

      if (completeControllerInput.userRoleTypes.isCasClinician) {
        processName = "SAVE_AND_RETURN_CAS_CLINICAL";

        completeUserStepsConfig = CompleteService.factoryUserStepsConfigSaveAndReturnCas(
          state,
          completeControllerInput
        );

        state.userOptions.exitReason.forUser =
          state.userOptions.exitReason.options;
      } else {
        processName = "SAVE_AND_RETURN_111_CLINICAL";
        completeUserStepsConfig = CompleteService.factoryUserStepsConfigSaveAndReturn111(
          state,
          completeControllerInput
        );

        state.userOptions.exitReason.forUser = state.userOptions.exitReason.options.filter(
          option => option.value !== "FURTHER_ACTION_REQUIRED"
        );
      }
    }

    /**
     * COMPLETE_PROCESS  this block builds the actual steps dependent on the user role.
     */
    if (completeControllerInput.completeProcess === "COMPLETE_PROCESS") {
      state.doesPassFailedContactValidation = CompleteService.doesPassFailedContactValidation(
        state,
        completeControllerInput.callDetail.failedContacts,
        state.failedContactConfig.config.seedTime
      );

      // Can't think of a better way than using permissions to determine the process!?!?!?
      const isBrisDoc = !!completeControllerInput.userPermissions
        .COMPLETE_USE_BRISDOC;

      if (isBrisDoc) {
        // Bris CAS, BASE, VISIT all use same flow.
        processName = "COMPLETE_CAS_CLINICAL";

        const useNonClinicalFlow = !!completeControllerInput.userPermissions
          .COMPLETE_USE_BRISDOC_NON_CLINI;

        if (useNonClinicalFlow) {
          completeUserStepsConfig = factoryUserStepsConfigNonClinical(
            state,
            completeControllerInput
          );
        } else {
          completeUserStepsConfig = factoryBrisDocStepFactoryCasEndAssessment(
            state,
            completeControllerInput
          );
        }

        state.userOptions.END_ASSESSMENT_CONFIRMATION.options.messages = [
          "Further action required on this case? - select Return to Open Case to return the case to the queue for clinician to add further data or save and close the case",
          "No further action required - select Next to complete this current case"
        ];
      } else {
        if (completeControllerInput.userRoleTypes.isCasClinician) {
          processName = "COMPLETE_CAS_CLINICAL";
          completeUserStepsConfig = CompleteService.factoryUserStepsEndAssessmentCas(
            state,
            completeControllerInput
          );
        } else {
          processName = "COMPLETE_111_CLINICAL";
          completeUserStepsConfig = CompleteService.factoryUserStepsEndAssessment111(
            state,
            completeControllerInput
          );
        }
      }
    }

    state.processName = processName;
    const stepKeys = Object.keys(completeUserStepsConfig.steps);
    if (stepKeys.length > 0) {
      state.completeUserStepsConfig = completeUserStepsConfig;

      state.steps = (completeUserStepsConfig.steps as any) as Record<
        Partial<CompleteStepName>,
        IStep<Partial<CompleteStepName>>
      >;

      state.currentStep = stepKeys[0] as CompleteStepName;
    }

    //  Get dx off case.
    state.dx.usingDx = (
      completeControllerInput.callDetail.dx.clini.dxCode ||
      completeControllerInput.callDetail.dx.ch.dxCode
    )
      .toUpperCase()
      .trim();

    if (processName === "COMPLETE_111_CLINICAL") {
      //uppercase
      const dxCodesThatRequireValidation = completeControllerInput.dx.requiringValidation.map(
        code => code.toUpperCase().trim()
      );

      state.dx.isDxValidationRequired = dxCodesThatRequireValidation.includes(
        state.dx.usingDx
      );
    }
  }

  function onCompleteSaveAndReturn() {
    console.log("useCompleteController.onCompleteSaveAndReturn");
  }

  function goto(stepName: StepAction) {
    //  Reset disabled buttons.
    disableButtons(false);
    stepName === "NEXT" ? gotoNext() : gotoBack();
  }

  function simpleLogger(message: string, data?: unknown) {
    if (state.debug) {
      console.warn("useCompleteController.simpleLogger >>> " + message, data);
    }
  }

  function gotoNext(): void {
    const currentStepName = state.currentStep;
    const currentStep: IStep<CompleteStepName> = state.steps[currentStepName];

    simpleLogger("gotoNext currentStepName: " + currentStepName);

    simpleLogger(
      "gotoNext validationMessages A: " + state.validationMessages.length
    );
    validate();
    simpleLogger(
      "gotoNext validationMessages B: " + state.validationMessages.length
    );

    if (state.validationMessages.length > 0 && currentStep.requiresValidation) {
      return;
    }

    //  Gets the
    const nextStepMap: Partial<Record<CompleteStepName, () => void>> =
      state.completeUserStepsConfig.gotoNextMap;

    if (nextStepMap[currentStepName]) {
      nextStepMap[currentStepName]!();
    }
  }

  function validate() {
    const currentStepName = state.currentStep;
    state.validationMessages = [];

    const validationMap: Partial<Record<
      CompleteStepName,
      () => IValidationMessage[]
    >> = state.completeUserStepsConfig.validateMap;

    const messages = validationMap[currentStepName]
      ? validationMap[currentStepName]!()
      : [
          {
            id: "COULD_NOT_VALIDATE",
            message: "COULD_NOT_VALIDATE"
          }
        ];
    state.validationMessages = messages;
    state.steps[currentStepName].validationMessages = messages;
    state.steps[currentStepName].isValid = messages.length === 0;
  }

  function isCurrentStepValid(): boolean {
    return validateStep(state.currentStep).length === 0;
  }

  function validateStep(stepName: CompleteStepName): IValidationMessage[] {
    const validationMap: Partial<Record<
      CompleteStepName,
      () => IValidationMessage[]
    >> = state.completeUserStepsConfig.validateMap;

    // get the validation function off the map.
    const stepValidator = validationMap[stepName];

    if (stepValidator) {
      return stepValidator();
    }
    return [
      {
        id: "COULD_NOT_VALIDATE",
        message: "COULD_NOT_VALIDATE"
      }
    ];
  }

  function gotoBack() {
    const currentStepName = state.currentStep;
    state.isProcessComplete = false;

    //  clear validationMessages
    state.validationMessages = [];

    //  Maintaining state of steps is actually quite hard, because if user goes back and changes something, then
    //  goes forward again but takes a different route, we need to clear the data of te different route.
    //  So, we need to clear the data of the different route....but, can't think how to do that.
    // we could keep a map of latest state for a step, clear on going back, then if they go back to that step,
    // we can restore the state.  But, what happens if go back jumps over a step they went through before?...then
    // we need to clear the state of that step too.  This is getting complicated.
    //   For now...clear the state of step they are leaving, if the go back does jump over a step...we're still stuffed..
    if (!state.cacheStepState) {
      CompleteService.replaceUserResponse(state, currentStepName);
    }

    const backStepMap: Partial<Record<CompleteStepName, () => void>> =
      state.completeUserStepsConfig.gotoBackMap;

    if (backStepMap[currentStepName]) {
      backStepMap[currentStepName]!();
    }

    //  If is first step then disable back button.
    const stepKeys = Object.keys(state.completeUserStepsConfig.steps);
    if (stepKeys.length > 0) {
      state.ui.disableBack = state.currentStep === stepKeys[0];
    }
  }

  function cancel() {
    console.error("useCompleteController.cancel under construction, ");
  }

  function onEndAssessmentConfirmation() {
    simpleLogger("onEndAssessmentConfirmation()");
    autoProgressStep();
  }

  function onHowMangedSelected(
    output: ISimpleButtonInputValue<HowManaged, HowManagedValue>
  ): void {
    state.userResponse.howManaged = simpleObjectClone(output);
    autoProgressStep();
  }

  function onContactMade(
    output: ISimpleButtonInputValue<ContactType, ContactType>
  ) {
    simpleLogger("onContactMade()", output);
    state.userResponse.contactMade = simpleObjectClone(output);
    autoProgressStep();
  }

  function onPatientReferredTo(patientReferredTo: IPatientReferredTo) {
    state.userResponse.patientReferredTo = simpleObjectClone(patientReferredTo);
    //  This step does NOT have auto progress. User has to do multiple things.
  }

  function onOutcomesSelected(outcomes: IOutcomes) {
    state.userResponse.outcomes = simpleObjectClone(outcomes);
    autoProgressStep();
  }

  function onReadCodesSelected(readCodes: IApiReadCode[]) {
    state.userResponse.readCodes.readCodesSelected = simpleObjectClone(
      readCodes
    );
    // User might select multiple possible outcomes, so no auto progress.
  }

  function onTaxiSelected(
    output: ISimpleButtonInputValue<
      SimpleButtonTypeBoolean,
      SimpleButtonTypeBoolean
    >
  ): void {
    state.userResponse.taxi = simpleObjectClone(output);
    autoProgressStep();
  }

  function onVulnerabilitySelected(output: IVulnerability): void {
    state.userResponse.vulnerability = simpleObjectClone(output);
    if (isCurrentStepValid()) {
      //  It's a multi radio...proceed if valid.
      autoProgressStep();
    }
  }

  function onFailedContactReasonSelected(
    output: FailedContactReasonType
  ): void {
    state.userResponse.failedContactReason = simpleObjectClone(output);
    autoProgressStep();
  }

  function onPatientRiskAssessmentSelected(output: IPatientRiskAssessment) {
    state.userResponse.patientRiskAssessment = simpleObjectClone(output);
    // NO auto progress if insufficient contact attempts.

    const enoughContactAttempts = CompleteService.doesPassFailedContactValidation(
      state,
      completeControllerInput.callDetail.failedContacts,
      state.failedContactConfig.config.seedTime
    );
    console.log(
      "useCompleteController.onPatientRiskAssessmentSelected() enoughContactAttempts: " +
        enoughContactAttempts
    );
    if (enoughContactAttempts) {
      autoProgressStep();
    }
  }

  function onInsufficientContactAttemptTypeSelected(
    output: ISimpleButtonInputValue<
      InsufficientContactAttemptType,
      InsufficientContactAttemptType
    >
  ): void {
    state.userResponse.insufficientContactAttempts = simpleObjectClone(output);
    autoProgressStep();
  }

  function onClinicalValidationSelected(
    output: ISimpleButtonInputValue<
      ClinicalValidationType,
      ClinicalValidationValue
    >
  ): void {
    state.userResponse.clinicalValidation = simpleObjectClone(output);
    autoProgressStep();
  }

  function onExitReasonSelected(
    output: ISimpleButtonInputValue<ExitReason, ExitReason>
  ) {
    state.userResponse.exitReason = output;
    autoProgressStep();
  }

  function onFailedContactWarningSelected(
    output: ISimpleButtonInputValue<CompleteFinalAction, CompleteFinalAction>
  ) {
    state.userResponse.failedContactWarning = simpleObjectClone(output);
    autoProgressStep();
  }

  function onFailedContactRiskAssessmentSelected(
    action: failedContactRiskAssesmentAction
  ) {
    //  This step does NOT have auto progress. User has to do multiple things.
    state.finalAction = action;
  }

  function onBrisDocNonClinicalAndPrescribingSelected(
    output: BrisDocNonClinicalAndPrescribingState
  ) {
    state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING = simpleObjectClone(
      output
    );
    // autoProgressStep();
  }

  function onBrisDocAuditQuestionsSelected(
    dynamicQuestionsOutput: DynamicQuestionsOutput
  ) {
    state.userResponse.BRISDOC_AUDIT_QUESTIONS = simpleObjectClone(
      dynamicQuestionsOutput
    );
    // autoProgressStep();
  }

  function onNonClinicalReasonSelected(output: INonClinicalReason) {
    state.userResponse.nonClinicalReason = simpleObjectClone(output);
    state.userOptions.OUTCOMES.overRideKey = ("NON_CLINICAL_REASON__" +
      output.reason.id
        .toUpperCase()
        .replace(/ /g, "_")) as UserOptionsOutcomesOverrideKey;
    // only auto progress if output is not "Other".
    if (output.reason.id !== "Other") {
      autoProgressStep();
    }
  }

  function autoProgressStep() {
    if (state.autoProgress) {
      goto("NEXT");
    }
  }

  function processComplete(): Promise<ICompleteControllerState | null> {
    simpleLogger("processComplete()");
    if (state.finalAction === "") {
      state.finalAction = "COMPLETE";
    }

    return Promise.resolve(simpleObjectClone(state));
  }

  function disableButtons(shouldDisable: boolean) {
    state.ui = {
      disableBack: shouldDisable,
      disableNext: shouldDisable,
      disableCancel: shouldDisable,
      disableComplete: shouldDisable,
      buttons: state.ui.buttons
    };
  }

  return {
    completeControllerInput,
    state,
    onCompleteSaveAndReturn,
    goto,
    validate,
    cancel,
    onEndAssessmentConfirmation,
    onHowMangedSelected,
    onContactMade,
    onFailedContactReasonSelected,
    onPatientReferredTo,
    onReadCodesSelected,
    onOutcomesSelected,
    onPatientRiskAssessmentSelected,
    onInsufficientContactAttemptTypeSelected,
    onClinicalValidationSelected,
    onExitReasonSelected,
    onFailedContactWarningSelected,
    onFailedContactRiskAssessmentSelected,
    onTaxiSelected,
    onVulnerabilitySelected,
    onBrisDocNonClinicalAndPrescribingSelected,
    onBrisDocAuditQuestionsSelected,
    onNonClinicalReasonSelected,
    processComplete,
    validateStep,
    isCurrentStepValid,
    disableButtons
  };
}
