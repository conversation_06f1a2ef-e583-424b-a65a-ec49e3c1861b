import { useUserMessageHandler } from "@/common/user-messages/UserMessage";
import { computed, reactive } from "@vue/composition-api";

export interface IHttpResponseControllerState<Data> {
  isLoading: boolean;
  data: Data | null;
  error: string;
  getData: (prom: Promise<Data>) => Promise<Data | null>;
  deferred: Promise<any>;
  updatedAt: number;
}

export function useHttpResponseControllerStateFactory<Data>() {
  return {
    isLoading: false,
    data: null,
    error: "",
    getData: Promise.resolve(null),
    deferred: Promise.resolve(),
    updatedAt: 0
  };
}

export function useHttpResponseController<Data>(
  initData: Data | null = null,
  successMessage?: string
): IHttpResponseControllerState<Data> {
  const userMessageHandler = useUserMessageHandler();

  const state: IHttpResponseControllerState<Data> = {
    isLoading: false,
    data: initData,
    error: "",
    getData,
    deferred: Promise.resolve(),
    updatedAt: 0
  };

  // const reactiveState = computed<Partial<IHttpResponseControllerState<Data>>>(
  //   () => {
  //     return {
  //       isLoading: state.isLoading,
  //       data: state.data,
  //       error: state.error,
  //       updatedAt: 0
  //     };
  //   }
  // );

  function getData(
    prom: Promise<Data>,
    cacheInSeconds = -1
  ): Promise<Data | null> {
    if (state.isLoading) {
      return state.deferred;
    }

    if (cacheInSeconds > 0) {
      const isFresh = Date.now() - state.updatedAt < cacheInSeconds * 1000;
      if (isFresh) {
        return Promise.resolve(state.data);
      }
    }

    state.isLoading = true;
    state.deferred = prom;

    return prom
      .then((response: Data) => {
        if (successMessage && successMessage.length > 0) {
          userMessageHandler.send(successMessage);
        }
        state.data = response;
        return response;
      })
      .catch((error: Error) => {
        userMessageHandler.send(error.message);
        state.error = error.message;
        return null;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  return state;
}

export interface IHttpResponseControllerReactiveState<Data> {
  isLoading: boolean;
  data: Data | null;
  error: string;
  updatedAt: number;
}

export function useHttpResponseControllerReactive<Data>(
  initData: Data | null = null,
  successMessage?: string
) {
  const userMessageHandler = useUserMessageHandler();

  const state = reactive<IHttpResponseControllerReactiveState<Data>>({
    isLoading: false,
    data: initData,
    error: "",
    updatedAt: 0
  });

  let deferred: Promise<Data | null> = Promise.resolve(null);

  function getData(
    prom: Promise<Data>,
    cacheInSeconds = -1
  ): Promise<Data | null> {
    if (state.isLoading) {
      return Promise.resolve(null);
    }

    if (cacheInSeconds > 0) {
      const isFresh = Date.now() - state.updatedAt < cacheInSeconds * 1000;
      if (isFresh) {
        // return Promise.resolve(state.data);
      }
    }

    state.isLoading = true;
    deferred = prom;

    return prom
      .then((response: Data) => {
        if (successMessage && successMessage.length > 0) {
          userMessageHandler.send(successMessage);
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        state.data = response;
        return response;
      })
      .catch((error: Error) => {
        userMessageHandler.send(error.message);
        state.error = error.message;
        return null;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  return {
    state: state,
    getData
  };
}
