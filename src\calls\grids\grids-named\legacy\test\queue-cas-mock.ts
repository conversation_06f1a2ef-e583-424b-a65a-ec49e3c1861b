import {
  GridLegacyCallSummary,
  GridLegacyServerResponse
} from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

import { TextSimpleInteger } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

const cachedAdditionalItems: GridLegacyCallSummary[] = [];

// Generate 100 additional items
const additionalItems = [];
// const additionalItems = Array.from({ length: 100 }, (_, i) =>
//   generateRandomCall(i)
// );

export const queueCasMockSource: GridLegacyServerResponse = ({
  Count: 45,
  Returned: 45,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: [
    {
      unid: "276809D39AE195F680258C430055B795",
      name: "250636161",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636161",
      CallID: "250636161",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (MHCN review)",
      CC: "Advice",
      CSC: "MHCN review",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=nick wall/O=sehnp",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-02-26T14:48:07",
      BreachPreActualTime: "2025-02-26T14:08:07",
      BreachActualTime: "2025-02-26T15:08:07",
      BreachPriority: "7",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "Broken DNU, Broken",
      CallTriaged: "",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Broken DNU",
      CallForename: "Broken",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "Test Comments",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "PAEDIATRICS",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "A8634525DCE0661B80258C4B004EBF84",
      name: "250636379",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636379",
      CallID: "250636379",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "ADVICE",
      CC: "ADVICE",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Ben Smythson/O=staging",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "ADVICENo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "2025-03-27T17:29:54",
      BreachWarnActualTime: "2025-02-26T14:48:07",
      BreachPreActualTime: "2025-02-26T14:08:07",
      BreachActualTime: "2025-02-26T15:08:07",
      BreachPriority: "7",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "17:29",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "CAS TEST, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "CAS TEST",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "2025-03-27T17:29:54 00:00~:~another tet",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "MENTAL_HEALTH",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: ""
    },
    {
      unid: "BA8C17BF1F5BD84880258C4D0042CF4C",
      name: "250636433",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636433",
      CallID: "250636433",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (Registered MH Sign Off)",
      CC: "Advice",
      CSC: "Registered MH Sign Off",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "26/03/2025 07:14:38",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=nick wall/O=sehnp",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-17T11:08:19",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-03-17T12:15:00",
      BreachPreActualTime: "2025-02-12T15:47:31",
      BreachActualTime: "2025-03-17T12:35:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "PAEDIATRICS, Test",
      CallTriaged: "",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "NBT SDEC",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "26/03/2025 07:15:12",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "PAEDIATRICS",
      CallForename: "Test",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "2 hours",
      FinalDispositionCode: "P4h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "WEEKDAY_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "17/03/2025 08:35:00",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "B7528B3AED6FCA1880258C500042B08C",
      name: "250636459",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636459",
      CallID: "250636459",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "19/03/2025 15:06:45",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "TEST",
      CallCRel: "Police",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-17T12:09:18",
      CallReceivedTimeISO: "2025-03-17T12:08:25",
      BreachWarnActualTime: "2025-03-17T12:28:22",
      BreachPreActualTime: "2025-03-17T12:09:18",
      BreachActualTime: "2025-03-17T12:38:22",
      BreachPriority: "2",
      BreachLevel1Mins: "30",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Urgent 30 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:08",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "MANUAL POLICE, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "MANUAL POLICE",
      CallForename: "Test",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "P30m",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "83A188DC694BB7B280258C5000424D69",
      name: "250636457",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636457",
      CallID: "250636457",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "2023-03-16",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "17/03/2025 13:43:08",
      Call1stContactPathways: "",
      PathwaysCaseId: "90D032C5-942D-4ED3-8CC6-275E780A5DBE",
      CallCreatedBy: "111",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-17T12:04:12",
      CallReceivedTimeISO: "2025-03-17T12:04:12",
      BreachWarnActualTime: "2025-03-17T13:44:12",
      BreachPreActualTime: "2025-03-17T12:04:12",
      BreachActualTime: "2025-03-17T14:04:12",
      BreachPriority: "9",
      BreachLevel1Mins: "120",
      Source: "NHS111Interop",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:04",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "2 yrs",
      CallDoctorNameCN: "",
      PatientName: "BrisDoc Dx35, Test",
      CallTriaged: "No",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Greenway",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233123123",
      CallTelNo_R: "01233123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "BrisDoc Dx35",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx35",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx35",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      UTC_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: ""
    },
    {
      unid: "E40DFB2D65E676A280258C500041133A",
      name: "250636453",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636453",
      CallID: "250636453",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "17/03/2025 17:00:46",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-17T11:51:27",
      CallReceivedTimeISO: "2025-03-17T11:50:47",
      BreachWarnActualTime: "2025-03-17T15:31:27",
      BreachPreActualTime: "2025-03-17T11:51:27",
      BreachActualTime: "2025-03-17T15:51:27",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "11:50",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "PATIENT LINE, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Christchurch",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "PATIENT LINE",
      CallForename: "Test",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "2025-03-17T11:52:13",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",

      // add quite long SMS message
      SMS_HAS: "1",
      SMS_SENT: "1",
      SMS_LATEST_AT: "2025-03-17T11:50:47",
      SMS_LATEST_USER: "CN=nick wall/O=sehnp",
      SMS_LATEST_MESSAGE:
        "This is a very long SMS message that should wrap to multiple lines in the grid. This is a very long SMS message that should wrap to multiple lines in the grid. This is a very long SMS message that should wrap to multiple lines in the grid. This is a very long SMS message that should wrap to multiple lines in the grid. This is a very long SMS message that should wrap to multiple lines in the grid. This is a very long"
    },
    {
      unid: "0FAD11216E7325BD80258C500049D84E",
      name: "250636466",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250636466",
      CallID: "250636466",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "2023-03-09",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "25/03/2025 12:39:02",
      Call1stContactPathways: "",
      PathwaysCaseId: "90D032C5-942D-4ED3-8CC6-275E780A5DBE",
      CallCreatedBy: "CN=nick wall/O=sehnp",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-25T12:39:06",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-03-18T10:40:00",
      BreachPreActualTime: "2025-03-11T14:45:28",
      BreachActualTime: "2025-03-18T11:00:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "2 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "ED Validation, Test",
      CallTriaged: "",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233123123",
      CallTelNo_R: "01233123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "ED Validation",
      CallForename: "Test",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "2025-03-28T08:59:33 00:00~:~TEST",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "OUT_OF_HOURS_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "18/03/2025 09:00:00",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",

      // Add an SMS message, but set SMS_SENT to false
      SMS_HAS: "1",
      SMS_SENT: "0",
      SMS_LATEST_AT: "2025-03-25T12:39:06",
      SMS_LATEST_USER: "CN=nick wall/O=sehnp",
      SMS_LATEST_MESSAGE: "Hello failed message"
    },
    {
      unid: "5A22A8C1E324A36480258C520055420D",
      name: "250646516",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646516",
      CallID: "250646516",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1985-03-12",
      CallPatientTitle: "",
      CallAddress1: "No. 2",
      CallAddress2: "Shelley Grove",
      CallAddress3: "Street",
      CallAddress4: "SOUTHPORT",
      CallTown: "Merseyside",
      CallPostCode: "PR8 6HA",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "19/03/2025 15:37:04",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-19T15:31:15",
      CallReceivedTimeISO: "2025-03-19T15:31:14",
      BreachWarnActualTime: "2025-03-19T21:11:15",
      BreachPreActualTime: "2025-03-19T15:31:15",
      BreachActualTime: "2025-03-19T21:31:15",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:31",
      CallAppointmentTime: "",
      CallArrivedTime: "15:34",
      dtArrivedTime: "27/03/2025 15:34:00",
      CallAge: "40 yrs",
      CallDoctorNameCN: "",
      PatientName: "HOMAN, Amos",
      CallTriaged: "No",
      CallSymptoms: "after user completes the End Assessment,",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "HOMAN",
      CallForename: "Amos",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82730",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "0",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "CN=Olubunmi Oderinde/O=staging",
      Courtesy_Time: "2025-03-27T14:08:32",
      Courtesy_Count: "2",
      Courtesy_Contact: "true",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      Call_HCP: "1"
    },
    {
      unid: "D42D4D0949B0DCD780258C53003D0F48",
      name: "250646544",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646544",
      CallID: "250646544",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "20/03/2025 13:01:28",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Paramedic on Scene",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-20T11:07:31",
      CallReceivedTimeISO: "2025-03-20T11:06:56",
      BreachWarnActualTime: "2025-03-20T11:47:31",
      BreachPreActualTime: "2025-03-20T11:07:31",
      BreachActualTime: "2025-03-20T12:07:31",
      BreachPriority: "7",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "11:06",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "TEST, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST",
      CallForename: "Test",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",

      SMS_HAS: "1",
      SMS_SENT: "1",
      SMS_LATEST_AT: "2025-03-20T11:06:56",
      SMS_LATEST_USER: "CN=Test Doctor/O=staging",
      SMS_LATEST_MESSAGE: "Hello"
    },
    {
      unid: "D710766FE3D226DD80258C530054BC77",
      name: "250646606",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646606",
      CallID: "250646606",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (Registered MH Sign Off)",
      CC: "Advice",
      CSC: "Registered MH Sign Off",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "24/03/2025 10:16:45",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "Tester",
      CallCRel: "Nursing Home",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-20T15:26:13",
      CallReceivedTimeISO: "2025-03-20T15:25:32",
      BreachWarnActualTime: "2025-03-20T19:06:13",
      BreachPreActualTime: "2025-03-20T15:26:13",
      BreachActualTime: "2025-03-20T19:26:13",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:25",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "NURSING HOME, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Admin only",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "24/03/2025 10:18:18",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NURSING HOME",
      CallForename: "Test",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "CN=Ben Smythson/O=staging",
      Courtesy_Time: "2025-03-24T14:19:19",
      Courtesy_Count: "1",
      Courtesy_Contact: "false",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "FRAILTY",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "B7054C6A21C533B080258C530054F8AC",
      name: "250646608",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646608",
      CallID: "250646608",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (HCP)",
      CC: "Advice",
      CSC: "HCP",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "25/03/2025 12:50:26",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "HCP",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-20T15:28:49",
      CallReceivedTimeISO: "2025-03-20T15:28:06",
      BreachWarnActualTime: "2025-03-20T19:08:49",
      BreachPreActualTime: "2025-03-20T15:28:49",
      BreachActualTime: "2025-03-20T19:28:49",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:28",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "HCP, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "HCP",
      CallForename: "Test",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "1",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "WEEKDAY_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "BEE94E74B9DB1DA180258C6500500B48",
      name: "250647506",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647506",
      CallID: "250647506",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=nick wall/O=sehnp",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-03-24T12:59:50",
      BreachPreActualTime: "2025-03-24T11:19:50",
      BreachActualTime: "2025-03-24T13:19:50",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "CONSULTATION CASE 1, Test",
      CallTriaged: "",
      CallSymptoms: "Test Case Symptoms.",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "CONSULTATION CASE 1",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "2 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "82ADE86015D061D880258C58004291D5",
      name: "250646721",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646721",
      CallID: "250646721",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1925-03-16",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice (Poisoning)",
      CC: "Advice",
      CSC: "Poisoning",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "27/03/2025 16:52:12",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-25T12:07:07",
      CallReceivedTimeISO: "2025-03-25T12:07:07",
      BreachWarnActualTime: "2025-03-25T12:17:07",
      BreachPreActualTime: "2025-03-25T12:07:07",
      BreachActualTime: "2025-03-25T12:22:07",
      BreachPriority: "1",
      BreachLevel1Mins: "15",
      Source: "",
      BreachPriorityGroup: "Pharmacist",
      BreachPriorityLabel: "Urgent 15 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:07",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "100 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "Urgent Care Queue Test 2, Test",
      CallTriaged: "No",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Urgent Care Queue Test 2",
      CallForename: "Test",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx325",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments:
        "2025-04-15T11:27:51 01:00~:~rewr3ere~~~2025-04-15T11:27:31 01:00~:~ vb gfb gbnghnghng",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "Toxic Ingestion",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "2",
      CasValidationUser: "CN=nick wall/O=sehnp",
      CasValidationTime: "09/04/2025 14:14:29"
    },
    {
      unid: "27D029FCF7F3D3DE80258C590036B05F",
      name: "250646769",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646769",
      CallID: "250646769",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1942-07-16",
      CallPatientTitle: "",
      CallAddress1: "1 CARR HILL RISE",
      CallAddress2: "CALVERLEY",
      CallAddress3: "PUDSEY",
      CallAddress4: "W YORKSHIRE",
      CallTown: "",
      CallPostCode: "LS28 5QD",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "26/03/2025 10:07:44",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-26T09:57:55",
      CallReceivedTimeISO: "2025-03-26T09:57:21",
      BreachWarnActualTime: "2025-03-26T11:37:15",
      BreachPreActualTime: "2025-03-26T09:57:55",
      BreachActualTime: "2025-03-26T11:57:15",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "09:57",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "82 yrs",
      CallDoctorNameCN: "",
      PatientName: "MCEVOY, Otto",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "No Answer",
      PatientContactCode_count: "1",
      PatientContactCode_Initial: "14/04/2025 14:25:15",
      PatientContactCode_Current_ForView: "2025-04-14T14:25:15",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "MCEVOY",
      CallForename: "Otto",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "B86071",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "1",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "DE168FDB198D2B0580258C5900369486",
      name: "250646768",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646768",
      CallID: "250646768",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Female",
      CallDobIso: "1989-09-28",
      CallPatientTitle: "",
      CallAddress1: "CEDAR LODGE",
      CallAddress2: "HIGH STREET",
      CallAddress3: "WROOT",
      CallAddress4: "DONCASTER",
      CallTown: "",
      CallPostCode: "DN9 2BT",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "26/03/2025 10:00:40",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-26T09:57:10",
      CallReceivedTimeISO: "2025-03-26T09:56:09",
      BreachWarnActualTime: "2025-03-26T13:37:10",
      BreachPreActualTime: "2025-03-26T09:57:10",
      BreachActualTime: "2025-03-26T13:57:10",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "09:56",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "35 yrs",
      CallDoctorNameCN: "Ben Smythson",
      PatientName: "DEVINE, Glenys",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "DEVINE",
      CallForename: "Glenys",
      CallDoctorName: "CN=Ben Smythson/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "A20047",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "FRAILTY",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      OversightValidationType: "Clinical Co-Ordinator Review in progress"
    },
    {
      unid: "C5345C9F1CF99A3A80258C5900408D25",
      name: "250646783",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646783",
      CallID: "250646783",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      CallClassification: "Advice (Registered MH Sign Off)",
      CC: "Advice",
      CSC: "Registered MH Sign Off",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "26/03/2025 12:02:38",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-26T11:45:04",
      CallReceivedTimeISO: "2025-03-26T11:45:04",
      BreachWarnActualTime: "2025-03-26T15:25:04",
      BreachPreActualTime: "2025-03-26T11:45:04",
      BreachActualTime: "2025-03-26T15:45:04",
      BreachPriority: "8",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "11:45",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "63 yrs",
      CallDoctorNameCN: "Anthony Soanes",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "No",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "07791 230000",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "CN=Anthony Soanes/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx03",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx03",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "OUT_OF_HOURS_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      OversightValidationType: "Approve",
      CasValidationUser: "CN=nick wall/O=sehnp",
      CasValidationTime: "09/04/2025 14:14:29"
    },
    {
      unid: "D2FD8F0E642C358180258C59004C5F25",
      name: "250646801",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646801",
      CallID: "250646801",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1999-01-17",
      CallPatientTitle: "",
      CallAddress1: "24 WHITE WILLOW CLOSE",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "ASHFORD",
      CallPostCode: "TN24 0SB",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "03/04/2025 12:31:21",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-26T13:59:19",
      CallReceivedTimeISO: "2025-03-26T13:54:11",
      BreachWarnActualTime: "2025-03-26T17:39:19",
      BreachPreActualTime: "2025-03-26T13:59:19",
      BreachActualTime: "2025-03-26T17:59:19",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "13:54",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "26 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "REED, Dan",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 333111",
      CallTelNo_R: "01233 333111",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "REED",
      CallForename: "Dan",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "BRIS123",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "FRAILTY",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      OversightValidationType: "Further Clinical Input Required",
      CasValidationUser: "CN=nick wall/O=sehnp",
      CasValidationTime: "09/04/2025 14:14:29"
    },
    {
      unid: "C598AF509042896680258C59004D047A",
      name: "250646802",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646802",
      CallID: "250646802",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Not Known",
      CallDobIso: "1999-01-01",
      CallPatientTitle: "",
      CallAddress1: "30 WHITE WILLOW CLOSE",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "ASHFORD",
      CallPostCode: "TN24 0SB",
      UTC_Assigned: "",
      CallClassification: "Advice (Registered MH Sign Off)",
      CC: "Advice",
      CSC: "Registered MH Sign Off",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "26/03/2025 15:26:19",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-26T14:09:22",
      CallReceivedTimeISO: "2025-03-26T14:01:14",
      BreachWarnActualTime: "2025-03-26T19:49:22",
      BreachPreActualTime: "2025-03-26T14:09:22",
      BreachActualTime: "2025-03-26T20:09:22",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:01",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "26 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "TOISE, Tor",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "Engaged",
      PatientContactCode_count: "1",
      PatientContactCode_Initial: "27/03/2025 00:18:21",
      PatientContactCode_Current_ForView: "2025-03-27T00:18:21",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01223 232323",
      CallTelNo_R: "01223 232323",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TOISE",
      CallForename: "Tor",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "BRIS123",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "1",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "MENTAL_HEALTH",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "935693E31E6ED73180258C5A0050A33C",
      name: "250646859",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646859",
      CallID: "250646859",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1925-03-16",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "90D032C5-942D-4ED3-8CC6-275E780A5DBE",
      CallCreatedBy: "111",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-27T14:40:46",
      CallReceivedTimeISO: "2025-03-27T14:40:46",
      BreachWarnActualTime: "2025-03-27T15:30:46",
      BreachPreActualTime: "2025-03-27T15:35:46",
      BreachActualTime: "2025-03-27T15:40:46",
      BreachPriority: "1",
      BreachLevel1Mins: "60",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Urgent 30 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:40",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "100 yrs",
      CallDoctorNameCN: "",
      PatientName: "Urgent Care Queue Test 4, Test",
      CallTriaged: "No",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233123123",
      CallTelNo_R: "01233123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Urgent Care Queue Test 4",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx333",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx333",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "C3/C4 Validation",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "AA06B6C51365398080258C5A0041B6E8",
      name: "250646840",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646840",
      CallID: "250646840",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "15/04/2025 16:25:33",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=nick wall/O=sehnp",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-04-15T16:25:36+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-03-27T16:00:00",
      BreachPreActualTime: "2025-03-27T09:50:20",
      BreachActualTime: "2025-03-27T16:20:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "63 yrs",
      CallDoctorNameCN: "Ben Smythson",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "07791 230000",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "CN=Ben Smythson/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "P4h",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "PATIENT_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "27/03/2025 12:20:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "BABD3C60D1DFE71780258C5A0050B1EB",
      name: "250646860",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646860",
      CallID: "250646860",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1925-03-16",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-03-27T14:41:24",
      CallReceivedTimeISO: "2025-03-27T14:41:24",
      BreachWarnActualTime: "2025-03-27T16:31:24",
      BreachPreActualTime: "2025-03-27T16:36:24",
      BreachActualTime: "2025-03-27T16:41:24",
      BreachPriority: "3",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Urgent 30 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:41",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "100 yrs",
      CallDoctorNameCN: "",
      PatientName: "Urgent Care Queue Test 4, Test",
      CallTriaged: "No",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233123123",
      CallTelNo_R: "01233123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Urgent Care Queue Test 4",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx337",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx337",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "ED Validation",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: ""
    },
    {
      unid: "8F405D3F47BDA56B80258C5A005959B8",
      name: "250646865",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646865",
      CallID: "250646865",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Not Known",
      CallDobIso: "1955-02-01",
      CallPatientTitle: "",
      CallAddress1: "JOBCENTRE PLUS",
      CallAddress2: "INTERNATIONAL HOUSE",
      CallAddress3: "DOVER PLACE",
      CallAddress4: "",
      CallTown: "ASHFORD",
      CallPostCode: "TN23 1HT",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "Yes",
      Call1stContact: "28/03/2025 13:33:08",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Other - HCP",
      BreachKey: "AdviceYes",
      ApplyBreach: "0",
      CallReceivedISO: "2025-03-27T16:23:32",
      CallReceivedTimeISO: "2025-03-27T16:15:56",
      BreachWarnActualTime: "2025-03-27T18:03:32",
      BreachPreActualTime: "2025-03-27T16:23:32",
      BreachActualTime: "2025-03-27T18:23:32",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "16:15",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "70 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "IRELAND, Dessie",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01323 343434",
      CallTelNo_R: "01323 343434",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "IRELAND",
      CallForename: "Dessie",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Y03703",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "2 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "WEEKDAY_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "07A4D3DECA5E185280258C5A00599477",
      name: "250646866",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646866",
      CallID: "250646866",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Ben Smythson/O=staging",
      CallCName: "",
      CallCRel: "Nursing Home",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-03-27T21:58:27",
      BreachPreActualTime: "2025-03-20T15:26:13",
      BreachActualTime: "2025-03-27T22:18:27",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "16:17",
      dtArrivedTime: "27/03/2025 16:17:53",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "NURSING HOME, Test",
      CallTriaged: "",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NURSING HOME",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "MENTAL_HEALTH",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "28/03/2025 18:20:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "CB106FEF0C43F3D680258C5F0076DA07",
      name: "250647099",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647099",
      CallID: "250647099",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1964-01-11",
      CallPatientTitle: "",
      CallAddress1: "Hscic",
      CallAddress2: "1 Trevelyan Square",
      CallAddress3: "LEEDS",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "LS1 6AE",
      UTC_Assigned: "",
      CallClassification: "Advice (Registered MH Sign Off)",
      CC: "Advice",
      CSC: "Registered MH Sign Off",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "03/04/2025 18:12:18",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-01T22:39:36+01:00",
      CallReceivedTimeISO: "2025-04-01T22:38:10+01:00",
      BreachWarnActualTime: "2025-04-02T02:19:36+01:00",
      BreachPreActualTime: "2025-04-01T22:39:36+01:00",
      BreachActualTime: "2025-04-02T02:39:36+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "22:38",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "61 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "XXTESTPATIENTIAKZ, Nic-donotuse",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Simple medication request",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "03/04/2025 18:14:32",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "XXTESTPATIENTIAKZ",
      CallForename: "Nic-donotuse",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82064",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "MENTAL_HEALTH",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: ""
    },
    {
      unid: "9C661D18A7BB692180258C60002FE8CE",
      name: "250647109",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647109",
      CallID: "250647109",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "07/04/2025 16:02:14",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-02T09:44:36+01:00",
      CallReceivedTimeISO: "2025-04-02T09:43:18+01:00",
      BreachWarnActualTime: "2025-04-02T10:24:36+01:00",
      BreachPreActualTime: "2025-04-02T09:44:36+01:00",
      BreachActualTime: "2025-04-02T10:44:36+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "09:43",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "WEFWF, Qwd",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "WEFWF",
      CallForename: "Qwd",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P1h",
      CHFinalDispositionDescription: "1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "6BE9BD2F599BAAC080258C60003D6DD7",
      name: "250647132",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647132",
      CallID: "250647132",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1985-03-12",
      CallPatientTitle: "",
      CallAddress1: "No. 2",
      CallAddress2: "Shelley Grove",
      CallAddress3: "Street",
      CallAddress4: "SOUTHPORT",
      CallTown: "Merseyside",
      CallPostCode: "PR8 6HA",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "08/04/2025 13:54:08",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-02T12:13:25+01:00",
      CallReceivedTimeISO: "2025-04-02T12:10:58+01:00",
      BreachWarnActualTime: "2025-04-02T13:53:25+01:00",
      BreachPreActualTime: "2025-04-02T12:13:25+01:00",
      BreachActualTime: "2025-04-02T14:13:25+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:10",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "40 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "HOMAN, Amos",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "HOMAN",
      CallForename: "Amos",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82730",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "2 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "MENTAL_HEALTH",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "E4405F356601D66480258C62003C6D3B",
      name: "250647387",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647387",
      CallID: "250647387",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Olubunmi Oderinde/O=staging",
      CallCName: "",
      CallCRel: "Police",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-04T13:40:58+01:00",
      BreachPreActualTime: "2025-03-20T15:22:05",
      BreachActualTime: "2025-04-04T14:00:58+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "TEST, Test",
      CallTriaged: "",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "2 hours",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "04/04/2025 16:59:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "A709755F092B0C5280258C6200503548",
      name: "250647448",
      CallCCMS: "",
      Info: "",
      IsLocked: "nick wall/sehnp",
      CallNo: "250647448",
      CallID: "250647448",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Female",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-04T15:36:05+01:00",
      CallReceivedTimeISO: "2025-04-04T15:36:05+01:00",
      BreachWarnActualTime: "2025-04-04T15:46:05+01:00",
      BreachPreActualTime: "2025-04-04T15:36:05+01:00",
      BreachActualTime: "2025-04-04T15:51:05+01:00",
      BreachPriority: "1",
      BreachLevel1Mins: "15",
      Source: "",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Urgent 15 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:36",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "AAAAAA, Qewd",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "AAAAAA",
      CallForename: "Qewd",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "ASHT-DFEK52-20250404T153441",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx327",
      CHFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Chemical Eye Splash",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "16C068C49831D68480258C62003C3642",
      name: "250647386",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647386",
      CallID: "250647386",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Olubunmi Oderinde/O=staging",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-04T15:48:46+01:00",
      BreachPreActualTime: "2025-03-26T11:45:06",
      BreachActualTime: "2025-04-04T16:08:46+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "63 yrs",
      CallDoctorNameCN: "",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "07791 230000",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "P4h",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "04/04/2025 17:56:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "526095D275A27EEA80258C6200500A9C",
      name: "250647446",
      CallCCMS: "",
      Info: "",
      IsLocked: "nick wall/sehnp",
      CallNo: "250647446",
      CallID: "250647446",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-04T15:35:08+01:00",
      CallReceivedTimeISO: "2025-04-04T15:34:16+01:00",
      BreachWarnActualTime: "2025-04-04T16:15:08+01:00",
      BreachPreActualTime: "2025-04-04T15:35:08+01:00",
      BreachActualTime: "2025-04-04T16:35:08+01:00",
      BreachPriority: "1",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Less Urgent 12 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:34",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "SDVS, Qw",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "SDVS",
      CallForename: "Qw",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "ASHT-DFEK52-20250404T153441",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx3315",
      CHFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Refused Over 12 Hours Primary Care Service Disposition",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "511563F71F95215D80258C6200502A0F",
      name: "250647447",
      CallCCMS: "",
      Info: "",
      IsLocked: "nick wall/sehnp",
      CallNo: "250647447",
      CallID: "250647447",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-04T15:35:36+01:00",
      CallReceivedTimeISO: "2025-04-04T15:35:36+01:00",
      BreachWarnActualTime: "2025-04-04T16:15:36+01:00",
      BreachPreActualTime: "2025-04-04T15:35:36+01:00",
      BreachActualTime: "2025-04-04T16:35:36+01:00",
      BreachPriority: "1",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Less Urgent 12 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "15:35",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "DFVDFV, Qewf",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "DFVDFV",
      CallForename: "Qewf",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "ASHT-DFEK52-20250404T153441",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx3315",
      CHFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Refused Over 12 Hours Primary Care Service Disposition",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "7DCAD42462AC3AD780258C62003DF519",
      name: "250647394",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647394",
      CallID: "250647394",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1964-01-11",
      CallPatientTitle: "",
      CallAddress1: "Hscic",
      CallAddress2: "1 Trevelyan Square",
      CallAddress3: "LEEDS",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "LS1 6AE",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Olubunmi Oderinde/O=staging",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-04T20:55:00+01:00",
      BreachPreActualTime: "2025-04-01T22:32:16+01:00",
      BreachActualTime: "2025-04-04T21:15:00+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "61 yrs",
      CallDoctorNameCN: "",
      PatientName: "XXTESTPATIENTIAKZ, Nic-donotuse",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "XXTESTPATIENTIAKZ",
      CallForename: "Nic-donotuse",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82064",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "30 mins",
      FinalDispositionCode: "P4h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "04/04/2025 17:15:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "CB64C65B484BB16280258C6600287174",
      name: "250647516",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647516",
      CallID: "250647516",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "2017-03-09",
      CallPatientTitle: "",
      CallAddress1: "C/O NHS DIGITAL TEST DATA MANAGER",
      CallAddress2: "SOLUTION ASSURANCE 1 TREVELYAN SQ.",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "LS1 6AE",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-08T08:22:18+01:00",
      CallReceivedTimeISO: "2025-04-08T08:21:44+01:00",
      BreachWarnActualTime: "2025-04-08T08:32:18+01:00",
      BreachPreActualTime: "2025-04-08T08:22:18+01:00",
      BreachActualTime: "2025-04-08T08:37:18+01:00",
      BreachPriority: "1",
      BreachLevel1Mins: "15",
      Source: "",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Urgent 15 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "08:21",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "8 yrs",
      CallDoctorNameCN: "",
      PatientName: "XXTESTPATIENT-TDTR, Donotuse",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "XXTESTPATIENT-TDTR",
      CallForename: "Donotuse",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82071",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx32",
      CHFinalDispositionDescription: "Speak To Immediately",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: ""
    },
    {
      unid: "DE12F90066A21E2980258C66002905B9",
      name: "250647519",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647519",
      CallID: "250647519",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "2017-03-09",
      CallPatientTitle: "",
      CallAddress1: "C/O NHS DIGITAL TEST DATA MANAGER",
      CallAddress2: "SOLUTION ASSURANCE 1 TREVELYAN SQ.",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "LS1 6AE",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "08/04/2025 08:29:07",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Paramedic on Scene",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-04-08T08:28:48+01:00",
      CallReceivedTimeISO: "2025-04-08T08:28:04+01:00",
      BreachWarnActualTime: "2025-04-08T12:08:48+01:00",
      BreachPreActualTime: "2025-04-08T08:28:48+01:00",
      BreachActualTime: "2025-04-08T12:28:48+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "08:28",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "8 yrs",
      CallDoctorNameCN: "Olubunmi Oderinde",
      PatientName: "XXTESTPATIENT-TDTR, Donotuse",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "XXTESTPATIENT-TDTR",
      CallForename: "Donotuse",
      CallDoctorName: "CN=Olubunmi Oderinde/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G82071",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "OUT_OF_HOURS_PROFESSIONAL_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "B086FC9ADF12624180258C66007D4A88",
      name: "250647567",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647567",
      CallID: "250647567",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-08T23:49:13+01:00",
      CallReceivedTimeISO: "2025-04-08T23:48:30+01:00",
      BreachWarnActualTime: "2025-04-09T01:28:29+01:00",
      BreachPreActualTime: "2025-04-08T23:49:13+01:00",
      BreachActualTime: "2025-04-09T01:48:29+01:00",
      BreachPriority: "9",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "23:48",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "WEWEF, W",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "WEWEF",
      CallForename: "W",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx118",
      CHFinalDispositionDescription:
        "Refer to Dental Treatment Centre within 1 hour",
      FinalDispositionCode: "Dx12",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "F3BBD2F7382439CD80258C670048FE6C",
      name: "250647613",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647613",
      CallID: "250647613",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-09T14:17:17+01:00",
      CallReceivedTimeISO: "2025-04-09T14:17:17+01:00",
      BreachWarnActualTime: "2025-04-09T17:57:17+01:00",
      BreachPreActualTime: "2025-04-09T14:17:17+01:00",
      BreachActualTime: "2025-04-09T18:17:17+01:00",
      BreachPriority: "8",
      BreachLevel1Mins: "240",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:17",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "64 yrs",
      CallDoctorNameCN: "",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "No",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "Christchurch Car1",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "07791230000",
      CallTelNo_R: "07791230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx03",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx03",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "3",
      CasValidationUser: "CN=nick wall/O=sehnp",
      CasValidationTime: "09/04/2025 14:32:35"
    },
    {
      unid: "C5E5F738CF8FA65780258C680012F32C",
      name: "250647644",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647644",
      CallID: "250647644",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-10T04:27:39+01:00",
      CallReceivedTimeISO: "2025-04-10T04:26:59+01:00",
      BreachWarnActualTime: "2025-04-10T06:06:57+01:00",
      BreachPreActualTime: "2025-04-10T04:27:39+01:00",
      BreachActualTime: "2025-04-10T06:26:57+01:00",
      BreachPriority: "9",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "04:26",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "NNN_DX1, Ew",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NNN_DX1",
      CallForename: "Ew",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx96",
      CHFinalDispositionDescription:
        "Refer to Health Information within 12 hours",
      FinalDispositionCode: "Dx12",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "5445835CFC6C643D80258C6800133338",
      name: "250647645",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647645",
      CallID: "250647645",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice (Patient Line)",
      CC: "Advice",
      CSC: "Patient Line",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-10T04:30:08+01:00",
      CallReceivedTimeISO: "2025-04-10T04:29:43+01:00",
      BreachWarnActualTime: "2025-04-10T06:09:41+01:00",
      BreachPreActualTime: "2025-04-10T04:30:08+01:00",
      BreachActualTime: "2025-04-10T06:29:41+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "04:29",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "NN_DX2, Qewd",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NN_DX2",
      CallForename: "Qewd",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "PATIENT_LINE",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "C1C35A3E9F38271980258C68004547C7",
      name: "250647708",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647708",
      CallID: "250647708",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1925-03-16",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "GL1 3NN",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "90D032C5-942D-4ED3-8CC6-275E780A5DBE",
      CallCreatedBy: "111",
      CallCName: "Donotuse XXTESTPATIENT-TEVQ",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-10T13:36:43+01:00",
      CallReceivedTimeISO: "2025-04-10T13:36:43+01:00",
      BreachWarnActualTime: "2025-04-10T15:26:43+01:00",
      BreachPreActualTime: "2025-04-10T15:31:43+01:00",
      BreachActualTime: "2025-04-10T15:36:43+01:00",
      BreachPriority: "3",
      BreachLevel1Mins: "120",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Urgent 30 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "13:36",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "100 yrs",
      CallDoctorNameCN: "",
      PatientName: "ED Validation, Test",
      CallTriaged: "No",
      CallSymptoms: "Test Call Please Ignore",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233123123",
      CallTelNo_R: "01233123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "ED Validation",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx337",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx337",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "ED Validation",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "2",
      CasValidationUser: "CN=Ben Smythson/O=staging",
      CasValidationTime: "10/04/2025 13:41:58"
    },
    {
      unid: "AE64374480D2727180258C680034E4C7",
      name: "250647659",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647659",
      CallID: "250647659",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-10T19:21:00+01:00",
      BreachPreActualTime: "2025-04-10T10:34:58+01:00",
      BreachActualTime: "2025-04-10T19:41:00+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "NW_44, Test",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NW_44",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "10/04/2025 13:41:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "B0E9F4575A319F4280258C6800114BA6",
      name: "250647640",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647640",
      CallID: "250647640",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "10/04/2025 14:29:18",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-04-10T14:29:22+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-11T03:50:00+01:00",
      BreachPreActualTime: "2025-04-10T04:04:43+01:00",
      BreachActualTime: "2025-04-11T04:10:00+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "nick wall",
      PatientName: "NN_F2F_2, Ewf",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NN_F2F_2",
      CallForename: "Ewf",
      CallDoctorName: "CN=nick wall/O=sehnp",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription:
        "Emergency Contraception required within 12 hours",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "10/04/2025 22:10:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "AC320B8D88ABEFAD80258C6800119BE0",
      name: "250647641",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647641",
      CallID: "250647641",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice (Follow Up)",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-11T15:58:00+01:00",
      BreachPreActualTime: "2025-04-10T04:06:52+01:00",
      BreachActualTime: "2025-04-11T16:18:00+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "NN_F2F_3, Ewfewrf",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NN_F2F_3",
      CallForename: "Ewfewrf",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription:
        "Repeat caller - Speak to the GP Practice within 1 hour (3 calls within 4 days)",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Follow Up",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "11/04/2025 10:18:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "6FC214BD648DD46880258C6A0029E1E7",
      name: "250647785",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250647785",
      CallID: "250647785",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "1996-08-07",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-04-12T08:38:13+01:00",
      CallReceivedTimeISO: "2025-04-12T08:37:28+01:00",
      BreachWarnActualTime: "2025-04-12T09:18:13+01:00",
      BreachPreActualTime: "2025-04-12T08:38:13+01:00",
      BreachActualTime: "2025-04-12T09:38:13+01:00",
      BreachPriority: "1",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "08:37",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "28 yrs",
      CallDoctorNameCN: "",
      PatientName: "GFH, Dfg",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "GFH",
      CallForename: "Dfg",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "C1",
      CHFinalDispositionDescription: "Speak To Immediately",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "false",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "D054C2BC351DE52280258C5A004FDD41",
      name: "250646857",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646857",
      CallID: "250646857",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Female",
      CallDobIso: "2014-02-04",
      CallPatientTitle: "",
      CallAddress1: "27 OLD ORCHARD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "ASHFORD",
      CallPostCode: "TN23 4PY",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "14/04/2025 14:57:30",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-04-14T14:36:20+01:00",
      CallReceivedTimeISO: "2025-03-27T14:32:19",
      BreachWarnActualTime: "2025-04-14T15:16:20+01:00",
      BreachPreActualTime: "2025-04-14T14:36:20+01:00",
      BreachActualTime: "2025-04-14T15:36:20+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:32",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "11 yrs",
      CallDoctorNameCN: "Ben Smythson",
      PatientName: "WEBB, Seren",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "WEBB",
      CallForename: "Seren",
      CallDoctorName: "CN=Ben Smythson/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "ASHT-DF6K3X-20250327T143319",
      CallPracticeOCS: "BRIS123",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "F5D73A59CEF5C05F80258C5A00505EFA",
      name: "250646858",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250646858",
      CallID: "250646858",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      CallDobIso: "2010-09-27",
      CallPatientTitle: "",
      CallAddress1: "27 OLD ORCHARD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "ASHFORD",
      CallPostCode: "TN23 4PY",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "14/04/2025 14:57:30",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111_FAX",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-04-14T14:36:20+01:00",
      CallReceivedTimeISO: "2025-03-27T14:37:52",
      BreachWarnActualTime: "2025-04-14T15:16:20+01:00",
      BreachPreActualTime: "2025-04-14T14:36:20+01:00",
      BreachActualTime: "2025-04-14T15:36:20+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:37",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "14 yrs",
      CallDoctorNameCN: "Ben Smythson",
      PatientName: "WEBB, Lucas",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 333322",
      CallTelNo_R: "01233 333322",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "WEBB",
      CallForename: "Lucas",
      CallDoctorName: "CN=Ben Smythson/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "ASHT-DF6K3X-20250327T143319",
      CallPracticeOCS: "BRIS123",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    }
  ],
  Page: {
    Enabled: 1,
    PageNumber: 1,
    PageSize: 100,
    getRowCount: 45,
    getStartRowNumber: 1,
    TotalSearchRowCount: 45,
    getTotalNumberOfPages: 1
  }
} as any) as GridLegacyServerResponse;

function generateRandomCall(index: number): GridLegacyCallSummary {
  const services = [
    "CAS",
    "BrisDoc",
    "FRAILTY",
    "OUT_OF_HOURS_PROFESSIONAL_LINE"
  ];
  const classifications = [
    "Advice",
    "Advice (Registered MH Sign Off)",
    "Clinical Assessment"
  ];
  const statuses = ["New", "In Progress", "Complete"];
  const towns = [
    "NORWICH",
    "ASHFORD",
    "COLYTON",
    "HOVETON",
    "MUNDESLEY",
    "SEVENOAKS"
  ];
  const streets = [
    "King Street",
    "Church Road",
    "High Street",
    "Market Place",
    "Station Road"
  ];
  const postcodes = ["NR11 8DH", "TN24 0SB", "EX24 6LF", "NR2 3TU", "TN13 3PG"];
  const doctors = [
    "Anthony Soanes",
    "Nick Wall",
    "Sarah Smith",
    "James Wilson",
    "Emma Brown"
  ];

  const baseCall = ({
    unid: "276809D39AE195F680258C430055B795",
    name: "250636161",
    CallCCMS: "",
    Info: "",
    IsLocked: "",
    CallNo: "250636161",
    CallID: "250636161",
    CallNHSNo: "",
    CallService: "CAS",
    CallServiceSub: "",
    CallServiceOriginal: "BrisDoc",
    CallServiceAlt: "",
    CallMF: "Male",
    CallDobIso: "1996-03-25",
    CallPatientTitle: "",
    CallAddress1: "",
    CallAddress2: "",
    CallAddress3: "",
    CallAddress4: "",
    CallTown: "",
    CallPostCode: "NR33 9LS",
    UTC_Assigned: "",
    CallClassification: "Advice (MHCN review)",
    CC: "Advice",
    CSC: "MHCN review",
    WalkIn: "0",
    CallUrgentYN: "No",
    Call1stContact: "",
    Call1stContactPathways: "",
    PathwaysCaseId: "",
    CallCreatedBy: "CN=nick wall/O=sehnp",
    CallCName: "",
    CallCRel: "Patient",
    BreachKey: "AdviceNo",
    ApplyBreach: "1",
    CallReceivedISO: "",
    CallReceivedTimeISO: "",
    BreachWarnActualTime: "2025-02-26T14:48:07",
    BreachPreActualTime: "2025-02-26T14:08:07",
    BreachActualTime: "2025-02-26T15:08:07",
    BreachPriority: "7",
    BreachLevel1Mins: "60",
    Source: "",
    BreachPriorityGroup: "Speak To",
    BreachPriorityLabel: "Less Urgent 60 mins",
    CallWithBaseAckTime: "",
    CallReceivedTime: "",
    CallAppointmentTime: "",
    CallArrivedTime: "",
    dtArrivedTime: "",
    CallAge: "28 yrs",
    CallDoctorNameCN: "",
    PatientName: "Broken DNU, Broken",
    CallTriaged: "",
    CallSymptoms: "TEST",
    CallPtcl: "",
    CallStatus: "New",
    CallStatusValue: "1",
    PatientContactCode: "",
    PatientContactCode_count: "",
    PatientContactCode_Initial: "",
    PatientContactCode_Current_ForView: "",
    CallInformationalOutcomes: "",
    CallInformationalOutcomesComment: "",
    DutyBase: "",
    Dispatch_Vehicle: "",
    CallCompletedYN: "No",
    CallCompleted: "",
    CallCallback: "",
    CallTelNo: "01233 123123",
    CallTelNo_R: "01233 123123",
    CallTelNoAlt_1: "",
    CallTelNoAltType_1: "",
    CallSurname: "Broken DNU",
    CallForename: "Broken",
    CallDoctorName: "",
    CallSecondOpenCall: "",
    CallAdastraSent: "",
    CallAdastraLock: "0",
    TomTomOrderID: "",
    Linked_Call_ID: "",
    CallPracticeOCS: "G00234",
    SDec_Service: "0",
    CAS_Booking_Dr: "",
    CAS_Booking_Time: "",
    CallWarmTransferred: "",
    CHFinalDispositionCode: "Dx11",
    CHFinalDispositionDescription:
      "Speak to a Primary Care Service within 1 hour",
    FinalDispositionCode: "",
    FinalDispositionDescription: "",
    FLAG_REMOVE_FIRST_CONTACT: "0",
    PDSTracedAndVerified: "No",
    PDSTraced: "true",
    CliniHighPriority: "",
    StartConsultationPerformed: "1",
    ClinicalHub_111ToHubReason: "",
    Courtesy_User: "",
    Courtesy_Time: "",
    Courtesy_Count: "",
    Courtesy_Contact: "",
    CAS_TRANSFER_ERROR: "",
    Pathways_ITK_Send: "",
    ITK_111_Online: "",
    AFT_appt_id: "",
    AFT_datetime_start: "",
    AFT_UNABLE_REASON: "",
    DAB_Id: "",
    DAB_StartDateTime: "",
    DAB_EndDateTime: "",
    AFT_time_start: "",
    AFT_datetime_start_ORIG: "",
    AFT_CANCELLED_REASON: "",
    ED_arrived: "",
    txtAppointmentBase: "",
    CareConnectAppointmentStart: "",
    IUC_CAS_AT_ONE_TIME: "1",
    IUC_Contract: "BrisDoc",
    SetBaseInfo_Base: "",
    Comfort_SENT_SERVICE: "",
    Comfort_SENT_SERVICE_TIME: "",
    Comfort_SENT_SERVICE2: "",
    Comfort_SENT_SERVICE2_TIME: "",
    Comfort_SMS2: "",
    Comfort_SMS_TIME2: "",
    Comfort_SMS: "",
    Comfort_SMS_TIME: "",
    Comfort_Cancelled_TIME: "",
    Comfort_Cancelled_REASON: "",
    Cov19Priority: "",
    dateAppointmentStart: "",
    EConsult_UTC: "",
    EConsult_code: "",
    EConsult_Priority: "",
    EConsult_PriorityLabel: "",
    EConsult_ITK_IN: "",
    EConsult_Status: "",
    EConsult_Source: "",
    EConsult_LivingArrangement: "",
    EConsult_AttendanceSource: "",
    EConsult_LiveOverseas: "",
    EConsult_ArrivalMethod: "",
    EConsult_SameProblem: "",
    EConsult_XRay: "",
    patient_alerts_count: "",
    Call_CaseComments: "Test Comments",
    EMIS_CDB: "",
    EMIS_PatientId: "",
    EMIS_UserId: "",
    KMS_SC_MESSAGE: "",
    CareHomeName: "",
    ALL_VIEW_INCLUDE: "",
    cleoClientService: "PAEDIATRICS",
    COMPLETE_PREVENT: "",
    FOLLOW_UP_Active: "",
    FOLLOW_UP_URGENT: "",
    OVERSIGHT_BASE_TRIAGE_TYPE: "",
    Cpl_supportTypeRequired: "",
    CasValidationCount: "",
    CasValidationUser: "",
    CasValidationTime: ""
  } as any) as GridLegacyCallSummary;
  const callId = (250646801 + index).toString();
  const today = new Date();
  const randomDate = new Date(
    today.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000
  );

  return {
    ...baseCall,
    unid:
      Math.random()
        .toString(36)
        .substring(2) + Date.now().toString(36),
    name: callId,
    CallNo: callId,
    CallID: callId,
    CallNHSNo: Math.floor(Math.random() * **********).toString(),
    CallService: services[Math.floor(Math.random() * services.length)],
    CallServiceOriginal: services[Math.floor(Math.random() * services.length)],
    cleoClientService: services[Math.floor(Math.random() * services.length)],
    CallMF: Math.random() > 0.5 ? "Male" : "Female",
    CallDobIso: `${1940 + Math.floor(Math.random() * 80)}-${String(
      Math.floor(Math.random() * 12) + 1
    ).padStart(2, "0")}-${String(Math.floor(Math.random() * 28) + 1).padStart(
      2,
      "0"
    )}`,
    CallPatientTitle: ["Mr", "Mrs", "Ms", "Dr"][Math.floor(Math.random() * 4)],
    CallAddress1: `${Math.floor(Math.random() * 100)} ${
      streets[Math.floor(Math.random() * streets.length)]
    }`,
    CallTown: towns[Math.floor(Math.random() * towns.length)],
    CallPostCode: postcodes[Math.floor(Math.random() * postcodes.length)],
    CallClassification:
      classifications[Math.floor(Math.random() * classifications.length)],
    WalkIn: Math.random() > 0.8 ? "1" : "0",
    CallUrgentYN: Math.random() > 0.8 ? "Yes" : "No",
    CallCreatedBy: `CN=${
      doctors[Math.floor(Math.random() * doctors.length)]
    }/O=sehnp`,
    ApplyBreach: Math.random() > 0.5 ? "1" : "0",
    CallReceivedISO: randomDate.toISOString(),
    CallReceivedTimeISO: randomDate.toISOString(),
    BreachWarnActualTime: randomDate.toISOString(),
    BreachPreActualTime: randomDate.toISOString(),
    BreachActualTime: randomDate.toISOString(),
    BreachPriority: (Math.floor(
      Math.random() * 5 + 1
    ).toString() as any) as TextSimpleInteger,
    CallAge: `${Math.floor(Math.random() * 90)} yrs`,
    CallDoctorNameCN: `CN=${
      doctors[Math.floor(Math.random() * doctors.length)]
    }/O=sehnp`,
    CallStatusValue: Math.floor(Math.random() * 3 + 1).toString(),
    CallTelNo: `07${Math.floor(Math.random() * 999999999)}`.padStart(11, "0"),
    CallSurname: ["Smith", "Jones", "Williams", "Brown", "Taylor"][
      Math.floor(Math.random() * 5)
    ],
    CallForename: ["John", "Emma", "Michael", "Sarah", "David"][
      Math.floor(Math.random() * 5)
    ],
    CallCallback: (Math.floor(
      Math.random() * 5
    ).toString() as any) as TextSimpleInteger,
    CallWarmTransferred: Math.random() > 0.8 ? "Yes" : "No",
    PDSTracedAndVerified: Math.random() > 0.7 ? "Yes" : "No",
    CliniHighPriority: Math.random() > 0.8 ? "1" : "",
    StartConsultationPerformed: Math.random() > 0.7 ? "1" : "",
    Courtesy_Count: (Math.floor(
      Math.random() * 5
    ).toString() as any) as TextSimpleInteger,
    Courtesy_Contact: Math.random() > 0.8 ? "1" : "",
    ITK_111_Online: Math.random() > 0.8 ? "1" : "0",
    Linked_Call_ID:
      Math.random() > 0.8 ? (250646801 + index + 1).toString() : ""
  };
}

// .concat(
//   cachedAdditionalItems.length > 0 ? cachedAdditionalItems : additionalItems
// )

// Add the new items to your existing data structure
export const queueCasMock = {
  Count: 45,
  Returned: 45,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: queueCasMockSource.items
};
