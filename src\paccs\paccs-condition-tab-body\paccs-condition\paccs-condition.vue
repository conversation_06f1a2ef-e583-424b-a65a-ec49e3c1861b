<template>
  <div>
    <div>
      <div class="paccs-condition--summary">
        <span
          v-text="paccsConditionInternal.conditionName"
          class="paccs-condition--condition-name"
        ></span>
        <a
          href="#"
          v-if="
            paccsConditionInternal.conditionSupportingInformation.length > 0
          "
          class="paccs-condition--toggle-show-detail"
          v-on:click.prevent="toggleShowDetail"
        >
          <span v-text="showDetail ? 'hide' : 'more'"></span>
        </a>
      </div>

      <div
        class="paccs-condition--fields"
        v-if="paccsConditionInternal.importance < 3"
      >
        <div class="paccs-condition--field-checkbox">
          <input
            type="checkbox"
            v-model="state.data.considered"
            v-on:change="handleCheckBoxClick('CONSIDERED')"
          />
        </div>
        <div class="paccs-condition--field-checkbox">
          <input
            type="checkbox"
            v-model="state.data.suspected"
            v-on:change="handleCheckBoxClick('SUSPECTED')"
          />
        </div>
      </div>

      <div
        class="paccs-condition--field-specify"
        v-if="getHasSelectionBeenMade"
      >
        <textarea
          type="text"
          :maxlength="state.ui.specifyMaxLenth"
          v-model="state.data.specify"
          :placeholder="
            'Specify (Max ' + state.ui.specifyMaxLenth + ' characters)'
          "
          v-on:blur="handleSpecifyChange"
        />
      </div>

      <div class="paccs-condition--buttons-section" v-if="getShowButtons">
        <div class="paccs-condition--buttons">
          <PaccsButtonPwJump
            title="Ambulance"
            :id="paccsConditionInternal.ambJump"
            button-type="AD"
            :symptom-group="paccsSymptomModel.symptomGroup"
            :paccs-symptom-model-template-id="paccsSymptomModel.templateId"
            :paccs-condition-id="paccsConditionInternal.conditionId"
            :class="
              shouldButtonBeDisplayed(paccsConditionInternal.ambJump)
                ? ''
                : 'paccs-condition--button-jump-to-hide'
            "
          ></PaccsButtonPwJump>

          <div class="adapter-button--separator"></div>

          <PaccsButtonPwJump
            title="Emergency Treatment Centres"
            :id="paccsConditionInternal.etcJump"
            button-type="ET"
            :symptom-group="paccsSymptomModel.symptomGroup"
            :paccs-symptom-model-template-id="paccsSymptomModel.templateId"
            :paccs-condition-id="paccsConditionInternal.conditionId"
            :class="
              shouldButtonBeDisplayed(paccsConditionInternal.etcJump)
                ? ''
                : 'paccs-condition--button-jump-to-hide'
            "
          ></PaccsButtonPwJump>

          <div class="adapter-button--separator"></div>

          <PaccsButtonPwJump
            title="Service Search"
            :id="paccsConditionInternal.servicesJump"
            button-type="SS"
            :symptom-group="paccsSymptomModel.symptomGroup"
            :paccs-symptom-model-template-id="paccsSymptomModel.templateId"
            :paccs-condition-id="paccsConditionInternal.conditionId"
            :class="
              shouldButtonBeDisplayed(paccsConditionInternal.servicesJump)
                ? ''
                : 'paccs-condition--button-jump-to-hide'
            "
          ></PaccsButtonPwJump>

          <div class="adapter-button--separator"></div>

          <PaccsButtonPwJump
            title="Home Care"
            :id="paccsConditionInternal.homeMgmtJump"
            button-type="HC"
            :symptom-group="paccsSymptomModel.symptomGroup"
            :paccs-symptom-model-template-id="paccsSymptomModel.templateId"
            :paccs-condition-id="paccsConditionInternal.conditionId"
            :class="
              shouldButtonBeDisplayed(paccsConditionInternal.homeMgmtJump)
                ? ''
                : 'paccs-condition--button-jump-to-hide'
            "
          ></PaccsButtonPwJump>
        </div>
      </div>
    </div>

    <div v-if="showDetail">
      <div
        v-for="supportingInformation in sortedPaccsConditionSupportingInformation"
        :key="
          supportingInformation.conditionId +
            '-' +
            supportingInformation.orderNo
        "
      >
        <PaccsConditionSupportingInfo
          class="paccs-condition--supporting-info"
          :condition-supporting-information="supportingInformation"
        ></PaccsConditionSupportingInfo>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { CommonService } from "@/common/common-service";
import { PaccsConditionService } from "@/paccs/paccs-condition-tab-body/paccs-condition-service";
import {
  IPaccsCondition,
  IPaccsConditionSupportingInformation
} from "@/paccs/paccs-condition-tab-body/paccs-condition-models";
import PaccsConditionSupportingInfo from "@/paccs/paccs-condition-tab-body/paccs-condition/paccs-condition-supporting-info.vue";
import PaccsButtonPwJump from "@/paccs/buttons/paccs-button-pw-jump.vue";
import { PaccsService } from "@/paccs/paccs-service";
import { appStore } from "@/store/store";
import {
  IPaccsStoreState,
  PACCS_STORE_CONST,
  TriageRecordConditionUpdate
} from "@/paccs/paccs-store";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import { ITriageRecordCondition } from "@/paccs/paccs-models";

const commonService: CommonService = new CommonService();
const paccsConditionService: PaccsConditionService = new PaccsConditionService();

export default defineComponent({
  name: "paccs-condition",
  props: {
    paccsSymptomModel: {
      required: true,
      type: Object as PropType<IPaccsSymptomModel>
    },
    paccsCondition: {
      default: () => {
        return paccsConditionService.factoryPaccsCondition();
      }
    },
    triageRecordConditionHydration: {
      default: () => {
        return 0;
      },
      type: Object as PropType<ITriageRecordCondition>
    },
    orderNumber: {
      required: true
    }
  },
  components: {
    PaccsConditionSupportingInfo,
    PaccsButtonPwJump
  },
  setup(
    props: {
      paccsSymptomModel: IPaccsSymptomModel;
      paccsCondition: IPaccsCondition;
      triageRecordConditionHydration: ITriageRecordCondition;
      orderNumber: boolean;
    },
    context: SetupContext
  ) {
    const store = appStore;

    const paccsStoreState = computed<IPaccsStoreState>(() => {
      return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
    });
    const paccsService: PaccsService = new PaccsService();

    //  TODO move into state
    const paccsConditionInternal: IPaccsCondition = reactive(
      commonService.simpleObjectClone(props.paccsCondition)
    );

    const state = reactive({
      ui: {
        showDetail: false,
        specifyMaxLenth: 250
      },
      data: {
        considered: false,
        suspected: false,
        specify: ""
      }
    });

    setHydrationState(props.triageRecordConditionHydration);

    //  TODO move into state
    const showDetail = ref(false);

    const sortedPaccsConditionSupportingInformation = computed<
      IPaccsConditionSupportingInformation[]
    >(sortConditionSupportingInformation);

    function sortConditionSupportingInformation() {
      return commonService.sortArray(
        "orderNo",
        paccsConditionInternal.conditionSupportingInformation
      );
    }

    watch(
      () => props.paccsCondition.conditionId,
      (newValue: string, oldValue: string) => {
        loggerInstance.log(
          "paccs-condition>>>>>>>>>>>>>>>>>>>watch conditionId!!!!"
        );
        Object.assign(paccsConditionInternal, props.paccsCondition);
      }
    );

    watch(
      () => props.triageRecordConditionHydration,
      (newValue: ITriageRecordCondition, oldValue: ITriageRecordCondition) => {
        loggerInstance.log(
          "paccs-condition>>>>>>>>>>>>>>>>>>>watch triageRecordConditionHydration!!!!"
        );
        setHydrationState(newValue);
      }
    );

    function setHydrationState(triageRecordCondition: ITriageRecordCondition) {
      state.data.considered = false;
      state.data.suspected = false;
      state.data.specify = "";

      if (triageRecordCondition.quId.length === 0) {
        return;
      }

      state.data.considered = triageRecordCondition.answerNumber === 1;
      state.data.suspected = triageRecordCondition.answerNumber === 2;
      state.data.specify = triageRecordCondition.userComment;
    }

    function toggleShowDetail() {
      showDetail.value = !showDetail.value;
    }

    const getHasSelectionBeenMade = computed<boolean>(() => {
      return state.data.considered || state.data.suspected;
    });

    const getShowButtons = computed<boolean>(() => {
      return state.data.suspected && paccsConditionInternal.importance > 0;
    });

    function shouldButtonBeDisplayed(
      jumpId: string | "NULL" | null | undefined
    ) {
      if (!jumpId) {
        return false;
      }
      return jumpId.length > 0 && jumpId.toLowerCase() !== "null";
    }

    function handleCheckBoxClick(fieldName: "CONSIDERED" | "SUSPECTED") {
      if (
        fieldName === "CONSIDERED" &&
        state.data.considered &&
        state.data.suspected
      ) {
        state.data.suspected = false;
      }
      if (
        fieldName === "SUSPECTED" &&
        state.data.considered &&
        state.data.suspected
      ) {
        state.data.considered = false;
      }

      const paccsQuestion = paccsService.createTriageRecordCondition(
        props.paccsSymptomModel,
        paccsConditionInternal,
        paccsStoreState.value.addCaseRecordResponse.caseId,
        paccsStoreState.value.pathwaysDataset,
        state.data
      );

      store.dispatch(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_ADD_TRIAGE_QUESTION,
        paccsQuestion
      );
    }

    function handleSpecifyChange() {
      const triageRecordConditionUpdate: TriageRecordConditionUpdate = {
        pathwayId: props.paccsSymptomModel.templateId,
        quId: props.paccsCondition.conditionId,
        userComment: state.data.specify
      };

      // store.commit(
      //   PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
      //     "/" +
      //     PACCS_STORE_CONST.PACCS__MUTATION_UPDATE_PACCS_QUESTION_SPECIFY,
      //   triageRecordConditionUpdate
      // );

      store.dispatch(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_ADD_QUESTION_SPECIFY,
        triageRecordConditionUpdate
      );
    }

    return {
      paccsConditionInternal,
      showDetail,
      toggleShowDetail,
      handleCheckBoxClick,
      handleSpecifyChange,
      shouldButtonBeDisplayed,
      getHasSelectionBeenMade,
      getShowButtons,
      sortedPaccsConditionSupportingInformation,
      state
    };
  }
});
</script>

<style>
.paccs-condition--summary {
  display: inline-block;
  width: 40%;
  vertical-align: top;
}

.paccs-condition--fields {
  display: inline-block;
  width: 20%;
  /*float: right;*/
  vertical-align: top;
}

.paccs-condition--field-checkbox {
  display: inline-block;
  width: 50%;
  text-align: center;
}

.paccs-condition--field-specify {
  display: inline-block;
  width: 40%;
}

.paccs-condition--field-specify textarea {
  width: 90%;
  height: 100px;
  float: right;
  overflow-x: hidden;
  overflow-y: auto;
  resize: none;
}

.paccs-condition--condition-name {
  font-weight: 600;
}
.paccs-condition--toggle-show-detail {
  margin-left: 1em;
  text-decoration: none;
}
.paccs-condition--supporting-info {
  margin-top: 0.5em;
}
.paccs-condition--buttons-section {
  width: 100%;
  overflow: auto;
}
.paccs-condition--buttons {
  float: right;
}
.paccs-condition--button-jump-to-hide {
  visibility: hidden;
}
</style>
