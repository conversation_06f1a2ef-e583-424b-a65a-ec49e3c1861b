<template>
  <AutocompleteSimple
    :is-loading="isLoading"
    label-prop="templateName"
    v-on:onSearchChanged="onSearchTermChanged"
    v-on:onReset="reset"
  ></AutocompleteSimple>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { PaccsSymptomData } from "@/paccs/paccs-symptom-search/paccs-symptom-data";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import AutocompleteSimple from "@/common/ui/fields/autocomplete-simple.vue";
import { PaccsSymptomService } from "@/paccs/paccs-symptom-search/paccs-symptom-service";
import { loggerInstance } from "@/common/Logger";
import { CommonService } from "@/common/common-service";

@Component({
  name: "paccs-symptom-search",
  components: { AutocompleteSimple }
})
export default class PaccsSymptomSearch extends Vue {
  public paccsSymptomService: PaccsSymptomService = new PaccsSymptomService();

  public paccsSymptomData: PaccsSymptomData = new PaccsSymptomData();
  public isLoading = false;
  public paccsSymptomModels: IPaccsSymptomModel[] = [];
  public paccsSymptomModelSelected: IPaccsSymptomModel = this.paccsSymptomService.factoryPaccsSymptomModel();

  public reset(): void {
    this.paccsSymptomModels = [];
    this.paccsSymptomModelSelected = this.paccsSymptomService.factoryPaccsSymptomModel();
    this.$emit("reset");
  }

  public onSearchTermChanged(searchTerm: string): void {
    loggerInstance.log(
      "PaccsSymptomSearch.onSearchTermChanged() searchTerm: " + searchTerm
    );
    this.isLoading = true;
    this.paccsSymptomData
      .getTemplateBySymptom(searchTerm)
      .then(resp => {
        if (typeof resp === "string") {
          resp = JSON.parse(resp);
        }
        this.paccsSymptomModels = resp;
        this.$emit("onResults", [...resp]);
      })
      .catch(err => {
        loggerInstance.log("Error", err);
      })
      .finally(() => {
        this.isLoading = false;
      });
  }
}
</script>
