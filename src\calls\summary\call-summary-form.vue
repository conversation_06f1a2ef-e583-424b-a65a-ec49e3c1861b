<template>
  <div>
    <table class="grid--call-summary-display-form">
      <tr>
        <td class="grid--call-summary-display-label">Call Number</td>
        <td class="grid--call-summary-display-data">
          <span v-text="cleoCallSummary.CallNo"></span>
        </td>
        <td class="grid--call-summary-display-label">Patient Name</td>
        <td class="grid--call-summary-display-data">
          <span v-text="getPatientName + ' (' + getPatientAge + ')'"></span>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Callers Name</td>
        <td class="grid--call-summary-display-data">
          <span v-text="cleoCallSummary.CallCName"></span>
        </td>
        <td class="grid--call-summary-display-label">
          Relationship to patient
        </td>
        <td class="grid--call-summary-display-data">
          <span v-text="cleoCallSummary.CallCRel"></span>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Contact status</td>
        <td class="grid--call-summary-display-data">
          <span v-text="getContactStatus"></span>
        </td>
        <td class="grid--call-summary-display-label">Call status</td>
        <td class="grid--call-summary-display-data">
          <span v-text="getCallStatus"></span>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Symptoms</td>
        <td colspan="3" class="grid--call-summary-display-data">
          <span v-text="getSymptoms"></span>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Address</td>
        <td colspan="3" class="grid--call-summary-display-data">
          <span v-text="getAddress"></span>
        </td>
      </tr>

      <tr v-if="hasSms">
        <td class="grid--call-summary-display-label">SMS</td>
        <td colspan="3" class="grid--call-summary-display-data">
          <div v-html="callSummaryHtml.smsSentText(cleoCallSummary)"></div>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Call taken by</td>
        <td colspan="3" class="grid--call-summary-display-data">
          <span v-text="cleoCallSummary.CallCreatedBy"></span>
        </td>
      </tr>

      <tr>
        <td class="grid--call-summary-display-label">Alternative Tel</td>
        <td rowspan="3" class="grid--call-summary-display-data">
          <span v-text="getAltTel"></span>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";

import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { parseISO, format } from "date-fns";
import { factoryCallsCommonService } from "@/calls/common/calls-common-service";
import { CallSummaryHtml } from "@/calls/summary/call-summary-html";

const callSummaryService: CallSummaryService = new CallSummaryService();
// const callsCommonService = factoryCallsCommonService();

@Component({
  name: "call-summary-form",
  components: {}
})
export default class CallSummaryForm extends Vue {
  @Prop({
    default: () => {
      return callSummaryService.factoryCleoCallSummary();
    }
  })
  public readonly cleoCallSummary!: ICleoCallSummary;

  public callSummaryService: CallSummaryService = callSummaryService;
  public callsCommonService = factoryCallsCommonService();
  public callSummaryHtml: CallSummaryHtml = new CallSummaryHtml();

  public get getPatientName(): string {
    return this.callSummaryService.getPatientName(this.cleoCallSummary);
  }

  public get getPatientAge(): string {
    return this.callSummaryService.getPatientAge(this.cleoCallSummary);
  }

  public get getSymptoms(): string {
    return this.callSummaryService.getSymptoms(this.cleoCallSummary);
  }

  public get getAddress(): string | string[] {
    return this.callSummaryService.getAddress(this.cleoCallSummary, ", ");
  }

  public get getContactStatus(): string {
    let firstContact = "";
    let contactCode = "";
    if (
      this.cleoCallSummary.Call1StContact &&
      this.cleoCallSummary.Call1StContact.length > 0
    ) {
      firstContact =
        "1st contact: " +
        format(
          parseISO(this.cleoCallSummary.Call1StContact),
          "dd/MM/yyyy HH:mm"
        );
    }
    if (
      this.cleoCallSummary.PatientContactCode &&
      this.cleoCallSummary.PatientContactCode.length > 0
    ) {
      contactCode =
        this.cleoCallSummary.PatientContactCode +
        ", total contact attempts (" +
        this.cleoCallSummary.PatientContactCodeCount +
        ")";
    }
    return firstContact + " " + contactCode;
  }

  public get getAltTel(): string {
    return this.cleoCallSummary.CallTelNoAlt1 &&
      this.cleoCallSummary.CallTelNoAlt1 !== ""
      ? this.cleoCallSummary.CallTelNoAltType1 === ""
        ? "Type not specified."
        : this.cleoCallSummary.CallTelNoAltType1
      : "";
  }

  public get getCallStatus(): string {
    return this.callsCommonService.getStatusDisplay(
      this.cleoCallSummary.CallStatusValue
    );
  }

  public get hasSms(): boolean {
    return this.cleoCallSummary.SMS_HAS;
  }
}
</script>

<style scoped></style>
