import { reactive } from "@vue/composition-api";
import * as BrisdocAuditQuestionService from "@/calls/details/complete/brisdoc/ui/audit-questions/models/brisdoc-audit-question-service";
import { BrisDocAuditQuestionsInput } from "@/calls/details/complete/brisdoc/ui/audit-questions/models/brisdoc-audit-questions-models";
import * as BrisdocAuditQuestionsApi from "@/calls/details/complete/brisdoc/ui/audit-questions/api/brisdoc-audit-questions-api";

export function useBrisdocAuditQuestionsController(
  brisDocAuditQuestionsInput: BrisDocAuditQuestionsInput
) {
  const state = reactive(
    BrisdocAuditQuestionService.factoryBrisdocAuditQuestionsState()
  );

  function init() {
    // fetch data
    const proms = [];

    state.data.isLoading = true;
    proms.push(
      BrisdocAuditQuestionsApi.getAuditQuestions(
        brisDocAuditQuestionsInput.cleoClientService,
        brisDocAuditQuestionsInput.classification
      ).then(resp => {
        console.log("getAuditQuestions resp", resp);
        state.data.questions = resp;
      })
    );

    return Promise.all(proms).finally(() => {
      state.data.isLoading = false;
    });
  }

  return {
    state,
    init
  };
}
