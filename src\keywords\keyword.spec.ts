import { KeywordService } from "@/keywords/keyword-service";
import {
  ICleoBase,
  IKeywordsRefData,
  IKeywordsRefDataKey
} from "@/keywords/keywords-models";
import { KeywordData } from "@/keywords/keyword-data";
import { CLEO_CONFIG } from "@/common/config/config-";
import axios from "axios";
import https from "@/common/https";

const keywordService: KeywordService = new KeywordService();

describe("KeywordService", () => {
  it("mapKeywordsRefData", () => {
    const keywordsRefData: IKeywordsRefData = {
      bases: [
        {
          Id: 18,
          Name: "Ashford CCG Review Clinic"
        },
        {
          Id: 19,
          Name: "Ashford PCC"
        }
      ],
      failedcontactcodes: [
        {
          Id: 1,
          Message: "Answerphone-Messageleft"
        },
        {
          Id: 2,
          Message: "Answerphone-Messagenotleft"
        }
      ]
    };

    const res: IKeywordsRefDataKey = keywordService.mapKeywordsRefData(
      keywordsRefData
    );
    expect(res.bases && res.bases["19"].Name).toBe("Ashford PCC");
    expect(res.failedcontactcodes && res.failedcontactcodes["2"].Message).toBe(
      "Answerphone-Messagenotleft"
    );

    expect(
      (keywordService.getKeywordsRefObject(res, "bases", "19") as ICleoBase)
        .Name
    ).toBe("Ashford PCC");
  });

  it("CLEO_CONFIG.ADAPTER_URL", () => {
    expect(CLEO_CONFIG.ADAPTER_URL).toBe(
      "https://sqladapter.dev.int.apps.ic24.nhs.uk"
    );
  });

  // test("getCompOrgs", async () => {
    // return axios
    //   .get("https://sqladapter.dev.int.apps.ic24.nhs.uk")
    //   .then(resp => {
    //     expect(resp).toBe(33);
    //   });

    // return https.get(
    //   "https://sqladapter.dev.int.apps.ic24.nhs.uk/api/refdata/getReferenceData?tableName=classifications",
    //   {
    //     responseType: "json"
    //   }
    // );

    // return new KeywordData().getRefData(["classifications"]).then(resp => {
    //   const keywordsRefDataKey: IKeywordsRefDataKey = keywordService.mapKeywordsRefData(
    //     resp
    //   );
    //   expect(keywordsRefDataKey).toBe(true);
    // });
  // }, 5000);
});
