<template>
  <div>
    <!--    <fieldset>-->
    <!--      <label></label>-->
    <!--      <div>-->
    <!--        -->
    <!--      </div>-->
    <!--    </fieldset>-->

    <!--    <tr>-->
    <!--      <td><i>CareHome&nbsp;&nbsp;</i></td>-->
    <!--      <td>-->

    <!--      </td>-->
    <!--    </tr>-->

    <LoadingSpinner v-if="state.isLoading" />
    <template v-if="userPermissions.CAREHOME_SELECT">
      <span
        v-if="state.carHomes.length === 0 && state.careHomeSelected.id === 0"
        >No care home(s) available.</span
      >
      <CarehomeSelect
        v-if="state.carHomes.length > 0"
        :care-homes="state.carHomes"
        :care-home-default="state.careHomeSelected"
        v-on:selected="careHomeSelected"
      />
    </template>

    <!-- Most likely on an existing call.-->
    <template v-if="!userPermissions.CAREHOME_SELECT">
      <span v-text="getCareHomeDescription"></span>
    </template>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext,
  watch
} from "@vue/composition-api";
import CarehomeSelect from "@/carehome/carehome-select.vue";
import * as CareHomeData from "@/carehome/carehome-data";
import {
  ICareHome,
  ICareHomeSelectOnCallState
} from "@/carehome/carehome-models";
import * as CareHomeService from "@/carehome/carehome-service";
import * as CommonUtils from "@/common/common-utils";
import {
  CleoPermissionName,
  CleoPermissionsForRole,
  ICleoPermission
} from "@/permissions/permission-models";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { IService } from "@/common/services/services-models";
import { IObjectConcrete } from "@/calls/summary/call-summarry-models";

export default defineComponent({
  name: "carehome-select-oncall",
  components: { LoadingSpinner, CarehomeSelect },
  props: {
    userHasPermission: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    careHome: {
      type: Object as PropType<IObjectConcrete>,
      default: () => {
        return {
          Id: 0,
          Description: ""
        } as IObjectConcrete;
      }
    },
    userPermissions: {
      type: Object as PropType<Record<CleoPermissionName, ICleoPermission>>,
      default: () => {
        return {};
      }
    },
    service: {
      type: Object as PropType<IService>,
      default: () => {
        const service: IService = {
          id: 0,
          name: "OOH",
          serviceType: "OOH"
        };
        return service;
      }
    },
    isNewCall: {
      type: Boolean,
      default: () => {
        return true;
      }
    }
  },
  setup(
    props: {
      userHasPermission: boolean;
      careHome: IObjectConcrete;
      userPermissions: CleoPermissionsForRole;
      service: IService;
      isNewCall: boolean;
    },
    context: SetupContext
  ) {
    const careHomeDefault: ICareHome = CareHomeService.factoryCareHome();
    const careHomeSelectOnCallState: ICareHomeSelectOnCallState = {
      isLoading: false,
      carHomes: [],
      careHomeSelected: careHomeDefault
    };

    const state = reactive(careHomeSelectOnCallState);

    watch(
      () => props.careHome,
      (newValue: IObjectConcrete) => {
        if (newValue.Id !== state.careHomeSelected.id) {
          setCurrentlySelected();
        }
      },
      {
        immediate: true
      }
    );

    state.isLoading = true;

    const prom = props.userPermissions.CAREHOME_SELECT
      ? CareHomeData.getCareHomes(props.service)
      : Promise.resolve([]);

    prom
      .then(resp => {
        state.carHomes = resp;
        legacyUi();
        setCurrentlySelected();
      })
      .finally(() => {
        state.isLoading = false;
      });

    function careHomeSelected(careHome: ICareHome) {
      state.careHomeSelected = careHome;
      context.emit("selected", careHome);
    }

    /**
     * Just so all code in one place.  Vue 2 has restriction on root tag,
     * which is a hassle, so easiest, but slightly messy, is to hide the
     * ui elements on the call (there are all sorts of labels etc.)
     */
    function legacyUi() {
      let displayValue = "";
      if (props.isNewCall) {
        displayValue = props.userPermissions.CAREHOME_SELECT ? "" : "none";
      } else {
        displayValue = props.careHome.Id > 0 ? "" : "none";
      }

      [].forEach.call(
        document.querySelectorAll(".care-home-section"),
        (el: HTMLElement) => {
          el.style.display = displayValue;
        }
      );
    }

    function setCurrentlySelected() {
      if (props.careHome.Id === 0) {
        return;
      }

      const careHomeFromProp: ICareHome = {
        id: props.careHome.Id,
        name: props.careHome.Description,
        email: "",
        isEnabled: true
      };

      if (state.carHomes.length === 0 && props.careHome.Id > 0) {
        state.careHomeSelected = careHomeFromProp;
      }

      const careHomeFound = state.carHomes.find((careHome: ICareHome) => {
        return careHome.id === props.careHome.Id;
      });

      if (!careHomeFound) {
        state.carHomes.push(CommonUtils.simpleObjectClone(careHomeFromProp));
      }

      if (careHomeFound && careHomeFound.id > 0) {
        state.careHomeSelected = CommonUtils.simpleObjectClone(careHomeFound);
      }
      // if (state.carHomes.length === 0 && props.careHome.Id > 0) {
      //   state.careHomeSelected = careHomeFromProp;
      // }
    }

    const getCareHomeDescription = computed(() => {
      return (
        state.careHomeSelected.name +
        (state.careHomeSelected.email.length > 0
          ? " - " + state.careHomeSelected.email
          : "")
      );
    });

    return { state, careHomeSelected, getCareHomeDescription };
  }
});
</script>

<style scoped></style>
