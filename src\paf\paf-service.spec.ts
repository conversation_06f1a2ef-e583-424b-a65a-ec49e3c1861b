import { PafService } from "@/paf/paf-service";

const pafService = new PafService();

describe("PafService", () => {
  it("isValidPostCode", () => {
    expect(pafService.isValidPostCode("SW19 2BP")).toBe(true);
    expect(pafService.isValidPostCode("SW192BP")).toBe(true);
    expect(pafService.isValidPostCode("SW19 2B")).toBe(false);
    expect(pafService.isValidPostCode("SW 2B")).toBe(false);
  });
});
