<template>
  <div>
    <input
      class="grid-controller-messages--filter-text"
      v-model="filterText"
      placeholder="Filter..."
    />
    <table class="grid-controller-messages--table">
      <tr
        v-for="gridControllerStackMessage in messages"
        :key="gridControllerStackMessage.id"
        class="grid-controller-messages--row"
        :class="getRowCss(gridControllerStackMessage)"
      >
        <td v-text="getMessageTime(gridControllerStackMessage.time)"></td>
        <td v-text="gridControllerStackMessage.callNo"></td>
        <td v-text="gridControllerStackMessage.message"></td>
        <td v-text="gridControllerStackMessage.serviceName"></td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, ref } from "@vue/composition-api";
import { IGridControllerStackMessage } from "@/calls/grids/grid-controller/grid-controller";
import { IsoDateTimeUTC } from "@/common/common-models";
import { format, parseISO } from "date-fns";
import { CommonService } from "@/common/common-service";
import { SOCKET_ACTIONS, SocketActionType } from "@/socket/socket-controller";

const commonService: CommonService = new CommonService();

export default defineComponent({
  name: "grid-controller-messages",
  props: {
    gridControllerStackMessages: {
      type: Array as PropType<IGridControllerStackMessage[]>,
      default: []
    }
  },
  setup(props: { gridControllerStackMessages: IGridControllerStackMessage[] }) {
    const filterText = ref("");

    function getMessageTime(isoDateTime: IsoDateTimeUTC) {
      return format(parseISO(isoDateTime), "HH:mm:ss SSS");
    }

    const messages = computed<IGridControllerStackMessage[]>(() => {
      let messages = commonService.sortArray(
        "time",
        props.gridControllerStackMessages,
        "DESC"
      );

      if (filterText.value.length > 0) {
        messages = messages.filter(message => {
          return message.callNo.toString().indexOf(filterText.value) > -1;
        });
      }

      return messages;
    });

    function getRowCss(
      gridControllerStackMessage: IGridControllerStackMessage
    ):
      | ""
      | "grid-controller-messages--row-removed"
      | "grid-controller-messages--row-new"
      | string {
      const mapCss: Record<SocketActionType, string> = {
        [SOCKET_ACTIONS.ON_CASE_NEW]: "grid-controller-messages--row-new",
        [SOCKET_ACTIONS.ON_CASE_UPDATED]: "xx",
        [SOCKET_ACTIONS.ON_CASE_CLOSED]:
          "grid-controller-messages--row-removed",
        [SOCKET_ACTIONS.ON_CASE_REMOVED]:
          "grid-controller-messages--row-removed"
      };

      return mapCss[gridControllerStackMessage.messageType]
        ? mapCss[gridControllerStackMessage.messageType]
        : "";
    }

    return {
      getMessageTime,
      getRowCss,
      messages,
      filterText
    };
  }
});
</script>

<style>
.grid-controller-messages--filter-text {
  margin-bottom: 0.5em;
}

.grid-controller-messages--table {
  border-collapse: collapse;
  width: 500px;
}
.grid-controller-messages--row {
  border-top: 1px solid lightgrey;
  margin-top: 10px;
}

.grid-controller-messages--row-removed {
  background-color: #fae5e8;
}

.grid-controller-messages--row-new {
  background-color: #d7f6d9;
}
</style>
