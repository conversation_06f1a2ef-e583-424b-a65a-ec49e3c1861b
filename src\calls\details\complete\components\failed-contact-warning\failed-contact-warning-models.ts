import { ISimpleButtonInputValue } from "../../SimpleButtonSelecter.vue";
import { CompleteFinalAction } from "@/calls/details/complete/complete-models";

// export type FailedContactWarningType =
//   | "RETURN_TO_OPEN_CASE"
//   | "SAVE_AND_RETURN_TO_QUEUE";

// export type FailedContactWarningValue = FailedContactWarningType;

export const returnToOpenCaseButtonOption: ISimpleButtonInputValue<
  CompleteFinalAction,
  CompleteFinalAction
> = {
  id: "RETURN_TO_OPEN_CASE",
  description: "Return to Open Case",
  value: "RETURN_TO_OPEN_CASE"
};

export const saveAndReturnToQueueButtonOption: ISimpleButtonInputValue<
  CompleteFinalAction,
  CompleteFinalAction
> = {
  id: "SAVE_AND_RETURN_TO_QUEUE",
  description: "Save and Return to Queue",
  value: "SAVE_AND_RETURN_TO_QUEUE"
};

// export type FailedContactWarningType = Pick<
//   Record<
//     CompleteFinalAction,
//     ISimpleButtonInputValue<CompleteFinalAction, CompleteFinalAction>
//   >,
//   "RETURN_TO_OPEN_CASE" | "SAVE_AND_RETURN_TO_QUEUE"
// >;

// export type FailedContactWarningType = Pick<
//   CompleteFinalAction,
//   "RETURN_TO_OPEN_CASE" | "SAVE_AND_RETURN_TO_QUEUE"
// >;

export type FailedContactWarning = Partial<
  Record<
    CompleteFinalAction,
    ISimpleButtonInputValue<CompleteFinalAction, CompleteFinalAction>
  >
>;

export const FailedContactWarningTypeMap: FailedContactWarning = {
  RETURN_TO_OPEN_CASE: returnToOpenCaseButtonOption,
  SAVE_AND_RETURN_TO_QUEUE: saveAndReturnToQueueButtonOption
};
