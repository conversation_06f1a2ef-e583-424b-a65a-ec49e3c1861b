import { ISimpleButtonInputValue } from "./SimpleButtonSelecter.vue";
import { ContactType } from "./components/contactmade/contactmade-models";
import { IFailedContactSafeguarding } from "./failedcontact/failed-contact-models";
import {
  FailedContactReason,
  FailedContactReasonValue
} from "./failedcontact/failed-contact-reasons/failed-contact-models";
import {
  PatientReferredToFurtherActionType,
  PatientReferredToType
} from "@/calls/details/complete/components/patient-referred-to/patient-referred-to-models";
import { OutcomesOptions } from "@/calls/details/complete/components/outcomes/outcomes-service";
import { SimpleButtonTypeBoolean } from "@/calls/details/complete/simple-button-selector-models";
import { InsufficientContactAttemptType } from "@/calls/details/complete/components/insufficient-contact-attempts/insufficient-contact-attempts";
import {
  ClinicalValidationType,
  ClinicalValidationValue
} from "@/calls/details/complete/components/clinical-validation/clinical-validation-models";
import { ExitReason } from "@/calls/details/complete/failedcontact/exitreasons/exit-reasons-models";
import { IApiReadCode } from "@/calls/details/complete/components/readcodes/readcodes-models";
import { IsoDateTimeWithOffset } from "@/common/common-models";
import { BrisDocNonClinicalAndPrescribingState } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";
import { DynamicQuestionsOutput } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export type CompleteProcess = "COMPLETE_PROCESS" | "SAVE_AND_RETURN_PROCESS";

export type ProcessName =
  | "COMPLETE_111_CLINICAL"
  | "COMPLETE_CAS_CLINICAL"
  | "SAVE_AND_RETURN_111_CLINICAL"
  | "SAVE_AND_RETURN_CAS_CLINICAL";

export type StepAction = "BACK" | "NEXT";

// Complete list of all steps available
export type CompleteStepName =
  | "END_ASSESSMENT_CONFIRMATION"
  | "NON_CLINICAL_REASON"
  | "OUTCOMES"
  | "HOW_WAS_CASE_MANAGED"
  | "CONTACT_MADE"
  | "PATIENT_REFERRED_TO"
  | "READ_CODES"
  | "FAILED_CONTACT_REASON"
  | "FAILED_CONTACT_SAFEGUARDING"
  | "FAILED_CONTACT_WARNING"
  | "FAILED_CONTACT_RISK_ASSESSMENT"
  | "PATIENT_RISK_ASSESSMENT"
  | "INSUFFICIENT_CONTACT_ATTEMPTS"
  | "CLINICAL_VALIDATION"
  | "EXIT_REASON"
  | "TAXI"
  | "VULNERABILITY"
  | "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
  | "BRISDOC_AUDIT_QUESTIONS"
  | "FURTHER_ACTION"
  | "UNKNOWN";

// Complete list of all steps available for 111 complete process
export type CompleteStepName111 = Extract<
  CompleteStepName,
  | "END_ASSESSMENT_CONFIRMATION"
  | "CONTACT_MADE"
  | "OUTCOMES"
  | "FAILED_CONTACT_REASON"
  | "INSUFFICIENT_CONTACT_ATTEMPTS"
  | "FAILED_CONTACT_RISK_ASSESSMENT"
  | "CLINICAL_VALIDATION"
  | "TAXI"
  | "VULNERABILITY"
  | "UNKNOWN"
>;

// Complete list of all steps available for CAS complete process
export type CompleteStepNameCas = Extract<
  CompleteStepName,
  | "END_ASSESSMENT_CONFIRMATION"
  | "HOW_WAS_CASE_MANAGED"
  | "CONTACT_MADE"
  | "PATIENT_REFERRED_TO"
  | "READ_CODES"
  | "OUTCOMES"
  | "FAILED_CONTACT_RISK_ASSESSMENT"
  | "FAILED_CONTACT_REASON"
  | "FAILED_CONTACT_SAFEGUARDING"
  | "PATIENT_RISK_ASSESSMENT"
  | "INSUFFICIENT_CONTACT_ATTEMPTS"
  | "TAXI"
  | "VULNERABILITY"
  | "UNKNOWN"
>;

// Complete list of all steps available for 111 save and return process
export type SaveAndReturnStepName111 = Extract<
  CompleteStepName,
  "EXIT_REASON" | "FAILED_CONTACT_REASON" | "UNKNOWN"
>;

// Complete list of all steps available for CAS save and return process
export type SaveAndReturnStepNameCas = Extract<
  CompleteStepName,
  | "EXIT_REASON"
  | "HOW_WAS_CASE_MANAGED"
  | "FAILED_CONTACT_REASON"
  | "FAILED_CONTACT_WARNING"
  | "UNKNOWN"
>;

// Complete list of all steps available for non clinical complete process
export type NonClinicalCompleteStepName = Extract<
  CompleteStepName,
  "END_ASSESSMENT_CONFIRMATION" | "NON_CLINICAL_REASON" | "OUTCOMES" | "UNKNOWN"
>;

export interface IStep<STEP_ID> {
  stepId: STEP_ID;
  title: string;
  isValid: boolean;
  requiresValidation: boolean;
  validationMessages: IValidationMessage[];
}

export interface IValidationMessage {
  id: string | number;
  message: string;
}

export type BaseStepController<SomeState> = {
  state: SomeState;
  goto: (stepAction: StepAction) => void;
  cancel: () => void;
};

export type StepName = string;

export interface IBaseControllerState<STEP_NAME> {
  debug: boolean;
  isReady: boolean;
  isLoading: boolean;
  processName: "" | ProcessName;
  currentStep: STEP_NAME;
  steps: Record<string, IStep<STEP_NAME>>; //  TODO, TS foo not good enough, need to know how to set Record key as STEP_NAME
  validationMessages: IValidationMessage[];
  autoProgress: boolean; //  If a step is valid, auto progress to next step.
  isProcessComplete: boolean;
  hasProcessBeenCancelled: boolean;
  cacheStepState: boolean;
}

/**
 * If no contact is made a case can not be closed unless a minimum contact attempts have been made
 * and these need to be made at least every x mins.
 */
export interface IFailedContactConfig {
  attemptsRequired: number;
  minsInterval: number;
  seedTime?: IsoDateTimeWithOffset;
}

/**
 * Config to drive failed contact process.
 */
export interface IFailedContactComplete {
  config: IFailedContactConfig;
}

export type IUserResponseGeneric<Type, VALUE> =
  | ISimpleButtonInputValue<Type, VALUE>
  | ISimpleButtonInputValue<"", "">;

export interface IPatientReferredTo {
  referredTo: IUserResponseGeneric<
    PatientReferredToType,
    PatientReferredToType
  >;
  referredText: string;
  furtherActionGP: IUserResponseGeneric<
    PatientReferredToFurtherActionType,
    PatientReferredToFurtherActionType
  >;
  furtherActionGPText: string;
}

export interface IOutcomes {
  outcome: string;
  subOutcome: string;
  otherOutcome: string;
  outcomeOptions: OutcomesOptions;
}

export interface IFailedContactRiskAssessment {
  completeFinalAction: CompleteFinalAction;
}

export interface IPatientRiskAssessment {
  risk: IUserResponseGeneric<SimpleButtonTypeBoolean, SimpleButtonTypeBoolean>;
  actionTaken: string;
}

export type NonClinicalReasonType =
  | "Cancel Case"
  | "Non Clinical Input required"
  | "Other";

export interface INonClinicalReason {
  reason: IUserResponseGeneric<NonClinicalReasonType, NonClinicalReasonType>;
  comment: string;
}

export interface IReadCodesUserResponse {
  readCodesSelected: IApiReadCode[];
}

export type TaxiResponse = IUserResponseGeneric<
  SimpleButtonTypeBoolean,
  SimpleButtonTypeBoolean
>;

export interface IVulnerability {
  adult: IUserResponseGeneric<SimpleButtonTypeBoolean, SimpleButtonTypeBoolean>;
  child: IUserResponseGeneric<SimpleButtonTypeBoolean, SimpleButtonTypeBoolean>;
}

export type FailedContactReasonType = IUserResponseGeneric<
  FailedContactReason,
  FailedContactReasonValue
>;

export interface IUserResponse {
  howManaged: ISimpleButtonInputValue<string, string>;
  contactMade: ISimpleButtonInputValue<string, string>;
  nonClinicalReason: INonClinicalReason;
  patientReferredTo: IPatientReferredTo;
  outcomes: IOutcomes;
  failedContactReason: FailedContactReasonType;
  failedContactRiskAssessment: IFailedContactRiskAssessment;
  patientRiskAssessment: IPatientRiskAssessment;
  insufficientContactAttempts: IUserResponseGeneric<
    InsufficientContactAttemptType,
    InsufficientContactAttemptType
  >;
  safeguarding: IFailedContactSafeguarding;
  clinicalValidation: IUserResponseGeneric<
    ClinicalValidationType,
    ClinicalValidationValue
  >;
  exitReason: IUserResponseGeneric<ExitReason, ExitReason>;
  failedContactWarning: IUserResponseGeneric<
    CompleteFinalAction,
    CompleteFinalAction
  >;
  readCodes: IReadCodesUserResponse;
  taxi: TaxiResponse;
  vulnerability: IVulnerability;
  BRISDOC_NON_CLINICAL_AND_PRESCRIBING: BrisDocNonClinicalAndPrescribingState;
  BRISDOC_AUDIT_QUESTIONS: DynamicQuestionsOutput;
}

//  This is really used as an override.  User can be on a "Complete" journey but
//  some decision point might require e.g. a "Save and Return" journey.
export type CompleteFinalAction =
  | ""
  | "RETURN_TO_OPEN_CASE"
  | "COMPLETE"
  | "SAVE_AND_RETURN_TO_QUEUE"
  | "FURTHER_ACTION_REQUIRED";

export type UserOptionsOutcomesOverrideKey =
  | ""
  | "NON_CLINICAL_REASON__CANCEL_CASE"
  | "NON_CLINICAL_REASON__NON_CLINICAL_INPUT_REQUIRED"
  | "NON_CLINICAL_REASON__OTHER";

export interface ICompleteControllerState
  extends IBaseControllerState<CompleteStepName> {
  failedContactConfig: IFailedContactComplete;
  doesPassFailedContactValidation: boolean;
  steps: Record<Partial<CompleteStepName>, IStep<Partial<CompleteStepName>>>;
  completeUserStepsConfig: ICompleteUserStepsConfig<Partial<CompleteStepName>>;
  debug: boolean;
  saveAndReturnControllerState: null;
  userResponse: IUserResponse;
  userOptions: {
    END_ASSESSMENT_CONFIRMATION: {
      options: {
        messages: string[];
      };
    };
    exitReason: {
      options: ISimpleButtonInputValue<ExitReason, ExitReason>[];
      forUser: ISimpleButtonInputValue<ExitReason, ExitReason>[]; //  Dependant on step, may need to filter options.
    };
    OUTCOMES: {
      overRideKey: UserOptionsOutcomesOverrideKey;
    };
  };
  dx: {
    usingDx: string;
    isDxValidationRequired: boolean;
  };
  ui: {
    disableNext: boolean;
    disableBack: boolean;
    disableCancel: boolean;
    disableComplete: boolean;
    buttons: {
      complete: {
        text: string;
      };
    };
  };
  finalAction: CompleteFinalAction;
}

export interface ICompleteUserStepsConfig<STEP_NAME> {
  steps: Partial<Record<string, IStep<STEP_NAME>>>;
  validateMap: Partial<Record<string, () => IValidationMessage[]>>;
  gotoNextMap: Partial<Record<string, () => void>>;
  gotoBackMap: Partial<Record<string, () => void>>;
}
