import * as CommonUtils from "@/common/common-utils";

describe("CommonUtils", () => {
  it("isOnlyNumbers", () => {
    expect(CommonUtils.isOnlyNumbers("123e")).toBe(false);

    expect(CommonUtils.isOnlyNumbers("123")).toBe(true);

    expect(CommonUtils.isOnlyNumbers(" 123")).toBe(false);

    expect(CommonUtils.isOnlyNumbers("123.34")).toBe(true);

    expect(CommonUtils.isOnlyNumbers("123.34.89")).toBe(false);

    expect(CommonUtils.isOnlyNumbers("123.34", false)).toBe(false);

    expect(CommonUtils.isOnlyNumbers("123.34", true)).toBe(true);

    expect(CommonUtils.isOnlyNumbers("1,023.34")).toBe(true);

    expect(CommonUtils.isOnlyNumbers("100030387833")).toBe(true);
  });

  it("isEmpty", () => {
    // <Strings>
    expect(CommonUtils.isEmpty("")).toBe(true);
    expect(CommonUtils.isEmpty("s")).toBe(false);
    expect(CommonUtils.isEmpty("   ")).toBe(true);
    expect(CommonUtils.isEmpty(" x  ")).toBe(false);
    //  Tab...
    expect(CommonUtils.isEmpty("  ")).toBe(true);
    // </Strings>

    // <Arrays>
    expect(CommonUtils.isEmpty([1, "e"])).toBe(false);
    expect(CommonUtils.isEmpty([undefined, null])).toBe(false);
    expect(CommonUtils.isEmpty([])).toBe(true);
    // </Arrays>
  });

  // write test for toIsoStringWithLocalOffsetWithoutTemporal
  it("toIsoStringWithLocalOffsetWithoutTemporal", () => {
    // const date = new Date("2023-10-01T12:00:00Z");
    // const result = CommonUtils.toIsoStringWithLocalOffsetWithoutTemporal(date);
    // expect(result).toBe("2023-10-01T13:00:00+01:00");

    expect(
      CommonUtils.toIsoStringWithLocalOffsetWithoutTemporal(
        new Date("2023-10-01T12:00:00Z")
      )
    ).toBe("2023-10-01T13:00:00+01:00");

    // a date not in BST
    expect(
      CommonUtils.toIsoStringWithLocalOffsetWithoutTemporal(
        new Date("2023-11-01T12:00:00Z")
      )
    ).toBe("2023-11-01T12:00:00+00:00");

    // a date in early Feb
    expect(
      CommonUtils.toIsoStringWithLocalOffsetWithoutTemporal(
        new Date("2023-02-01T12:00:00Z")
      )
    ).toBe("2023-02-01T12:00:00+00:00");
  });

  it("toLocalISOWithOffset", () => {
    expect(CommonUtils.toLocalISOWithOffset("07/10/2024 14:07:03").iso).toBe(
      "2024-10-07T14:07:03+01:00"
    );

    // expect(
    //   CommonUtils.toLocalISOWithOffset("2021-05-31T23:00:00.000+01:00")
    // ).toBe("2021-05-31T23:00:00+01:00");

    // early Feb
    expect(CommonUtils.toLocalISOWithOffset("02/02/2023 14:07:03").iso).toBe(
      "2023-02-02T14:07:03+00:00"
    );

    expect(CommonUtils.toLocalISOWithOffset("08/05/2025 8:05:08").error).toBe(
      "date not in format DD/MM/YYYY: 08/05/2025 8:05:08"
    );
  });

  it("properCaseQuestionConstant", () => {
    expect(
      CommonUtils.properCaseQuestionConstant("Out_Of_Hours_Professional_Line")
    ).toBe("Out Of Hours Professional Line");
    expect(CommonUtils.properCaseQuestionConstant("MENTAL_HEALTH")).toBe(
      "Mental Health"
    );
    expect(CommonUtils.properCaseQuestionConstant("PAEDIATRICS")).toBe(
      "Paediatrics"
    );
  });

  describe("isMobileNumber", () => {
    it("returns true for valid UK mobile numbers", () => {
      expect(CommonUtils.isMobileNumber("**********9")).toBe(true);
      expect(CommonUtils.isMobileNumber("+447123456789")).toBe(true);
      // expect(CommonUtils.isMobileNumber("447123456789")).toBe(true);
    });

    it("returns false for invalid mobile numbers", () => {
      expect(CommonUtils.isMobileNumber("06123456789")).toBe(false); // wrong start
      expect(CommonUtils.isMobileNumber("**********")).toBe(false); // too short
      expect(CommonUtils.isMobileNumber("**********90")).toBe(false); // too long
      expect(CommonUtils.isMobileNumber("abcdefg")).toBe(false); // not numbers
      expect(CommonUtils.isMobileNumber("+44712345678a")).toBe(false); // alpha char
      expect(CommonUtils.isMobileNumber("")).toBe(false); // empty string
    });
  });

  describe("isTelephoneNumber", () => {
    it("returns true for valid UK landline numbers", () => {
      expect(CommonUtils.isTelephoneNumber("02079460000")).toBe(true);
      expect(CommonUtils.isTelephoneNumber("01632960000")).toBe(true);
      expect(CommonUtils.isTelephoneNumber("+442079460000")).toBe(true);
      // expect(CommonUtils.isTelephoneNumber("442079460000")).toBe(true);
    });

    it("returns false for invalid landline numbers", () => {
      expect(CommonUtils.isTelephoneNumber("12345")).toBe(false); // too short
      expect(CommonUtils.isTelephoneNumber("0207946000000")).toBe(false); // too long
      expect(CommonUtils.isTelephoneNumber("abcdefghijk")).toBe(false); // not numbers
      expect(CommonUtils.isTelephoneNumber("+44207946000a")).toBe(false); // alpha char
      expect(CommonUtils.isTelephoneNumber("")).toBe(false); // empty string
    });
  });
});
