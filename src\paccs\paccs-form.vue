<template>
  <div>
    <div v-if="!isPaccsReady">PACCS has not yet been initialised.</div>
    <div v-if="isPaccsReady">
      <div style="display: inline-block;">
        <button style="padding: 5px;" v-on:click="scrollToNotes">
          Scroll to Notes
        </button>

        <div class="adapter-button--separator"></div>
      </div>

      <div
        class="paccs-form--early-exit-buttons"
        v-if="showEarlyExitRestartButtons"
      >
        <button
          class="adapter-button adapter-width-8 adapter-button--red"
          v-on:click="restartTriageGetConfirmation"
        >
          Restart Triage
        </button>

        <div class="adapter-button--separator"></div>

        <button
          class="adapter-button adapter-width-8 adapter-button--red"
          v-on:click="earlyExit"
        >
          Early Exit
        </button>

        <div class="adapter-button--separator"></div>

        <button
          class="adapter-button adapter-width-8 adapter-button--blue"
          v-if="showResendNotesButton"
          v-on:click="resendCaseNotes"
        >
          Send Referral
        </button>
      </div>

      <div v-show="showSection === 'PACCS'">
        <div>
          <div
            class="paccs-form--change-buttons"
            style="display: inline-block;"
            v-if="formState.pathways.changingAnswer"
          >
            <button
              class="adapter-button adapter-width-8 adapter-button--orange"
              v-on:click="changeAnswer"
            >
              Change Answer
            </button>

            <div class="adapter-button--separator"></div>

            <button
              class="adapter-button adapter-width-8 adapter-button--orange"
              v-on:click="returnNoChange"
            >
              Return No Change
            </button>

            <div class="adapter-button--separator"></div>
          </div>
        </div>

        <PaccsSymptomSearchSection
          :is-disabled="formState.pathways.changingAnswer"
          :add-case-record-response="paccsStoreState.addCaseRecordResponse"
          class="paccs-form--symptom-search"
          v-on:onSelected="onSymptomSearchSelected"
        />

        <PaccsSymptomTabs
          class="paccs-form--symptom-tabs"
          :condition-tabs="paccsFormInternal.conditionTabsArray"
          :show-close-button="!formState.pathways.changingAnswer"
          v-on:tabSelected="symptomTabSelected"
          v-on:tabDeSelected="symptomTabDeSelected"
        ></PaccsSymptomTabs>

        <PaccsConditionTabBody
          v-show="conditionTabSelected.templateId.length > 0"
          class="paccs-form--symptom-body"
          :is-tab-body-disabled="formState.pathways.changingAnswer"
          :condition-tab="conditionTabSelected"
          :triage-record-condition-hydration="triageRecordsSelected"
        ></PaccsConditionTabBody>
      </div>

      <div
        v-if="showSection === 'PATHWAYS' || formState.pathways.changingAnswer"
      >
        <PathwaysIframe
          :pathways-payload="paccsStoreState.pathwaysPayload"
          :hide-but-keep-pathways-session-open="
            formState.pathways.changingAnswer
          "
          :show-mask="paccsStoreState.pathways.showMask"
          :mask-message="paccsStoreState.pathways.showMaskMessage"
          v-on:message="pathwaysMessage"
        ></PathwaysIframe>
      </div>

      <div
        v-if="showSection === 'PATHWAYS_REPORT'"
        class="paccs-form--pathways-report"
      >
        <PathwaysReport
          :pathways-case-id="formState.pathways.caseId"
          :report-html="formState.pathways.reportHtml"
        ></PathwaysReport>
      </div>

      <PaccsTriageRecordsReport
        :show-links="showSection === 'PACCS'"
        :triage-records="getTriageRecords"
      />
    </div>

    <CleoModal
      header-message="Tab removal"
      :body-message="formState.tabRemove.message"
      v-if="formState.tabRemove.showModal"
      v-on:closePrimary="formState.tabRemove.showModal = false"
    >
      <div slot="button-close-secondary"></div>
    </CleoModal>

    <CleoModal
      header-message="Restart Triage"
      v-if="formState.restartTriage.showModal"
      body-message="Are you sure you would like to restart triage?"
      v-on:closePrimary="restartTriage"
      v-on:closeSecondary="formState.restartTriage.showModal = false"
    >
    </CleoModal>

    <CleoModal
      header-message="Change Answer(s)"
      v-if="formState.pathways.changingAnswerGetConfirmation"
      body-message="This will clear all currently answered Pathways questions. Are you sure you would like to change answers?"
      v-on:closePrimary="changeAnswer"
      v-on:closeSecondary="
        formState.pathways.changingAnswerGetConfirmation = false
      "
    >
    </CleoModal>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  PropType,
  reactive,
  ref,
  watch
} from "@vue/composition-api";
import { PaccsService } from "@/paccs/paccs-service";
import { loggerInstance } from "@/common/Logger";
import PaccsSymptomSearchSection from "@/paccs/paccs-symptom-search/paccs-symptom-search-section.vue";
import PaccsSymptomTabs from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs.vue";
import {
  IPaccsForm,
  IPaccsFormState,
  ITriageRecord,
  ITriageRecordCondition,
  ITriageRecordTemplate,
  PaccsAgeGroup,
  PaccsGender
} from "@/paccs/paccs-models";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import { CommonService } from "@/common/common-service";
import { IConditionTab } from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs-models";
import { PaccsSymptomService } from "@/paccs/paccs-symptom-search/paccs-symptom-service";
import PaccsConditionTabBody from "@/paccs/paccs-condition-tab-body/paccs-condition-tab-body.vue";
import { PaccsConditionData } from "@/paccs/paccs-condition-tab-body/paccs-condition-data";
import { PaccsConditionService } from "@/paccs/paccs-condition-tab-body/paccs-condition-service";
import { appStore } from "@/store/store";
import {
  FORM_SECTION,
  IPaccsStoreState,
  PACCS_STORE_CONST
} from "@/paccs/paccs-store";
import PaccsTriageRecordsReport from "@/paccs/paccs-triage-records/paccs-triage-records-report.vue";
import PathwaysIframe from "@/paccs/pathways/pathways-iframe.vue";
import PathwaysReport from "@/paccs/pathways/pathways-report.vue";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import {
  IPathwaysReturnData,
  PathwaysExitType
} from "@/paccs/pathways/pathways-models";
import { PaccsData } from "@/paccs/paccs-data";
import { PaccsLegacyFormController } from "@/paccs/paccs-legacy-form-controller";
import { usePaccsController } from "@/paccs/usePaccsController";

const paccsService: PaccsService = new PaccsService();
const paccsSymptomService: PaccsSymptomService = new PaccsSymptomService();
const commonService: CommonService = new CommonService();
const paccsConditionService: PaccsConditionService = new PaccsConditionService();
const paccsController = usePaccsController();
const paccsLegacyFormController = new PaccsLegacyFormController();

export default defineComponent({
  name: "paccs-form",
  props: {
    paccsFormData: {
      required: true,
      propType: Object as PropType<IPaccsForm>
    }
  },
  components: {
    PathwaysReport,
    PaccsSymptomSearchSection,
    PaccsSymptomTabs,
    PaccsConditionTabBody,
    PaccsTriageRecordsReport,
    PathwaysIframe,
    CleoModal
  },
  setup(props: { paccsFormData: IPaccsForm }) {
    const store = appStore;

    const paccsData = new PaccsData();

    const paccsStoreState = computed<IPaccsStoreState>(() => {
      return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
    });

    const formState = reactive<IPaccsFormState>(
      paccsService.factoryPaccsFormState()
    );

    const paccsFormInternal: IPaccsForm = reactive(
      commonService.simpleObjectClone(props.paccsFormData)
    );
    const conditionTabSelected: IConditionTab = reactive(
      paccsService.factoryConditionTab(
        paccsSymptomService.factoryPaccsSymptomModel()
      )
    );

    const triageRecordsSelected = ref<ITriageRecordCondition[]>([]);

    const paccsController = usePaccsController();
    paccsController.init();

    // paccsLegacyFormController.setUpEventListenersConsultFields();

    onBeforeMount(() => {
      loggerInstance.log("paccs-form>>>>>>>>>>>>>>>>>>>>>>mounted!");
      Object.assign(
        paccsFormInternal,
        commonService.simpleObjectClone(props.paccsFormData)
      );
    });

    watch(
      () => props.paccsFormData,
      (newValue: IPaccsForm) => {
        loggerInstance.log(
          "paccs-condition-tab-body>>>>>>>>>>>>>>>>>>>watch!!!!"
        );
        Object.assign(
          paccsFormInternal,
          commonService.simpleObjectClone(props.paccsFormData)
        );
      }
    );

    function getConditionTab(templateId: string): IConditionTab | null {
      const pred = (conditionTab: IConditionTab) => {
        return conditionTab.templateId === templateId;
      };
      return commonService.findFirst(
        pred,
        paccsFormInternal.conditionTabsArray
      );
    }

    function onSymptomSearchSelected(
      paccsSymptomModel: IPaccsSymptomModel
    ): void {
      const cond = getConditionTab(paccsSymptomModel.templateId);
      if (cond) {
        loggerInstance.log(
          "paccs-form onSymptomSearchSelected() tab: " +
            paccsSymptomModel.templateId +
            "-" +
            paccsSymptomModel.templateName +
            " already exists"
        );
      } else {
        const conditionTab: IConditionTab = paccsService.factoryConditionTab(
          paccsSymptomModel
        );
        paccsFormInternal.conditionTabsArray.push(conditionTab);
        // if (paccsFormInternal.conditionTabsArray.length === 1) {
        symptomTabSelected(paccsSymptomModel.templateId);
        // }
        auditTriageRecordTemplate(conditionTab, true);
      }
    }

    watch(
      () => paccsStoreState.value.reportTriageClicked,
      (newValue: ITriageRecord) => {
        loggerInstance.log("reportTriageClicked>>>>>>>>>>>>>>>>>>>watch!!!!");
        symptomTabSelected(newValue.pathwayId);
      }
    );

    function symptomTabSelected(id: string): void {
      const conditionTab = getConditionTab(id);
      if (
        conditionTab &&
        paccsService.canSwitchConditionTab(paccsFormInternal, conditionTab).isOK
      ) {
        triageRecordsSelected.value = paccsService.getActiveTriageRecordConditionsForTab(
          conditionTab,
          paccsStoreState.value.triageRecords
        );
        setConditionTabSelected(conditionTab);
        setSymptomTabSelectedState(id, true);
      }
    }

    function auditTriageRecordTemplate(
      conditionTab: IConditionTab,
      isSelecting: boolean
    ): void {
      const triageRecordTemplate: ITriageRecordTemplate = paccsService.createTriageRecordTemplate(
        paccsStoreState.value.addCaseRecordResponse.caseId,
        paccsStoreState.value.pathwaysDataset,
        conditionTab.paccsSymptomModel
      );
      triageRecordTemplate.actionId = isSelecting ? 2 : 1; //  2=next, 1=back
      triageRecordTemplate.includeInReport = triageRecordTemplate.actionId > 1;

      store.dispatch(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_ADD_TRIAGE_QUESTION,
        triageRecordTemplate
      );
    }

    function symptomTabDeSelected(id: string): void {
      const conditionTab = getConditionTab(id);
      if (conditionTab) {
        const canConditionTabBeRemoved = paccsService.canConditionTabBeRemoved(
          conditionTab,
          paccsStoreState.value.triageRecords
        );

        if (!canConditionTabBeRemoved.isOK) {
          formState.tabRemove.message =
            "Options still enabled on tab: " +
            canConditionTabBeRemoved.simpleMessage;
          formState.tabRemove.showModal = true;
          return;
        }
        auditTriageRecordTemplate(conditionTab, false);
        setSymptomTabSelectedState(id, false);
        if (paccsFormInternal.conditionTabsArray.length === 0) {
          Object.assign(
            conditionTabSelected,
            paccsService.factoryConditionTab(
              paccsSymptomService.factoryPaccsSymptomModel()
            )
          );
        }
        return;
      }
    }

    function setSymptomTabSelectedState(
      templateId: string,
      toBeSelected: boolean
    ): void {
      if (toBeSelected) {
        paccsFormInternal.conditionTabsArray.forEach(conditionTab => {
          conditionTab.isSelected = conditionTab.templateId === templateId;
          if (conditionTab.templateId === templateId) {
            if (conditionTab.paccsConditionOrder.length === 0) {
              loadTab(conditionTab);
            } else {
              loggerInstance.log(
                "paccs-form onSymptomSearchSelected() tab: " +
                  conditionTab.templateId +
                  "-" +
                  " already loaded server data"
              );
            }
          }
        });
      } else {
        paccsFormInternal.conditionTabsArray = paccsFormInternal.conditionTabsArray.filter(
          conditionTab => {
            return conditionTab.templateId !== templateId;
          }
        );
      }
    }

    function loadTab(conditionTab: IConditionTab): void {
      function getGenderFromLegacy(): PaccsGender {
        return window.CallControllerClient.getFieldValue("CallMF") === "Female"
          ? "Female"
          : "Male";
      }

      const gender = getGenderFromLegacy();
      const ageGroup = paccsService.getPaccsAgeGroup(
        window.CallControllerClient.getCallDob()
      );

      loadTabData(conditionTab, gender, ageGroup).then(condTab => {
        if (condTab) {
          updateConditionModel(condTab);
        }
      });
    }

    function loadTabData(
      conditionTab: IConditionTab,
      gender: PaccsGender,
      ageGroup: PaccsAgeGroup
    ): Promise<IConditionTab | void> {
      const conditionTabInternal = commonService.simpleObjectClone(
        conditionTab
      );
      conditionTabInternal.isLoading = true;

      return new PaccsConditionData()
        .getConditionByTemplateId(
          conditionTabInternal.templateId,
          gender,
          ageGroup
        )
        .then(response => {
          if (typeof response === "string") {
            response = JSON.parse(response);
          }
          conditionTabInternal.paccsConditionOrder = paccsConditionService.sortPaccsConditionOrder(
            response
          );
          conditionTabInternal.isLoading = false;
          return conditionTabInternal;
        })
        .catch((err: Error) => {
          loggerInstance.error(err.message);
        })
        .finally(() => {
          return conditionTabInternal;
        });
    }

    function updateConditionModel(conditionTab: IConditionTab): void {
      paccsFormInternal.conditionTabsArray = paccsFormInternal.conditionTabsArray.map(
        condTab => {
          if (condTab.templateId === conditionTab.templateId) {
            return conditionTab;
          }
          return condTab;
        }
      );
      if (conditionTabSelected.templateId === conditionTab.templateId) {
        // Object.assign(conditionTabSelected, conditionTab);
        setConditionTabSelected(conditionTab);
      }
    }

    function setConditionTabSelected(conditionTab: IConditionTab): void {
      Object.assign(conditionTabSelected, conditionTab);
    }

    const isPaccsReady = computed<boolean>(() => {
      return paccsStoreState.value.addCaseRecordResponse.caseId.length > 0;
    });

    const getTriageRecords = computed<ITriageRecord[]>(() => {
      return paccsStoreState.value.triageRecords;
    });

    const showSection = computed<FORM_SECTION>(() => {
      return paccsStoreState.value.showSection &&
        paccsStoreState.value.showSection.length > 0
        ? paccsStoreState.value.showSection
        : "PACCS";
    });

    function setShowSection(section: FORM_SECTION) {
      store.commit(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
        section
      );
    }

    /**
     * When Pathways closes do something with returned data...will migrate this in eventually.
     * @param pathwaysReturnData
     */
    function pathwaysMessage(pathwaysReturnData: IPathwaysReturnData) {
      const exitType = pathwaysReturnData.ExitType;

      formState.pathways.pathwaysReturnData = commonService.simpleObjectClone(
        pathwaysReturnData
      );

      if (exitType === "POSTNOTES") {
        // formState.pathways.showMask = true;
        // formState.pathways.showMaskMessage =
        //   "Temporarily disabled while notes completed.";

        store.commit(
          PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
            "/" +
            PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_MASK,
          {
            showMask: true,
            showMaskMessage: "Temporarily disabled while notes completed."
          }
        );

        return;
      }

      if (exitType === "FINISHED") {
        return;
      }

      if (
        exitType === "RETURN" ||
        exitType === "EARLYEXIT" ||
        exitType === "END"
      ) {
        //  But the old function decodes...doh!...so re-encode it.
        window.CallControllerClient._onPathwaysCompleted(
          JSON.stringify(pathwaysReturnData)
        );
      }

      if (exitType === "CHANGE") {
        //  Sets tabs in READ mode.
        formState.pathways.changingAnswer = true;

        //  TODO switch to appropriate Tab....from PW payload
      }

      const showPathwaysReportWhenExitingWith: PathwaysExitType[] = [
        "END",
        "EARLYEXIT"
      ];
      if (showPathwaysReportWhenExitingWith.indexOf(exitType) > -1) {
        formState.pathways.caseId = pathwaysReturnData.CaseId;
        formState.pathways.reportHtml = pathwaysReturnData.ReportTextHtml;
        setShowSection("PATHWAYS_REPORT");
        paccsLegacyFormController.setPaccsHasStarted(
          true,
          formState.pathways.caseId
        );

        if (
          pathwaysReturnData?.LastItkMessage?.Type === "Itk" &&
          pathwaysReturnData?.LastItkMessage.Status === "Success"
        ) {
          //  If ITK successful, then do not allow user to enter any more notes.
          paccsController.setLegacyConsultFieldsEnabledState(false);
        }
      } else {
        setShowSection("PACCS");
        paccsLegacyFormController.setPaccsHasStarted(
          false,
          formState.pathways.caseId
        );
      }
    }

    function restartTriage() {
      formState.restartTriage.isLoading = true;
      formState.pathways.changingAnswer = false;
      store
        .dispatch(
          PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
            "/" +
            PACCS_STORE_CONST.PACCS__ACTION_RESET
        )
        .then(() => {
          paccsFormInternal.conditionTabsArray = [];
          setConditionTabSelected(
            paccsService.factoryConditionTab(
              paccsSymptomService.factoryPaccsSymptomModel()
            )
          );
        })
        .finally(() => {
          formState.restartTriage.showModal = false;
          formState.restartTriage.isLoading = false;
        });
    }

    function restartTriageGetConfirmation() {
      formState.restartTriage.showModal = true;
    }

    function changeAnswerGetConfirmation() {
      formState.pathways.changingAnswerGetConfirmation = true;
    }

    function changeAnswer(): void {
      paccsData
        .restartPathwaysQuestions(
          paccsStoreState.value.addCaseRecordResponse.caseId
        )
        .then(() => {
          formState.pathways.changingAnswer = false;
          formState.pathways.changingAnswerGetConfirmation = false;
        });
    }

    function returnNoChange() {
      store.commit(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
        "PATHWAYS"
      );
      formState.pathways.changingAnswer = false;
    }

    function earlyExit() {
      store.dispatch(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_EARLY_EXIT
      );
    }

    function resendCaseNotes(): void {
      store
        .dispatch(
          PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
            "/" +
            PACCS_STORE_CONST.PACCS__ACTION_POST_NOTES,
          paccsController.getCurrentClinicianNotes()
        )
        .finally(() => {
          // formState.pathways.showMask = false;
          // formState.pathways.showMaskMessage = "";
          formState.pathways.pathwaysReturnData = null;
          store.commit(
            PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
              "/" +
              PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_MASK,
            {
              showMask: false,
              showMaskMessage: ""
            }
          );
        });
    }

    const showResendNotesButton = computed<boolean>(() => {
      return !!(
        formState.pathways.pathwaysReturnData &&
        formState.pathways.pathwaysReturnData.ExitType === "POSTNOTES"
      );
    });

    const showEarlyExitRestartButtons = computed<boolean>(() => {
      if (
        formState.pathways.pathwaysReturnData &&
        formState.pathways.pathwaysReturnData.ExitType === "FINISHED"
      ) {
        return false;
      }
      if (showSection.value === "PATHWAYS_REPORT") {
        return false;
      }
      return true;
    });

    // watch(
    //   () => paccsController.state.currentClinicianNotes,
    //   (newValue: string, oldValue: string) => {
    //     loggerInstance.log(
    //       "paccs-form.paccsController currentClinicianNotes changed >>>>>>>>>>>>>>>>>>>watch!!!!"
    //     );
    //     if (showSection.value === "PATHWAYS") {
    //       loggerInstance.log(
    //         "paccs-form.paccsController currentClinicianNotes changed SEND."
    //       );
    //       store.dispatch(
    //         PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
    //           "/" +
    //           PACCS_STORE_CONST.PACCS__ACTION_POST_NOTES,
    //         paccsController.state.currentClinicianNotes
    //       );
    //     }
    //   }
    // );

    function scrollToNotes() {
      window.CallControllerClient.showConsultSection("CONSULTS");
    }

    return {
      formState,
      paccsStoreState,
      showSection,
      isPaccsReady,
      paccsLegacyFormController,
      getTriageRecords,
      paccsFormInternal,
      onSymptomSearchSelected,
      symptomTabSelected,
      symptomTabDeSelected,
      conditionTabSelected,
      triageRecordsSelected,
      pathwaysMessage,
      restartTriageGetConfirmation,
      restartTriage,
      earlyExit,
      returnNoChange,
      changeAnswerGetConfirmation,
      changeAnswer,
      showEarlyExitRestartButtons,
      paccsController,
      resendCaseNotes,
      showResendNotesButton,
      scrollToNotes
    };
  }
});
</script>

<style>
.paccs-form--symptom-search {
  margin-bottom: 1em;
  border: 1px solid #2980b9;
}

.paccs-form--symptom-tabs {
}

.paccs-form--symptom-body {
  padding: 1em;
  border: 1px solid #2980b9;
  overflow: auto;
}

.paccs-form--early-exit-buttons {
  margin-bottom: 1em;
  display: inline-block;
}

.paccs-form--change-buttons {
  margin-bottom: 1em;
}

.paccs-form--pathways-report {
  padding-left: 1em;
}
</style>
