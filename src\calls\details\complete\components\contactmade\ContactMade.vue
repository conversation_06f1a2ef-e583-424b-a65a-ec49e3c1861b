<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">
      Please confirm is successful contact has been made.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <div class="ic24-vertical-spacer-large"></div>

    <div>
      There have been (<span v-text="failedContacts.length"></span>) contact
      attempts.
    </div>

    <div>
      The system requires you to make
      <span v-text="failedContactComplete.config.attemptsRequired"></span>
      attempts at
      <span v-text="failedContactComplete.config.minsInterval"></span>
      minute intervals.
    </div>

    <div class="ic24-flex-row ic24-flex-gap ic24-justify-flex-space-evenly">
      <Ic24Button
        class="complete-step--big-button complete-step--big-button-green"
        :class="
          value.id === 'CONTACT_MADE'
            ? 'complete-step--big-button-selected'
            : ''
        "
        :title="getContactType.CONTACT_MADE.description"
        @click="onContactSelected('CONTACT_MADE')"
      />
      <div class="ic24-flex-column ic24-flex-gap-large ic24-flex-end">
        <Ic24Button
          class="complete-step--big-button complete-step--big-button-red"
          :class="
            value.id === 'CONTACT_FAILURE'
              ? 'complete-step--big-button-selected'
              : ''
          "
          :title="getContactType.CONTACT_FAILURE.description"
          button-style="destructive"
          @click="onContactSelected('CONTACT_FAILURE')"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext
} from "@vue/composition-api";
import {
  IFailedContactComplete,
  IStep,
  IUserResponseGeneric
} from "../../complete-models";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { ContactType, ContactTypeMap } from "./contactmade-models";
import { IFailedContact } from "@/calls/details/call-details-models";
import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "ContactMade",
  components: { CompleteStepHeader, Ic24Button },
  props: {
    step: {
      type: Object as PropType<IStep<"CONTACT_MADE">>,
      required: true
    },
    value: {
      type: Object as PropType<IUserResponseGeneric<ContactType, ContactType>>,
      required: true
    },
    failedContactComplete: {
      type: Object as PropType<IFailedContactComplete>,
      required: true
    },
    failedContacts: {
      type: Array as PropType<IFailedContact[]>,
      required: true
    },
    isDxValidationRequired: {
      type: Boolean as PropType<boolean>,
      required: false
    }
  },
  setup(
    props: {
      step: IStep<"CONTACT_MADE">;
      value: IUserResponseGeneric<ContactType, ContactType>;
      failedContactComplete: IFailedContactComplete;
      failedContacts: IFailedContact[];
      isDxValidationRequired: boolean;
    },
    context: SetupContext
  ) {
    const contactType = ContactTypeMap;

    const getContactType = computed(() => {
      if (props.isDxValidationRequired) {
        const contactTypeLocal = simpleObjectClone(contactType);
        contactTypeLocal.CONTACT_MADE.description =
          "YES - Validated Contact Made";
        contactTypeLocal.CONTACT_FAILURE.description =
          "NO - Unvalidated Contact Failure";
        return contactTypeLocal;
      }
      return contactType;
    });

    function onContactSelected(contactTypeMade: ContactType) {
      console.log(
        "ContactMade.onContactSelected: " + contactTypeMade,
        contactType[contactTypeMade]
      );
      context.emit("input", contactType[contactTypeMade]);
    }

    return { getContactType, onContactSelected };
  }
});
</script>

<style scoped></style>
