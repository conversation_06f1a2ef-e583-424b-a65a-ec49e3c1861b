<template>
  <a href="#" :class="permClass" @click.prevent="handleClick">
    <span
      v-text="cleoAction && cleoAction.title ? cleoAction.title : '?'"
    ></span>
  </a>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from "@vue/composition-api";
import { ICleoAction } from "@/common/cleo-common-models";

export default defineComponent({
  name: "CallSummaryToolbarLink",

  props: {
    keyPerms: {
      type: Object as PropType<Record<string, boolean>>,
      required: true
    },
    cleoAction: {
      type: Object as PropType<ICleoAction>,
      required: true
    }
  },

  setup(props, { emit }) {
    const permClass = computed(() => {
      if (!props.keyPerms) {
        console.log("CallSummaryToolbarLink permClass props.keyPerms is null", {
          keyPerms: props.keyPerms,
          cleoAction: props.cleoAction
        });
        return "not-active";
      }
      if (!props.keyPerms[props.cleoAction.permissionName]) {
        console.log(
          "CallSummaryToolbarLink permClass props.keyPerms[props.cleoAction.permissionName] is null",
          {
            keyPerms: props.keyPerms,
            cleoAction: props.cleoAction
          }
        );
        return "not-active";
      }
      return props.keyPerms[props.cleoAction.permissionName]
        ? ""
        : "not-active";
    });

    const handleClick = () => {
      emit("onLinkClick", props.cleoAction);
    };

    return {
      permClass,
      handleClick
    };
  }
});
</script>
