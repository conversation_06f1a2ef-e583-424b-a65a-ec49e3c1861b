import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";
import {
  factoryGridFilters,
  IGridFilter
} from "@/calls/grids/grid-filter/grid-filter-models";
import { filter } from "ag-grid-community/dist/lib/utils/array";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CLEO_CLIENT_SERVICE } from "@/common/common-models";

const gridFilterService: GridFilterService = new GridFilterService();

describe("gridFilterService", () => {
  it("applyFiltersToCall", () => {
    const call: ICleoCallSummary = ({
      sourceSystemId: 1,
      unid: "",
      name: "",
      Info: "",
      IsLocked: "",
      CallNo: **********,
      CallID: "01hVPINCKy58rzE6n4QuK9lWz",
      CallNHSNo: "",
      CallService: "Norfolk and Wisbech 111",
      IUC_Contract: "",
      CallServiceOriginal: "",
      CallMF: "Female",
      CallDobIso: null,
      CallAddress1: "323 Victoria Road",
      CallAddress2: "Lowestoft",
      CallAddress3: "Suffolk",
      CallAddress4: "",
      CallTown: "Ashford",
      CallPostCode: "NR33 9LS",
      CallClassification: "Nurse Advice",
      CC: "",
      CSC: "",
      WalkIn: "",
      CallUrgentYN: "Yes",
      Call1stContact: "2019-12-18T11:02:37+00:00",
      Call1stContactPathways: null,
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "",
      ApplyBreach: "1",
      CallReceivedISO: null,
      CallReceivedTimeISO: null,
      BreachWarnActualTime: "2020-03-06T16:36:44+00:00",
      BreachActualTime: "2020-03-06T16:46:44+00:00",
      BreachPriority: "3",
      CallWithBaseAckTime: null,
      CallReceivedTime: "2019-12-18T11:02:37+00:00",
      CallAppointmentTime: null,
      CallArrivedTime: null,
      CallAge: "49",
      CallAgeClass: "yrs",
      CallDoctorNameCN: "",
      PatientName: "",
      CallTriaged: "",
      CallSymptoms: "Diabetes Blood Sugar Problem (Declared)",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      CallSubClassification: {
        Id: 2,
        Description: "PCV Possible Corona Virus"
      },
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "",
      CallCallback: "0",
      CallTelNo_R: "02070033002",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TESTER",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallAllergies: "",
      CallSpecialInstructions: "",
      CallLast72: "1",
      XRayDisp: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx34",
      CHFinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FinalDispositionCode: "Dx34",
      FinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: false,
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      Comfort_SENT_SERVICE_TIME: null,
      Comfort_SMS_TIME: "",
      AFT_ORDER: null,
      CasePatientName: "TESTER Test",
      CallGenderId: 2,
      CasePatientAge: "12/11/1970 00:00:00 +00:00",
      CaseNew: "True",
      CaseWarmTransferFail: "",
      CaseCallBackRequired: "False",
      CasePDSTraceVerified: "False",
      CaseAlternativeTelephoneFlag: "False",
      CasePatientGender: "",
      CasePatientPostCode: "",
      CasePatientTelephoneNumber: "",
      CaseSGCode: "",
      CasePriority: "",
      ComfortCallResponse: null
    } as any) as ICleoCallSummary;

    const filters: IGridFilter[] = [];

    const gridFilterCov19: IGridFilter = factoryGridFilters.COV19;

    filters.push(gridFilterCov19);
    expect(gridFilterService.applyFiltersToCall(call, filters)).toBe(true);

    const gridFilterTown: IGridFilter = factoryGridFilters.TOWN;

    filters.push(gridFilterTown);
    expect(gridFilterService.applyFiltersToCall(call, filters)).toBe(true);
  });

  it("applyFilters", () => {
    const call_1_COV19: ICleoCallSummary = ({
      sourceSystemId: 1,
      unid: "",
      name: "",
      Info: "",
      IsLocked: "",
      CallNo: **********,
      CallID: "01hVPINCKy58rzE6n4QuK9lWz",
      CallNHSNo: "",
      CallService: "Norfolk and Wisbech 111",
      IUC_Contract: "",
      CallServiceOriginal: "",
      CallMF: "Female",
      CallDobIso: null,
      CallAddress1: "323 Victoria Road",
      CallAddress2: "Lowestoft",
      CallAddress3: "Suffolk",
      CallAddress4: "",
      CallTown: "Ashford",
      CallPostCode: "NR33 9LS",
      CallClassification: "Nurse Advice",
      CC: "",
      CSC: "",
      WalkIn: "",
      CallUrgentYN: "Yes",
      Call1stContact: "2019-12-18T11:02:37+00:00",
      Call1stContactPathways: null,
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "",
      ApplyBreach: "1",
      CallReceivedISO: null,
      CallReceivedTimeISO: null,
      BreachWarnActualTime: "2020-03-06T16:36:44+00:00",
      BreachActualTime: "2020-03-06T16:46:44+00:00",
      BreachPriority: "3",
      CallWithBaseAckTime: null,
      CallReceivedTime: "2019-12-18T11:02:37+00:00",
      CallAppointmentTime: null,
      CallArrivedTime: null,
      CallAge: "49",
      CallAgeClass: "yrs",
      CallDoctorNameCN: "",
      PatientName: "",
      CallTriaged: "",
      CallSymptoms: "Diabetes Blood Sugar Problem (Declared)",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      CallSubClassification: {
        Id: 2,
        Description: "PCV Possible Corona Virus"
      },
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "",
      CallCallback: "0",
      CallTelNo_R: "02070033002",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TESTER",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallAllergies: "",
      CallSpecialInstructions: "",
      CallLast72: "1",
      XRayDisp: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx34",
      CHFinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FinalDispositionCode: "Dx34",
      FinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: false,
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      Comfort_SENT_SERVICE_TIME: null,
      Comfort_SMS_TIME: "",
      AFT_ORDER: null,
      CasePatientName: "TESTER Test",
      CallGenderId: 2,
      CasePatientAge: "12/11/1970 00:00:00 +00:00",
      CaseNew: "True",
      CaseWarmTransferFail: "",
      CaseCallBackRequired: "False",
      CasePDSTraceVerified: "False",
      CaseAlternativeTelephoneFlag: "False",
      CasePatientGender: "",
      CasePatientPostCode: "",
      CasePatientTelephoneNumber: "",
      CaseSGCode: "",
      CasePriority: "",
      ComfortCallResponse: null
    } as any) as ICleoCallSummary;
    const call_2_COV19: ICleoCallSummary = ({
      sourceSystemId: 1,
      unid: "",
      name: "",
      Info: "",
      IsLocked: "",
      CallNo: **********,
      CallID: "01hVPINCKy58rzE6n4QuK9lWz",
      CallNHSNo: "",
      CallService: "Norfolk and Wisbech 111",
      IUC_Contract: "",
      CallServiceOriginal: "",
      CallMF: "Female",
      CallDobIso: null,
      CallAddress1: "323 Victoria Road",
      CallAddress2: "Lowestoft",
      CallAddress3: "Suffolk",
      CallAddress4: "",
      CallTown: "Ashford",
      CallPostCode: "NR33 9LS",
      CallClassification: "Nurse Advice",
      CC: "",
      CSC: "",
      WalkIn: "",
      CallUrgentYN: "Yes",
      Call1stContact: "2019-12-18T11:02:37+00:00",
      Call1stContactPathways: null,
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "",
      ApplyBreach: "1",
      CallReceivedISO: null,
      CallReceivedTimeISO: null,
      BreachWarnActualTime: "2020-03-06T16:36:44+00:00",
      BreachActualTime: "2020-03-06T16:46:44+00:00",
      BreachPriority: "3",
      CallWithBaseAckTime: null,
      CallReceivedTime: "2019-12-18T11:02:37+00:00",
      CallAppointmentTime: null,
      CallArrivedTime: null,
      CallAge: "49",
      CallAgeClass: "yrs",
      CallDoctorNameCN: "",
      PatientName: "",
      CallTriaged: "",
      CallSymptoms: "Diabetes Blood Sugar Problem (Declared)",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      CallSubClassification: {
        Id: 2,
        Description: "PCV Possible Corona Virus"
      },
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "",
      CallCallback: "0",
      CallTelNo_R: "02070033002",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TESTER",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallAllergies: "",
      CallSpecialInstructions: "",
      CallLast72: "1",
      XRayDisp: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx34",
      CHFinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FinalDispositionCode: "Dx34",
      FinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: false,
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      Comfort_SENT_SERVICE_TIME: null,
      Comfort_SMS_TIME: "",
      AFT_ORDER: null,
      CasePatientName: "TESTER Test",
      CallGenderId: 2,
      CasePatientAge: "12/11/1970 00:00:00 +00:00",
      CaseNew: "True",
      CaseWarmTransferFail: "",
      CaseCallBackRequired: "False",
      CasePDSTraceVerified: "False",
      CaseAlternativeTelephoneFlag: "False",
      CasePatientGender: "",
      CasePatientPostCode: "",
      CasePatientTelephoneNumber: "",
      CaseSGCode: "",
      CasePriority: "",
      ComfortCallResponse: null
    } as any) as ICleoCallSummary;
    const call_3_NON_COV19: ICleoCallSummary = ({
      sourceSystemId: 1,
      unid: "",
      name: "",
      Info: "",
      IsLocked: "",
      CallNo: **********,
      CallID: "01hVPINCKy58rzE6n4QuK9lWz",
      CallNHSNo: "",
      CallService: "Norfolk and Wisbech 111",
      IUC_Contract: "",
      CallServiceOriginal: "",
      CallMF: "Female",
      CallDobIso: null,
      CallAddress1: "323 Victoria Road",
      CallAddress2: "Lowestoft",
      CallAddress3: "Suffolk",
      CallAddress4: "",
      CallTown: "Ashford",
      CallPostCode: "NR33 9LS",
      CallClassification: "Nurse Advice",
      CC: "",
      CSC: "",
      WalkIn: "",
      CallUrgentYN: "Yes",
      Call1stContact: "2019-12-18T11:02:37+00:00",
      Call1stContactPathways: null,
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "",
      ApplyBreach: "1",
      CallReceivedISO: null,
      CallReceivedTimeISO: null,
      BreachWarnActualTime: "2020-03-06T16:36:44+00:00",
      BreachActualTime: "2020-03-06T16:46:44+00:00",
      BreachPriority: "3",
      CallWithBaseAckTime: null,
      CallReceivedTime: "2019-12-18T11:02:37+00:00",
      CallAppointmentTime: null,
      CallArrivedTime: null,
      CallAge: "49",
      CallAgeClass: "yrs",
      CallDoctorNameCN: "",
      PatientName: "",
      CallTriaged: "",
      CallSymptoms: "Diabetes Blood Sugar Problem (Declared)",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      CallSubClassification: { Id: null, Description: "" },
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "",
      CallCallback: "0",
      CallTelNo_R: "02070033002",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TESTER",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallAllergies: "",
      CallSpecialInstructions: "",
      CallLast72: "1",
      XRayDisp: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx34",
      CHFinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FinalDispositionCode: "Dx34",
      FinalDispositionDescription:
        "Speak to Clinician from our service within 30 minutes",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: false,
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      Comfort_SENT_SERVICE_TIME: null,
      Comfort_SMS_TIME: "",
      AFT_ORDER: null,
      CasePatientName: "TESTER Test",
      CallGenderId: 2,
      CasePatientAge: "12/11/1970 00:00:00 +00:00",
      CaseNew: "True",
      CaseWarmTransferFail: "",
      CaseCallBackRequired: "False",
      CasePDSTraceVerified: "False",
      CaseAlternativeTelephoneFlag: "False",
      CasePatientGender: "",
      CasePatientPostCode: "",
      CasePatientTelephoneNumber: "",
      CaseSGCode: "",
      CasePriority: "",
      ComfortCallResponse: null
    } as any) as ICleoCallSummary;

    const filters: IGridFilter[] = [];

    const gridFilterCov19: IGridFilter = factoryGridFilters.COV19;

    const calls = [call_1_COV19, call_2_COV19, call_3_NON_COV19];

    filters.push(gridFilterCov19);
    expect(gridFilterService.applyFilters(calls, filters).length).toBe(2);
  });

  it("isCleoClientServiceMatched", () => {
    const gridFilterService: GridFilterService = new GridFilterService();

    const cleoCallSummary: ICleoCallSummary = ({
      cleoClientService: "FOLLOW UP"
    } as any) as ICleoCallSummary;

    expect(
      gridFilterService.isCleoClientServiceMatched(cleoCallSummary, [
        "TOXIC INGESTION"
      ])
    ).toBe(false);

    cleoCallSummary.cleoClientService = "Follow Up";
    expect(
      gridFilterService.isCleoClientServiceMatched(cleoCallSummary, [
        "FOLLOW UP"
      ])
    ).toBe(true);

    cleoCallSummary.cleoClientService = "FOLLOW_UP";
    expect(
      gridFilterService.isCleoClientServiceMatched(cleoCallSummary, [
        "FOLLOW UP"
      ])
    ).toBe(true);

    cleoCallSummary.cleoClientService = "FOLLOW UP";
    expect(
      gridFilterService.isCleoClientServiceMatched(cleoCallSummary, [
        "FOLLOW UP"
      ])
    ).toBe(true);

    cleoCallSummary.cleoClientService = "FOLLOW UP";
    expect(
      gridFilterService.isCleoClientServiceMatched(cleoCallSummary, [
        "FOLLOW UP",
        "FRAILTY"
      ])
    ).toBe(true);
  });
});
