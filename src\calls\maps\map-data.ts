// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { IAdapterPagedResponse } from "@/common/common-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";

export class MapData {
  public getCalls(): Promise<IAdapterPagedResponse<ICleoCallSummary>> {
    return Promise.resolve({
      CurrentPage: 1,
      RecordsPerPage: 1000,
      TotalPages: 1,
      TotalRecords: 3,
      Records: [
        {
          AllViewInclude: "",
          AllViewExclude: "0",
          ApplyBreach: true,
          BreachActualTime: "2021-07-13T15:14:13+01:00",
          BreachPreActualTime: "2021-07-13T14:59:13+01:00",
          BreachPriority: 1,
          BreachWarnActualTime: "2021-07-13T15:09:13+01:00",

          CallAddress1: "Church Rd",
          CallAddress2: "<PERSON><PERSON>on",
          CallAddress3: "Norwich",
          CallAddress4: "",
          CallTown: "",
          CallPostCode: "NR12 8UG",
          Lat: 52.71179706593955,
          Long: 1.4108625102982146,

          CallAge: 96,
          CallAgeClass: "yrs",
          CallAppointmentTime: null,
          CallArrivedTime: null,
          CallCallback: 0,
          CallClassification: {
            Id: 10,
            Description: "Nurse Advice"
          },
          CallCName: "Kayleigh - Care Coord",
          CallCreatedBy: "Paula Irvine",
          CallCRel: "Carer",
          CallDobIso: null,
          CallDoctorName: "CN=Antony Parsons/O=sehnp",
          CallForename: "Harold",
          CallGenderId: 1,
          CallInformationalSubOutcomes: "",
          CallInformationalOutcomes: "",
          CallInformationalOutcomesComment: "",
          CallNhsNo: "",
          CallNo: **********,
          CallReceivedTime: "2021-07-13T14:38:47+01:00",
          CallService: {
            Id: 3,
            Description: "Norfolk and Wisbech 111",
            Type: "111"
          },
          CallServiceOriginal: "",
          CallStatusValue: 1,
          CallSubClassification: {
            Id: null,
            Description: ""
          },
          CallSurname: "WARNER",
          CallSymptoms:
            "**Disorientated**, Had a fall today 13.07.21, very lucid prior to fall. Skin tear to elbow. On blood thinning medication. Unable to reliably answer the questions, Carer feels this is not like him, usually able to respond to questions.",
          CallTelNoR: "",
          CallTelNoAlt1: "",
          CallTelNoAltType1: "",
          CallUrgentYn: true,
          CallWithBaseAckTime: "2021-07-13T15:42:39+01:00",
          CallWarmTransferred: false,
          Call1StContact: "2021-07-13T15:42:31+01:00",
          Call1StContactPathways: "2021-07-13T14:42:28+01:00",
          CaseWarmTransferFail: "",
          ChFinalDispositionCode: "Dx32",
          ChFinalDispositionDescription:
            "Speak to a Clinician from our service Immediately",
          CliniHighPriority: true,
          ComfortSentServiceTime: null,
          ComfortSmsTime: "",
          ComfortCallResponse: null,
          CourtesyUser: "",
          CourtesyTime: "",
          CourtesyCount: 0,
          CourtesyContact: false,
          DutyBase: "",
          DispatchVehicle: "Delta 2",
          FinalDispositionCode: "Dx32",
          FinalDispositionDescription: "",
          FinalDispositionDescription: "",
          IsLocked: "CN=Antony Parsons/O=sehnp",
          Itk111Online: false,
          LastFailedContactTime: null,
          IucContract: {
            Id: null,
            Description: "",
            Type: ""
          },
          PathwaysCaseId: "02ab5558-3ff5-4bd8-8e30-c01dc4d32279",
          PatientContactCode: "",
          PatientContactCodeCount: 0,
          PdsTracedAndVerified: true,
          StartConsultationPerformed: true,
          WalkIn: false
        } as ICleoCallSummary,
        {
          AllViewInclude: "",
          AllViewExclude: "0",
          ApplyBreach: true,
          BreachActualTime: "2021-07-13T15:19:27+01:00",
          BreachPreActualTime: "2021-07-13T14:49:27+01:00",
          BreachPriority: 3,
          BreachWarnActualTime: "2021-07-13T15:09:27+01:00",

          CallAddress1: "60 Station Rd S",
          CallAddress2: "Belton",
          CallAddress3: "Great Yarmouth",
          CallAddress4: "",
          CallTown: "",
          CallPostCode: "NR31 9AA",
          Lat: 52.564932766212465,
          Long: 1.6576224788016425,

          CallAge: 43,
          CallAgeClass: "yrs",
          CallAppointmentTime: null,
          CallArrivedTime: null,
          CallCallback: 0,
          CallClassification: {
            Id: 10,
            Description: "Nurse Advice"
          },
          CallCName: "",
          CallCreatedBy: "Ambika Gaha",
          CallCRel: "Patient",
          CallDobIso: null,
          CallDoctorName: "",
          CallForename: "Ciprian",
          CallGenderId: 1,
          CallInformationalSubOutcomes: "",
          CallInformationalOutcomes: "",
          CallInformationalOutcomesComment: "",
          CallNhsNo: "",
          CallNo: **********,
          CallReceivedTime: "2021-07-13T14:38:53+01:00",
          CallService: {
            Id: 1,
            Description: "South Essex 111",
            Type: "111"
          },
          CallServiceOriginal: "",
          CallStatusValue: 1,
          CallSubClassification: {
            Id: 8,
            Description: "Requires Closing"
          },
          CallSurname: "POP",
          CallSymptoms:
            "high temp, headache, chilles. complete isolation today- but still not feeling well enough. main concern tiredness. pt terminated the call in middle of the assessment due to length of triage, called back no answers- no voicemail option to leave worsening advice.",
          CallTelNoR: "",
          CallTelNoAlt1: "",
          CallTelNoAltType1: "",

          CallUrgentYn: false,
          CallWithBaseAckTime: null,
          CallWarmTransferred: null,
          Call1StContact: "2021-07-13T15:11:55+01:00",
          Call1StContactPathways: "2021-07-13T14:41:38+01:00",
          CaseWarmTransferFail: "",
          ChFinalDispositionCode: "Dx108",
          ChFinalDispositionDescription:
            "Call is closed with no further action needed",
          CliniHighPriority: false,
          ComfortSentServiceTime: null,
          ComfortSmsTime: "",
          ComfortCallResponse: null,
          CourtesyUser: "",
          CourtesyTime: "",
          CourtesyCount: 0,
          CourtesyContact: false,
          DutyBase: "",
          DispatchVehicle: "",
          FinalDispositionCode: "Dx108",
          FinalDispositionDescription: "",
          IsLocked: "",
          Itk111Online: false,
          LastFailedContactTime: "2021-07-13T15:29:03+01:00",
          IucContract: {
            Id: null,
            Description: "",
            Type: ""
          },
          PathwaysCaseId: "77d23186-6457-4d80-8a6f-d22c6d8855b5",
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 2,
          PdsTracedAndVerified: true,
          StartConsultationPerformed: true,
          WalkIn: false
        } as ICleoCallSummary,
        {
          AllViewInclude: "",
          AllViewExclude: "0",
          ApplyBreach: true,
          BreachActualTime: "2021-07-13T15:36:43+01:00",
          BreachPreActualTime: "2021-07-13T15:21:43+01:00",
          BreachPriority: 1,
          BreachWarnActualTime: "2021-07-13T15:31:43+01:00",

          CallAddress1: "Glenmore Gardens",
          CallAddress2: "Norwich",
          CallAddress3: "",
          CallAddress4: "",
          CallTown: "",
          CallPostCode: "NR3 2RW",
          Lat: 52.648303778511355,
          Long: 1.2803665974551084,

          CallAge: 2,
          CallAgeClass: "yrs",
          CallAppointmentTime: null,
          CallArrivedTime: null,
          CallCallback: 0,
          CallClassification: {
            Id: 10,
            Description: "Nurse Advice"
          },
          CallCName: "dad - steven Lay",
          CallCreatedBy: "Theresa Pounds",
          CallCRel: "Relative or Friend",
          CallDobIso: null,
          CallDoctorName: "CN=Allan Grace/O=sehnp",
          CallForename: "James",
          CallGenderId: 1,
          CallInformationalSubOutcomes: "",
          CallInformationalOutcomes: "",
          CallInformationalOutcomesComment: "",
          CallNhsNo: "",
          CallNo: **********,
          CallReceivedTime: "2021-07-13T15:08:34+01:00",
          CallService: {
            Id: 1,
            Description: "South Essex 111",
            Type: "111"
          },
          CallServiceOriginal: "",
          CallStatusValue: 1,
          CallSubClassification: {
            Id: null,
            Description: ""
          },
          CallSurname: "HUNTER",
          CallSymptoms:
            "brother sent home from school due to positive COVID in school bubble - patient has had a temp since last night - has had more than recommended dose of calpol - taking some fluids but becoming a bit more listless - 30mg more than recommended dose since last night  SCR was viewed for this call.",
          CallTelNoR: "",
          CallTelNoAlt1: "",
          CallTelNoAltType1: "",
          CallUrgentYn: true,
          CallWithBaseAckTime: null,
          CallWarmTransferred: false,
          Call1StContact: "2021-07-13T15:23:50+01:00",
          Call1StContactPathways: "2021-07-13T15:10:30+01:00",
          CaseWarmTransferFail: "",
          ChFinalDispositionCode: "Dx325",
          ChFinalDispositionDescription:
            "Speak to a Clinician from our service Immediately - Toxic Ingestion/Inhalation",
          CliniHighPriority: false,
          ComfortSentServiceTime: null,
          ComfortSmsTime: "",
          ComfortCallResponse: null,
          CourtesyUser: "",
          CourtesyTime: "",
          CourtesyCount: 0,
          CourtesyContact: false,
          DutyBase: "",
          DispatchVehicle: "",
          FinalDispositionCode: "Dx325",
          FinalDispositionDescription: "",
          IsLocked: "Allan Grace",
          Itk111Online: false,
          LastFailedContactTime: "2021-07-13T15:25:27+01:00",
          IucContract: {
            Id: null,
            Description: "",
            Type: ""
          },
          PathwaysCaseId: "957a9373-08b4-4bd8-9bc3-782deb74950a",
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 1,
          PdsTracedAndVerified: true,
          StartConsultationPerformed: true,
          WalkIn: false
        } as ICleoCallSummary,
        {
          AllViewInclude: "",
          AllViewExclude: "0",
          ApplyBreach: true,
          BreachActualTime: "2021-07-13T15:36:43+01:00",
          BreachPreActualTime: "2021-07-13T15:21:43+01:00",
          BreachPriority: 1,
          BreachWarnActualTime: "2021-07-13T15:31:43+01:00",

          CallAddress1: "2 Panxworth Rd",
          CallAddress2: "South Walsham",
          CallAddress3: "Norwich",
          CallAddress4: "",
          CallTown: "",
          CallPostCode: "NR13 6DY",
          Lat: 52.66391914750446,
          Long: 1.4920853425383356,

          CallAge: 2,
          CallAgeClass: "yrs",
          CallAppointmentTime: null,
          CallArrivedTime: null,
          CallCallback: 0,
          CallClassification: {
            Id: 10,
            Description: "Nurse Advice"
          },
          CallCName: "dad - steven Lay",
          CallCreatedBy: "Theresa Pounds",
          CallCRel: "Relative or Friend",
          CallDobIso: null,
          CallDoctorName: "CN=Allan Grace/O=sehnp",
          CallForename: "James",
          CallGenderId: 1,
          CallInformationalSubOutcomes: "",
          CallInformationalOutcomes: "",
          CallInformationalOutcomesComment: "",
          CallNhsNo: "",
          CallNo: **********,
          CallReceivedTime: "2021-07-13T15:08:34+01:00",
          CallService: {
            Id: 1,
            Description: "South Essex 111",
            Type: "111"
          },
          CallServiceOriginal: "",
          CallStatusValue: 1,
          CallSubClassification: {
            Id: null,
            Description: ""
          },
          CallSurname: "Bloggs",
          CallSymptoms:
            "brother sent home from school due to positive COVID in school bubble - patient has had a temp since last night - has had more than recommended dose of calpol - taking some fluids but becoming a bit more listless - 30mg more than recommended dose since last night  SCR was viewed for this call.",
          CallTelNoR: "",
          CallTelNoAlt1: "",
          CallTelNoAltType1: "",
          CallUrgentYn: true,
          CallWithBaseAckTime: null,
          CallWarmTransferred: false,
          Call1StContact: "2021-07-13T15:23:50+01:00",
          Call1StContactPathways: "2021-07-13T15:10:30+01:00",
          CaseWarmTransferFail: "",
          ChFinalDispositionCode: "Dx325",
          ChFinalDispositionDescription:
            "Speak to a Clinician from our service Immediately - Toxic Ingestion/Inhalation",
          CliniHighPriority: false,
          ComfortSentServiceTime: null,
          ComfortSmsTime: "",
          ComfortCallResponse: null,
          CourtesyUser: "",
          CourtesyTime: "",
          CourtesyCount: 0,
          CourtesyContact: false,
          DutyBase: "",
          DispatchVehicle: "Delta 2",
          FinalDispositionCode: "Dx325",
          FinalDispositionDescription: "",
          IsLocked: "Allan Grace",
          Itk111Online: false,
          LastFailedContactTime: "2021-07-13T15:25:27+01:00",
          IucContract: {
            Id: null,
            Description: "",
            Type: ""
          },
          PathwaysCaseId: "957a9373-08b4-4bd8-9bc3-782deb74950a",
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 1,
          PdsTracedAndVerified: true,
          StartConsultationPerformed: true,
          WalkIn: false
        } as ICleoCallSummary
      ]
    });
  }
}
