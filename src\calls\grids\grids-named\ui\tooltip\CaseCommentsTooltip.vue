<template>
  <div class="custom-tooltip--case-comments">
    <div v-html="getCaseComments"></div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { ITooltipParams } from "ag-grid-community";
import CallSummaryForm from "@/calls/summary/call-summary-form.vue";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CallSummaryHtml } from "@/calls/summary/call-summary-html";

@Component({
  name: "CaseCommentsTooltip",
  components: { CallSummaryForm }
})
export default class CallSummaryFormTooltip extends Vue {
  public params!: ITooltipParams;

  public callSummaryService: CallSummaryService = new CallSummaryService();
  public cleoCallSummary: ICleoCallSummary = this.callSummaryService.factoryCleoCallSummary();

  public created() {
    this.cleoCallSummary = this.params.data;
  }

  public get getCaseComments(): string {
    const caseSummaryHtml = new CallSummaryHtml();
    return caseSummaryHtml.getCaseCommentsToolTip(this.cleoCallSummary);
  }
}
</script>

<style>
.custom-tooltip--case-comments {
  position: absolute;
  width: fit-content;
  height: fit-content;
  border: 1px solid lightgrey;
  background-color: white;
  overflow: hidden;
  pointer-events: none;
  transition: opacity 1s;
  padding: 10px;
}
</style>
