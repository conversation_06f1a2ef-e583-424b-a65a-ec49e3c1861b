import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import {
  CLEO_CLIENT_SERVICE,
  DominoNameCN,
  DxCode,
  IsoDateTimeOffset
} from "@/common/common-models";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";

export type GRID_FILTER_NAME =
  | "QUICK"
  | "COV19"
  | "NOT_COV19"
  | "TOWN"
  | "CLASSIFICATION"
  | "CLEO_CLIENT_SERVICE"
  | "SUB_CLASSIFICATION"
  | "DX"
  | "ASSIGNED_TO"
  | "BREACHED"
  | "PDS_TRACED_AND_VERIFIED"
  | "TOXIC_INGESTION_AND_EMPTY"
  | "AMBULANCE_CLEO_CLIENT_SERVICES"
  | "ED_VALIDATION_CLEO_CLIENT_SERVICES"
  | "MY_CASE_AND_PARAMEDIC_ON_SCENE"
  | "REQUIRES_VALIDATION"
  | "FOLLOW_UP";

//  Why didn't I just pass filter state (IGridFilterUserInput) into all of them !!!?!?!?!?!?
export interface IGridFilter {
  filterName: GRID_FILTER_NAME;
  filterValue?:
    | string
    | string[]
    | boolean
    | GridFilterUserInputDxCodes
    | GridFilterUserInputMyCasesParaMedicOnScene
    | IGridFilterUserInput;
  filterFunction?: (
    cleoCallSummary: ICleoCallSummary,
    filterValue: any
  ) => boolean;
}

const gridFilterService = new GridFilterService();

export const factoryGridFilters: Record<GRID_FILTER_NAME, IGridFilter> = {
  QUICK: {
    filterName: "QUICK",
    filterValue: ""
  },
  COV19: {
    filterName: "COV19",
    filterValue: "PCV Possible Corona Virus".toUpperCase(),
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: string
    ): boolean => {
      if (
        cleoCallSummary.CallSubClassification &&
        cleoCallSummary.CallSubClassification.Description
      ) {
        return (
          cleoCallSummary.CallSubClassification.Description.toUpperCase() ===
          filterValue
        );
      }
      return false;
    }
  },
  NOT_COV19: {
    filterName: "NOT_COV19",
    filterValue: "PCV Possible Corona Virus".toUpperCase(),
    filterFunction: (cleoCallSummary: ICleoCallSummary): boolean => {
      if (
        cleoCallSummary.CallSubClassification &&
        cleoCallSummary.CallSubClassification.Description
      ) {
        return (
          cleoCallSummary.CallSubClassification.Description.toUpperCase() !==
          "PCV Possible Corona Virus".toUpperCase()
        );
      }
      return true;
    }
  },
  TOWN: {
    filterName: "TOWN",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: string
    ): boolean => {
      return (
        cleoCallSummary.CallTown.toUpperCase().indexOf(
          filterValue.toUpperCase()
        ) > -1
      );
    }
  },
  CLASSIFICATION: {
    filterName: "CLASSIFICATION",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: string
    ): boolean => {
      if (
        cleoCallSummary.CallClassification &&
        cleoCallSummary.CallClassification.Description
      ) {
        return (
          cleoCallSummary.CallClassification.Description.toUpperCase() ===
          filterValue.toUpperCase()
        );
      }
      return false;
    }
  },
  CLEO_CLIENT_SERVICE: {
    filterName: "CLEO_CLIENT_SERVICE",
    filterValue: [],
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      gridFilterUserInput: IGridFilterUserInput // CLEO_CLIENT_SERVICE[]
    ): boolean => {
      const isMatched = gridFilterService.isCleoClientServiceMatched(
        cleoCallSummary,
        gridFilterUserInput.CLEO_CLIENT_SERVICE
      );

      let isMatched2 = false;
      if (gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled) {
        isMatched2 = gridFilterService.clientServicesMyAssignedAndParamedicOnScene(
          cleoCallSummary,
          gridFilterUserInput
        );
      }

      /*
      const consoleMessage =
        "CLEO_CLIENT_SERVICE cleoCallSummary.CallNo: " +
        cleoCallSummary.CallNo +
        " cleoCallSummary.cleoClientService: " +
        cleoCallSummary.cleoClientService +
        " isMatched: " +
        isMatched +
        " isMatched2: " +
        isMatched2;

      if (isMatched || isMatched2) {
        console.warn(consoleMessage);
      }
      */

      return isMatched || isMatched2;
    }
  },
  SUB_CLASSIFICATION: {
    filterName: "SUB_CLASSIFICATION",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: string
    ): boolean => {
      if (!cleoCallSummary.CallSubClassification.Description) {
        return false;
      }
      return (
        cleoCallSummary.CallSubClassification.Description.toUpperCase().indexOf(
          filterValue.toUpperCase()
        ) > -1
      );
    }
  },
  DX: {
    filterName: "DX",
    filterValue: [],
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: GridFilterUserInputDxCodes
    ): boolean => {
      if (filterValue.dxCodes.length === 0) {
        return true;
      }

      let dxCode = "";
      let dxCodeCH = "";
      let dxCodeFinal = "";
      dxCodeCH = cleoCallSummary.ChFinalDispositionCode
        ? cleoCallSummary.ChFinalDispositionCode
        : "";
      dxCodeFinal = cleoCallSummary.FinalDispositionCode
        ? cleoCallSummary.FinalDispositionCode
        : "";
      dxCode = (dxCodeFinal.length === 0
        ? dxCodeCH
        : dxCodeFinal
      ).toUpperCase();

      if (filterValue.include) {
        return filterValue.dxCodes.indexOf(dxCode) > -1;
      } else {
        // if include is false, then we want to exclude the dxCode
        // so we check if dxCode is not in the filterValue.dxCodes
        return filterValue.dxCodes.indexOf(dxCode) === -1;
      }
    }
  },
  ASSIGNED_TO: {
    filterName: "ASSIGNED_TO",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean
    ): boolean => {
      // console.log(
      //   "ASSIGNED_TO cleoCallSummary.CallNo: " +
      //     cleoCallSummary.CallNo +
      //     ", CallDoctorName: " +
      //     cleoCallSummary.CallDoctorName
      // );

      return filterValue
        ? cleoCallSummary.CallDoctorName.length > 0
        : cleoCallSummary.CallDoctorName.length === 0;
    }
  },
  BREACHED: {
    filterName: "BREACHED",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: IsoDateTimeOffset
    ): boolean => {
      if (!cleoCallSummary.BreachActualTime) {
        return false;
      }

      // console.log(
      //   "BREACHED cleoCallSummary.CallNo: " +
      //     cleoCallSummary.CallNo +
      //     "filterValue: " +
      //     filterValue +
      //     " cleoCallSummary.BreachActualTime: " +
      //     cleoCallSummary.BreachActualTime
      // );
      return filterValue > cleoCallSummary.BreachActualTime;
    }
  },
  PDS_TRACED_AND_VERIFIED: {
    filterName: "PDS_TRACED_AND_VERIFIED",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      // console.log(
      //   "cleoCallSummary.PdsTracedAndVerified callNo: " +
      //     cleoCallSummary.CallNo +
      //     cleoCallSummary.PdsTracedAndVerified
      // );

      if (filterValue === null) {
        return true;
      }

      return filterValue
        ? cleoCallSummary.PdsTracedAndVerified
        : !cleoCallSummary.PdsTracedAndVerified;
    }
  },
  TOXIC_INGESTION_AND_EMPTY: {
    filterName: "TOXIC_INGESTION_AND_EMPTY",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      if (filterValue === null) {
        return true;
      }

      return (
        cleoCallSummary.cleoClientService.toUpperCase() === "TOXIC INGESTION" ||
        cleoCallSummary.cleoClientService === ""
      );
    }
  },
  AMBULANCE_CLEO_CLIENT_SERVICES: {
    filterName: "AMBULANCE_CLEO_CLIENT_SERVICES",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      if (filterValue === null) {
        return true;
      }

      return (
        cleoCallSummary.cleoClientService.toUpperCase() === "C3/C4 VALIDATION"
      );
    }
  },
  ED_VALIDATION_CLEO_CLIENT_SERVICES: {
    filterName: "ED_VALIDATION_CLEO_CLIENT_SERVICES",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      if (filterValue === null) {
        return true;
      }

      return (
        cleoCallSummary.cleoClientService.toUpperCase() === "ED VALIDATION"
      );
    }
  },
  MY_CASE_AND_PARAMEDIC_ON_SCENE: {
    filterName: "MY_CASE_AND_PARAMEDIC_ON_SCENE",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: IGridFilterUserInput // GridFilterUserInputMyCasesParaMedicOnScene
    ): boolean => {
      return gridFilterService.clientServicesMyAssignedAndParamedicOnScene(
        cleoCallSummary,
        filterValue
      );
    }
  },
  REQUIRES_VALIDATION: {
    filterName: "REQUIRES_VALIDATION",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      if (filterValue === null) {
        return true;
      }
      return (
        !cleoCallSummary.CasValidationCount ||
        cleoCallSummary.CasValidationCount <= 0
      );
    }
  },
  FOLLOW_UP: {
    filterName: "FOLLOW_UP",
    filterValue: "",
    filterFunction: (
      cleoCallSummary: ICleoCallSummary,
      filterValue: boolean | null
    ): boolean => {
      // console.log(
      //   "FOLLOW_UP cleoCallSummary.CallNo: " +
      //     cleoCallSummary.CallNo +
      //     " filterValue: " +
      //     filterValue
      // );

      if (filterValue === null) {
        return true;
      }

      const cleoClientService = cleoCallSummary.cleoClientService.toUpperCase();
      return cleoClientService === "FOLLOW UP";
    }
  }
};

export interface GridFilterUserInputDxCodes {
  include: boolean;
  dxCodes: DxCode[];
}

export interface GridFilterUserInputMyCasesParaMedicOnScene {
  userName: DominoNameCN;
  enabled: boolean | null;
}

export interface IGridFilterUserInput {
  QUICK: string;
  COV19: "COV19" | "NOT_COV19" | "";
  NOT_COV19: "NOT_COV19" | "";
  TOWN: string;
  CLASSIFICATION: string;
  CLEO_CLIENT_SERVICE: CLEO_CLIENT_SERVICE[];
  SUB_CLASSIFICATION: string;
  DX: GridFilterUserInputDxCodes;
  ASSIGNED_TO: boolean | null;
  BREACHED: boolean | null;
  PDS_TRACED_AND_VERIFIED: boolean | null;
  TOXIC_INGESTION_AND_EMPTY: boolean | null;
  AMBULANCE_CLEO_CLIENT_SERVICES: boolean | null;
  ED_VALIDATION_CLEO_CLIENT_SERVICES: boolean | null;
  MY_CASE_AND_PARAMEDIC_ON_SCENE: GridFilterUserInputMyCasesParaMedicOnScene;
  REQUIRES_VALIDATION: boolean | null;
  FOLLOW_UP: boolean | null;
}
// DX: string[];

export type DxCodeGroupType =
  | "Ambulance Validation"
  | "Toxic Ingestion"
  | "Other";
