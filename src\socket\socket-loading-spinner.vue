<template>
  <img
    class="loading-icon"
    :class="cssClass"
    alt="Loading"
    src="../assets/Ripple-1s-30px.gif"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { debounce } from "@/common/debounce";
import { loggerInstance } from "@/common/Logger";
import { ISimpleTrigger } from "@/calls/summary/call-summarry-models";

@Component({
  name: "socket-loading-spinner"
})
export default class SocketLoadingSpinner extends Vue {
  @Prop({
    default: () => {
      return {
        timeIso: ""
      } as ISimpleTrigger<unknown>;
    }
  })
  public readonly socketLoadingTrigger!: ISimpleTrigger<unknown>;

  public cssClass = "trans-hide";

  public debounceHide: any;

  public created(): void {
    this.debounceHide = debounce(() => {
      loggerInstance.log("SocketLoadingSpinner.debounceShow()...");
      this.cssClass = "trans-hide";
    }, 2000);
  }

  @Watch("socketLoadingTrigger")
  public onshowItChanged(): void {
    this.cssClass = "trans-show";
    this.debounceHide();
  }
}
</script>

<style scoped>
.loading-icon {
  vertical-align: middle;
  height: 25px;
  width: 25px;
}

.trans-show {
  visibility: visible;
  opacity: 1;
  transition-delay: 0.51s;
}

.trans-hide {
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.5s, opacity 0.5s linear;
}
</style>
