import { loggerInstance } from "@/common/Logger";
import { computed, reactive, SetupContext } from "@vue/composition-api";

import {
  What3WordsResponse,
  What3WordsSuggestion,
  What3WordsSuggestionResponse,
  What3WordsThreeWords,
  What3WordsThreeWordsPartial
} from "@/typings/unknown-lib";
import {
  IWhat3wordsControllerState,
  IWhat3WordsMapResponse
} from "@/what3words/what3words-cleo-models";
import { debounce } from "@/common/debounce";
import { IMapController } from "@/calls/maps/map-controller-factory";
import { CommonService } from "@/common/common-service";
import { MapService } from "@/calls/maps/map-service";
import { PafData } from "@/paf/paf-data";
import {
  ILegacyCleoServerResponse,
  ILegacyDojoResponse
} from "@/common/cleo-legacy-models";
import { IPafAddress } from "@/paf/paf-models";
import { PafService } from "@/paf/paf-service";
import MapMouseEvent = google.maps.MapMouseEvent;
import IconMouseEvent = google.maps.IconMouseEvent;
import { What3wordsCleoData } from "@/what3words/what3words-cleo-data";
import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
import PlaceResult = google.maps.places.PlaceResult;
import { ICallDetail } from "@/calls/details/call-details-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { useVehicleController } from "@/vehicles/useVehicleController";
import { ITomTomVehicle, IVehicle } from "@/vehicles/vehicles-models";
import { ICleoGoogleMarker } from "@/calls/maps/map-models";
import Vue from "vue";
import CallInfoPopWindowComponent from "@/calls/maps/calls/call-info-pop-window.vue";
import { VehiclesData } from "@/vehicles/vehicles-data";
import { VehicleService } from "@/vehicles/vehicle-service";

const commonService = new CommonService();
const mapService = new MapService();
const pafService = new PafService();
const what3wordsCleoData = new What3wordsCleoData();
const what3wordsCleoService = new What3wordsCleoService();

export function useWhat3WordsController(
  what3wordsControllerState: IWhat3wordsControllerState,
  context: SetupContext
) {
  const state = reactive(what3wordsControllerState);
  let map: google.maps.Map | null = null;
  let mapController: IMapController | null = null;
  const vehicleController = useVehicleController();

  function init() {
    if (state.userInput.words.length > 0) {
      state.userInput.autoSuggest = state.userInput.words;
      convertToCoordinates(state.userInput.words).then(resp => {
        doAddressPafChain(resp);
      });
    }
    // mapController?.loadVehicles(); //  Needs Long/Lat....currently (doing nearest cars to call).
    // mapController?.loadBases();
    if (mapController) {
      mapController.loadBases().then(mapController.addBasesToMap);
    }
  }

  function convertToThreeWordAddress(
    lat: number,
    lng: number
  ): Promise<What3WordsResponse> {
    return what3words.api
      .convertTo3wa({ lat, lng }, "en")
      .then((response: What3WordsResponse) => {
        loggerInstance.log("[convertTo3wa]", response);
        // state.results.what3WordsResponse = response;
        updateUserInfoFromResponse(response);
        moveToResponse(response);
        return response;
      });
  }

  function convertToCoordinates(
    what3WordsThreeWords: What3WordsThreeWords
  ): Promise<What3WordsResponse> {
    return what3words.api
      .convertToCoordinates(what3WordsThreeWords)
      .then((response: What3WordsResponse) => {
        loggerInstance.log("[convertToCoordinates]", response);
        state.userInput.coordinates = response.coordinates;
        updateUserInfoFromResponse(response);
        moveToResponse(response);
        return response;
      });
  }

  function updateUserInfoFromResponse(response: What3WordsResponse): void {
    state.results.what3WordsResponse = response;
    state.userInput.words = response.words;
    state.userInput.autoSuggest = response.words;
    state.userInput.coordinates = response.coordinates;
  }

  function moveToResponse(response: What3WordsResponse): void {
    if (map) {
      map.panTo(response.coordinates);

      //  Zoom in to what3Words minimum zoom in.
      const zoom = map.getZoom();
      const loadFeatures = zoom! > 17;
      if (!loadFeatures) {
        map.setZoom(18);
      }

      mapController?.removeMarkersFromMap(state.results.mapMarkers);

      dropWhat3WordsMarker(response);
    }
  }

  function dropWhat3WordsMarker(response: What3WordsResponse): void {
    const pin: string = require("../assets/red-pushpin.png");

    const marker = new google.maps.Marker({
      position: response.coordinates,
      title: response.words,
      icon: pin
    });
    state.results.mapMarkers.push(marker);
    marker.setMap(map);
    marker.addListener("click", (event: any) => {
      getAddresses(event.latLng);
    });
  }

  function getAddresses(
    latLng: google.maps.LatLng | google.maps.LatLngLiteral
  ): Promise<void> {
    state.results.geocoderResultsPostCodes = [];
    if (!mapController) {
      return Promise.resolve();
    }
    return mapController
      .getAddresses(latLng)
      .then((resp: google.maps.GeocoderResult[]) => {
        // const geocoderResults = mapService.filterAddresses(resp);
        state.results.geocoderResults = commonService.simpleObjectClone(resp);
        state.results.geocoderResultsPostCodes = mapService.getGeocoderPostCodes(
          state.results.geocoderResults
        );

        //  Drop marker to nearest address
        const pin: string = require("../assets/greendrop.png");
        const nearest = resp[0];

        state.results.nearestAddress = commonService.simpleObjectClone(nearest);

        const marker = new google.maps.Marker({
          position: nearest.geometry.location,
          title: "Nearest address: " + nearest.formatted_address,
          icon: pin
        });
        state.results.mapMarkers.push(marker);
        marker.setMap(map);

        const addressHtml = nearest.formatted_address.split(",").join("<br>");

        const contentHtml = "<b>Nearest Address:</b><br>" + addressHtml;
        const infoWindow = new google.maps.InfoWindow({
          content: contentHtml
        });

        marker.addListener("click", () => {
          infoWindow.open({ anchor: marker, map, shouldFocus: false });
          return;
        });

        infoWindow.open(map, marker);

        return;
      });
  }

  // const geocoderResultsPostCodes = computed<string[]>(() => {
  //   return mapService.getGeocoderPostCodes(state.results.geocoderResults);
  // });

  function autoSuggest(
    what3WordsThreeWordsPartial: What3WordsThreeWordsPartial
  ) {
    what3words.api
      .autosuggest(what3WordsThreeWordsPartial, { clipToCountry: ["GB"] })
      .then((response: What3WordsSuggestionResponse) => {
        loggerInstance.log("[autoSuggest]", response);
        state.results.suggestions = response;
      });
  }

  function autoSuggestClear() {
    state.results.suggestions = null;
  }

  const currentSuggestions = computed<What3WordsSuggestion[]>(() => {
    return state.results.suggestions?.suggestions
      ? state.results.suggestions.suggestions
      : [];
  });

  function suggestionSelected(
    what3WordsSuggestion: What3WordsSuggestion
  ): void {
    // map.panTo(what3WordsSuggestion.);
    state.results.suggestion = commonService.simpleObjectClone(
      what3WordsSuggestion
    );
    state.userInput.words = what3WordsSuggestion.words;
    state.userInput.autoSuggest = what3WordsSuggestion.words;
    convertToCoordinates(what3WordsSuggestion.words).then(resp => {
      doAddressPafChain(resp);
    });
  }

  function doAddressPafChain(
    what3WordsResponse: What3WordsResponse
  ): Promise<void> {
    // return;
    state.results.geocoderResultsPostCodes = [];
    state.results.pafResults = [];
    return getAddresses(what3WordsResponse.coordinates).then(() => {
      if (state.results.geocoderResultsPostCodes.length === 1) {
        // searchPaf(state.results.geocoderResultsPostCodes[0]);
      }
    });
  }

  function setMap(mapCont: IMapController): void {
    mapController = mapCont;
    map = mapController.getGoogleMap();

    map.addListener("bounds_changed", (resp: any) => {
      loggerInstance.log(
        "useWhat3WordsController.Map Bounds Changed at: " +
          new Date().toISOString()
      );
      state.map.debounceApplyOverlay();
    });

    map.addListener("click", (resp: MapMouseEvent | IconMouseEvent) => {
      loggerInstance.log(
        "useWhat3WordsController.click: " +
          new Date().toISOString() +
          ", lat/lng" +
          resp.latLng
      );

      if (resp.latLng) {
        state.userInput.coordinates.lat = resp.latLng.lat();
        state.userInput.coordinates.lng = resp.latLng.lng();
        debounceUserClickedMap();
      }
    });

    // mapController.loadBases().then(mapController.addBasesToMap);
    // mapController.loadCalls();
    // mapController.loadVehicles();

    state.map.debounceApplyOverlay = debounce(() => {
      applyGridToMap();
    }, 500);
  }

  const debounceUserClickedMap = debounce(() => {
    state.userInput.autoSuggestSearchType = "w3w";

    convertToThreeWordAddress(
      state.userInput.coordinates.lat,
      state.userInput.coordinates.lng
    ).then(resp => {
      doAddressPafChain(resp);
    });
  }, 500);

  function applyGridToMap(): void {
    if (!map) {
      loggerInstance.log(
        "useWhat3WordsController.applyGridToMap() No mpa found...exiting."
      );
      return;
    }

    const zoom = map.getZoom();
    const loadFeatures = zoom! > 17;
    state.map.isZoomOkForOverlay = loadFeatures;

    if (loadFeatures) {
      // Zoom level is high enough
      const ne = map.getBounds()!.getNorthEast();
      const sw = map.getBounds()!.getSouthWest();

      // Call the what3words Grid API to obtain the grid squares within the current visble bounding box
      what3words.api
        .gridSectionGeoJson({
          southwest: {
            lat: sw.lat(),
            lng: sw.lng()
          },
          northeast: {
            lat: ne.lat(),
            lng: ne.lng()
          }
        })
        .then(function(data) {
          const gridData = state.map.gridOverlayData;
          if (gridData) {
            for (let i = 0; i < gridData.length; i++) {
              map!.data.remove(gridData[i]);
            }
          }
          state.map.gridOverlayData = map!.data.addGeoJson(data);
        })
        .catch(console.error);
    }

    // Set the grid display style
    map.data.setStyle({
      visible: loadFeatures,
      strokeColor: "#777",
      strokeWeight: 0.1,
      clickable: true
    });
  }

  const debounceSearchPaf = debounce(() => {
    searchPaf(state.userInput.pafPostCode);
  }, 500);

  function doSearchPaf() {
    state.results.pafResults = [];
    if (isUserInputPafPostCodeValid.value) {
      debounceSearchPaf();
    }
  }

  const isUserInputPafPostCodeValid = computed<boolean>(() => {
    return pafService.isValidPostCode(state.userInput.pafPostCode);
  });

  function searchPaf(postCode: string) {
    state.userInput.pafPostCode = postCode;
    state.results.pafResults = [];
    state.results.pafSelected = null;
    const pafData = new PafData();
    state.display.pafPicker = true;
    state.display.pafIsLoading = true;
    pafData
      .getPafData(postCode)
      .then(resp => {
        //  TODO   fix this cack
        state.results.pafResults = ((resp.DATA as any)[0] as ILegacyDojoResponse<
          IPafAddress[]
        >).items;
      })
      .finally(() => {
        state.display.pafIsLoading = false;
      });
  }

  function pafAddressSelected(pafAddress: IPafAddress) {
    state.results.pafSelected = commonService.simpleObjectClone(pafAddress);
    state.display.pafPicker = false;

    const pafGps: string[] = pafAddress.GPS.split(",");

    state.userInput.coordinates.lat = Number(pafGps[0]);
    state.userInput.coordinates.lng = Number(pafGps[1]);

    convertToThreeWordAddress(
      state.userInput.coordinates.lat,
      state.userInput.coordinates.lng
    );
  }
  function toggleWhat3WordsConfirmed() {
    state.userInput.what3WordsConfirmed = !state.userInput.what3WordsConfirmed;
  }

  function toggleAddressConfirmed() {
    state.userInput.addressConfirmed = !state.userInput.addressConfirmed;
  }

  function pafAddressConfirmed() {
    context.emit("pafAddressConfirmed", state.results.pafSelected);
  }

  function wordsConfirmed() {
    context.emit("wordsConfirmed", state.results.what3WordsResponse);
  }

  function confirm() {
    const cleoAddress = state.results.nearestAddress
      ? what3wordsCleoService.mapGoogleNearestAddressToCleoAddress(
          state.results.nearestAddress
        )
      : null;

    const what3WordsMapResponse: IWhat3WordsMapResponse = {
      what3WordsResponse: state.results.what3WordsResponse,
      nearestAddress: state.results.nearestAddress,
      smsCount: state.results.smsCount,
      cleoAddress: cleoAddress
    };
    context.emit("confirmed", what3WordsMapResponse);
  }

  function sendSms() {
    state.display.smsIsLoading = true;
    what3wordsCleoData
      .sendSMS(state.userInput.mobileNumber)
      .then(resp => {
        loggerInstance.log("useWhat3WordsController.sendSms() resp", resp);
        state.results.smsCount++;
      })
      .finally(() => {
        state.display.smsIsLoading = false;
      });
  }

  const isValidPhoneNumber = computed<boolean>(() => {
    const mobileNumber = state.userInput.mobileNumber;
    if (mobileNumber.length > 0) {
      return what3wordsCleoService.isMobileNumber(mobileNumber);
    }
    return true;
  });

  const isSmsButtonDisabled = computed<boolean>(() => {
    return !isValidPhoneNumber.value || state.display.smsIsLoading;
  });

  function close() {
    context.emit("close");
  }

  function getQueryPredictions(query: string): Promise<void> {
    if (!mapController) {
      return Promise.resolve();
    }

    return mapController.autoCompleteLookup(query).then(
      (
        resp: {
          predictions: google.maps.places.QueryAutocompletePrediction[] | null;
          status: google.maps.places.PlacesServiceStatus;
        } | null
      ) => {
        loggerInstance.log("getQueryPredictions()", resp);
        if (resp && resp.predictions) {
          const predictions = resp.predictions.filter(prediction => {
            return prediction.place_id && prediction.place_id.length > 0;
          });

          state.results.googleAutoPredictions = commonService.simpleObjectClone(
            predictions
          );
        }
        return;
      }
    );
  }

  function autoQueryPredictionSelected(
    prediction: google.maps.places.QueryAutocompletePrediction
  ): Promise<void> {
    if (!prediction) {
      return Promise.resolve();
    }
    state.userInput.googleAutoPredictionQuery = prediction.description;
    state.results.googleAutoPrediction = commonService.simpleObjectClone(
      prediction
    );

    if (!mapController) {
      return Promise.resolve();
    }

    return mapController
      .getDetails(prediction)
      .then((placeResult: PlaceResult | null) => {
        if (placeResult) {
          state.results.googlePlaceResult = commonService.simpleObjectClone(
            placeResult
          );
          // const latLng: google.maps.LatLng =
          if (placeResult.geometry) {
            mapController?.panToLocation(
              placeResult.geometry.location as google.maps.LatLng
            );
          }
        }
        return;
      });
  }

  function dropMarkerForCurrentCallLocation(
    callData: ICallDetail | ICleoCallSummary,
    vehicles: IVehicle[]
  ) {
    loggerInstance.log(
      "useWhat3WordsController.dropMarkerForCurrentCallLocation()...",
      callData
    );

    const query = what3wordsCleoService.getQueryStringFromAddress(
      what3wordsCleoService.getWhat3WordsMapCleoAddressFromCallData(callData)
    );
    state.userInput.autoSuggestSearchType = "google";
    state.userInput.googleAutoPredictionQuery = query;
    getQueryPredictions(query).then(() => {
      if (state.results.googleAutoPredictions.length === 1) {
        autoQueryPredictionSelected(
          state.results.googleAutoPredictions[0]
        ).then(() => {
          if (state.results.googlePlaceResult) {
            addCurrentCallToMap(
              state.results.googlePlaceResult,
              callData,
              vehicles
            );

            //  we can load cars
            const geometry = state.results.googlePlaceResult.geometry;
            if (geometry && geometry.location) {
              setTomTomVehicles(
                (geometry.location.lat as any) as number,
                (geometry.location.lng as any) as number
              ).then(() => {
                loadTomTomVehiclesToMap();
              });
            }
          }
        });
      }
    });
  }

  function setTomTomVehicles(lat: number, lng: number): Promise<void> {
    // //  TODO only 10 requests per minute allowed, so needs work on back end.
    // return Promise.resolve();

    return new VehiclesData().getTomTomVehicles(lat, lng).then(resp => {
      if (!(resp as ILegacyCleoServerResponse<null>).RESULT) {
        state.results.tomTomVehicles = resp as ITomTomVehicle[];
      }
    });
  }

  function loadTomTomVehiclesToMap() {
    const vehicleService = new VehicleService();
    const vehicles = state.results.tomTomVehicles.map(vehicle => {
      return vehicleService.mapVehicleTomTomToVehicle(vehicle);
    });
    mapController?.addVehiclesToMap(vehicles);
  }

  function addCurrentCallToMap(
    placeResult: PlaceResult,
    callData: ICallDetail | ICleoCallSummary,
    vehicles: IVehicle[] = []
  ) {
    if (!map) {
      return;
    }

    const icon: string = require("../assets/red-down-arrow.png");
    const title = "Current Call: " + callData.CallNo;

    const marker = new google.maps.Marker({
      position: placeResult.geometry?.location as google.maps.LatLng,
      title: title,
      icon: icon
    });
    (marker as ICleoGoogleMarker).id = callData.CallNo.toString();
    marker.setMap(map);

    map.setMapTypeId("satellite");

    marker.addListener("click", () => {
      infoWindow.open({ anchor: marker, map, shouldFocus: false });
      return;
    });

    //  This is the component to display in the pop up
    const CallInfoPopWindow = Vue.extend(CallInfoPopWindowComponent);
    const getW3w = computed(() => {
      let resp = {
        coordinates: {
          lat: 0,
          lng: 0
        },
        words: ""
      };
      if (state.results.what3WordsResponse) {
        resp = state.results.what3WordsResponse;
      }
      return resp;
    });

    const instance = new CallInfoPopWindow({
      propsData: {
        cleoCall: callData,
        vehicles: vehicles,
        what3WordsResponse: getW3w,
        userPermissions: state.userInput.userPermissions
      }
    });
    instance.$mount();

    instance.$on("dispatched", function(payload: {
      cleoCall: ICallDetail | ICleoCallSummary;
      vehicle: IVehicle;
    }) {
      loggerInstance.log("useWhat3WordsController.addCurrentCallToMap() $on.dispatched", payload);
    });

    // instance.$on("retrieveFromVehicle", function(
    //   callData: ICallDetail | ICleoCallSummary
    // ) {
    //   // useVehicleController().dispatchToVehicle()
    // });

    instance.$on("close", function(callData: ICallDetail | ICleoCallSummary) {
      infoWindow.close();
    });

    const infoWindow = new google.maps.InfoWindow({
      content: instance.$el
    });

    //  ...default display the info.
    infoWindow.open(map, marker);
  }

  function switchAutoSuggestSearchType() {
    const current = state.userInput.autoSuggestSearchType;
    if (current === "w3w") {
      state.userInput.autoSuggestSearchType = "google";
    } else {
      state.userInput.autoSuggestSearchType = "w3w";
    }
  }

  return {
    state,
    init,
    setMap,
    convertToThreeWordAddress,
    convertToCoordinates,
    autoSuggest,
    autoSuggestClear,
    currentSuggestions,
    suggestionSelected,
    searchPaf,
    doSearchPaf,
    pafAddressSelected,
    isUserInputPafPostCodeValid,
    pafAddressConfirmed,
    wordsConfirmed,
    toggleWhat3WordsConfirmed,
    toggleAddressConfirmed,
    close,
    confirm,
    sendSms,
    isValidPhoneNumber,
    isSmsButtonDisabled,
    getQueryPredictions,
    autoQueryPredictionSelected,
    switchAutoSuggestSearchType,
    dropMarkerForCurrentCallLocation,
    vehicleController
  };
}
