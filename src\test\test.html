<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
  </head>
  <body>
    <div class="consultation-wrapper">
      <!-- PaCCs module, place PaCCs within this container. -->
      <div class="paccs-wrapper"></div>
      <!-- End of: PaCCs module. -->

      <!-- Consultation fields. -->
      <div class="consultation-container">
        <!-- Please confirm patient details. -->
        <div class="consult-row" id="PatientDetailsContainer">
          <div class="field-container">
            <label for="CHUB_CheckNameDOB"
              >Please confirm patient details, name, DOB</label
            >
            <select name="CHUB_CheckNameDOB" id="CHUB_CheckNameDOB">
              <option value="PLEASE_SELECT">-Please select-</option>
              <option value="YES">Yes</option>
            </select>
          </div>
        </div>
        <!-- End of: Please confirm patient details. -->

        <!-- Are you speaking to the patient or the patient representative. -->
        <div class="consult-row" id="RepresentativeContainer">
          <div class="field-container">
            <label for="CHUB_Tele"
              >Are you speaking to the patient or the patient
              representative</label
            >
            <select
              name="CHUB_Tele"
              id="CHUB_Tele"
              onchange="CallControllerClient.hubConsultPatientChange()"
            >
              <option value="PLEASE_SELECT">-Please select-</option>
              <option value="Patient">Patient</option>
              <option value="Patient Representative"
                >Patient Representative</option
              >
            </select>
          </div>
        </div>
        <!-- End of: Are you speaking to the patient or the patient representative. -->

        <!-- Presenting Complaint -->
        <div class="consult-row" id="PresentingComplaintContainer">
          <div class="field-container">
            <label for="CHUB_Presenting">Presenting complaint</label>
            <textarea id="CHUB_Presenting"></textarea>
          </div>
        </div>
        <!-- End of: Presenting Complaint. -->

        <!-- Past medical history, Allergies and Medications. -->
        <div class="consult-row" id="MedicalHistoryContainer">
          <div class="field-container">
            <label for="CHUB_AppropHist"
              >Past medical history, Allergies and Medications</label
            >
            <textarea id="CHUB_AppropHist"></textarea>
          </div>
        </div>
        <!-- End of: Past medical history, Allergies and Medications. -->

        <!-- Red Flags. -->
        <div class="consult-row" id="RedFlagsContainer">
          <div class="field-container">
            <label for="CHUB_RedFlags"
              >Red Flags
              <span class="small"
                >[Document any area of the history or symptoms that helped
                diagnose the patients condition].</span
              >
            </label>

            <textarea id="CHUB_RedFlags"></textarea>
          </div>
        </div>
        <!-- End of: Red Flags. -->

        <!-- Diagnosis / Assessment. -->
        <div class="consult-row" id="DiagnosisAssessmentContainer">
          <div class="field-container">
            <label for="CHUB_DiffDiagnosis">Diagnosis / Assessment</label>
            <textarea id="CHUB_DiffDiagnosis"></textarea>
          </div>
        </div>
        <!-- End of: Diagnosis / Assessment. -->

        <!-- Management Plan -->
        <div class="consult-row" id="ManagementPlanContainer">
          <div class="field-container">
            <label for="CHUB_Management">Management Plan</label>
            <textarea id="CHUB_Management"></textarea>
          </div>
        </div>
        <!-- End of: Management Plan. -->

        <!-- Safety netting. -->
        <div class="consult-row" id="SafetyNettingContainer">
          <div class="field-container">
            <label for="CHUB_SafetyNet">Safety netting</label>
            <textarea id="CHUB_SafetyNet"></textarea>
          </div>
        </div>
        <!-- End of: Safety netting. -->

        <!-- Urgent. -->
        <div class="consult-row" id="UrgentContainer">
          <div class="field-container">
            <label for="CallUrgentYN">Urgent</label>
            <select
              name="CallUrgentYN"
              id="CallUrgentYN"
              onchange="CallControllerClient.CallSetToUrgent(this);"
            >
              <option value="No">No</option>
              <option value="Yes">Yes</option>
            </select>
          </div>
        </div>
        <!-- End of: Urgent. -->

        <!-- Grouped Classification: Classification, Sub Classification. -->
        <div class="consult-row" id="ClassificationContainer">
          <div class="field-container-fit-content">
            <label for="CallClassification">Classification</label>
            <select
              id="CallClassification"
              onchange="CallControllerClient.onChangeclassification(this,'rowCallClassAssign','CallClassAssign','spanCallClassAssignNP');return false;"
            >
              <option value="Nurse Advice" selected="">Nurse Advice</option>
            </select>
          </div>
          <div class="field-container-fit-content">
            <label for="CallSubClassification">Sub Classification</label>
            <select id="CallSubClassification">
              <option value=""></option>
              <option value="Clinician Awaiting Callback"
                >Clinician Awaiting Callback</option
              >
              <!--<option value="Follow-Up">Follow-Up</option>-->
              <option value="Palliative">Palliative</option>
              <option value="Path Lab">Path Lab</option>
              <option value="PCV Possible Corona Virus"
                >PCV Possible Corona Virus</option
              >
              <option value="Pharmacist">Pharmacist</option>
              <option value="Prescription">Prescription</option>
              <option value="RIP">RIP</option>
              <!--<option value="Ambulance">Ambulance</option>-->
              <!--<option value="Police">Police</option>-->
            </select>
          </div>
        </div>
        <!-- End of: Grouped Classification: Classification, Sub Classification. -->

        <!-- Consultation Actions. -->
        <div class="consult-row" id="ConsultationActionsContainer">
          <!-- Prescription Details. -->
          <div class="field-container-fit-content">
            <label>Prescription Details</label>
            <div class="consult-row no-flex-wrap">
              <!-- EPS / FP10. -->
              <div class="field-container">
                <button id="btnPrescribeDrugsFP10" type="button">
                  EPS / FP10
                </button>
              </div>
              <!-- End of: EPS / FP10. -->

              <!-- FP10Prec. -->
              <div class="field-container">
                <button id="btnPrescribeDrugsFP10PREC" type="button">
                  FP10Prec
                </button>
              </div>
              <!-- End of: FP10Prec. -->
            </div>
          </div>
          <!-- End of: Prescription Details. -->

          <!-- Video Consultation. -->
          <div class="field-container-fit-content">
            <label>Video Consultation</label>
            <div class="consult-row">
              <div class="field-container">
                <button id="btnStartVideoConsult" type="button">
                  Start Video Consultation
                </button>
              </div>
            </div>
          </div>
          <!-- End of: Video Consultation. -->
        </div>
        <!-- End of: Consultation Actions. -->

        <!-- Comments (Non Clinical) -->
        <div class="consult-row" id="CommentsContainer">
          <div class="field-container">
            <label for="CallComments">Comments (NON CLINICAL)</label>
            <span id="CallComments"></span>
          </div>
        </div>
        <!-- End of: Comments (Non Clinical). -->
      </div>
      <!-- End of: Consultation fields -->
    </div>
  </body>
</html>
