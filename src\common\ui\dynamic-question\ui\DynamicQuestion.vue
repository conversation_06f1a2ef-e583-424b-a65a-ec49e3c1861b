<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <label
      :for="valueInternal.id"
      class="dynamic-question--label"
      :class="valueInternal.mandatory ? 'dynamic-question--required' : ''"
      >{{ valueInternal.label }}
      <!--      <span v-if="valueInternal.mandatory">*</span>-->
    </label>

    <!-- Text Input -->
    <input
      v-if="valueInternal.type === 'text'"
      v-model="valueInternal.value"
      :id="valueInternal.id"
      type="text"
      :required="valueInternal.mandatory"
      @change="onInput"
    />

    <!-- Radio Buttons -->
    <div
      v-else-if="valueInternal.type === 'radio'"
      :class="
        valueInternal.options.length > 5
          ? 'ic24-flex-column ic24-flex-gap-small'
          : 'ic24-flex-row ic24-flex-gap-small'
      "
    >
      <label
        v-for="option in valueInternal.options"
        :key="option.label ? option.label : option"
        class="ic24-flex-row ic24-flex-gap"
        style="align-items: baseline"
      >
        <input
          type="radio"
          :name="valueInternal.id"
          :value="option.value ? option.value : option"
          v-model="valueInternal.value"
          :required="valueInternal.mandatory"
          @change="onInput"
        />
        {{ option.label ? option.label : option }}
      </label>
    </div>

    <!-- Checkbox -->
    <div v-else-if="valueInternal.type === 'checkbox'">
      <label
        v-for="option in valueInternal.options"
        :key="option.label ? option.label : option"
      >
        <input
          type="checkbox"
          :value="option.value ? option.value : option"
          v-model="valueInternal.value"
          @change="onInput"
        />
        {{ option.label ? option.label : option }}
      </label>
    </div>

    <!-- Select Dropdown -->
    <select
      v-else-if="valueInternal.type === 'select'"
      v-model="valueInternal.value"
      class="dynamic-question--select"
      :required="valueInternal.mandatory"
      @change="onInput"
    >
      <option
        v-for="option in valueInternal.options"
        :key="option.label ? option.label : option"
        :value="option.value ? option.value : option"
        >{{ option.label ? option.label : option }}</option
      >
    </select>

    <div v-if="valueInternal.helpText" class="dynamic-question--help-text">
      {{ valueInternal.helpText }}
    </div>

    <!-- Validation Message -->
    <!--    <p v-if="valueInternal.mandatory && !valueInternal.value" class="error">-->
    <!--      This field is required.-->
    <!--    </p>-->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { deepCloneObject } from "@/common/common-utils";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export default defineComponent({
  name: "DynamicQuestion",
  props: {
    value: {
      type: Object as PropType<Question>,
      required: true
    }
  },
  setup(props: { value: Question }, context: SetupContext) {
    const valueInternal = ref(deepCloneObject(props.value));

    function onInput() {
      console.log("DynamicQuestion onInput:", valueInternal.value);
      context.emit("input", valueInternal.value);
    }

    // watch for changes to the value prop and set the internal value
    watch(
      () => props.value,
      (newValue: Question, oldValue: Question) => {
        valueInternal.value = deepCloneObject(newValue);
      }
    );

    //watch for changes to the valueInternal and emit the input event
    // watch(
    //   () => valueInternal.value,
    //   (newValue: Question, oldValue: Question) => {
    //     context.emit("input", valueInternal.value);
    //   }
    // );

    return { valueInternal, onInput };
  }
});
</script>

<style scoped>
.error {
  color: red;
  font-size: 0.9em;
}
.dynamic-question--label {
  font-weight: 600;
}

.dynamic-question--required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.dynamic-question--select {
  width: fit-content;
}

.dynamic-question--help-text {
  font-style: italic;
  color: #333;
}
</style>
