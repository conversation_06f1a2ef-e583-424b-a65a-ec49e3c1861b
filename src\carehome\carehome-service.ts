import { ICareHome } from "@/carehome/carehome-models";
import { ICareHomeCleo } from "@/carehome/carehome-data";

export function factoryCareHome(): ICareHome {
  return {
    id: 0,
    name: "",
    email: "",
    isEnabled: true
  };
}

export function mapCareHomeCleo(careHomeCleo: ICareHomeCleo): ICareHome {
  return {
    id: careHomeCleo.id,
    name: careHomeCleo.careHomeName,
    email: careHomeCleo.email,
    isEnabled: careHomeCleo.isEnabled
  };
}
