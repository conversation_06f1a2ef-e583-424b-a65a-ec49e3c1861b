<template>
  <div>
    <CompleteStepHeader :step="step" />
    <div class="complete-step--subheader">
      Have you contacted/attempted to contact the patient.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <SimpleButtonSelecter
      class="ic24-flex-column ic24-flex-gap-large complete-step--simple-buttons"
      :options="options"
      :value="value"
      @input="onInput"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, PropType } from "@vue/composition-api";
import SimpleButtonSelecter, {
  ISimpleButtonInputValue
} from "../../SimpleButtonSelecter.vue";
import { IStep } from "@/calls/details/complete/complete-models";
import {
  ClinicalValidationType,
  ClinicalValidationValue
} from "@/calls/details/complete/components/clinical-validation/clinical-validation-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "ClinicalValidation",
  components: { CompleteStepHeader, <PERSON>ButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"CLINICAL_VALIDATION">>,
      required: true
    },
    value: {
      type: Object as PropType<
        ISimpleButtonInputValue<ClinicalValidationType, ClinicalValidationValue>
      >,
      required: true
    },
    options: {
      type: Array as PropType<
        ISimpleButtonInputValue<
          ClinicalValidationType,
          ClinicalValidationValue
        >[]
      >,
      default: () => {
        const options: ISimpleButtonInputValue<
          ClinicalValidationType,
          ClinicalValidationValue
        >[] = [
          {
            id: "LACK_OF_CLINICAL_CAPACITY",
            description: "Lack of clinical capacity",
            value: "LACK_OF_CLINICAL_CAPACITY"
          },
          {
            id: "ATTEMPTED_AND_FAILED_CONTACT",
            description: "Attempted and failed contact",
            value: "ATTEMPTED_AND_FAILED_CONTACT"
          },
          {
            id: "AMBULANCE_REQD_DUE_TO_CLINICAL_CONCERNS",
            description: "Ambulance required due to clinical concerns",
            value: "AMBULANCE_REQD_DUE_TO_CLINICAL_CONCERNS"
          }
        ];
        return options;
      }
    }
  },
  setup(
    props: {
      options: ISimpleButtonInputValue<
        ClinicalValidationType,
        ClinicalValidationValue
      >[];
    },
    context: SetupContext
  ) {
    function onInput(
      simpleButtonInputValue: ISimpleButtonInputValue<
        ClinicalValidationType,
        ClinicalValidationValue
      >
    ) {
      console.log("ClinicalValidation.onInput()", simpleButtonInputValue);
      context.emit("input", simpleButtonInputValue);
    }

    return { onInput };
  }
});
</script>

<style></style>
