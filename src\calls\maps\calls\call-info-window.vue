<template>
  <div class="cleo-map--marker-wrapper">
    <div class="cleo-map--marker-header">
      <div class="cleo-map--marker-label">Call</div>
      <div class="cleo-map--marker-data">
        <span v-text="cleoCallSummary.CallNo"></span>
      </div>
    </div>

    <div class="cleo-map--marker-body">

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Class</div>
        <div class="cleo-map--marker-data">
          <span
            v-text="cleoCallSummary.CallClassification.Description"
          ></span>
          <span v-if="cleoCallSummary.CallUrgentYn" class="cleo-map--marker-call-urgent">Urgent</span>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Patient</div>
        <div class="cleo-map--marker-data">
          <span
            v-text="
              cleoCallSummary.CallForename + ' ' + cleoCallSummary.CallSurname
            "
          ></span>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Age</div>
        <div class="cleo-map--marker-data">
          <span
            v-text="cleoCallSummary.CallAge + cleoCallSummary.CallAgeClass"
          ></span>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Address</div>
        <div class="cleo-map--marker-data">
          <div v-html="getAddress"></div>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Symptoms</div>
        <div class="cleo-map--marker-data cleo-map--marker-data-large">
          <div v-text="cleoCallSummary.CallSymptoms"></div>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Vehicle</div>
        <div class="cleo-map--marker-data cleo-map--marker-data-large">
          <div v-text="cleoCallSummary.DispatchVehicle"></div>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <button
          class="adapter-button adapter-width-5 adapter-button--green adapter-button--separator"
          v-on:click.stop="$emit('openCall', cleoCallSummary)"
        >
          <span>Open Call</span>
        </button>

        <div class="adapter-button--separator"></div>

        <select v-model="nearestCars">
          <option value="1">1</option>
          <option value="2">2</option>
          <option value="3">3</option>
        </select>
        <button
          class="adapter-button adapter-width-5 adapter-button--green"
          v-on:click.stop="findNearestVehicles"
        >
          <span>Nearest</span>
        </button>

        <button
          class="adapter-button adapter-width-5 adapter-button--red cleo-map--marker-button-dispatch"
          v-on:click.stop="$emit('dispatchCall', cleoCallSummary)"
        >
          <span v-text="isDispatched ? 'Retrieve' : 'Dispatch'"></span>
        </button>
      </div>
    </div>
  </div>
  <!--  <a href="#" v-on:click.prevent="handleClick" class="map-base&#45;&#45;title-link">-->
  <!--    <span v-text="cleoCallSummary.CallNo"></span>-->

  <!--  </a>-->
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import { ICleoCallSummary } from "../../summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";

export default defineComponent({
  name: "call-info-window",
  components: {},
  props: {
    cleoCallSummary: {
      required: true,
      type: Object as PropType<ICleoCallSummary>
    }
  },
  setup(
    props: { cleoCallSummary: ICleoCallSummary; message: string },
    context: SetupContext
  ) {
    const callSummaryService = new CallSummaryService();

    const nearestCars = ref(2);

    function handleClick() {
      context.emit("cleoCallSummaryClicked", props.cleoCallSummary);
    }

    const getAddress = computed<string>(() => {
      return (callSummaryService.getAddress(
        props.cleoCallSummary
      ) as string[]).join("<br>");
    });

    const isDispatched = computed<boolean>(() => {
      return props.cleoCallSummary.DispatchVehicle.length > 0;
    });

    function findNearestVehicles() {
      context.emit("findNearestVehicles", {
        cleoCallSummary: props.cleoCallSummary,
        nearestCount: nearestCars.value
      });
    }

    return {
      handleClick,
      getAddress,
      isDispatched,
      nearestCars,
      findNearestVehicles
    };
  }
});
</script>

<style scoped>
.map-base--title {
  font-weight: 700;
  padding: 0 0 5px 0;
}
/*.map-base--title-link {*/
/*  text-decoration: none;*/
/*}*/
.cleo-map--marker-header {
  font-weight: 600;
  /*margin-bottom: 10px;*/
}

.cleo-map--marker-body {
  margin-top: 10px;
}

.cleo-map--marker-section {
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #e8e8e8;
}

.cleo-map--marker-label {
  width: 100px;
  display: inline-block;
  vertical-align: top;
}

.cleo-map--marker-data {
  width: 200px;
  display: inline-block;
}

.cleo-map--marker-data-large {
  max-height: 100px;
  overflow: auto;
}

.cleo-map--marker-button-dispatch {
  float: right;
}

.cleo-map--marker-call-urgent {
  margin-left: 5px;
  font-weight: 600;
  color: #fb3939;
}

</style>
