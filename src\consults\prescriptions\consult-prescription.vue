<template>
  <div>
    <div
      v-text="consultPrescription.PrescriptionType"
      class="app-div--cell-like consult-prescription--type"
    ></div>
    <div
      class="app-div--cell-like"
      v-for="prescriptionItem in consultPrescription.PrescriptionItems"
      :key="prescriptionItem.Id"
    >
      <div v-text="prescriptionItem.Name"></div>
      <div v-text="prescriptionItem.Quantity"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";

import { IConsultPrescription } from "@/consults/consult-models";

export default defineComponent({
  name: "consult-prescription",
  components: {},
  props: {
    consultPrescription: {
      type: Object as PropType<IConsultPrescription>,
      required: true
    }
  }
});
</script>

<style>
.consult-prescription--type {
  width: 5em;
}
</style>
