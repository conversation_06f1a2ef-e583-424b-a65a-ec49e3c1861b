<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" :title-end="getTitleEnd" />

    <!--    <div-->
    <!--      class="complete-step&#45;&#45;subheader"-->
    <!--      v-text="getAuditQuestionTypeLabel"-->
    <!--    ></div>-->

    <div class="ic24-vertical-spacer-large"></div>

    <DynamicQuestionForm
      :questions="brisdocAuditQuestionsController.state.data.questions"
      @input="onInput"
    />

    <CleoModalLoadingV2
      v-if="brisdocAuditQuestionsController.state.data.isLoading"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext
} from "@vue/composition-api";

import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { IStep } from "@/calls/details/complete/complete-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import { useBrisdocAuditQuestionsController } from "@/calls/details/complete/brisdoc/ui/audit-questions/models/useBrisdocAuditQuestionsController";
import DynamicQuestionForm from "@/common/ui/dynamic-question/ui/DynamicQuestionForm.vue";
import CleoModalLoadingV2 from "@/common/ui/modal/cleo-modal-loading-v2.vue";
import { DynamicQuestionsOutput } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export default defineComponent({
  name: "BrisDocAuditQuestions",
  components: { CleoModalLoadingV2, DynamicQuestionForm, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"BRISDOC_AUDIT_QUESTIONS">>,
      required: true
    },
    cleoClientService: {
      type: String as PropType<CLEO_CLIENT_SERVICE>,
      required: true
    },
    classification: {
      type: String as PropType<CALL_CLASSIFICATION>,
      required: true
    }
  },
  setup(
    props: {
      value: boolean | null;
      cleoClientService: CLEO_CLIENT_SERVICE;
      classification: CALL_CLASSIFICATION;
    },
    context: SetupContext
  ) {
    const brisdocAuditQuestionsController = useBrisdocAuditQuestionsController({
      cleoClientService: props.cleoClientService,
      classification: props.classification
    });

    brisdocAuditQuestionsController.init();

    function onInput(dynamicQuestionsOutput: DynamicQuestionsOutput) {
      context.emit("input", dynamicQuestionsOutput);
    }

    // const getAuditQuestionTypeLabel = computed(() => {
    //   if (props.classification === "Advice") {
    //     return props.cleoClientService;
    //   }
    //   return props.classification;
    // });

    const getTitleEnd = computed(() => {
      const messages = [];

      if (props.cleoClientService.length > 0) {
        messages.push(props.cleoClientService);
      }

      if (props.classification.length > 0) {
        messages.push(props.classification);
      }

      return messages.join(" | ");
    });

    return {
      brisdocAuditQuestionsController,

      getTitleEnd,

      onInput
    };
  }
});
</script>
