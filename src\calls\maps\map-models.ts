import { IBaseSummary } from "@/bases/base-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { IVehicle } from "@/vehicles/vehicles-models";

// declare const google: any;

export type MARKER_TYPE = "BASE" | "CAR" | "CALL";
export interface ICleoMapControllerState {
  currentCall: {
    marker: ICleoGoogleMarker | Record<any, any>;
  };
  bases: {
    isLoading: boolean;
    objs: IBaseSummary[];
    markers: ICleoGoogleMarker[];
  };
  calls: {
    isLoading: boolean;
    objs: ICleoCallSummary[];
    markers: ICleoGoogleMarker[];
  };
  cars: {
    isLoading: boolean;
    objs: IVehicle[];
    markers: ICleoGoogleMarker[];
    assignedCalls: Record<string, ICleoCallSummary[]>;
    infoWindowMap: Record<string, google.maps.InfoWindow>;
  };
}

export interface ICleoGoogleMarker extends google.maps.Marker {
  id: string;
}
