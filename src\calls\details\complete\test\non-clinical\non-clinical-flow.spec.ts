import {
  ICompleteControllerInput,
  useCompleteController
} from "../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import { INonClinicalReason } from "@/calls/details/complete/complete-models";

// let callDetail: ICallDetail;

describe("Non Clinical Flow", () => {
  // beforeEach(() => {
  //   callDetail = factoryCallDetail();
  // });

  it("should validate and progress through all steps", () => {
    const callDetail: ICallDetail = factoryCallDetail();
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        },
        COMPLETE_USE_BRISDOC_NON_CLINI: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC_NON_CLINI"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: false
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    // Step 1: END_ASSESSMENT_CONFIRMATION
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("NON_CLINICAL_REASON");

    // Step 2: NON_CLINICAL_REASON - validate empty selection
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("NON_CLINICAL_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "NON_CLINICAL_REASON__NOT_SELECTED"
    );

    // Step 2: NON_CLINICAL_REASON - provide valid input
    const nonClinicalReason: INonClinicalReason = {
      reason: {
        id: "Cancel Case",
        description: "Cancel Case",
        value: "Cancel Case"
      },
      comment: "Test comment"
    };

    expect(controller.state.userOptions.OUTCOMES.overRideKey).toBe("");

    controller.onNonClinicalReasonSelected(nonClinicalReason);

    // .state.userOptions.OUTCOMES.overRideKey
    expect(controller.state.userOptions.OUTCOMES.overRideKey).toBe(
      "NON_CLINICAL_REASON__CANCEL_CASE"
    );

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");

    // Step 3: OUTCOMES - validate empty selection
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");
    expect(controller.state.validationMessages.length).toBe(1);

    // Step 3: OUTCOMES - provide valid input
    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    controller.goto("NEXT");

    // Verify process completion
    expect(controller.state.isProcessComplete).toBe(true);
  });

  it("should allow backwards navigation through steps", () => {
    const callDetail: ICallDetail = factoryCallDetail();
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        },
        COMPLETE_USE_BRISDOC_NON_CLINI: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC_NON_CLINI"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: false
      }
    };
    const controller = useCompleteController(completeControllerInput);

    // Step 1: END_ASSESSMENT_CONFIRMATION
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("NON_CLINICAL_REASON");

    // Move forward to OUTCOMES
    const nonClinicalReason: INonClinicalReason = {
      reason: {
        id: "Other",
        description: "Other",
        value: "Other"
      },
      comment: ""
    };
    controller.onNonClinicalReasonSelected(nonClinicalReason);
    expect(controller.state.userResponse.nonClinicalReason.reason.id).toBe(
      "Other"
    );
    expect(controller.state.userResponse.nonClinicalReason.comment).toBe("");
    controller.goto("NEXT");

    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.currentStep).toBe("NON_CLINICAL_REASON");

    controller.onNonClinicalReasonSelected({
      reason: {
        id: "Other",
        description: "Other",
        value: "Other"
      },
      comment: "XXX"
    });
    controller.goto("NEXT");
    expect(controller.state.userResponse.nonClinicalReason.reason.id).toBe(
      "Other"
    );
    expect(controller.state.userResponse.nonClinicalReason.comment).toBe("XXX");

    expect(controller.state.validationMessages.length).toBe(0);
    expect(controller.state.currentStep).toBe("NON_CLINICAL_REASON");

    // is process complete?
    expect(controller.state.isProcessComplete).toBe(true);
  });

  it("should maintain state when navigating back and forth", () => {
    const callDetail: ICallDetail = factoryCallDetail();
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        },
        COMPLETE_USE_BRISDOC_NON_CLINI: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC_NON_CLINI"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: false
      }
    };
    const controller = useCompleteController(completeControllerInput);

    // Set initial values
    const nonClinicalReason: INonClinicalReason = {
      reason: {
        id: "Non Clinical Input required",
        description: "Non Clinical Input required",
        value: "Non Clinical Input required"
      },
      comment: "Test comment"
    };

    controller.onNonClinicalReasonSelected(nonClinicalReason);
    controller.goto("NEXT");

    // const outcome: IOutcome = {
    //   id: "OUTCOME_1",
    //   description: "Test Outcome",
    //   value: "OUTCOME_1"
    // };
    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });

    expect(controller.state.userResponse.outcomes.outcome).toEqual("Base");

    // Navigate back
    controller.goto("BACK");
    expect(controller.state.userResponse.nonClinicalReason).toEqual(
      nonClinicalReason
    );
  });
});
