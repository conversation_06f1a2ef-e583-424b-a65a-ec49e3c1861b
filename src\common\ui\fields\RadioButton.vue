<template>
  <label class="ic24-flex-row ic24-flex-gap" style="align-items: baseline">
    <input
      type="radio"
      class="browser-default ic24-input-field ic24-input-field--primary"
      :value="optionValue"
      v-model="valueInternal"
      v-on:change="onValueChanged"
      :disabled="isDisabled"
    />
    <slot><span v-text="label"></span></slot>
  </label>
  <!--  Example use  -->
  <!--  <div class="e4s-flex-row e4s-gap&#45;&#45;large">-->
  <!--  <RadioButton-->
  <!--    label="No"-->
  <!--    :option-value="false"-->
  <!--    v-model="valueInternal.nonClinicalSupportToCompleteCaseRequired"-->
  <!--  />-->
  <!--  <RadioButton-->
  <!--    label="Yes"-->
  <!--    :option-value="true"-->
  <!--    v-model="valueInternal.nonClinicalSupportToCompleteCaseRequired"-->
  <!--  />-->
  <!--  </div>-->
</template>

<script lang="ts">
import {
  ref,
  defineComponent,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { simpleObjectClone } from "@/common/common-utils";

export default defineComponent({
  name: "RadioButton",
  components: {},
  props: {
    optionValue: {
      type: [String, Number, Boolean] as PropType<unknown>,
      required: true
    },
    value: {
      type: [String, Number, Boolean, null] as PropType<
        string | number | boolean | null
      >,
      required: false,
      default: null
    },
    label: {
      type: String as PropType<unknown>,
      default: ""
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  setup(
    props: {
      optionValue: unknown;
      value: unknown;
      label: string;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(simpleObjectClone(props.value));

    watch(
      () => props.value,
      (newValue: unknown) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = simpleObjectClone(newValue);
        }
      },
      {
        immediate: true
      }
    );

    function onValueChanged() {
      console.log("RadioButton.onValueChanged", valueInternal.value);
      context.emit("input", valueInternal.value);
      context.emit("onChanged", valueInternal.value);
    }

    return { onValueChanged, valueInternal };
  }
});
</script>
