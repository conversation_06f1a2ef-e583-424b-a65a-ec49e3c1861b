import { IConditionTab } from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs-models";
import { GUID, IsoDateTimeUTC } from "@/common/common-models";
import { IPathwaysReturnData } from "@/paccs/pathways/pathways-models";

export const PaccsGenders = ["Male", "Female"] as const;
export type PaccsGender = typeof PaccsGenders[number];

export const PaccsAgeGroups = [
  "Adult",
  "Child",
  "Toddler",
  "Infant",
  "Neonate"
] as const;
export type PaccsAgeGroup = typeof PaccsAgeGroups[number];

export const PATHWAYS_SKILL_SET__PACCS = 14;

// export interface IPaccsGlobalConfig {
//   caseId: string; //  E.g. 3fa85f64-5717-4562-b3fc-2c963f66afa6, a "session" id.
//   ambJump: string;
//   homeMgmtJump: string;
// }

export interface IPaccsForm {
  // globalConfig: IPaccsGlobalConfig;
  patientInfo: {
    gender: PaccsGender;
    ageGroup: PaccsAgeGroup;
  };
  conditionTabsArray: IConditionTab[];
}

export type ANSWER_NUMBER = 0 | 1 | 2; // 0 = no selection, considered = 1, suspected = 2

/**
 * Page 113
 */
export interface ITriageRecord {
  caseId: string; //  E.g. 3fa85f64-5717-4562-b3fc-2c963f66afa6  (Session ID)
  userId: string; //  E.g. CN=Joe Bloggs, O=sehnp or whatever user unique ID.
  timeIn: IsoDateTimeUTC; // "2021-05-07T08:51:00.891Z",	// ....	time happened, selected template\condition, whatever.
  // timeOut: string;               // "2021-05-07T08:51:00.891Z",....  Server does this.
  pathwayId: string; //  ....templateId
  actionId: 1 | 2 | 3 | 4 | 5 | 6; //  1 (Back) 2 (Next) 3 (Change) 4 (Early Exit)  5 (Restart) 6 (End)

  // hostSystem: "CLEO"; //  ?
  includeInReport: false | true | null; //  always 1
  interfaceId: 15; //.... set of questions, dos screen, paccs, etc.
  skillSet: number;
  siteId: "WHAT_VALUE_SHOULD_GO_HERE"; //  What value???
  symptomGroup: number; //  E.g. 1185

  cxKeywords?: string; //  ........not used
  dispositionRationale?: string; //	....not used
  userComment?: string; // ....the 300char text field
  quId?: string; // ....cs number
  answerNumber?: ANSWER_NUMBER; //  0 = no selection, considered = 1, suspected = 2
  reportText?: string; // ....The concatenation of Template::TemplateName, “/”, Condition::Title, “, Suspected” or “, Considered” depending on option selected.
}

export interface ITriageRecordTemplate extends ITriageRecord {
  dispositionRationale: string; //	TemplateName
}

export interface ITriageRecordCondition extends ITriageRecord {
  userComment: string; // ....the 300char text field
  quId: string; // ....cs number
  answerNumber: ANSWER_NUMBER; //  0 = no selection, considered = 1, suspected = 2
  reportText: string; // ....The concatenation of Template::TemplateName, “/”, Condition::Title, “, Suspected” or “, Considered” depending on option selected.
}

export interface ITriageRecordResponse {
  caseRecordId: number; //   "row" index in table
}

export interface IPathwaysDatasetLegacy {
  skillset: number;
  userId: string;
  pathwaysCaseId: string;
  InjuryModule: string; //  E.g. "On"   ???
  cadCaseId: string;
  hostCaseId: string; //  CallNo
  setting: string;
  serviceId: string; //  Currently ServiceName, E.g. East Kent 111
  hostDocId: string; //  Call unique identifier, currently doc unid.
  casQ: 0 | 1;
  caseStartTime: string; //  E.g. 2021-05-12T09:34:01 01:00  crappy non iso format.
  patientId: string; //  E.g. ASHD-AKECKS-497
  surname: string;
  forename: string;
  CallerTelephoneNumber: string;
  // returnTelephoneNumber?: string;
  // callerTelephoneNumber?: string;
  ageInHours: number;
  gender: 1 | 2 | 3; //  1 = Male, 2 = Female, 3 = Unknown
  party: 1 | 3; //  CallCRel = Patient | Walk-In Patient ? 3 : 1
  surgeryId: string; //  OCS Code
  previousEncounters: number | string; //  ...but actually a number, E.g. "1"
  presentingCondition: string;
  addressBuildingPrefix: string;
  addressBuildingIdentifier: string;
  addressStreetName1: string;
  addressStreetName2: string;
  addressTown: string;
  addressCounty: string;
  postCode: string;
  // consentToShare: string;
  // scrConsent: string;
  // callSPA: string;
  // scrReason: string;
  addressDescription: string;
  addressEasting: number | string; //  E.g. 58470
  addressNorthing: number | string; //  E.g.20820
  // callerName: string;
  addressKey: string;
  clinicianCallStartTime: string; //  E.g. 2021-05-12T09:34:01 01:00   on iso...again.
  nhsId: string;
  TraceVerified: boolean;
  dob: string; //  E.g. 20210512.
}

export interface IPathwaysDataset {
  addressBuildingPrefix: string;
  addressBuildingIdentifier: string;
  addressStreetName1: string;
  addressStreetName2: string;
  addressTown: string;
  addressCounty: string;
  addressDescription: string;
  addressEasting: number;
  addressNorthing: number;
  addressKey: string;
  ageInHours: number;
  callerTelephoneNumber: string;
  caseStartTime: string; // "2021-05-18T11:21:48.492Z";
  clinicianCallStartTime: string; // "2021-05-18T11:21:48.492Z";
  forename: string;
  gender: "Male" | "Female";
  hostCaseId: string;
  hostDocId: string;
  nhsId: string;
  party: "First";
  patientId: string;
  postCode: string;
  presentingCondition: string;
  previousEncounters: number;
  serviceId: string; //  E.g. East Kent 111
  skillSet: number;
  surgeryId: string;
  surname: string;
  traceVerified: boolean;
  userId: string;
  dob: string; //  E.g. 20210512.
}

export interface IPathwaysDatasetContinue {
  caseId: GUID;
}

export interface IPaccsFormState {
  tabRemove: {
    showModal: boolean;
    message: string;
  };
  restartTriage: {
    showModal: boolean;
    isLoading: boolean;
  };
  pathways: IPaccsFormStatePathways;
}

export interface IPaccsFormStatePathways {
  changingAnswer: boolean;
  changingAnswerGetConfirmation: boolean;
  caseId: string;
  reportHtml: string;
  pathwaysReturnData: IPathwaysReturnData | null;
}
