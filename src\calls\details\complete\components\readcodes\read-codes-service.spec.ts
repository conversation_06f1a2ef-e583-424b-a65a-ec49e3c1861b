import { simpleObjectClone } from "@/common/common-utils";
import { mockReadCodesBaseSearch } from "@/calls/details/complete/components/readcodes/mock/mock-read-codes-base-search";
import { convertReadCodeDataForTree } from "@/calls/details/complete/components/readcodes/read-codes-service";

describe("ReadCodesService", () => {
  it("convertReadCodeDataForTree", () => {
    const data = simpleObjectClone(mockReadCodesBaseSearch);

    const result = convertReadCodeDataForTree(data.DATA.readCodes, 0);
    expect(result.nodeCount).toBe(5);
    expect(result.readCodesTree.length).toBe(5);
    expect(result.readCodesTree[0].label).toBe("Additional values : X78tJ");
  });
});
