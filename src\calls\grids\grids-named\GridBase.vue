<template>
  <GridStandardLayout :grid-definition="gridDefinition" />
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { GRID_DEFINITIONS } from "../grid-models";
import GridStandardLayout from "@/calls/grids/grids-named/GridStandardLayout.vue";
export default defineComponent({
  name: "grid-base",
  components: { GridStandardLayout },
  setup(context: SetupContext) {
    const gridDefinition = GRID_DEFINITIONS.NavBase;

    //  TODO switch to v3 useRoute()
    const id: number = isNaN(Number(context.root.$route.params.id))
      ? 0
      : parseInt(context.root.$route.params.id, 0);
    gridDefinition.params.DutyBaseId = id;

    return {
      gridDefinition
    };
  }
});
</script>
