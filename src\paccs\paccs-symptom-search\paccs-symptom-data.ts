import { CLEO_CONFIG } from "@/common/config/config-";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import https from "@/common/https";

export class PaccsSymptomData {
  private endPoint = CLEO_CONFIG.PACCS_URL ? CLEO_CONFIG.PACCS_URL : "";

  public getTemplateBySymptom(symptom: string): Promise<IPaccsSymptomModel[]> {
    return https.get(
      this.endPoint + "/api/template/gettemplatebysymptom/" + symptom
    );
  }
}
