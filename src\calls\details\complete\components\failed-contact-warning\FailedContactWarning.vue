<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <complete-step-header :step="step" />

    <div class="ic24-flex-column ic24-flex-gap-large">
      <span class="complete-step--subheader">
        You are recording a failed contact.
      </span>
      <span class="complete-step--subheader">
        The patient will now be returned to the call queue as an open case.
      </span>
      <span class="complete-step--subheader">
        Please ensure a risk assessment is made and documented as per our failed
        contact policy.
      </span>
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <div class="ic24-flex-row ic24-flex-gap ic24-justify-flex-space-between">
      <!--      <Ic24Button-->
      <!--        class="failed-contact-warning&#45;&#45;button complete-step&#45;&#45;simple-buttons"-->
      <!--        :title="failedContactWarningTypeMap.RETURN_TO_OPEN_CASE.description"-->
      <!--        @click="onSelected(failedContactWarningTypeMap.RETURN_TO_OPEN_CASE)"-->
      <!--      />-->
      <Ic24Button
        @click="onSelected(failedContactWarningTypeMap.RETURN_TO_OPEN_CASE)"
        :title="failedContactWarningTypeMap.RETURN_TO_OPEN_CASE.description"
        class="complete-step--select-button complete-step--simple-buttons failed-contact-warning--button"
      />
      <!--      <Ic24Button-->
      <!--        class="failed-contact-warning&#45;&#45;button"-->
      <!--        :title="-->
      <!--          failedContactWarningTypeMap.SAVE_AND_RETURN_TO_QUEUE.description-->
      <!--        "-->
      <!--        @click="-->
      <!--          onSelected(failedContactWarningTypeMap.SAVE_AND_RETURN_TO_QUEUE)-->
      <!--        "-->
      <!--      />-->
      <Ic24Button
        @click="
          onSelected(failedContactWarningTypeMap.SAVE_AND_RETURN_TO_QUEUE)
        "
        :title="
          failedContactWarningTypeMap.SAVE_AND_RETURN_TO_QUEUE.description
        "
        class="complete-step--select-button complete-step--simple-buttons failed-contact-warning--button"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import {
  CompleteFinalAction,
  IStep,
  IUserResponseGeneric
} from "../../complete-models";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { FailedContactWarningTypeMap } from "@/calls/details/complete/components/failed-contact-warning/failed-contact-warning-models";
import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import { ISimpleButtonInputValue } from "@/calls/details/complete/simple-button-selector-models";

export default defineComponent({
  name: "FailedContactWarning",
  components: { CompleteStepHeader, Ic24Button },
  props: {
    step: {
      type: Object as PropType<IStep<"FAILED_CONTACT_WARNING">>,
      required: true
    },
    value: {
      type: Object as PropType<
        IUserResponseGeneric<CompleteFinalAction, CompleteFinalAction>
      >,
      required: true
    }
  },
  setup(
    props: {
      step: IStep<"FAILED_CONTACT_WARNING">;
      value: IUserResponseGeneric<CompleteFinalAction, CompleteFinalAction>;
    },
    context: SetupContext
  ) {
    const failedContactWarningTypeMap = FailedContactWarningTypeMap;

    function onSelected(
      failedContactWarningType: ISimpleButtonInputValue<
        CompleteFinalAction,
        CompleteFinalAction
      >
    ) {
      console.log(
        "FailedContactWarning.onSelected: ",
        failedContactWarningType
      );
      context.emit("input", simpleObjectClone(failedContactWarningType));

      if (failedContactWarningType.id === "RETURN_TO_OPEN_CASE") {
        context.emit("returnToOpenCase");
      }
      if (failedContactWarningType.id === "SAVE_AND_RETURN_TO_QUEUE") {
        context.emit("saveAndReturnToQueue");
      }
    }

    return { failedContactWarningTypeMap, onSelected };
  }
});
</script>

<style scoped>
.failed-contact-warning--button {
  width: 200px;
}
</style>
