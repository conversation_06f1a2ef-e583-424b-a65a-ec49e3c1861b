import { PERMISSION_NAMES } from "@/permissions/permission-models";

export interface IAddress {
  line1: string;
  line2: string;
  line3: string;
  line4: string;
  postcode: string;
}

//  This does closely mimic PERMISSIONS, but it is not the same
export type CLEO_ACTION_NAME =
  | "MAKE_APPOINTMENT"
  | "ARRIVED"
  | "MODIFY_ARRIVED"
  | "ACKNOWLEDGE_RECEIPT_BASE"
  | "COMFORT_COURTESY_CALL"
  | "EDIT CLASSIFICATION"
  | "DOWNGRADE"
  | "ASSIGN"
  | "ASSIGN_BASE"
  | "SECONDARY_ASSIGN"
  | "PRIORITY"
  | "DISPATCH_VEHICLE"
  | "RETRIEVE_VEHICLE"
  | "PRINT"
  | "UNLOCK CALL"
  | "RESEND_INDIV"
  | "RESEND_SUMMARY"
  | "RESEND_DTS"
  | "RESEND_RESOLVED"
  | "RESEND_PEMS"
  | "RESEND_CAN_NOT_RESOLVE"
  | "ADD_CONSULTATION"
  | "SUPPORT"
  | "NEW_CALL__OOH"
  | "NEW_CALL__EAST_KENT_111"
  | "NEW_CALL__NOR_WIS_111"
  | "NEW_CALL__NORTH_ESSEX_111"
  | "NEW_CALL__SOUTH_ESSEX_111"
  | "NEW_CALL__DEVON_111"
  | "NEW_CALL__POSL"
  | "NEW_CALL__BRIGHTON_DISTRICT_NURSE"
  | "NEW_CALL__ROVING_GP"
  | "NEW_CALL__FCMS"
  | "NEW CALL - BRISDOC"
  | "NEW CALL - FRAILTY"
  | "NEW CALL - MENTAL HEALTH"
  | "NEW CALL - OUT OF HOURS PROFESSIONAL LINE"
  | "NEW CALL - PAEDIATRICS"
  | "NEW CALL - PATIENTLINE"
  | "NEW CALL - WEEKDAY PROFESSIONAL LINE"
  | "OVERSIGHT_VALIDATION"
  | "VALIDATE_CAS"
  | "ASSIGN_DX"
  | "ASSIGN_TO_BASE_NO_ROTA"
  | "ASSIGN_TO_CLINICIAN_NO_ROTA"
  | "ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR"
  | "MOVE_CASE"
  | "MOVE_CASE_OUT_URGENT_FOLLOW_UP"
  | "CAS_APPOINTMENT"
  | "ADDCOMMENTS"
  | "SMS_MANUAL"
  | "APPTS_EXTERNAL"
  | "REQUEST_GS_PHOTO"
  | "REFRESH_GS_PHOTO"
  | "PLS_REMOVE";

export interface ICleoAction {
  id: CLEO_ACTION_NAME;
  permissionName: string;
  title: string;
}

export const CLEO_CALL_ACTIONS: Record<CLEO_ACTION_NAME, ICleoAction> = {
  MAKE_APPOINTMENT: {
    id: "MAKE_APPOINTMENT",
    permissionName: PERMISSION_NAMES.MAKE_APPOINTMENT,
    title: "Make Appointment"
  },
  ARRIVED: {
    id: "ARRIVED",
    permissionName: PERMISSION_NAMES.ARRIVED,
    title: "Arrived"
  },
  MODIFY_ARRIVED: {
    id: "MODIFY_ARRIVED",
    permissionName: PERMISSION_NAMES.ARRIVED,
    title: "Modify Arrived"
  },
  ACKNOWLEDGE_RECEIPT_BASE: {
    id: "ACKNOWLEDGE_RECEIPT_BASE",
    permissionName: PERMISSION_NAMES.ACKNOWLEDGE_RECEIPT_BASE,
    title: "Acknowledge Receipt Base"
  },
  COMFORT_COURTESY_CALL: {
    id: "COMFORT_COURTESY_CALL",
    permissionName: PERMISSION_NAMES.COMFORT_COURTESY_CALL,
    title: "Comfort / Courtesy Call"
  },
  "EDIT CLASSIFICATION": {
    id: "EDIT CLASSIFICATION",
    permissionName: PERMISSION_NAMES["EDIT CLASSIFICATION"],
    title: "Edit Classification"
  },
  DOWNGRADE: {
    id: "DOWNGRADE",
    permissionName: PERMISSION_NAMES.DOWNGRADE,
    title: "DownGrade"
  },
  ASSIGN: {
    id: "ASSIGN",
    permissionName: PERMISSION_NAMES.ASSIGN_CALL,
    title: "Assign"
  },
  ASSIGN_BASE: {
    id: "ASSIGN_BASE",
    permissionName: PERMISSION_NAMES.ASSIGN_CALL_BASE,
    title: "Assign to Base"
  },
  SECONDARY_ASSIGN: {
    id: "SECONDARY_ASSIGN",
    permissionName: PERMISSION_NAMES.ASSIGN_CALL_SECONDARY,
    title: "Secondary Assign"
  },
  PRIORITY: {
    id: "PRIORITY",
    permissionName: PERMISSION_NAMES.PRIORITY,
    title: "Priority"
  },
  DISPATCH_VEHICLE: {
    id: "DISPATCH_VEHICLE",
    permissionName: PERMISSION_NAMES.DISPATCH_VEHICLE,
    title: "Dispatch To Vehicle"
  },
  RETRIEVE_VEHICLE: {
    id: "RETRIEVE_VEHICLE",
    permissionName: PERMISSION_NAMES.RETRIEVE_VEHICLE,
    title: "Retrieve From Vehicle"
  },
  PRINT: {
    id: "PRINT",
    permissionName: PERMISSION_NAMES.PRINT,
    title: "Print"
  },
  ["UNLOCK CALL"]: {
    id: "UNLOCK CALL",
    permissionName: PERMISSION_NAMES["UNLOCK CALL"],
    title: "Unlock Call"
  },
  RESEND_INDIV: {
    id: "RESEND_INDIV",
    permissionName: PERMISSION_NAMES.RESEND_INDIV,
    title: "Resend Individual Email"
  },
  RESEND_SUMMARY: {
    id: "RESEND_SUMMARY",
    permissionName: PERMISSION_NAMES.RESEND_SUMMARY,
    title: "Resend Summary Email"
  },
  RESEND_DTS: {
    id: "RESEND_DTS",
    permissionName: PERMISSION_NAMES.RESEND_DTS,
    title: "Resend DTS"
  },
  RESEND_RESOLVED: {
    id: "RESEND_RESOLVED",
    permissionName: PERMISSION_NAMES.RESEND_RESOLVED,
    title: "Resolve Messaging"
  },
  RESEND_PEMS: {
    id: "RESEND_PEMS",
    permissionName: PERMISSION_NAMES.RESEND_PEMS,
    title: "Resend All PEMS"
  },
  RESEND_CAN_NOT_RESOLVE: {
    id: "RESEND_CAN_NOT_RESOLVE",
    permissionName: PERMISSION_NAMES.RESEND_CAN_NOT_RESOLVE,
    title: "Cannot Resolve Messaging"
  },
  ADD_CONSULTATION: {
    id: "ADD_CONSULTATION",
    permissionName: PERMISSION_NAMES.ADD_CONSULTATION,
    title: "Add Consult to 111 case"
  },
  SUPPORT: {
    id: "SUPPORT",
    permissionName: PERMISSION_NAMES.NO_SUPPORT,
    title: "Support"
  },

  NEW_CALL__OOH: {
    id: "NEW_CALL__OOH",
    permissionName: PERMISSION_NAMES.NEW_CALL__OOH,
    title: "OOH"
  },
  NEW_CALL__EAST_KENT_111: {
    id: "NEW_CALL__EAST_KENT_111",
    permissionName: PERMISSION_NAMES.NEW_CALL__EAST_KENT_111,
    title: "East Kent 111"
  },
  NEW_CALL__NOR_WIS_111: {
    id: "NEW_CALL__NOR_WIS_111",
    permissionName: PERMISSION_NAMES.NEW_CALL__NOR_WIS_111,
    title: "Norfolk and Waveney 111"
  },
  NEW_CALL__NORTH_ESSEX_111: {
    id: "NEW_CALL__NORTH_ESSEX_111",
    permissionName: PERMISSION_NAMES.NEW_CALL__NORTH_ESSEX_111,
    title: "North Essex 111"
  },
  NEW_CALL__SOUTH_ESSEX_111: {
    id: "NEW_CALL__SOUTH_ESSEX_111",
    permissionName: PERMISSION_NAMES.NEW_CALL__SOUTH_ESSEX_111,
    title: "South Essex 111"
  },
  NEW_CALL__DEVON_111: {
    id: "NEW_CALL__DEVON_111",
    permissionName: PERMISSION_NAMES.NEW_CALL__DEVON_111,
    title: "Devon 111"
  },

  NEW_CALL__POSL: {
    id: "NEW_CALL__POSL",
    permissionName: PERMISSION_NAMES.NEW_CALL__POSL,
    title: "POSL"
  },
  NEW_CALL__BRIGHTON_DISTRICT_NURSE: {
    id: "NEW_CALL__BRIGHTON_DISTRICT_NURSE",
    permissionName: PERMISSION_NAMES.NEW_CALL__BRIGHTON_DISTRICT_NURSE,
    title: "Brighton District Nurse"
  },
  NEW_CALL__ROVING_GP: {
    id: "NEW_CALL__ROVING_GP",
    permissionName: PERMISSION_NAMES.NEW_CALL__ROVING_GP,
    title: "Roving GP"
  },
  NEW_CALL__FCMS: {
    id: "NEW_CALL__FCMS",
    permissionName: PERMISSION_NAMES.NEW_CALL__FCMS,
    title: "FCMS"
  },
  "NEW CALL - BRISDOC": {
    id: "NEW CALL - BRISDOC",
    permissionName: PERMISSION_NAMES["NEW CALL - BRISDOC"],
    title: "CAS"
  },
  "NEW CALL - FRAILTY": {
    id: "NEW CALL - FRAILTY",
    permissionName: PERMISSION_NAMES["NEW CALL - FRAILTY"],
    title: "Frailty"
  },
  "NEW CALL - MENTAL HEALTH": {
    id: "NEW CALL - MENTAL HEALTH",
    permissionName: PERMISSION_NAMES["NEW CALL - MENTAL HEALTH"],
    title: "Mental Health"
  },
  "NEW CALL - OUT OF HOURS PROFESSIONAL LINE": {
    id: "NEW CALL - OUT OF HOURS PROFESSIONAL LINE",
    permissionName:
      PERMISSION_NAMES["NEW CALL - OUT OF HOURS PROFESSIONAL LINE"],
    title: "Out Of Hours Professional Line"
  },
  "NEW CALL - PAEDIATRICS": {
    id: "NEW CALL - PAEDIATRICS",
    permissionName: PERMISSION_NAMES["NEW CALL - PAEDIATRICS"],
    title: "Paediatrics"
  },
  "NEW CALL - WEEKDAY PROFESSIONAL LINE": {
    id: "NEW CALL - WEEKDAY PROFESSIONAL LINE",
    permissionName: PERMISSION_NAMES["NEW CALL - WEEKDAY PROFESSIONAL LINE"],
    title: "Weekday Professional Line"
  },
  "NEW CALL - PATIENTLINE": {
    id: "NEW CALL - PATIENTLINE",
    permissionName: PERMISSION_NAMES["NEW CALL - PATIENTLINE"],
    title: "PatientLine"
  },
  OVERSIGHT_VALIDATION: {
    id: "OVERSIGHT_VALIDATION",
    permissionName: PERMISSION_NAMES.OVERSIGHT_VALIDATION,
    title: "Oversight Validation"
  },
  VALIDATE_CAS: {
    id: "VALIDATE_CAS",
    permissionName: PERMISSION_NAMES.VALIDATE_CAS,
    title: "Validate"
  },
  ASSIGN_DX: {
    id: "ASSIGN_DX",
    permissionName: PERMISSION_NAMES.ASSIGN_DX,
    title: "Change Priority"
  },
  ASSIGN_TO_BASE_NO_ROTA: {
    id: "ASSIGN_TO_BASE_NO_ROTA",
    permissionName: PERMISSION_NAMES.ASSIGN_TO_BASE_NO_ROTA,
    title: "Assign Base"
  },
  ASSIGN_TO_CLINICIAN_NO_ROTA: {
    id: "ASSIGN_TO_CLINICIAN_NO_ROTA",
    permissionName: PERMISSION_NAMES.ASSIGN_TO_CLINICIAN_NO_ROTA,
    title: "Assign Clinician"
  },
  ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR: {
    id: "ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR",
    permissionName: PERMISSION_NAMES.ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR,
    title: "Un-Assign Clinician"
  },
  MOVE_CASE: {
    id: "MOVE_CASE",
    permissionName: PERMISSION_NAMES.MOVE_CASE,
    title: "Move Case"
  },
  MOVE_CASE_OUT_URGENT_FOLLOW_UP: {
    id: "MOVE_CASE_OUT_URGENT_FOLLOW_UP",
    permissionName: PERMISSION_NAMES.MOVE_CASE_OUT_URGENT_FOLLOW_UP,
    title: "Make Active"
  },
  CAS_APPOINTMENT: {
    id: "CAS_APPOINTMENT",
    permissionName: PERMISSION_NAMES.CAS_APPOINTMENT,
    title: "CAS Appointment"
  },
  ADDCOMMENTS: {
    id: "ADDCOMMENTS",
    permissionName: PERMISSION_NAMES.ADDCOMMENTS,
    title: "Case Comments"
  },
  SMS_MANUAL: {
    id: "SMS_MANUAL",
    permissionName: PERMISSION_NAMES.SMS_MANUAL,
    title: "Send SMS"
  },
  APPTS_EXTERNAL: {
    id: "APPTS_EXTERNAL",
    permissionName: PERMISSION_NAMES.APPTS_EXTERNAL,
    title: "Appointment"
  },
  REQUEST_GS_PHOTO: {
    id: "REQUEST_GS_PHOTO",
    permissionName: PERMISSION_NAMES.REQUEST_GS_PHOTO,
    title: "Request Photo"
  },
  REFRESH_GS_PHOTO: {
    id: "REFRESH_GS_PHOTO",
    permissionName: PERMISSION_NAMES.REQUEST_GS_PHOTO,
    title: "Refresh Photo"
  },
  PLS_REMOVE: {
    id: "PLS_REMOVE",
    permissionName: PERMISSION_NAMES.PLS_REMOVE,
    title: "PLS Remove"
  }
};

/**
 *   | "NEW CALL - BRISDOC"
 *   | "NEW CALL - FRAILTY"
 *   | "NEW CALL - MENTAL HEALTH"
 *   | "NEW CALL - OUT OF HOURS PROFESSIONAL LINE"
 *   | "NEW CALL - PAEDIATRICS"
 *   | "NEW CALL - PATIENTLINE"
 *   | "NEW CALL - WEEKDAY PROFESSIONAL LINE";
 */

export type PEM_MESSAGE_TYPE =
  | "Resend Individual"
  | "Resend Summary"
  | "Resend DTS"
  | "Resend PEMS"
  | "Cannot Resolve";

export interface ICleoServerResponse<T> {
  RESULT: "SUCCESS" | "FAILURE";
  DATA: T;
  MESSAGE?: string;
}

export interface ICleoServerResponseList<T> {
  RESULT: "SUCCESS" | "FAILURE";
  DATA: T[];
  MESSAGE?: string;
}

export interface IBaseConcrete {
  id: string;
  name: string;
}
