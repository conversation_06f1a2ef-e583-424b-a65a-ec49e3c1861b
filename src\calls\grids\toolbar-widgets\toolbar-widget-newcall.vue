<template>
  <div class="dropdown" v-if="getHasAnyNewCallPerms">
    <!--    <button class="dropbtn">-->
    <!--      <span-->
    <!--        class="standard-icon dijit-editor-icon dijit-editor-icon&#45;&#45;copy"-->
    <!--      ></span>-->
    <!--      New Call-->
    <!--    </button>-->
    <Ic24Button title="New Call" class="dropbtn" />
    <div class="dropdown-content">
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__OOH]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__OOH"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__NOR_WIS_111]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__NOR_WIS_111"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__EAST_KENT_111]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__EAST_KENT_111"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__NORTH_ESSEX_111]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__NORTH_ESSEX_111"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__SOUTH_ESSEX_111]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__SOUTH_ESSEX_111"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__DEVON_111]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__DEVON_111"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>

      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__POSL]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__POSL"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__BRIGHTON_DISTRICT_NURSE]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__BRIGHTON_DISTRICT_NURSE"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__ROVING_GP]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__ROVING_GP"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.NEW_CALL__FCMS]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.NEW_CALL__FCMS"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>

      <!--BrisDoc-->
      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - BRISDOC']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - BRISDOC']"
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - FRAILTY']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - FRAILTY']"
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - MENTAL HEALTH']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - MENTAL HEALTH']"
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - OUT OF HOURS PROFESSIONAL LINE']"
        :key-perms="userAppPermsShort"
        :cleo-action="
          cleoCallActions['NEW CALL - OUT OF HOURS PROFESSIONAL LINE']
        "
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - PAEDIATRICS']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - PAEDIATRICS']"
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - PATIENTLINE']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - PATIENTLINE']"
        v-on:onLinkClick="onLinkClick"
      />

      <CallSummaryToolbarLink
        v-if="!!userAppPermsShort['NEW CALL - WEEKDAY PROFESSIONAL LINE']"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions['NEW CALL - WEEKDAY PROFESSIONAL LINE']"
        v-on:onLinkClick="onLinkClick"
      />
    </div>
    <!--/BrisDoc-->
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { ICleoPermission } from "@/permissions/permission-models";
import CallSummaryToolbarLink from "@/calls/summary/call-summary-toolbar/call-summary-toolbar-link.vue";
import { CLEO_CALL_ACTIONS, ICleoAction } from "@/common/cleo-common-models";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import { PERMISSION_NAMES } from "@/permissions/permission-models";
import { PermissionService } from "@/permissions/permission-service";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";

@Component({
  name: "toolbar-widget-new-call",
  components: { Ic24Button, CallSummaryToolbarLink, LoadingSpinner }
})
export default class ToolbarWidgetNewCall extends Vue {
  @Prop({
    default: () => {
      return {};
    }
  })
  public readonly userAppPermsShort!: Record<string, ICleoPermission>;

  public permissionNames = PERMISSION_NAMES;

  public keysPerm: Record<string, boolean> = {};
  public cleoCallActions = CLEO_CALL_ACTIONS;
  public callSummaryController: CallSummaryController = new CallSummaryController();
  public permissionService: PermissionService = new PermissionService();

  public created(): void {
    this.keysPerm = this.getKeysPerm();
  }

  @Watch("userAppPermsShort")
  public onUserAppPermsShortChanged(): void {
    this.keysPerm = this.getKeysPerm();
  }

  public getKeysPerm(): Record<string, boolean> {
    if (Object.keys(this.userAppPermsShort).length === 0) {
      return {};
    }

    //  Object.values(this.permissionNames)  would do it dynamically, but...
    //  we could even filter for "NEW CALL", but until we have all the new
    //  calls in here, use this method.  Side effect is, getHasAnyNewCallPerms()
    //  thinks user has "NEW CALL - Some Old Service", because the user has the perm
    //  but not displaying that above, so New Call button displays.

    const permsToCheck = [
      this.permissionNames.NEW_CALL__OOH,
      this.permissionNames.NEW_CALL__NOR_WIS_111,
      this.permissionNames.NEW_CALL__EAST_KENT_111,
      this.permissionNames.NEW_CALL__NORTH_ESSEX_111,
      this.permissionNames.NEW_CALL__SOUTH_ESSEX_111,
      this.permissionNames.NEW_CALL__DEVON_111,
      this.permissionNames.NEW_CALL__POSL,
      this.permissionNames.NEW_CALL__ROVING_GP,
      this.permissionNames.NEW_CALL__BRIGHTON_DISTRICT_NURSE,
      this.permissionNames.NEW_CALL__FCMS,
      this.permissionNames["NEW CALL - BRISDOC"],
      this.permissionNames["NEW CALL - FRAILTY"],
      this.permissionNames["NEW CALL - MENTAL HEALTH"],
      this.permissionNames["NEW CALL - OUT OF HOURS PROFESSIONAL LINE"],
      this.permissionNames["NEW CALL - PAEDIATRICS"],
      this.permissionNames["NEW CALL - PATIENTLINE"],
      this.permissionNames["NEW CALL - WEEKDAY PROFESSIONAL LINE"]
    ];

    console.log("ToolbarWidgetNewCall permsToCheck", {
      permsToCheck,
      userAppPermsShort: this.userAppPermsShort
    });
    return permsToCheck.reduce((accum, permName) => {
      const hasPerm = this.userAppPermsShort[permName.toUpperCase()];
      console.log("ToolbarWidgetNewCall", {
        permName,
        hasPerm
      });
      if (hasPerm) {
        accum[permName] = true;
      }
      return accum;
    }, {} as Record<string, boolean>);
  }

  public get getHasAnyNewCallPerms(): boolean {
    return Object.keys(this.keysPerm).length > 0;
  }

  public onLinkClick(cleoAction: ICleoAction): void {
    this.callSummaryController.processAction(cleoAction.id, []);
  }
}
</script>
