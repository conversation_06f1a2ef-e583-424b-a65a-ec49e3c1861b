// import {CleoRightClickAgGridService} from "@/calls/grids/ag-grid-specific/ag-grid-right-click";
// import {CallSummaryService} from "@/calls/summary/call-summary-service";
//
// const cleoRightClickAgGridService: CleoRightClickAgGridService = new CleoRightClickAgGridService();
// const callSummaryService: CallSummaryService = new CallSummaryService();
//
describe("CleoRightClickAgGridService", () => {
  it("timeHumanShort", () => {
    // expect(cleoRightClickAgGridService.getContextMenuItems().length).toBe(3);
    expect(true).toBe(true);
  });
});
