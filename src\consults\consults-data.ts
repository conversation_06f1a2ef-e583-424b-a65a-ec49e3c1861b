import { CLEO_CONFIG } from "@/common/config/config-";
import https from "@/common/https";
import { IAdapterPagedResponse } from "@/common/common-models";
import { IConsultServerOutbound } from "@/consults/consult-models";

export class ConsultsData {
  private adapterEndPoint = CLEO_CONFIG.ADAPTER_URL
    ? CLEO_CONFIG.ADAPTER_URL
    : "";

  /**
   * E.g. /api/consultations/searchConsultations?callNo=22222&callNo=1111&CallNo=4321
   * @param callNumbers
   */
  public getConsultsApi(
    callNumbers: number[]
  ): Promise<IAdapterPagedResponse<IConsultServerOutbound>> {
    const params = callNumbers
      .map(callNumber => {
        return "callNo=" + callNumber;
      })
      .join("&");

    return https.get(
      this.adapterEndPoint + "/api/consultations/searchConsultations?" + params,
      {
        responseType: "json"
      }
    );
  }
}
