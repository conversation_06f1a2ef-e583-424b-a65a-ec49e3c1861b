<template>
  <div class="paccs-pathways--container">
    <div style="z-index: 100;position: absolute" v-if="isLocalDevServer">
      <button v-on:click="testChange">POSTNOTES</button>
    </div>

    <iframe
      :style="{ display: hideButKeepPathwaysSessionOpen ? 'none' : '' }"
      :src="pathwaysLaunchUrl"
      id="paccs-pathways-iframe"
      class="paccs-pathways--iframe"
    ></iframe>
    <div class="paccs-pathways--mask-class" v-if="showMask">
      <div class="paccs-pathways--mask-message" v-text="maskMessage"></div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { IPathwaysDatasetLegacy } from "@/paccs/paccs-models";
import {
  IPathwaysJumpToPayload,
  IPathwaysReturnData
} from "@/paccs/pathways/pathways-models";
import { loggerInstance } from "@/common/Logger";
import { useStore } from "@/store/store";
import { CONFIG_STORE_CONST } from "@/common/config/config-store";

export default defineComponent({
  name: "pathways-iframe",
  props: {
    pathwaysPayload: {
      required: true,
      type: Object as PropType<IPathwaysDatasetLegacy | IPathwaysJumpToPayload>
    },
    hideButKeepPathwaysSessionOpen: {
      type: Boolean
    },
    showMask: {
      type: Boolean
    },
    maskMessage: {
      type: String
    }
  },
  components: {},
  setup(
    props: {
      pathwaysPayload: IPathwaysDatasetLegacy | IPathwaysJumpToPayload;
      hideButKeepPathwaysSessionOpen: boolean;
    },
    context: SetupContext
  ) {
    const pathwaysLaunchUrl =
      window.NHSPathwaysSEHUrl +
      "/StartPathways.htm#" +
      encodeURIComponent(document.location.href);

    const isLocalDevServer = useStore().state[CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME].isLocalDevServer;

    loggerInstance.log(
      "pathways-iframe pathwaysPayload: ",
      props.pathwaysPayload
    );

    if (window.$) {
      window.$.receiveMessage(function(e: any) {
        close(JSON.parse(e.data) as IPathwaysReturnData);
      }, window.NHSPathwaysSEHUrl);
    }
    window.setTimeout(function() {
      sendPayloadToPathways(props.pathwaysPayload);
    }, 1500);

    watch(
      () => props.pathwaysPayload,
      (newValue: IPathwaysDatasetLegacy | IPathwaysJumpToPayload) => {
        loggerInstance.log("pathways-iframe: pathwaysPayload changed");
        sendPayloadToPathways(newValue);
      }
    );

    function sendPayloadToPathways(
      pathwaysPayload: IPathwaysDatasetLegacy | IPathwaysJumpToPayload
    ) {
      const iframe: HTMLIFrameElement | null = window.document.getElementById(
        "paccs-pathways-iframe"
      ) as HTMLIFrameElement;
      if (iframe && window.$) {
        window.$.postMessage(
          JSON.stringify(pathwaysPayload),
          pathwaysLaunchUrl,
          iframe.contentWindow
        );
      }
    }

    function close(pathwaysReturnData: IPathwaysReturnData): void {
      // context.emit("close", pathwaysReturnData);
      context.emit("message", pathwaysReturnData);
    }

    function testChange() {
      context.emit("message", {
        ExitType: "POSTNOTES",
        CaseId: "",
        ReportTextHtml: ""
      } as IPathwaysReturnData);
    }

    function getMaskClass() {
      return "mask-class";
    }

    return {
      pathwaysLaunchUrl,
      close,
      testChange,
      getMaskClass,
      isLocalDevServer
    };
  }
});
</script>

<style>
.paccs-pathways--container {
  width: 100%;
  height: 800px;
  position: relative;
}

.paccs-pathways--iframe {
  width: inherit;
  height: inherit;
  position: absolute;
  top: 0;
  left: 0;
}

.paccs-pathways--mask-class {
  width: inherit;
  height: inherit;
  z-index: 9;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.3;
  background-color: #8b8a8a;
}

.paccs-pathways--mask-message {
  font-size: 20px;
  text-align: center;
  z-index: 10;
  opacity: 1;
  color: white;
  background-color: black;
  padding: 10px;
  margin: 10px 10px 0 10px;
}

</style>
