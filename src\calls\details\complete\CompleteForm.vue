<template>
  <div class="complete-form--container ic24-flex-column">
    <div class="complete-form--content ic24-flex-column ic24-flex-gap">
      <div class="ic24-flex-column ic24-flex-gap" v-if="false">
        <div class="ic24-flex-row ic24-flex-gap">
          <div>processName:</div>
          <div v-text="completeController.state.processName"></div>
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <div>currentStep:</div>
          <div v-text="completeController.state.currentStep"></div>
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <div>usingDx:</div>
          <div v-text="completeController.state.dx.usingDx"></div>
          <div>isDxValidationRequired:</div>
          <div
            v-text="completeController.state.dx.isDxValidationRequired"
          ></div>
        </div>
      </div>

      <!--    <SaveAndReturnFlow-->
      <!--      v-if="false"-->
      <!--      @onComplete="completeController.onCompleteSaveAndReturn"-->
      <!--      @onCancel="completeController.onCompleteSaveAndReturn"-->
      <!--    />-->

      <!-- <button @click="completeController.addCounter">Add Counter</button>
      {{ completeController.state.counter }} -->

      <!--      <FailedContactFlow v-if="false" />-->

      <EndAssessmentConfirmation
        v-if="
          completeController.state.currentStep === 'END_ASSESSMENT_CONFIRMATION'
        "
        :messages="
          completeController.state.userOptions.END_ASSESSMENT_CONFIRMATION
            .options.messages
        "
        :step="completeController.state.steps.END_ASSESSMENT_CONFIRMATION"
      />

      <NonClinicalReason
        v-if="completeController.state.currentStep === 'NON_CLINICAL_REASON'"
        :step="completeController.state.steps.NON_CLINICAL_REASON"
        :value="completeController.state.userResponse.nonClinicalReason"
        @input="completeController.onNonClinicalReasonSelected"
      />

      <HowManaged
        v-if="completeController.state.currentStep === 'HOW_WAS_CASE_MANAGED'"
        :step="completeController.state.steps.HOW_WAS_CASE_MANAGED"
        :value="completeController.state.userResponse.howManaged"
        @input="completeController.onHowMangedSelected"
      />

      <ContactMade
        v-if="completeController.state.currentStep === 'CONTACT_MADE'"
        :step="completeController.state.steps.CONTACT_MADE"
        :value="completeController.state.userResponse.contactMade"
        :failed-contact-complete="completeController.state.failedContactConfig"
        :failed-contacts="
          completeController.completeControllerInput.callDetail.failedContacts
        "
        :is-dx-validation-required="
          completeController.state.dx.isDxValidationRequired
        "
        @input="completeController.onContactMade"
      />

      <PatientReferredTo
        v-if="completeController.state.currentStep === 'PATIENT_REFERRED_TO'"
        :step="completeController.state.steps.PATIENT_REFERRED_TO"
        :value="completeController.state.userResponse.patientReferredTo"
        @input="completeController.onPatientReferredTo"
      />

      <Outcomes
        v-if="completeController.state.currentStep === 'OUTCOMES'"
        :step="completeController.state.steps.OUTCOMES"
        :value="completeController.state.userResponse.outcomes"
        :service="completeController.completeControllerInput.callDetail.Service"
        :cleo-client-service="
          completeController.completeControllerInput.callDetail
            .cleoClientService
        "
        :classification="
          completeController.completeControllerInput.callDetail.Classification
            .Description
        "
        :use-call-informational-outcomes="
          !!userPermissions.COMPLETE_USE_BRISDOC
        "
        :user-options-outcomes-override-key="
          completeController.state.userOptions.OUTCOMES.overRideKey
        "
        @input="completeController.onOutcomesSelected"
      />

      <!--      v-if="completeController.state.currentStep === 'FURTHER_ACTION'"-->
      <FurtherAction
        v-if="completeController.state.currentStep === 'FURTHER_ACTION'"
        :step="completeController.state.steps.FURTHER_ACTION"
        :value="completeController.state.userResponse.patientReferredTo"
        @input="completeController.onPatientReferredTo"
      />

      <ClinicalValidation
        v-if="completeController.state.currentStep === 'CLINICAL_VALIDATION'"
        :step="completeController.state.steps.CLINICAL_VALIDATION"
        :value="completeController.state.userResponse.clinicalValidation"
        @input="completeController.onClinicalValidationSelected"
      />

      <ReadCodes
        v-if="completeController.state.currentStep === 'READ_CODES'"
        :step="completeController.state.steps.READ_CODES"
        @input="completeController.onReadCodesSelected"
      />

      <Taxi
        v-if="completeController.state.currentStep === 'TAXI'"
        :value="completeController.state.userResponse.taxi"
        :step="completeController.state.steps.TAXI"
        @input="completeController.onTaxiSelected"
      />

      <Vulnerability
        v-if="completeController.state.currentStep === 'VULNERABILITY'"
        :value="completeController.state.userResponse.vulnerability"
        :step="completeController.state.steps.VULNERABILITY"
        @input="completeController.onVulnerabilitySelected"
      />

      <FailedContactReasons
        v-if="completeController.state.currentStep === 'FAILED_CONTACT_REASON'"
        :step="completeController.state.steps.FAILED_CONTACT_REASON"
        :value="completeController.state.userResponse.failedContactReason"
        @input="completeController.onFailedContactReasonSelected"
      />

      <PatientRiskAssessment
        v-if="
          completeController.state.currentStep === 'PATIENT_RISK_ASSESSMENT'
        "
        :step="completeController.state.steps.PATIENT_RISK_ASSESSMENT"
        :patient-risk-assessment="
          completeController.state.userResponse.patientRiskAssessment
        "
        @input="completeController.onPatientRiskAssessmentSelected"
      />

      <FailedContactRiskAssesment
        v-if="
          completeController.state.currentStep ===
            'FAILED_CONTACT_RISK_ASSESSMENT'
        "
        :step="completeController.state.steps.FAILED_CONTACT_RISK_ASSESSMENT"
        :value="
          completeController.state.userResponse.failedContactRiskAssessment
        "
        :does-pass-failed-contact-validation="
          completeController.state.doesPassFailedContactValidation
        "
        :failed-contacts="
          completeController.completeControllerInput.callDetail.failedContacts
        "
        :failed-contact-config="completeController.state.failedContactConfig"
        @input="triggerFinished"
      />
      <!--      @returnToOpenCase="cancelProcess"-->
      <!--      @saveAndReturnToQueue="saveAndReturnToQueue"-->
      <!--      @complete="processComplete"-->

      <InsufficientContactAttempts
        v-if="
          completeController.state.currentStep ===
            'INSUFFICIENT_CONTACT_ATTEMPTS'
        "
        :step="completeController.state.steps.INSUFFICIENT_CONTACT_ATTEMPTS"
        :insufficient-contact-attempts="
          completeController.state.userResponse.insufficientContactAttempts
        "
        @input="completeController.onInsufficientContactAttemptTypeSelected"
        @saveAndReturnToQueue="saveAndReturnToQueue"
        @complete="processComplete"
      />

      <ExitReasons
        v-if="completeController.state.currentStep === 'EXIT_REASON'"
        :step="completeController.state.steps.EXIT_REASON"
        :options="completeController.state.userOptions.exitReason.forUser"
        :value="completeController.state.userResponse.exitReason"
        @input="completeController.onExitReasonSelected"
        @saveAndReturnToQueue="saveAndReturnToQueue"
        @furtherActionRequired="furtherActionRequired"
      />

      <FailedContactWarning
        v-if="completeController.state.currentStep === 'FAILED_CONTACT_WARNING'"
        :step="completeController.state.steps.FAILED_CONTACT_WARNING"
        :value="completeController.state.userResponse.failedContactWarning"
        @input="completeController.onFailedContactWarningSelected"
        @returnToOpenCase="cancelProcess"
        @saveAndReturnToQueue="saveAndReturnToQueue"
      />

      <BrisDocNonClinicalAndPrescribing
        v-if="
          completeController.state.currentStep ===
            'BRISDOC_NON_CLINICAL_AND_PRESCRIBING'
        "
        :step="
          completeController.state.steps.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
        "
        :value="
          completeController.state.userResponse
            .BRISDOC_NON_CLINICAL_AND_PRESCRIBING
        "
        :service="completeController.completeControllerInput.callDetail.Service"
        :cleo-client-service="
          completeController.completeControllerInput.callDetail
            .cleoClientService
        "
        :classification="
          completeController.completeControllerInput.callDetail.Classification
            .Description
        "
        :complete-user-role-types="
          completeController.completeControllerInput.userRoleTypes
        "
        :follow-up-input-state="callDetailState.followUpInputState"
        @input="completeController.onBrisDocNonClinicalAndPrescribingSelected"
      />

      <BrisDocAuditQuestions
        v-if="
          completeController.state.currentStep === 'BRISDOC_AUDIT_QUESTIONS'
        "
        :step="completeController.state.steps.BRISDOC_AUDIT_QUESTIONS"
        :value="completeController.state.userResponse.BRISDOC_AUDIT_QUESTIONS"
        :cleo-client-service="
          completeController.completeControllerInput.callDetail
            .cleoClientService
        "
        :classification="
          completeController.completeControllerInput.callDetail.Classification
            .Description
        "
        @input="completeController.onBrisDocAuditQuestionsSelected"
      />

      <div
        v-if="completeController.state.currentStep === 'UNKNOWN'"
        style="background: deeppink"
      >
        Step "UNKNOWN" reached. A route is missing for this step, see DEV.
      </div>
      <CompleteValidationMessages
        class="complete-form--validation-error"
        :validation-messages="completeController.state.validationMessages"
      />

      <div class="complete-form--bottom-buttons ic24-flex-row ic24-flex-gap">
        <div class="ic24-flex-row ic24-flex-gap-large">
          <Ic24Button
            class="complete-form--bottom-button"
            title="Back"
            @click="completeController.goto('BACK')"
            :disabled="completeController.state.ui.disableBack"
          />
          <Ic24Button
            class="complete-form--bottom-button"
            title="Return to open case"
            :disabled="completeController.state.ui.disableCancel"
            @click="cancelProcess"
          />
        </div>
        <div class="ic24-flex-row ic24-flex-gap-large">
          <!--          <Ic24Button-->
          <!--            class="complete-form&#45;&#45;bottom-button"-->
          <!--            button-style="destructive"-->
          <!--            title="Debug"-->
          <!--            @click="showDebugSection = !showDebugSection"-->
          <!--          />-->

          <Ic24Button
            class="complete-form--bottom-button"
            title="Next"
            @click="completeController.goto('NEXT')"
            :disabled="
              completeController.state.ui.disableNext ||
                completeController.state.isProcessComplete
            "
          />
          <Ic24Button
            class="complete-form--bottom-button"
            :title="completeController.state.ui.buttons.complete.text"
            @click="processComplete"
            :disabled="
              completeController.state.ui.disableComplete ||
                !completeController.state.isProcessComplete
            "
          />
        </div>
      </div>

      <!--      -->
      <div v-if="showDebugSection">
        Debug Section
        <div class="ic24-flex-column ic24-flex-gap">
          <div class="ic24-flex-row ic24-flex-gap">
            <div>processName:</div>
            <div>{{ completeController.state.processName }}</div>
          </div>

          <div class="ic24-flex-row ic24-flex-gap">
            <div>currentStep:</div>
            <div>{{ completeController.state.currentStep }}</div>
          </div>

          <div class="ic24-flex-row ic24-flex-gap">
            <div>usingDx / isDxValidationRequired:</div>
            <div>
              {{
                completeController.state.dx.usingDx +
                  "/" +
                  completeController.state.dx.isDxValidationRequired
              }}
            </div>
          </div>

          <div class="ic24-flex-row ic24-flex-gap">
            <div>disabled:</div>
            <div>
              {{
                "cancel: " +
                  completeController.state.ui.disableCancel +
                  ", " +
                  "back: " +
                  completeController.state.ui.disableBack +
                  ", " +
                  "next: " +
                  completeController.state.ui.disableNext +
                  ", " +
                  "complete: " +
                  completeController.state.ui.disableComplete
              }}
            </div>
          </div>

          <div class="ic24-flex-row ic24-flex-gap">
            <div>followUpInputState:</div>
            <div>
              {{
                completeController.completeControllerInput.callDetailState
                  .followUpInputState
              }}
            </div>
          </div>

          <div class="ic24-flex-row ic24-flex-gap">
            <div>failedContactConfig:</div>
            <div>{{ completeController.state.failedContactConfig }}</div>
          </div>
          <div class="ic24-flex-row ic24-flex-gap">
            <div>
              failedContacts ({{
                completeController.completeControllerInput.callDetail
                  .failedContacts.length
              }}):
            </div>
            <div>
              {{
                completeController.completeControllerInput.callDetail
                  .failedContacts
              }}
            </div>
          </div>

          <div>completeController.state:</div>
          <div>{{ completeController.state.userResponse.outcomes }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  PropType,
  reactive,
  ref
} from "@vue/composition-api";
import {
  ICallDetail,
  ICallDetailState,
  ICaseConfig
} from "../call-details-models";
import { useCompleteController } from "../complete/useCompleteController";
import * as CompleteService from "./complete-service";

import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import EndAssessmentConfirmation from "./components/EndAssessmentConfirmation.vue";
import HowManaged from "./failedcontact/how-managed/HowManaged.vue";
import ContactMade from "@/calls/details/complete/components/contactmade/ContactMade.vue";
import PatientReferredTo from "@/calls/details/complete/components/patient-referred-to/PatientRefererredTo.vue";
import CompleteValidationMessages from "@/calls/details/complete/validation/CompleteValidationMessages.vue";
import ReadCodes from "@/calls/details/complete/components/readcodes/ReadCodes.vue";
import Outcomes from "@/calls/details/complete/components/outcomes/Outcomes.vue";
import { useUserStore } from "@/user/useUserStore";
import { CleoPermissionsForRole } from "@/permissions/permission-models";
import FailedContactReasons from "@/calls/details/complete/failedcontact/failed-contact-reasons/FailedContactReasons.vue";
import PatientRiskAssessment from "@/calls/details/complete/components/patient-risk-assesment/PatientRiskAssesment.vue";
import InsufficientContactAttempts from "@/calls/details/complete/components/insufficient-contact-attempts/InsufficientContactAttempts.vue";
import ClinicalValidation from "@/calls/details/complete/components/clinical-validation/ClinicalValidation.vue";
import { simpleObjectClone } from "@/common/common-utils";
import {
  CompleteFinalAction,
  CompleteProcess
} from "@/calls/details/complete/complete-models";
import ExitReasons from "@/calls/details/complete/failedcontact/exitreasons/ExitReasons.vue";
import FailedContactWarning from "@/calls/details/complete/components/failed-contact-warning/FailedContactWarning.vue";
import { useConfigHelper } from "@/common/config/config-store";
import FailedContactRiskAssesment from "@/calls/details/complete/components/failed-contact-risk-assesment/FailedContactRiskAssesment.vue";
import Taxi from "@/calls/details/complete/components/taxi/Taxi.vue";
import Vulnerability from "@/calls/details/complete/components/vulnerability/Vulnerability.vue";
import { factoryICallDetailState } from "@/calls/details/call-detail-service";
import BrisDocNonClinicalAndPrescribing from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/BrisDocNonClinicalAndPrescribing.vue";
import BrisDocAuditQuestions from "@/calls/details/complete/brisdoc/ui/audit-questions/ui/BrisDocAuditQuestions.vue";
import FurtherAction from "@/calls/details/complete/components/further-action/ui/FurtherAction.vue";
import NonClinicalReason from "@/calls/details/complete/non-clinical/ui/NonClinicalReason.vue";

export default defineComponent({
  // type inference enabled
  name: "CompleteForm",
  components: {
    NonClinicalReason,
    FurtherAction,
    BrisDocAuditQuestions,
    BrisDocNonClinicalAndPrescribing,
    Vulnerability,
    Taxi,
    FailedContactRiskAssesment,
    FailedContactWarning,
    ExitReasons,
    ClinicalValidation,
    InsufficientContactAttempts,
    PatientRiskAssessment,
    FailedContactReasons,
    Outcomes,
    ReadCodes,
    CompleteValidationMessages,
    PatientReferredTo,
    Ic24Button,
    EndAssessmentConfirmation,
    HowManaged,
    ContactMade
  },
  props: {
    caseConfig: {
      type: Object as PropType<ICaseConfig>,
      required: true
    },
    callDetail: {
      type: Object as PropType<ICallDetail>,
      required: true
    },
    callDetailState: {
      type: Object as PropType<ICallDetailState>,
      required: true
    },
    userPermissions: {
      type: Object as PropType<CleoPermissionsForRole>,
      required: true
    },
    completeProcess: {
      type: String as PropType<CompleteProcess>,
      required: true
    }
  },
  setup(
    props: {
      caseConfig: ICaseConfig;
      callDetail: ICallDetail;
      callDetailState: ICallDetailState;
      userPermissions: CleoPermissionsForRole;
      completeProcess: CompleteProcess;
    },
    context: SetupContext
  ) {
    const userStore = useUserStore();

    const configHelper = useConfigHelper();

    console.log("CompleteForm setup", props);

    const completeController = useCompleteController(
      {
        completeProcess: props.completeProcess,
        callDetail: props.callDetail,
        callDetailState: factoryICallDetailState(),
        userRole: userStore.user.userRole,
        userPermissions: props.userPermissions,
        dx: {
          requiringValidation: props.caseConfig.dx.validation
        },
        failedContactConfig: props.caseConfig.failedContactConfig,
        userRoleTypes: {
          isCasClinician: !!props.userPermissions.COMPLETE_USE_ROTA_CLINICIAN,
          isClinician: !!props.userPermissions["START CONSULTATION"]
        }
      },
      reactive(CompleteService.factoryCompleteControllerState())
    );

    function cancelProcess() {
      completeController.cancel();
      context.emit(
        "cancelProcess",
        simpleObjectClone(completeController.state)
      );
    }

    function triggerFinished(action: CompleteFinalAction) {
      // Can't think of a better way than using permissions to determine the process!?!?!?
      const isBrisDoc = !!props.userPermissions.COMPLETE_USE_BRISDOC;
      if (isBrisDoc && action === "COMPLETE") {
        completeController.state.userResponse.failedContactRiskAssessment.completeFinalAction = action;
        completeController.goto("NEXT");
        return;
      }

      const actionMap: Record<CompleteFinalAction, () => void> = {
        "": () => {
          console.error("No action provided to triggerFinished");
        },
        RETURN_TO_OPEN_CASE: cancelProcess,
        COMPLETE: processComplete,
        SAVE_AND_RETURN_TO_QUEUE: saveAndReturnToQueue,
        FURTHER_ACTION_REQUIRED: furtherActionRequired
      };
      completeController.state.userResponse.failedContactRiskAssessment.completeFinalAction = action;
      completeController.state.finalAction = action;
      actionMap[action]();
    }

    function processComplete() {
      // check if current step state is valid
      if (!completeController.isCurrentStepValid()) {
        console.error("Current step is not valid, cannot complete");
        return;
      }

      if (
        props.callDetailState.followUpInputState.userConfirmed &&
        completeController.completeControllerInput.completeProcess ===
          "COMPLETE_PROCESS"
      ) {
        // now stick this function
        window.ApplicationControllerClient.moveCaseToOversightState =
          props.callDetailState.followUpInputState;

        //  TODO leaving this here as a reference for now.
        /*
        completeController.state.ui.disableComplete = true;
        completeController.state.validationMessages.push({
          id: "SUBMIT_OVERSIGHT",
          message: "Creating oversight case..."
        });
        FollowUpApi.createOversight(
          props.callDetailState.followUpInputState,
          props.callDetail.CallNo
        )
          .then(resp => {
            if (resp.RESULT === "SUCCESS") {
              completeController.state.validationMessages = [];
              proceedWithComplete();
            } else {
              completeController.state.validationMessages = [
                {
                  id: "COULD_NOT_SUBMIT_OVERSIGHT",
                  message: "Could not submit oversight: " + resp.MESSAGE
                }
              ];
              return;
            }
          })
          .finally(() => {
            completeController.state.ui.disableComplete = false;
          });
        */
      }

      proceedWithComplete();
    }

    function proceedWithComplete() {
      completeController.processComplete().then(resp => {
        if (resp === null) {
          return;
        }
        context.emit("processComplete", resp);
      });
    }

    function saveAndReturnToQueue() {
      //  TODO should state be passed back and set on the parent component?
      context.emit("saveAndReturnToQueue", completeController.state);
    }

    function furtherActionRequired() {
      //  TODO should state be passed back and set on the parent component?
      context.emit("furtherActionRequired", completeController.state);
    }

    const showDebugSection = ref(false);

    return {
      completeController,
      showDebugSection,
      configHelper,

      cancelProcess,
      triggerFinished,
      saveAndReturnToQueue,
      furtherActionRequired,
      processComplete
    };
  }
});
</script>
