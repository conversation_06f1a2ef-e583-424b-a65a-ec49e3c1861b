/**
 * N.B. the prop value is the ACTUAL CLEO PERMISSION name! Case insensitive.
 */
export const PERMISSION_NAMES = {
  MAKE_APPOINTMENT: "MAKE APPOINTMENT",
  ACKNOWLEDGE_RECEIPT_BASE: "Acknowledge Receipt".toUpperCase(),
  ARRIVED: "ARRIVED",
  COMFORT_COURTESY_CALL: "COMFORT_COURTESY_CALL",
  "EDIT CLASSIFICATION": "EDIT CLASSIFICATION",
  DOWNGRADE: "DOWNGRADE",
  ASSIGN_CALL: "ASSIGN",
  ASSIGN_CALL_BASE: "ASSIGN BASE",
  ASSIGN_CALL_SECONDARY: "SECONDARY ASSIGN",
  PRIORITY: "PRIORITY",
  DISPATCH_VEHICLE: "DISPATCH SELECT",
  RETRIEVE_VEHICLE: "DISPATCH RETRIEVE",
  PRINT: "PRINT",
  ["UNLOCK CALL"]: "UNLOCK CALL",
  RESEND_INDIV: "Resend Individual".toUpperCase(),
  RESEND_SUMMARY: "Resend Summary".toUpperCase(),
  RESEND_DTS: "Resend DTS".toUpperCase(),
  RESEND_PEMS: "",
  RESEND_RESOLVED: "Resend Resolved".toUpperCase(),
  RESEND_CAN_NOT_RESOLVE: "Resend Resolved".toUpperCase(), //  Currently Shares perm with above.
  ADD_CONSULTATION: "ADD_CONSULTATION",
  NO_SUPPORT: "NO_SUPPORT",
  PACCS_USER: "PACCS_USER",
  CAREHOME_SELECT: "CAREHOME_SELECT",
  SESUI_TELEPHPONE: "SESUI_TELEPHPONE",
  COMPLETE_USE_ROTA_CLINICIAN: "COMPLETE_USE_ROTA_CLINICIAN",
  COMPLETE_USE_BRISDOC: "COMPLETE_USE_BRISDOC",
  COMPLETE_USE_BRISDOC_NON_CLINI: "COMPLETE_USE_BRISDOC_NON_CLINI",
  CASE_MOVE_TO_OVERSIGHT: "CASE_MOVE_TO_OVERSIGHT",
  "START CONSULTATION": "START CONSULTATION",

  NEW_CALL__OOH: "New Call - OOH".toUpperCase(),
  NEW_CALL__EAST_KENT_111: "New Call - East Kent 111".toUpperCase(),
  NEW_CALL__NOR_WIS_111: "New Call - Norfolk and Wisbech 111".toUpperCase(),
  NEW_CALL__NORTH_ESSEX_111: "New Call - North Essex 111".toUpperCase(),
  NEW_CALL__SOUTH_ESSEX_111: "NEW CALL - SOUTH ESSEX 111",
  NEW_CALL__POSL: "NEW CALL - POSL",
  NEW_CALL__BRIGHTON_DISTRICT_NURSE: "New Call - Brighton District Nurse".toUpperCase(),
  NEW_CALL__ROVING_GP: "NEW CALL - ROVING GP",
  NEW_CALL__DEVON_111: "NEW CALL - DEVON 111",
  NEW_CALL__FCMS: "NEW CALL - FCMS",

  ["NEW CALL - BRISDOC"]: "NEW CALL - BRISDOC",
  ["NEW CALL - FRAILTY"]: "NEW CALL - FRAILTY",
  ["NEW CALL - MENTAL HEALTH"]: "NEW CALL - MENTAL HEALTH",
  ["NEW CALL - OUT OF HOURS PROFESSIONAL LINE"]:
    "NEW CALL - OUT OF HOURS PROFESSIONAL LINE",
  ["NEW CALL - PAEDIATRICS"]: "NEW CALL - PAEDIATRICS",
  ["NEW CALL - PATIENTLINE"]: "NEW CALL - PATIENTLINE",
  ["NEW CALL - WEEKDAY PROFESSIONAL LINE"]:
    "NEW CALL - WEEKDAY PROFESSIONAL LINE",

  SOCKET__INT_TEST: "SOCKET__INT_TEST",
  GRID__INT_TEST: "GRID__INT_TEST",

  OVERSIGHT_VALIDATION: "OVERSIGHT_VALIDATION",
  VALIDATE_CAS: "VALIDATE_CAS",
  ASSIGN_DX: "ASSIGN_DX",
  ASSIGN_TO_BASE_NO_ROTA: "ASSIGN_TO_BASE_NO_ROTA",
  ASSIGN_TO_CLINICIAN_NO_ROTA: "ASSIGN_TO_CLINICIAN_NO_ROTA",
  ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR: "ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR",
  MOVE_CASE: "MOVE_CASE",
  MOVE_CASE_OUT_URGENT_FOLLOW_UP: "MOVE_CASE_OUT_URGENT_FOLLOW_UP",
  CAS_APPOINTMENT: "CAS_APPOINTMENT",
  ADDCOMMENTS: "ADDCOMMENTS",
  SMS_MANUAL: "SMS_MANUAL",
  APPTS_EXTERNAL: "APPTS_EXTERNAL",
  SHOW_GS_PHOTO: "SHOW_GS_PHOTO",
  REQUEST_GS_PHOTO: "REQUEST_GS_PHOTO",
  REFRESH_GS_PHOTO: "REFRESH_GS_PHOTO",
  PLS_REMOVE: "PLS_REMOVE"
};

//  In CLEO all we really care about is "MAKE_APPOINTMENT", not "CALL.MAKE_APPOINTMENT", hence "| string"
export type CleoPermissionName = keyof typeof PERMISSION_NAMES | string;

export type CLEO_PERMISSION_FORM_TYPE =
  | "CALL"
  | "WEBUI_DOCVIEW"
  | "GLOBAL"
  | "ADMIN"
  | "PATIENT"
  | "<ANY>";

export type CleoRoleName = string;

export type CleoPermissionsForRole = Record<
  CleoPermissionName,
  ICleoPermission
>;

export interface ICleoPermission {
  PermissionAccess: "A" | "";
  PermissionForm: CLEO_PERMISSION_FORM_TYPE;
  PermissionAction: string;
}

/**
 * {
          "JobRoles": {
            "[Role Disp Cont]": "[Despatch Controller, NHS 111, OOH / Warwickshire Healthline, R8008, R8010, Roving GP, Standard]",
            "[Role Nurse 111]": "[NHS 111, NHSP, Nurse 111, Nurse Triage, R1070, R8001, R8003, Standard]"
          },
          "Permissions": {
            "[Role Disp Cont]": {
              "Call.First Contact": {
                PermissionAccess: "A",
                PermissionForm: "Call",
                PermissionAction: "First Contact",
                AuthorityProfile: "Base Receptionist",
                PermissionActionMode: "",
                PermissionFormulaResult: "1"
              },
              "Call.Make Appointment": {
                PermissionAccess: "A",
                PermissionForm: "Call",
                PermissionAction: "Make Appointment",
                AuthorityProfile: "Base Receptionist",
                PermissionActionMode: "",
                PermissionFormulaResult: "0"
              },
              "Call.Acknowledge Call On Open": {
                PermissionAccess: "A",
                PermissionForm: "Call",
                PermissionAction: "Acknowledge Call On Open",
                AuthorityProfile: "Base Receptionist",
                PermissionActionMode: "",
                PermissionFormulaResult: ""
              }
            }
          }
        }
 */
export interface CLeoPermissionServerResponse {
  JobRoles: Record<string, string>; // E.g. "[Role Disp Cont]": "[Despatch Controller, NHS 111, OOH / Warwickshire Healthline, R8008, R8010, Roving GP, Standard]",
  Permissions: Record<CleoRoleName, CleoPermissionsForRole>; //  Permissions: Record<Role Name, Record<Permission Name, CleoPermission>>
}

export type JwtScopeType = "api1";

export interface IJwtClaims extends Record<string, unknown> {
  OrganisationCode: Record<string, unknown>;
  sub: string;
  auth_time: number;
  exp: number;
  idp: string;
  Uid: string;
  ssbSessionRoleUid: string;
  scope: JwtScopeType[];
}
