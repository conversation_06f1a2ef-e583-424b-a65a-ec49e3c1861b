import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";
import {
  DxCodeServerResponse,
  FollowUpClassification,
  FollowUpInputState,
  FollowUpServerPayload
} from "@/calls/details/complete/components/followup/models/follow-up-models";
import { LegacyKeywordServerResponse } from "@/common/cleo-legacy-models";
import { ICleoServerResponse } from "@/common/cleo-common-models";
import { getKeywordsStandard } from "@/common/api/keywords-api";

/**
 * https://ash-brisdoc-cleouat.sehnp.nhs.uk/dev/livecalld.nsf/cleobreach?Openagent&action=CAS_BREACH_CONFIG&TYPE=CAS_ONLY&SERVICE=BrisDoc&_=1741699933786
 * {
 *     "data": [
 *         {
 *             "dxCode": "Dx325",
 *             "priorityGroup": "Pharmacist",
 *             "priorityLabel": "Urgent 15 mins",
 *             "queueRestrict": "CAS_ONLY",
 *             "description": "Speak to a Clinician from our service Immediately - Toxic Ingestion/Inhalation"
 *         },
 *         {
 *             "dxCode": "Dx333",
 *             "priorityGroup": "Speak To",
 *             "priorityLabel": "Urgent 30 mins",
 *             "queueRestrict": "CAS_ONLY",
 *             "description": "Speak to a clinician from our service immediately - Ambulance Validation"
 *         }
 *         ]
 * }
 *
 * https://ash-brisdoc-cleouat.sehnp.nhs.uk/dev/livexcleolock.nsf/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=DX%20Code~BrisDoc
 * {
 *   "P6h": {
 *     "unid": "8F8FC1495AEB143E80258C27004FEFC2",
 *     "codeID3": "ADVICE;BASE;VISIT",
 *     "description": "P6h",
 *     "KeywordService": "BrisDoc;FRAILTY;PATIENT_LINE",
 *     "codeID2": "360",
 *     "codeID1": "6 hours",
 *     "keyType": "DX Code"
 *   },
 *   "P30m": {
 *     "unid": "84EDB64ECF4181F78025852A00446803",
 *     "codeID3": "",
 *     "description": "P30m",
 *     "KeywordService": "BrisDoc;FRAILTY;PATIENT_LINE",
 *     "codeID2": "30",
 *     "codeID1": "30 mins",
 *     "keyType": "DX Code"
 *   },
 *   "P4h": {
 *     "unid": "5AD14231A9A3FAA380258C2700502F5C",
 *     "codeID3": "ADVICE",
 *     "description": "P4h",
 *     "KeywordService": "BrisDoc;FRAILTY;PATIENT_LINE",
 *     "codeID2": "240",
 *     "codeID1": "4 hours",
 *     "keyType": "DX Code"
 *   },
 *   "P1h": {
 *     "unid": "75499889D4FEB2A680258C27004FEF8A",
 *     "codeID3": "",
 *     "description": "P1h",
 *     "KeywordService": "BrisDoc;FRAILTY;PATIENT_LINE",
 *     "codeID2": "60",
 *     "codeID1": "1 hour",
 *     "keyType": "DX Code"
 *   },
 *   "P2h": {
 *     "unid": "EB17594B651E7C3980258C27004FEFA6",
 *     "codeID3": "ADVICE;BASE;VISIT",
 *     "description": "P2h",
 *     "KeywordService": "BrisDoc;FRAILTY;PATIENT_LINE",
 *     "codeID2": "120",
 *     "codeID1": "2 hours",
 *     "keyType": "DX Code"
 *   }
 * }
 *
 */
export function getDxData(service: string): Promise<DxCodeServerResponse> {
  if (process.env.NODE_ENV === "development") {
    return Promise.resolve({
      P6h: {
        unid: "8F8FC1495AEB143E80258C27004FEFC2",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P6h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "360",
        codeID1: "6 hours",
        keyType: "DX Code"
      },
      P30m: {
        unid: "84EDB64ECF4181F78025852A00446803",
        codeID3: "",
        description: "P30m",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "30",
        codeID1: "30 mins",
        keyType: "DX Code"
      },
      P4h: {
        unid: "5AD14231A9A3FAA380258C2700502F5C",
        codeID3: "ADVICE",
        description: "P4h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "240",
        codeID1: "4 hours",
        keyType: "DX Code"
      },
      P1h: {
        unid: "75499889D4FEB2A680258C27004FEF8A",
        codeID3: "",
        description: "P1h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "60",
        codeID1: "1 hour",
        keyType: "DX Code"
      },
      P2h: {
        unid: "EB17594B651E7C3980258C27004FEFA6",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P2h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "120",
        codeID1: "2 hours",
        keyType: "DX Code"
      }
    });
  }

  return https.get(
    CLEO_CONFIG.CLEO.XCLEO_PATH +
      "/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=DX%20Code_FOLLOW_UP~" +
      service,
    {
      responseType: "json"
    }
  );
}

export function getCleoClientServices(
  service: string
): Promise<LegacyKeywordServerResponse> {
  // CLEO_CLIENT_SERVICE
  if (process.env.NODE_ENV === "development") {
    return Promise.resolve({
      OUT_OF_HOURS_PROFESSIONAL_LINE: {
        unid: "8B0730CD3A33826380258C4A0057CE40",
        codeID3: "",
        description: "OUT_OF_HOURS_PROFESSIONAL_LINE",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "OOHsPL",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      PATIENT_LINE: {
        unid: "9EF8DCC67F188ACB80258C59003AD217",
        codeID3: "",
        description: "PATIENT_LINE",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Patient Line",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      MENTAL_HEALTH: {
        unid: "75499889D4FEB2A680258C27004FEF8A",
        codeID3: "",
        description: "MENTAL_HEALTH",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Mental Health",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      PAEDIATRICS: {
        unid: "6C708E6B0CAE613080258C4A0057CDE8",
        codeID3: "",
        description: "PAEDIATRICS",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Paediatric",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      WEEKDAY_PROFESSIONAL_LINE: {
        unid: "1125DE64661F3D4A80258C4A0057CE2A",
        codeID3: "",
        description: "WEEKDAY_PROFESSIONAL_LINE",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "WDPL",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      FOLLOW_UP: {
        unid: "AF5A42A7FDF8ED0380258C610036CF56",
        codeID3: "",
        description: "FOLLOW_UP",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Follow Up",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      FRAILTY: {
        unid: "1749C35C6D77AFA080258C4C00373E9D",
        codeID3: "",
        description: "FRAILTY",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Frailty",
        keyType: "CLEO_CLIENT_SERVICE"
      }
    });
  }

  return https.get(
    CLEO_CONFIG.CLEO.XCLEO_PATH +
      "/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=CLEO_CLIENT_SERVICE~" +
      service,
    {
      responseType: "json"
    }
  );
}

/**
 *
 * @param service
 *
 * E.g. service = "BrisDoc"
 * {
 *   "TRIAGE_TYPE": {
 *     "unid": "FCF5AAA4B833213E80258C4A0057CE0F",
 *     "codeID3": "",
 *     "description": "TRIAGE_TYPE",
 *     "KeywordService": "BrisDoc;CAS",
 *     "codeID2": "",
 *     "codeID1": "Does patient need PPE;Isolation;High consequence infection disease",
 *     "keyType": "OVERSIGHT_BASE_QUESTION__TRIAGE_TYPE"
 *   }
 * }
 */
export function getBaseQuestion(
  service: string
): Promise<LegacyKeywordServerResponse> {
  const urlParams = {
    action: "GETKEYWORDSTANDARDJSON",
    sid:
      "OVERSIGHT_BASE_QUESTION__TRIAGE_TYPE" +
      (service.length > 0 ? "~" + service : "")
  };

  if (process.env.NODE_ENV === "development") {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          TRIAGE_TYPE: {
            unid: "FCF5AAA4B833213E80258C4A0057CE0F",
            codeID3: "",
            description: "TRIAGE_TYPE",
            KeywordService: "BrisDoc;CAS",
            codeID2: "",
            codeID1:
              "Does patient need PPE;Isolation;High consequence infection disease",
            keyType: "OVERSIGHT_BASE_QUESTION__TRIAGE_TYPE"
          }
        });
      }, 0);
    });
  }

  return getKeywordsStandard(urlParams);
}

/**
 *
 * @param followUpInputState
 * @param callNo
 */
export function createOversight(
  followUpInputState: FollowUpInputState,
  callNo: string | number
): Promise<ICleoServerResponse<unknown>> {
  console.warn(
    "FollowUpApi.createOversight() followUpInputState:",
    followUpInputState
  );

  const urlParams: FollowUpServerPayload = {
    action: "MOVE_CASE_OVERSIGHT",
    CALLNO: callNo,
    CLASSIFICATION: (followUpInputState.followUpType
      ? followUpInputState.followUpType.classification
      : "") as FollowUpClassification,
    SUB_CLASSIFICATION: "",
    IS_ACTIVE_TIME: followUpInputState.dateTimePicker,
    DX: followUpInputState.dxCode ? followUpInputState.dxCode.description : "",
    CLEO_CLIENT_SERVICE: followUpInputState.cleoClientService
      ? followUpInputState.cleoClientService.description
      : "",
    BASE_TRIAGE_QUESTION: followUpInputState.baseQuestionTriageType
  };

  console.warn("FollowUpApi.createOversight() urlParams", urlParams);

  // Ran into issue: User is in the "source" case, if we run this before
  // the source case has been committed to the database, the oversight case
  // will not be able to find the consultation created on source case...as
  // it doesn't exist yet.  Therefore, we need to delay this call until the
  // source case has been saved.  @See

  // MOVE_CASE_OVERSIGHT
  // if (process.env.NODE_ENV === "development") {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        RESULT: "SUCCESS",
        MESSAGE: "Case moved to oversight",
        DATA: null
      });
    }, 2000);
  });
  // }

  // return https.get(CLEO_CONFIG.CLEO.CALL_DB + "/(cleocloneapi)?openagent", {
  //   responseType: "json",
  //   params: urlParams
  // });
}
