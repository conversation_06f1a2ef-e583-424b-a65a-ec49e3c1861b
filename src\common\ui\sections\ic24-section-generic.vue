<template>
  <div class="ic24-flex-column ic24-full-width">
    <Ic24SectionHeaderGeneric :title="title" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";
import Ic24SectionHeaderGeneric from "@/common/ui/sections/ic24-section-header-generic.vue";

export default defineComponent({
  name: "ic24-section-generic",
  components: { Ic24SectionHeaderGeneric },
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      }
    }
  }
});
</script>
