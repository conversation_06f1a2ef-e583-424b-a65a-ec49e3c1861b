<template>
  <div class="paccs-condition-supporting-info--wrapper">
    <div
      v-text="conditionSupportingInformation.categoryTitle"
      class="paccs-condition-supporting-info--title"
    ></div>
    <div
      v-html="conditionSupportingInformation.text"
      class="paccs-condition-supporting-info--text"
    ></div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { PaccsConditionService } from "@/paccs/paccs-condition-tab-body/paccs-condition-service";
import { IPaccsConditionSupportingInformation } from "@/paccs/paccs-condition-tab-body/paccs-condition-models";

const commonService: CommonService = new CommonService();
const paccsConditionService: PaccsConditionService = new PaccsConditionService();

export default defineComponent({
  name: "paccs-condition-supporting-information",
  props: {
    conditionSupportingInformation: {
      default: () => {
        return paccsConditionService.factoryPaccsConditionSupportingInformation();
      },
      type: Object as PropType<IPaccsConditionSupportingInformation>
    }
  },
  components: {},
  setup(
    props: {
      conditionSupportingInformation: IPaccsConditionSupportingInformation;
    },
    context: SetupContext
  ) {
    // onBeforeMount(() => {
    //   loggerInstance.log("paccs-condition>>>>>>>>>>>>>>>>>>>mounted!");
    // });

    return {};
  }
});
</script>

<style>
.paccs-condition-supporting-info--wrapper {
  background-color: #f0f6ff;
  padding: 0.5em;
  border: 1px solid #f3f0f0;
}

.paccs-condition-supporting-info--title {
  font-weight: 600;
}
.paccs-condition-supporting-info--text {
}
</style>
