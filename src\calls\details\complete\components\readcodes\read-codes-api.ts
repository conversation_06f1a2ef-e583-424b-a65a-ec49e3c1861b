import { CLEO_CONFIG } from "@/common/config/config-";
import { IApiServerReadCodes } from "@/calls/details/complete/components/readcodes/readcodes-models";
import { ICleoServerResponse } from "@/common/cleo-common-models";
import { mockReadCodesBaseSearch } from "@/calls/details/complete/components/readcodes/mock/mock-read-codes-base-search";

export class ReadCodesApi {
  /**
   *
   * @param key
   * @param isFullSearch  true = something like:
   * &key=sun&SEARCH=1&FULLSEARCH=0   e.g. user is doing free text search for "sun"
   * false = something like:
   * &key=XaBVJ
   */
  public getReadCodes(
    key: string,
    isFullSearch = false
  ): Promise<ICleoServerResponse<IApiServerReadCodes>> {
    if (process.env.NODE_ENV === "development") {
      return new Promise(resolve => {
        resolve(mockReadCodesBaseSearch);
      }) as Promise<ICleoServerResponse<IApiServerReadCodes>>;
    } else {
      const url =
        CLEO_CONFIG.CLEO.CALL_DB +
        "/(readCodes)?Openagent&key=" +
        key +
        (isFullSearch ? "&SEARCH=1&FULLSEARCH=0" : "");
      return localCache.getUrlDataWithCache(url, false) as Promise<
        ICleoServerResponse<IApiServerReadCodes>
      >;
    }
  }
}
