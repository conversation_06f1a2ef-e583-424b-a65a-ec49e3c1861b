<template>
  <div class="ic24-flex ic24-flex-grow ic24-justify-flex-space-between">
    <div class="ic24-flex ic24-flex-wrap">
      <div class="ic24-flex ic24-flex-gap ic24-flex-wrap">
        <slot name="buttons-left"></slot>
      </div>
    </div>

    <div class="ic24-flex ic24-flex-wrap">
      <div class="ic24-flex ic24-flex-gap ic24-flex-wrap">
        <slot name="buttons-right"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "ic24-button-group",
  props: {}
});
</script>
