import {
  factoryFollowUpInputState,
  factoryFollowUpState,
  getDxCodesForClassification,
  getFollowUpStateDisplayText
} from "@/calls/details/complete/components/followup/models/follow-up-service";
import { DxCodeServerResponse } from "@/calls/details/complete/components/followup/models/follow-up-models";

describe("follow-up-service-spec", () => {
  it("getDxForClassification", () => {
    const dxCodes: DxCodeServerResponse = {
      P6h: {
        unid: "8F8FC1495AEB143E80258C27004FEFC2",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P6h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "360",
        codeID1: "6 hours",
        keyType: "DX Code"
      },
      P30m: {
        unid: "84EDB64ECF4181F78025852A00446803",
        codeID3: "",
        description: "P30m",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "30",
        codeID1: "30 mins",
        keyType: "DX Code"
      },
      P4h: {
        unid: "5AD14231A9A3FAA380258C2700502F5C",
        codeID3: "ADVICE",
        description: "P4h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "240",
        codeID1: "4 hours",
        keyType: "DX Code"
      },
      P1h: {
        unid: "75499889D4FEB2A680258C27004FEF8A",
        codeID3: "",
        description: "P1h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "60",
        codeID1: "1 hour",
        keyType: "DX Code"
      },
      P2h: {
        unid: "EB17594B651E7C3980258C27004FEFA6",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P2h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "120",
        codeID1: "2 hours",
        keyType: "DX Code"
      }
    };

    const result = getDxCodesForClassification(dxCodes, "Advice");

    expect(result.length).toEqual(3);
    expect(result[0].description).toEqual("P2h");
    expect(result[1].description).toEqual("P4h");
    expect(result[2].description).toEqual("P6h");
  });

  // getFollowUpStateDisplayText
  it("getFollowUpStateDisplayText", () => {
    let followUpState = factoryFollowUpInputState();
    expect(getFollowUpStateDisplayText(followUpState)).toEqual("");

    followUpState = {
      followUpType: {
        classification: "Base",
        label: "Face-to-Face Urgent Care Referral"
      },
      dxCode: {
        unid: "8F8FC1495AEB143E80258C27004FEFC2",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P6h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "360",
        codeID1: "6 hours",
        keyType: "DX Code"
      },
      cleoClientService: null,
      baseQuestionTriageType: "Isolation",
      followUpNotes: "",
      dateTimePicker: "",
      userConfirmed: true
    };
    expect(getFollowUpStateDisplayText(followUpState)).toEqual(
      "Follow up requested: Face-to-Face Urgent Care Referral P6h, Isolation"
    );

    followUpState = {
      followUpType: {
        classification: "Visit",
        label: "Home Visit Urgent Care Referral"
      },
      dxCode: {
        unid: "8F8FC1495AEB143E80258C27004FEFC2",
        codeID3: "ADVICE;BASE;VISIT",
        description: "P6h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "360",
        codeID1: "6 hours",
        keyType: "DX Code"
      },
      cleoClientService: null,
      baseQuestionTriageType: "Isolation",
      followUpNotes: "",
      dateTimePicker: "",
      userConfirmed: true
    };

    expect(getFollowUpStateDisplayText(followUpState)).toEqual(
      "Follow up requested: Home Visit Urgent Care Referral P6h, Isolation"
    );

    followUpState = {
      followUpType: {
        classification: "Advice",
        label: "Urgent Care Follow Up"
      },
      dxCode: {
        unid: "5AD14231A9A3FAA380258C2700502F5C",
        codeID3: "ADVICE",
        description: "P4h",
        KeywordService: "BrisDoc;FRAILTY;PATIENT_LINE",
        codeID2: "240",
        codeID1: "4 hours",
        keyType: "DX Code"
      },
      cleoClientService: {
        unid: "AF5A42A7FDF8ED0380258C610036CF56",
        codeID3: "",
        description: "FOLLOW_UP",
        KeywordService: "BrisDoc;CAS;OOH",
        codeID2: "",
        codeID1: "Follow Up",
        keyType: "CLEO_CLIENT_SERVICE"
      },
      baseQuestionTriageType: "",
      followUpNotes: "",
      dateTimePicker: "2025-06-27T17:30",
      userConfirmed: true
    };

    expect(getFollowUpStateDisplayText(followUpState)).toEqual(
      "Follow up requested: Urgent Care Follow Up P4h at Jun 27th 17:30"
    );
  });
});
