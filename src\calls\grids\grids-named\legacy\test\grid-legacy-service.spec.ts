// import { toLocalISOWithOffset } from "@/calls/grids/grids-named/legacy/models/grid-legacy-service";

// import { queueCasMockYellow } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-yellow";

import { queueCasMockWeirdError } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-weird-error";
import { mapGridLegacyCallSummaryToICleoCallSummary } from "@/calls/grids/grids-named/legacy/models/grid-legacy-service";

describe("grid-legacy-service", () => {
  it("formatDateTimeToISO", () => {
    expect(true).toEqual(true);

    // expect(toLocalISOWithOffset("2021-06-01 00:00:00")).toEqual(
    //   "2021-06-01T00:00:00+01:00"
    // );

    // expect(toLocalISOWithOffset("07/10/2024 14:07:03")).toEqual(
    //   "2024-10-07T14:07:03+01:00"
    // );

    // 07/10/2024 14:07:03
    // "2021-05-31T23:00:00.000+01:00"
  });

  // take the first item from queueCasMock mapGridLegacyCallSummaryToICleoCallSummary() it, then mapICleoCallSummaryToGridLegacyCallSummary
  // are the source and final resutl the same?

  it("queueCasMockWeirdError", () => {
    const resp = queueCasMockWeirdError;

    const itemsReturned = resp.items.map(item => {
      return mapGridLegacyCallSummaryToICleoCallSummary(item);
    });

    // const item = queueYellow.items[0];
    // const mappedItem = mapGridLegacyCallSummaryToICleoCallSummary(item);
    // const mappedBackItem = mapICleoCallSummaryToGridLegacyCallSummary(
    //   mappedItem
    // );
    expect(itemsReturned.length).toEqual(resp.items.length);
  });
});
