<template>
  <label>
    <input
      type="radio"
      class="browser-default ic24-input-field ic24-input-field--primary"
      :value="optionValue"
      v-model="valueInternal"
      v-on:change="onValueChanged"
      :disabled="isDisabled"
    />
    <slot><span v-text="label"></span></slot>
  </label>
  <!--  Example use  -->
  <!--  <div class="e4s-flex-row e4s-gap&#45;&#45;large">-->
  <!--    <FieldRadioV2-->
  <!--      option-value="0"-->
  <!--      v-model.number="builderCompetition.yearFactor"-->
  <!--      label="THIS year"-->
  <!--    />-->
  <!--    <FieldRadioV2-->
  <!--      option-value="-1"-->
  <!--      v-model.number="builderCompetition.yearFactor"-->
  <!--      label="LAST year"-->
  <!--    />-->
  <!--  </div>-->
</template>

<script lang="ts">
import {
  ref,
  defineComponent,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { simpleObjectClone } from "@/common/common-utils";

export default defineComponent({
  name: "RadioButtonObj",
  components: {},
  props: {
    optionValue: {
      type: Object as PropType<unknown>,
      required: true
    },
    value: {
      type: Object as PropType<unknown>,
      required: true
    },
    label: {
      type: String as PropType<unknown>,
      default: ""
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  setup(
    props: {
      optionValue: unknown;
      value: unknown;
      label: string;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(simpleObjectClone(props.value));

    watch(
      () => props.value,
      (newValue: unknown) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = simpleObjectClone(newValue);
        }
      },
      {
        immediate: true
      }
    );

    function onValueChanged() {
      console.log("RadioButtonObj.onValueChanged", valueInternal.value);
      context.emit("input", valueInternal.value);
      context.emit("onChanged", valueInternal.value);
    }

    return { onValueChanged, valueInternal };
  }
});
</script>

<style scoped>
label span {
  padding-left: var(--ic24-flex-gap);
}
</style>
