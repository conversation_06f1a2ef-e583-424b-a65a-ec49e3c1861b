import { CLEO_CONFIG } from "@/common/config/config-";
import https from "@/common/https";
import { IKeywordsRefData } from "@/keywords/keywords-models";
import { KeywordService } from "@/keywords/keyword-service";

const keywordService: KeywordService = new KeywordService();

export class KeywordData {
  private adapterEndPoint = CLEO_CONFIG.ADAPTER_URL
    ? CLEO_CONFIG.ADAPTER_URL
    : "";

  public getRefData(
    tableNames: (keyof IKeywordsRefData)[]
  ): Promise<IKeywordsRefData> {
    let keys: (keyof IKeywordsRefData)[];
    if (tableNames.length === 0) {
      keys = Object.keys(
        keywordService.factoryKeywordsRefData()
      ) as (keyof IKeywordsRefData)[];
    } else {
      keys = tableNames;
    }

    const params = keys
      .map(tableName => {
        return "tableName=" + tableName;
      })
      .join("&");

    return https.get(
      this.adapterEndPoint + "/api/refdata/getReferenceData?" + params,
      {
        responseType: "json"
      }
    );
  }
}
