import {
  ICompleteControllerInput,
  useCompleteController
} from "@/calls/details/complete/useCompleteController";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import { ICallDetail } from "@/calls/details/call-details-models";

const callDetail: ICallDetail = factoryCallDetail();

describe("BRISDOC CAS Clini contact-made", () => {
  it("CAS Clini contact made flow failure: END_ASSESSMENT", () => {
    callDetail.Service.serviceType = "CAS";

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    // const step: IStep<CompleteStepName>;

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    // expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    //
    // controller.onHowMangedSelected({
    //   id: "BASE",
    //   description: "Base - Face to Face",
    //   value: "3-BaseF2F"
    // });

    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.onContactMade({
      id: "CONTACT_MADE",
      description: "Contact Made",
      value: "CONTACT_MADE"
    });

    expect(controller.state.currentStep).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
    );

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: null,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
    );
    expect(controller.state.validationMessages[0].id).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__NON_CLINICAL_SUPPORT_REQUIRED"
    );

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: true,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
    );

    expect(
      controller.state.validationMessages.find(
        m =>
          m.id === "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__SUPPORT_TYPE_REQUIRED"
      )
    ).toBeDefined();

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: false,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.validationMessages.length).toBe(0);

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: true,
      supportTypeRequired: "CHILDREN_ED_REFERRAL",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe("BRISDOC_AUDIT_QUESTIONS");
    controller.onBrisDocAuditQuestionsSelected({
      questions: [
        {
          id: "Q1",
          type: "radio",
          label:
            "Q1 - Do you have any safeguarding concerns relating to the current consultation?",
          options: ["Yes", "No"],
          mandatory: true,
          value: null,
          visible: true
        }
      ],
      answers: {
        Q1: "Yes"
      },
      isValid: true,
      questionsThatNeedAnswer: []
    });

    controller.goto("NEXT");

    expect(controller.state.userResponse.BRISDOC_AUDIT_QUESTIONS.isValid).toBe(
      true
    );

    expect(controller.state.validationMessages.length).toBe(0);

    expect(controller.state.currentStep).toBe("OUTCOMES");

    controller.onOutcomesSelected({
      outcome: "Some Outcome",
      subOutcome: "Some Sub Outcome",
      otherOutcome: "Some Other Outcome",
      outcomeOptions: {}
    });
    expect(controller.state.currentStep).toBe("FURTHER_ACTION");

    controller.onPatientReferredTo({
      referredTo: { id: "", description: "", value: "" },
      referredText: "",
      furtherActionGP: { id: "", description: "", value: "" },
      furtherActionGPText: ""
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");

    controller.onReadCodesSelected([
      {
        ReadCode: "123",
        ReadCodeDescription: "Some description",
        HasChildren: false
      }
    ]);

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");
    expect(controller.state.isProcessComplete).toBe(true);
  });
});
