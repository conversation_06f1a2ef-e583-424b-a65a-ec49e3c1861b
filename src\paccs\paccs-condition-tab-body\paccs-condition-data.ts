import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";
import { IPaccsConditionOrder } from "@/paccs/paccs-condition-tab-body/paccs-condition-models";

export class PaccsConditionData {

  private endPoint = CLEO_CONFIG.PACCS_URL ? CLEO_CONFIG.PACCS_URL : "";

  public getConditionByTemplateId(
    id: string,
    gender: string,
    ageGroup: string
  ): Promise<IPaccsConditionOrder[]> {
    return https.get(
      this.endPoint +
      "/api/template/getconditionbytemplateid/" +
      id +
      "/" +
      gender +
      "/" +
      ageGroup
    );
  }
}
