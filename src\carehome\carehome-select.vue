<template>
  <select v-model="careHomeSelected" v-on:change="selected">
    <option
      v-for="careHome in getCareHomes"
      :key="careHome.id"
      :value="careHome"
      v-text="careHome.name + ' : ' + careHome.email"
    ></option>
  </select>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { ICareHome } from "@/carehome/carehome-models";
import * as CareHomeService from "@/carehome/carehome-service";
import * as CommonUtils from "@/common/common-utils";

export default defineComponent({
  name: "carehome-select",
  components: {},
  props: {
    careHomes: {
      type: Array as PropType<ICareHome[]>,
      default: () => {
        return [];
      }
    },
    careHomeDefault: {
      type: Object as PropType<ICareHome>,
      default: () => {
        return CareHomeService.factoryCareHome();
      }
    }
  },
  setup(
    props: { careHomes: ICareHome[]; careHomeDefault: ICareHome },
    context: SetupContext
  ) {
    const careHomeDefault: ICareHome = CareHomeService.factoryCareHome();

    const careHomeSelected = ref(careHomeDefault);

    const getCareHomes = computed(() => {
      const careHomes = props.careHomes.filter(careHome => {
        return careHome.isEnabled || careHome.id === careHomeSelected.value.id;
      });
      return CommonUtils.simpleObjectClone([careHomeDefault, ...careHomes]);
    });

    function selected() {
      context.emit("selected", CommonUtils.simpleObjectClone(careHomeSelected.value));
    }

    watch(
      () => props.careHomeDefault,
      (newValue: ICareHome) => {
        props.careHomes.forEach(careHome => {
          if (careHome.id === props.careHomeDefault.id) {
            careHomeSelected.value = CommonUtils.simpleObjectClone(careHome);
          }
        });
      },
      {
        immediate: true
      }
    );

    return { careHomeSelected, getCareHomes, selected };
  }
});
</script>

<style></style>
