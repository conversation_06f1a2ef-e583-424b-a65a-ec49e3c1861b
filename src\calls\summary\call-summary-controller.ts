import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import {
  CLEO_ACTION_NAME,
  PEM_MESSAGE_TYPE
} from "@/common/cleo-common-models";
import { loggerInstance } from "@/common/Logger";
import { SERVICE_NAME } from "@/common/services/services-models";
import { ILegacyCleoCallSummary } from "@/common/cleo-legacy-models";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";
import { IUser } from "@/user/user-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CLEO_CLIENT_SERVICE } from "@/common/common-models";
import * as CommonUtils from "@/common/common-utils";

const cleoCommonService: CleoCommonService = new CleoCommonService();
const callSummaryService: CallSummaryService = new CallSummaryService();

export class CallSummaryController {
  private adapterEndPoint = CLEO_CONFIG.ADAPTER_URL
    ? CLEO_CONFIG.ADAPTER_URL
    : "";

  public processAction(
    cleoAction: CLEO_ACTION_NAME,
    cleoCallSummaries: ICleoCallSummary[]
  ): void {
    const actionMap: Record<CLEO_ACTION_NAME, () => void> = {
      MAKE_APPOINTMENT: () => {
        this.makeAppointment(cleoCallSummaries[0]);
      },
      ARRIVED: () => {
        this.setArrivedTime(cleoCallSummaries[0]);
      },
      MODIFY_ARRIVED: () => {
        return;
      },
      ACKNOWLEDGE_RECEIPT_BASE: () => {
        this.acknowledgeReceiptBase(cleoCallSummaries[0]);
      },
      COMFORT_COURTESY_CALL: () => {
        this.setComfortCourtesy(cleoCallSummaries[0]);
      },
      "EDIT CLASSIFICATION": () => {
        this.editClassification(cleoCallSummaries[0]);
      },
      DOWNGRADE: () => {
        this.downgradeCall(cleoCallSummaries);
      },
      ASSIGN: () => {
        this.assignCall(cleoCallSummaries);
      },
      ASSIGN_BASE: () => {
        this.assignCallBase(cleoCallSummaries);
      },
      SECONDARY_ASSIGN: () => {
        this.assignCallSecondary(cleoCallSummaries);
      },
      PRIORITY: () => {
        this.priorityCall(cleoCallSummaries[0], cleoCallSummaries);
      },
      DISPATCH_VEHICLE: () => {
        this.dispatchCarSelect(cleoCallSummaries);
      },
      RETRIEVE_VEHICLE: () => {
        this.dispatchCarRetrieveSelect(cleoCallSummaries);
      },
      PRINT: () => {
        this.printCall(cleoCallSummaries[0]);
      },
      "UNLOCK CALL": () => {
        this.unlockCall(cleoCallSummaries[0]);
      },
      RESEND_INDIV: () => {
        this.resendEmailDialog(cleoCallSummaries, "Resend Individual");
      },
      RESEND_SUMMARY: () => {
        this.resendEmailDialog(cleoCallSummaries, "Resend Summary");
      },
      RESEND_DTS: () => {
        this.resendEmailDialog(cleoCallSummaries, "Resend DTS");
      },
      RESEND_RESOLVED: () => {
        this.resolveMessagingDialog(cleoCallSummaries);
      },
      RESEND_PEMS: () => {
        this.resendEmailDialog(cleoCallSummaries, "Resend PEMS");
      },
      RESEND_CAN_NOT_RESOLVE: () => {
        this.resendEmailDialog(cleoCallSummaries, "Cannot Resolve");
      },
      ADD_CONSULTATION: () => {
        this.addConsultation();
      },
      SUPPORT: () => {
        window.CallControllerClient.showSupport();
      },

      NEW_CALL__OOH: () => {
        this.createNewCall("OOH");
      },
      NEW_CALL__NOR_WIS_111: () => {
        this.createNewCall("Norfolk and Wisbech 111");
      },
      NEW_CALL__EAST_KENT_111: () => {
        this.createNewCall("East Kent 111");
      },
      NEW_CALL__NORTH_ESSEX_111: () => {
        this.createNewCall("North Essex 111");
      },
      NEW_CALL__SOUTH_ESSEX_111: () => {
        this.createNewCall("South Essex 111");
      },
      NEW_CALL__DEVON_111: () => {
        this.createNewCall("Devon 111");
      },
      NEW_CALL__POSL: () => {
        this.createNewCall("POSL");
      },
      NEW_CALL__BRIGHTON_DISTRICT_NURSE: () => {
        this.createNewCall("Brighton District Nurse");
      },
      NEW_CALL__ROVING_GP: () => {
        this.createNewCall("Roving GP");
      },
      NEW_CALL__FCMS: () => {
        this.createNewCall("FCMS");
      },
      "NEW CALL - BRISDOC": () => {
        this.createNewCall("BrisDoc");
      },
      "NEW CALL - MENTAL HEALTH": () => {
        this.createNewCall("BrisDoc", undefined, "MENTAL_HEALTH");
      },
      "NEW CALL - FRAILTY": () => {
        this.createNewCall("BrisDoc", undefined, "FRAILTY");
      },
      "NEW CALL - PAEDIATRICS": () => {
        this.createNewCall("BrisDoc", undefined, "PAEDIATRICS");
      },
      "NEW CALL - WEEKDAY PROFESSIONAL LINE": () => {
        this.createNewCall("BrisDoc", undefined, "WEEKDAY_PROFESSIONAL_LINE");
      },
      "NEW CALL - OUT OF HOURS PROFESSIONAL LINE": () => {
        this.createNewCall(
          "BrisDoc",
          undefined,
          "OUT_OF_HOURS_PROFESSIONAL_LINE"
        );
      },
      "NEW CALL - PATIENTLINE": () => {
        this.createNewCall("BrisDoc", undefined, "PATIENT_LINE");
      },
      OVERSIGHT_VALIDATION: () => {
        this.oversightValidation(cleoCallSummaries[0]);
      },
      VALIDATE_CAS: () => {
        this.validateCas(cleoCallSummaries[0]);
      },
      ASSIGN_DX: () => {
        this.assignDx(cleoCallSummaries[0]);
      },
      ASSIGN_TO_BASE_NO_ROTA: () => {
        this.assignToBaseNoRota(cleoCallSummaries[0]);
      },
      ASSIGN_TO_CLINICIAN_NO_ROTA: () => {
        this.assignToClinician(cleoCallSummaries[0], true);
      },
      ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR: () => {
        this.assignToClinician(cleoCallSummaries[0], false);
      },
      MOVE_CASE: () => {
        this.moveCase(cleoCallSummaries[0]);
      },
      MOVE_CASE_OUT_URGENT_FOLLOW_UP: () => {
        this.urgentFollowUpRemove(cleoCallSummaries[0]);
      },
      CAS_APPOINTMENT: () => {
        this.casAppointment(cleoCallSummaries[0]);
      },
      ADDCOMMENTS: () => {
        this.askCaseComments(cleoCallSummaries[0]);
      },
      SMS_MANUAL: () => {
        this.sendSmsManual(cleoCallSummaries[0]);
      },
      APPTS_EXTERNAL: () => {
        this.appointmentExternal(cleoCallSummaries[0]);
      },
      REQUEST_GS_PHOTO: () => {
        this.requestPhotoFromGoodsam(cleoCallSummaries[0]);
      },
      REFRESH_GS_PHOTO: () => {
        this.refreshPhotoFromGoodsam(cleoCallSummaries[0]);
      },
      PLS_REMOVE: () => {
        this.plsRemove(cleoCallSummaries[0]);
      }
    };

    if (actionMap[cleoAction]) {
      loggerInstance.log(
        "!!!!!!!!!!!!!!!!!!!!!!!!!!CallSummaryController.processAction() cleoAction: " +
          cleoAction +
          " FOUND!"
      );
      actionMap[cleoAction]();
    } else {
      loggerInstance.log(
        "CallSummaryController.processAction() cleoAction: " +
          cleoAction +
          " NOT FOUND!"
      );
    }
  }

  public lockCall(cleoCallSummary: ICleoCallSummary, user: IUser): any {
    return https.get(
      this.adapterEndPoint +
        "/api/calls/lockCallRecord?callNo=" +
        cleoCallSummary.CallNo +
        "&userName=" +
        user.userName,
      {
        responseType: "json"
      }
    );
  }

  public openCall(cleoCallSummary: ICleoCallSummary, user: IUser): void {
    if (
      !callSummaryService.isCallLockedByAnotherUser(
        cleoCallSummary,
        user.userName
      )
    ) {
      this.lockCall(cleoCallSummary, user);
    }

    window.CallControllerClient.OpenDocumentNewWindow(
      cleoCallSummary.CallNo.toString(),
      null,
      null,
      cleoCallSummary.CallStatusValue.toString(),
      cleoCallSummary.CallService.Description &&
        cleoCallSummary.CallService.Description.length > 0
        ? cleoCallSummary.CallService.Description
        : "",
      this.convertToLegacyCleoCallSummary(cleoCallSummary)
    );
  }

  public setLegacyClickItem(cleoCallSummary: ICleoCallSummary): void {
    window.CGC.clickItem = this.convertToLegacyCleoCallSummary(cleoCallSummary);
  }

  public makeAppointment(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.makeAppointment();
  }

  public setArrivedTime(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.setArrivedTime();
  }

  public acknowledgeReceiptBase(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.acknowledgeReceiptBase();
  }

  public setComfortCourtesy(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.setComfortCourtesy();
  }

  public editClassification(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.editClassification();
  }

  public downgradeCall(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.downgradeDialog(() => {
      return;
    }, callNumbers);
  }

  public assignCall(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.assignCallDialog(
      false,
      () => {
        return;
      },
      callNumbers
    );
  }

  public assignCallBase(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.assignBaseDialog(
      () => {
        return;
      },
      false,
      callNumbers
    );
  }

  public assignCallSecondary(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.selectSecondaryAssignee(() => {
      return;
    }, callNumbers);
  }

  public priorityCall(
    cleoCallSummary: ICleoCallSummary,
    cleoCallSummaries: ICleoCallSummary[]
  ): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.priorityDialog(
      () => {
        return;
      },
      callNumbers,
      this.convertToLegacyCleoCallSummary(cleoCallSummary)
    );
  }

  public dispatchCarSelect(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.dispatchCarSelect(() => {
      return;
    }, callNumbers);
  }

  public dispatchCarRetrieveSelect(
    cleoCallSummaries: ICleoCallSummary[]
  ): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.CallControllerClient.dispatchCarRetrieveSelect(() => {
      return;
    }, callNumbers);
  }

  public printCall(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.printCall();
  }

  public unlockCall(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    window.ContextMenuController.onUnlockCallMenu();
  }

  public addConsultation(): void {
    callSummaryDisplay(window.CallControllerClient).selectDraft111Call();
  }

  public resendEmailDialog(
    cleoCallSummaries: ICleoCallSummary[],
    messageType: PEM_MESSAGE_TYPE
  ): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.ContextMenuController.resendEmailDialog(
      () => {
        return;
      },
      callNumbers,
      messageType
    );
  }

  public moveCase(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.CallControllerClient.caseMove.openDialog();
  }

  public urgentFollowUpRemove(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.urgentFollowUpRemove().openDialog();
  }

  public assignDx(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.assignDx().openDialog();
  }

  public validateCas(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.validateCas().openDialog();
  }

  public oversightValidation(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.oversightValidation().openDialog();
  }

  public assignToClinician(
    cleoCallSummary: ICleoCallSummary,
    doAssign: boolean
  ): void {
    this.setLegacyClickItem(cleoCallSummary);

    if (doAssign) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //  @ts-ignore
      window.assignToClinician().openDialog();
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //  @ts-ignore
      window.assignToClinician().openDialog("UN-ASSIGN");
    }
  }

  public assignToBaseNoRota(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.assignToBaseNoRota().openDialog();
  }

  public casAppointment(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.AppointmentController.openFromMenu();
  }

  public askCaseComments(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.CallControllerClient.askCaseComments();
  }

  public sendSmsManual(cleoCallSummary: ICleoCallSummary): void {
    const leagacyCleoCallSummary = this.convertToLegacyCleoCallSummary(
      cleoCallSummary
    );

    const isReturnNumberMobile = CommonUtils.isMobileNumber(
      leagacyCleoCallSummary.CallTelNo_R
    );

    loggerInstance.log(
      "isReturnNumberMobile " +
        leagacyCleoCallSummary.CallTelNo_R +
        ": " +
        isReturnNumberMobile
    );
    if (!isReturnNumberMobile) {
      leagacyCleoCallSummary.CallTelNo_R = "";
    }

    window.CGC.clickItem = leagacyCleoCallSummary;
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.smsDialog().openDialog();
  }

  public appointmentExternal(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.appointmentViewer().openDialog();
  }

  public requestPhotoFromGoodsam(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.goodsamImageController.requestPhotoFromGoodsam();
  }

  public refreshPhotoFromGoodsam(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.goodsamImageController.getPhotosFromGoodsamDialog();
  }
  public plsRemove(cleoCallSummary: ICleoCallSummary): void {
    this.setLegacyClickItem(cleoCallSummary);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    window.plsRemove().openDialog();
  }

  public resolveMessagingDialog(cleoCallSummaries: ICleoCallSummary[]): void {
    const callNumbers = this.getCallNumbers(cleoCallSummaries);
    window.ContextMenuController.resolveMessagingDialog(
      () => {
        return;
      },
      callNumbers,
      "Resend Resolved"
    );
  }

  public createNewCall(
    serviceName: SERVICE_NAME,
    callServiceAlt?: string,
    cleoClientService?: CLEO_CLIENT_SERVICE
  ): void {
    loggerInstance.log(
      "!!!!!!!!!!!!!!!!!!!!!!! CallSummaryController.createNewCall() serviceName: " +
        serviceName
    );
    window.CallControllerClient.loadNewCallDocument(
      serviceName,
      callServiceAlt,
      cleoClientService
    );
  }

  //ICleoCallSummary

  // GridLegacyCallSummary
  // ILegacyCleoCallSummary

  public convertToLegacyCleoCallSummary(
    cleoCallSummary: ICleoCallSummary
  ): ILegacyCleoCallSummary {
    const callClassification =
      cleoCallSummary.CallClassification &&
      cleoCallSummary.CallClassification.Description
        ? cleoCallSummary.CallClassification.Description
        : "";
    const callSubClassification =
      cleoCallSummary.CallSubClassification &&
      cleoCallSummary.CallSubClassification.Description
        ? cleoCallSummary.CallSubClassification.Description
        : "";

    return {
      CallStatusValue: cleoCallSummary.CallStatusValue.toString(),
      CallCreatedBy: cleoCallSummary.CallCreatedBy,
      CallNo: cleoCallSummary.CallNo.toString(),
      CallID: cleoCallSummary.CallNo.toString(),
      CallService:
        cleoCallSummary.CallService && cleoCallSummary.CallService.Description
          ? cleoCallSummary.CallService.Description
          : "",
      IUC_Contract:
        cleoCallSummary.IucContract && cleoCallSummary.IucContract.Description
          ? cleoCallSummary.IucContract.Description
          : "",
      CallClassification: callClassification,
      CC: callClassification,
      CallSubClassification: callSubClassification,
      CSC: callSubClassification,
      CallUrgentYN: cleoCallSummary.CallUrgentYn ? "Yes" : "No",
      CallSurname: cleoCallSummary.CallSurname,
      CallForename: cleoCallSummary.CallForename,
      CallDoctorName: cleoCallSummary.CallDoctorName,
      CallDoctorNameCN: cleoCommonService.formatUserDominoName(
        cleoCallSummary.CallDoctorName
      ),
      CallAppointmentTime: cleoCallSummary.CallAppointmentTime
        ? cleoCallSummary.CallAppointmentTime
        : "",
      DutyBase: cleoCallSummary.DutyBase ? cleoCallSummary.DutyBase : "",
      BreachActualTime: cleoCallSummary.BreachActualTime
        ? cleoCallSummary.BreachActualTime
        : "",
      StartConsultationPerformed: cleoCallSummary.StartConsultationPerformed
        ? "1"
        : "",
      CliniHighPriority: cleoCallSummary.CliniHighPriority ? "1" : "",
      Linked_Call_ID: "",
      CallArrivedTime: cleoCallSummary.CallArrivedTime
        ? cleoCallSummary.CallArrivedTime
        : "",
      CallAddress1: cleoCallSummary.CallAddress1,
      CallAddress2: cleoCallSummary.CallAddress2,
      CallAddress3: cleoCallSummary.CallAddress3,
      CallAddress4: cleoCallSummary.CallAddress4,
      CallTown: cleoCallSummary.CallTown,
      CallPostCode: cleoCallSummary.CallPostCode,
      FinalDispositionCode: cleoCallSummary.FinalDispositionCode
        ? cleoCallSummary.FinalDispositionCode
        : "",
      CHFinalDispositionCode: cleoCallSummary.ChFinalDispositionCode
        ? cleoCallSummary.ChFinalDispositionCode
        : "",
      cleoClientService: cleoCallSummary.cleoClientService,
      Cpl_supportTypeRequired: cleoCallSummary.Cpl_supportTypeRequired,
      OVERSIGHT_BASE_TRIAGE_TYPE: cleoCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE,
      CallTelNo: cleoCallSummary.CallTelNo,
      CallTelNo_R: cleoCallSummary.CallTelNo_R
    };
  }

  public factoryLegacyCleoCallSummary(): ILegacyCleoCallSummary {
    return {
      CallCreatedBy: "",
      CallNo: "",
      CallID: "",
      CallService: "",
      IUC_Contract: "",
      CallClassification: "",
      CC: "",
      CallSubClassification: "",
      CSC: "",
      CallUrgentYN: "No",
      CallSurname: "",
      CallForename: "",
      CallDoctorName: "",
      CallDoctorNameCN: "",
      CallAppointmentTime: "",
      DutyBase: "",
      BreachActualTime: "",
      StartConsultationPerformed: "",
      CliniHighPriority: "",
      Linked_Call_ID: "",
      CallArrivedTime: "",
      CallStatusValue: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "",
      FinalDispositionCode: "",
      CHFinalDispositionCode: "",
      cleoClientService: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      Cpl_supportTypeRequired: "",
      CallTelNo: "",
      CallTelNo_R: ""
    };
  }

  private getCallNumbers(cleoCallSummaries: ICleoCallSummary[]): string[] {
    return cleoCallSummaries.map(cleoCall => {
      return cleoCall.CallNo.toString();
    });
  }
}
