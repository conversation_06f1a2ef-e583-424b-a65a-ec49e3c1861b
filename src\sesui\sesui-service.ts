import { ISesuiCallFormState } from "@/sesui/callform/useSesuiCallForm";
import { isMobileNumber, isTelephoneNumber } from "@/common/common-utils";
import { ISesuiEventMessageNewCallState } from "@/sesui/use-sesui-models";

export function factorySesuiCallFormState(): ISesuiCallFormState {
  return {
    callState: factorySesuiEventPayloadNewCallState(),
    telephoneNumberToCall: "",
    ui: {
      allowTelephoneEdit: false,
      showMakeCall: true,
      showEndCall: false
    },
    message: {
      text: "",
      css: {}
    }
  };
}

export function factorySesuiEventPayloadNewCallState(): ISesuiEventMessageNewCallState {
  return {
    call_id: 0,
    event_type: "call_new_state",
    new_state: 0,
    operator_id: 0,
    status_text: ""
  };
}

export function isTelephoneNumberValid(telephone: string): boolean {
  // const pattern = /^(((\+44\s?\d{4}|\(?0\d{4}\)?)\s?\d{3}\s?\d{3})|((\+44\s?\d{3}|\(?0\d{3}\)?)\s?\d{3}\s?\d{4})|((\+44\s?\d{2}|\(?0\d{2}\)?)\s?\d{4}\s?\d{4}))(\s?\#(\d{4}|\d{3}))?$/;
  // const pattern = /^\s*((?:[+](?:\s?\d)(?:[-\s]?\d)|0)?(?:\s?\d)(?:[-\s]?\d){9}|[(](?:\s?\d)(?:[-\s]?\d)+\s*[)](?:[-\s]?\d)+)\s*$/;
  // return pattern.test(telephone);
  return isMobileNumber(telephone) || isTelephoneNumber(telephone);
}
