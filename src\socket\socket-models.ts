import { GENDER } from "@/common/common-models";

export interface ISingleQCall {
  Id: number;
  SystemCaseReference: number;
  CaseReceivedDateTime: string;
  CaseBreachedTime: string;
  CasePatientName: string;
  CasePatientNHSNumber: string;
  CasePatientAge: string;
  CasePDSTraceVerified: boolean;
  CaseAlternativeTelephoneFlag: boolean;
  CasePatientGender: GENDER; //  Should this be {id: number, name: string}
  CurrentPostCode: string;
  CasePatientTelephoneNumber: string;
  CaseSGCode: string;
  SystemId: number;
  CaseLocked: boolean;
}
