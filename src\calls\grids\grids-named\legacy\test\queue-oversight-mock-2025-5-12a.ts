import { GridLegacyServerResponse } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

export const queueOversightMockSource20250512a: GridLegacyServerResponse = ({
  Count: 5,
  Returned: 5,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: [
    {
      unid: "B26CBF1C1F8670CD80258C6F0055C149",
      name: "250658031",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250658031",
      CallID: "250658031",
      CallNHSNo: "",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1966-06-06",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall3/O=cleouat",
      CallCName: "",
      CallCRel: "Patient",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-17T22:09:23+01:00",
      BreachPreActualTime: "2025-04-17T16:30:20+01:00",
      BreachActualTime: "2025-04-17T22:29:23+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "58 yrs",
      CallDoctorNameCN: "",
      PatientName: "NNN_2H, Ewfew",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NNN_2H",
      CallForename: "Ewfew",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx1234",
      CHFinalDispositionDescription: "Speak to within 2hrs",
      FinalDispositionCode: "Dx1234",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "17/04/2025 16:29:23",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE:
        "No specific PPE requirements/infectious concerns",
      OversightValidationType: "Further Clinical Input Required",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "E59B57A296093E3780258C7900543F4C",
      name: "250658420",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250658420",
      CallID: "250658420",
      CallNHSNo: "",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1989-06-07",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      CallClassification: "Visit",
      CC: "Visit",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Nursing Home",
      BreachKey: "VisitNo",
      ApplyBreach: "1",
      CallReceivedISO: "",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-04-27T22:00:12+01:00",
      BreachPreActualTime: "2025-04-24T15:25:12+01:00",
      BreachActualTime: "2025-04-27T22:20:12+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "35 yrs",
      CallDoctorNameCN: "",
      PatientName: "TEST, Pd",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Christchurch",
      Dispatch_Vehicle: "Christchurch Car",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST",
      CallForename: "Pd",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "4 hours",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "false",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Home Visit",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "27/04/2025 16:20:12",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "Isolation Room/PPE Required",
      OversightValidationType: "Approve",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: ""
    },
    {
      unid: "CAC0EBD424C8357780258C7D003603D6",
      name: "250658705",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250658705",
      CallID: "250658705",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "UNK",
      UTC_Assigned: "",
      CallClassification: "Advice (Non Clinical Complete)",
      CC: "Advice",
      CSC: "Non Clinical Complete",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "08/05/2025 14:10:14",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "Ben Smythson",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-01T10:51:24+01:00",
      CallReceivedTimeISO: "2025-05-01T10:49:59+01:00",
      BreachWarnActualTime: "2025-05-01T11:31:24+01:00",
      BreachPreActualTime: "2025-05-01T10:51:24+01:00",
      BreachActualTime: "2025-05-01T11:51:24+01:00",
      BreachPriority: "7",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "10:49",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "29 yrs",
      CallDoctorNameCN: "Nick Wall2",
      PatientName: "UNK, Unk",
      CallTriaged: "No",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Severnside Face-to-Face",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "08/05/2025 14:14:19",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "UNK",
      CallForename: "Unk",
      CallDoctorName: "CN=Nick Wall2/O=cleouat",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "Here are a load of comments",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "TAKE_LIST",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "21564668DD18494580258C84001D895A",
      name: "250658874",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250658874",
      CallID: "250658874",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      CallClassification: "Advice (Non Clinical Complete)",
      CC: "Advice",
      CSC: "Non Clinical Complete",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "08/05/2025 06:25:24",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "Ben Smythson",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-05-08T06:23:41+01:00",
      CallReceivedTimeISO: "2025-05-08T06:22:37+01:00",
      BreachWarnActualTime: "2025-05-08T07:03:41+01:00",
      BreachPreActualTime: "2025-05-08T06:23:41+01:00",
      BreachActualTime: "2025-05-08T07:23:41+01:00",
      BreachPriority: "7",
      BreachLevel1Mins: "60",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "06:22",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "29 yrs",
      CallDoctorNameCN: "Nick Wall2",
      PatientName: "BEN SMS, Test",
      CallTriaged: "No",
      CallSymptoms: "SMS TEST.",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Severnside Home Visit",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "08/05/2025 14:45:39",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "07903 746919",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "BEN SMS",
      CallForename: "Test",
      CallDoctorName: "CN=Nick Wall2/O=cleouat",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx11",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 1 hour",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "TAKE_LIST",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "1",
      SMS_SENT: "0",
      SMS_LATEST_AT: "08/05/2025 06:57:32",
      SMS_LATEST_USER: "CN=Ben Smythson/O=staging",
      SMS_LATEST_MESSAGE: "TEST",
      SMS_COUNT: ""
    },
    {
      unid: "78561CCCC6E03AEE80258C880054A492",
      name: "250659174",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250659174",
      CallID: "250659174",
      CallNHSNo: "**********",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-12T16:24:31+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-12T22:04:31+01:00",
      BreachPreActualTime: "2025-05-08T07:42:27+01:00",
      BreachActualTime: "2025-05-12T22:24:31+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "NHS111Interop",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "64 yrs",
      CallDoctorNameCN: "",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "07912 626865",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "12/05/2025 16:24:31",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE:
        "No specific PPE requirements/infectious concerns",
      OversightValidationType: "Clinical Co-Ordinator Review in progress",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "1",
      SMS_SENT: "0",
      SMS_LATEST_AT: "08/05/2025 07:42:54",
      SMS_LATEST_USER: "ITK",
      SMS_LATEST_MESSAGE:
        "Your details have been passed to us by NHS111.  We are working hard to contact you as soon as possible, but please be aware this can be longer than the timeframe given by NHS111. If this is the case, one of our call handlers will phone you to check how you are.  Severnside Integrated Urgent Care. ",
      SMS_COUNT: ""
    }
  ],
  Page: {
    Enabled: 1,
    PageNumber: 1,
    PageSize: 1000,
    getRowCount: 5,
    getStartRowNumber: 1,
    TotalSearchRowCount: 5,
    getTotalNumberOfPages: 1
  }
} as any) as GridLegacyServerResponse;
