<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
<!--    <script src="https://assets.what3words.com/sdk/v3/what3words.js?key=J5XCKWYY"></script>-->
<!--    <script type="module" src="https://cdn.what3words.com/javascript-components@4-latest/dist/what3words/what3words.esm.js"></script>-->
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>

    <div id="app"></div>

    <div id="adapter-paccs" class="widget-sidebar">
      <portal-target></portal-target>
      paccs will go here
    </div>


    <hr/>

    <div id="adapter-paccs-triage-records-report" class="widget-sidebar">
      <portal-target></portal-target>
      paccs triage record report will go here
    </div>

    <hr/>

    <div id="adapter-consults" class="widget-sidebar">
      <portal-target></portal-target>
      Consults will go here...
    </div>

    <div id="adapter--sesui-login-modal">
      <portal-target></portal-target>
      Sesui Login Modal contents will go here
    </div>

    Local server mounting portal for CareHome
    <div id="carehome-select"></div>

    <button id="id_ECD">Edit Address1</button>

    CallObjective:
    <textarea id="CallObjective" rows="5" style="width: 100%;"></textarea>

    Presenting Conditions:
    <textarea id="CHUB_Presenting" rows="5" style="width: 100%;"></textarea>

    PathwaysCaseId:
    <input type="text" id="PathwaysCaseId" name="PathwaysCaseId" value=""/>

    <div id="adapter--follow-up">
      <portal-target></portal-target>
      Return Tel number
    </div>

<!--    <div id="widget" class="widget-sidebar">-->
<!--      This Element is not controlled by our Vue-App, but we can create a-->
<!--    </div>-->
    <!-- built files will be auto injected -->
  </body>
</html>
