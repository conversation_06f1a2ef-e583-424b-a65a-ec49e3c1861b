import { IPathwaysDataset, IPathwaysDatasetLegacy } from "@/paccs/paccs-models";
import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";
import { userPermissionServerResponseMockData } from "@/permissions/permission-mock-data";

export const MOCK_PATHWAYS_DATASET: IPathwaysDataset = {
  addressBuildingPrefix: "",
  addressBuildingIdentifier: "",
  addressStreetName1: "",
  addressStreetName2: "",
  addressTown: "Some Town",
  addressCounty: "",
  addressDescription: "",
  addressEasting: 0,
  addressNorthing: 0,
  addressKey: "",
  ageInHours: 130560,
  callerTelephoneNumber: "00000000000",
  caseStartTime: "2021-05-18T11:21:48.492Z",
  clinicianCallStartTime: "2021-05-18T11:21:48.492Z",
  forename: "SomeForename",
  gender: "Male",
  hostCaseId: "1",
  hostDocId: "5A89CC56B241176A802586D3002F1057",
  nhsId: "",
  party: "First",
  patientId: "ASHD-AKECKS-497",
  postCode: "NR33 9LS",
  presentingCondition: "My symptoms are: I feel sick",
  previousEncounters: 0,
  serviceId: "NORFOLK AND WISBECH 111",
  skillSet: 15,
  surgeryId: "Z10000",
  surname: "SomeSurname",
  traceVerified: true,
  userId: "Joe Bloggs/sehnp",
  dob: "20210518"
};
export const MOCK_PATHWAYS_DATASET_LEGACY: IPathwaysDatasetLegacy = {
  InjuryModule: "",
  skillset: 15,
  userId: "Joe Bloggs/sehnp",
  pathwaysCaseId: "",
  cadCaseId: "",
  hostCaseId: "1",
  setting: "",
  serviceId: "EAST KENT 111",
  hostDocId: "5A89CC56B241176A802586D3002F1058",
  casQ: 0,
  caseStartTime: "2021-02-04T15:08:55+00:00",
  patientId: "ASHD-AKECKS-497",
  surname: "SomeSurname",
  forename: "SomeForename",
  CallerTelephoneNumber: "00000%20000000",
  ageInHours: 130560,
  gender: 1,
  party: 3,
  surgeryId: "Z10000",
  previousEncounters: "1",
  presentingCondition:
    "%7E%20%5BBen%20Smythson%20%28Call%20Back%29%2004-Feb-2021%2015%3A20%5D%20Test%20Callback%201",
  addressBuildingPrefix: "",
  addressBuildingIdentifier: "",
  addressStreetName1: "",
  addressStreetName2: "",
  addressTown: "",
  addressCounty: "",
  postCode: "SW19 2BP",
  addressDescription: "",
  addressEasting: 0,
  addressNorthing: 0,
  addressKey: "",
  clinicianCallStartTime: "2021-05-18T13:37:37+01:00",
  nhsId: "",
  TraceVerified: true,
  dob: "20210518"
};
