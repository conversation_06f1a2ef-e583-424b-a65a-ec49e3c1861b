import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";

export function canOverSightValidation(
  cleoCallSummary: ICleoCallSummary,
  callPermsShort: Record<CleoPermissionName, ICleoPermission>
): boolean {
  if (!callPermsShort.OVERSIGHT_VALIDATION) {
    return false;
  }
  if (cleoCallSummary.AllViewInclude !== "OVERSIGHT_FOLLOW_UP") {
    return false;
  }

  if (!cleoCallSummary.CallClassification.Description) {
    return false;
  }

  // Only Base, Visit.
  if (
    cleoCallSummary.CallClassification.Description.toUpperCase() === "ADVICE"
  ) {
    return false;
  }
  return true;
}

export function canAssignToClinicianNoRota(
  cleoCallSummary: ICleoCallSummary,
  callPermsShort: Record<CleoPermissionName, ICleoPermission>
): boolean {
  if (!callPermsShort.ASSIGN_TO_CLINICIAN_NO_ROTA) {
    return false;
  }

  return cleoCallSummary.CallDoctorName.length === 0;
}

export function canAssignToClinicianNoRotaClear(
  cleoCallSummary: ICleoCallSummary,
  callPermsShort: Record<CleoPermissionName, ICleoPermission>
): boolean {
  if (!callPermsShort.ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR) {
    return false;
  }

  return cleoCallSummary.CallDoctorName.length > 0;
}
