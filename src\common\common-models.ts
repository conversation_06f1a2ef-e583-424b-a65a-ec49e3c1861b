import { Opaque } from "@/typings/unknown-lib";

export interface MyGlobalSession {
  UserNameCN: string;
  Global_DB_Paths: {
    HOST_PATH: string | undefined;
    PATH_CALL: string | undefined;
    FULL_PATH: string | undefined;
    PATH_XCLEO: string | undefined;
    PATH_GLOBAL: string | undefined;
  };
  SOCKET_URL: string | undefined;
  PACCS_URL: string | undefined;
  ADAPTER_URL: string | undefined;
  DAB_URL: string | undefined;
  LOG_JS_ERRORS: "YES" | "NO";
  LOG_JS_ERRORS_PATH: string | undefined;
  GOOGLE_API_KEY: string;
  WHAT3WORDS_API_KEY: string;
  GRID_LONG_POLLING_SECS: number;
  GRID_BREACH_REFRESH_SECS: number;
  sesui: {
    enabled: boolean;
    url: string;
    user: string;
    pw: string;
  };
}

/**
 * this is test comment about IBase
 * A test Hot Fix
 */
export interface IBase {
  Id: number;
}

export interface IBaseName extends IBase {
  name: string;
}

export interface IObjectKeyType<T> {
  [key: string]: T;
  //  Another way to write this is: let stuff: { [s: string]: string; } = {};
}

export interface IPaging {
  page: number;
  pageSize: number;
  totalCount: number;
}

export interface IServerResponse<T> {
  errNo: number;
  error: string;
  data: T;
  meta?: any;
  message?: string;
}

export interface IServerResponseList<T> {
  data: T[];
}

export interface IServerPagingResponseList<T> extends IServerResponseList<T> {
  meta: IPaging;
}

export const GENDER_CONFIG = {
  0: "",
  1: "Male",
  2: "Female",
  3: "Unknown"
};

// export type GENDER_ID = typeof Object.keys(GENDER_CONFIG)
export type GENDER_ID = 0 | 1 | 2 | 3;
export type GENDER = "" | "Male" | "Female" | "Unknown";

export const legacyGenderMap: Record<GENDER, GENDER_ID> = {
  "": 0,
  Male: 1,
  Female: 2,
  Unknown: 3
};

export type CALL_CLASSIFICATION =
  | "Advice"
  | "Base"
  | "CH Advice"
  | "Nurse Advice"
  | "Message"
  | "Visit";

export type CALL_CLASSIFICATION_UPPERCASE = Uppercase<CALL_CLASSIFICATION>;

// These are "sub services" that get created ad-hoc...
export type CLEO_CLIENT_SERVICE_NO_QUESTIONS =
  | "TOXIC INGESTION"
  | "C3/C4 Validation"
  | "ED Validation"
  | "Follow Up";

// These have question sets that need answering on creating a new case.
export type CLEO_CLIENT_SERVICE_WITH_QUESTIONS =
  | "FRAILTY"
  | "MENTAL_HEALTH"
  | "OUT_OF_HOURS_PROFESSIONAL_LINE"
  | "PAEDIATRICS"
  | "PATIENT_LINE"
  | "WEEKDAY_PROFESSIONAL_LINE";

export type CLEO_CLIENT_SERVICE =
  | ""
  | CLEO_CLIENT_SERVICE_WITH_QUESTIONS
  | CLEO_CLIENT_SERVICE_NO_QUESTIONS
  | string;

export const CLEO_CLIENT_SERVICE_MAP: Record<CLEO_CLIENT_SERVICE, string> = {
  OUT_OF_HOURS_PROFESSIONAL_LINE: "OOHsPL",
  MENTAL_HEALTH: "Mental Health",
  PAEDIATRICS: "Paediatrics",
  WEEKDAY_PROFESSIONAL_LINE: "WDPL",
  FRAILTY: "Frailty",
  PATIENT_LINE: "Patient Line",
  "TOXIC INGESTION": "Toxic Ingestion",
  "C3/C4 Validation": "C3/C4 Validation",
  "ED Validation": "ED Validation",
  "Follow Up": "Follow Up",
  FOLLOW_UP: "Follow Up"
};

export interface IAdapterPagedResponse<T> {
  CurrentPage: number;
  RecordsPerPage: number;
  TotalPages: number;
  TotalRecords: number;
  Records: T[];
}

export type GUID = string;
export type IsoDateTimeOffset = Opaque<"IsoDateTimeOffset", string>;
export type IsoDateTimeWithOffset = string;
export type IsoDateTimeUTC = string;
export type IsoDateTimeLocal = string;
export type PostCodeWithSpace = string;
export type PostCodeNoSpace = string;

export type DxCode = string;
export type DominoNameCN = string; // E.g. Joe Bloggs
export type DominoNameFull = string; // e.g. CN=Joe Bloggs/O=sehnp
export type DominoNameAbbrev = string; // e.g. Joe Bloggs/sehnp
