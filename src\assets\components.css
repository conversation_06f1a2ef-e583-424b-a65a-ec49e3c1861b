:root {
    --slate-50: #f8fafc;
    --slate-100: #f1f5f9;
    --slate-200: #e2e8f0;
    --slate-300: #cbd5e1;
    --slate-400: #94a3b8;
    --slate-500: #64748b;
    --slate-600: #475569;
    --slate-700: #334155;
    --slate-800: #1e293b;
    --slate-900: #0f172a;
    --grey-50: #f9fafb;
    --grey-100: #f3f4f6;
    --grey-200: #e5e7eb;
    --grey-300: #d1d5db;
    --grey-400: #9ca3af;
    --grey-500: #6b7280;
    --grey-600: #4b5563;
    --grey-700: #374151;
    --grey-800: #1f2937;
    --grey-900: #111827;
    --zinc-50: #fafafa;
    --zinc-100: #f4f4f5;
    --zinc-200: #e4e4e7;
    --zinc-300: #d4d4d8;
    --zinc-400: #a1a1aa;
    --zinc-500: #71717a;
    --zinc-600: #52525b;
    --zinc-700: #3f3f46;
    --zinc-800: #27272a;
    --zinc-900: #18181b;
    --neutral-0: #ffffff;
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --white: #ffffff;
    --black: #000000;
    --stone-50: #fafaf9;
    --stone-100: #f5f5f4;
    --stone-200: #e7e5e4;
    --stone-300: #d6d3d1;
    --stone-400: #a8a29e;
    --stone-500: #78716c;
    --stone-600: #57534e;
    --stone-700: #44403c;
    --stone-800: #292524;
    --stone-900: #1c1917;
    --red-50: #fef2f2;
    --red-100: #fee2e2;
    --red-200: #fecaca;
    --red-300: #fca5a5;
    --red-400: #f87171;
    --red-500: #ef4444;
    --red-600: #dc2626;
    --red-700: #b91c1c;
    --red-800: #991b1b;
    --red-900: #7f1d1d;
    --orange-50: #fff7ed;
    --orange-100: #ffedd5;
    --orange-200: #fed7aa;
    --orange-300: #fdba74;
    --orange-400: #fb923c;
    --orange-500: #f97316;
    --orange-600: #ea580c;
    --orange-700: #c2410c;
    --orange-800: #9a3412;
    --orange-900: #7c2d12;
    --amber-50: #fffbeb;
    --amber-100: #fef3c7;
    --amber-200: #fde68a;
    --amber-300: #fcd34d;
    --amber-400: #fbbf24;
    --amber-500: #f59e0b;
    --amber-600: #d97706;
    --amber-700: #b45309;
    --amber-800: #92400e;
    --amber-900: #78350f;
    --yellow-50: #fefce8;
    --yellow-100: #fef9c3;
    --yellow-200: #fef08a;
    --yellow-300: #fde047;
    --yellow-400: #facc15;
    --yellow-500: #eab308;
    --yellow-600: #ca8a04;
    --yellow-700: #a16207;
    --yellow-800: #854d0e;
    --yellow-900: #713f12;
    --lime-50: #f7fee7;
    --lime-100: #ecfccb;
    --lime-200: #d9f99d;
    --lime-300: #bef264;
    --lime-400: #a3e635;
    --lime-500: #84cc16;
    --lime-600: #65a30d;
    --lime-700: #4d7c0f;
    --lime-800: #3f6212;
    --lime-900: #365314;
    --green-50: #f0fdf4;
    --green-100: #dcfce7;
    --green-200: #bbf7d0;
    --green-300: #86efac;
    --green-400: #4ade80;
    --green-500: #22c55e;
    --green-600: #16a34a;
    --green-700: #15803d;
    --green-800: #166534;
    --green-900: #14532d;
    --emerald-50: #ecfdf5;
    --emerald-100: #d1fae5;
    --emerald-200: #a7f3d0;
    --emerald-300: #6ee7b7;
    --emerald-400: #34d399;
    --emerald-500: #10b981;
    --emerald-600: #059669;
    --emerald-700: #047857;
    --emerald-800: #065f46;
    --emerald-900: #064e3b;
    --teal-50: #f0fdfa;
    --teal-100: #ccfbf1;
    --teal-200: #99f6e4;
    --teal-300: #5eead4;
    --teal-400: #2dd4bf;
    --teal-500: #14b8a6;
    --teal-600: #0d9488;
    --teal-700: #0f766e;
    --teal-800: #115e59;
    --teal-900: #134e4a;
    --cyan-50: #ecfeff;
    --cyan-100: #cffafe;
    --cyan-200: #a5f3fc;
    --cyan-300: #67e8f9;
    --cyan-400: #22d3ee;
    --cyan-500: #06b6d4;
    --cyan-600: #0891b2;
    --cyan-700: #0e7490;
    --cyan-800: #155e75;
    --cyan-900: #164e63;
    --sky-50: #f0f9ff;
    --sky-100: #e0f2fe;
    --sky-200: #bae6fd;
    --sky-300: #7dd3fc;
    --sky-400: #38bdf8;
    --sky-500: #0ea5e9;
    --sky-600: #0284c7;
    --sky-700: #0369a1;
    --sky-800: #075985;
    --sky-900: #0c4a6e;
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;
    --indigo-50: #eef2ff;
    --indigo-100: #e0e7ff;
    --indigo-200: #c7d2fe;
    --indigo-300: #a5b4fc;
    --indigo-400: #818cf8;
    --indigo-500: #6366f1;
    --indigo-600: #4f46e5;
    --indigo-700: #4338ca;
    --indigo-800: #3730a3;
    --indigo-900: #312e81;
    --violet-50: #f5f3ff;
    --violet-100: #ede9fe;
    --violet-200: #ddd6fe;
    --violet-300: #c4b5fd;
    --violet-400: #a78bfa;
    --violet-500: #8b5cf6;
    --violet-600: #7c3aed;
    --violet-700: #6d28d9;
    --violet-800: #5b21b6;
    --violet-900: #4c1d95;
    --purple-50: #faf5ff;
    --purple-100: #f3e8ff;
    --purple-200: #e9d5ff;
    --purple-300: #d8b4fe;
    --purple-400: #c084fc;
    --purple-500: #a855f7;
    --purple-600: #9333ea;
    --purple-700: #7e22ce;
    --purple-800: #6b21a8;
    --purple-900: #581c87;
    --fuchsia-50: #fdf4ff;
    --fuchsia-100: #fae8ff;
    --fuchsia-200: #f5d0fe;
    --fuchsia-300: #f0abfc;
    --fuchsia-400: #e879f9;
    --fuchsia-500: #d946ef;
    --fuchsia-600: #c026d3;
    --fuchsia-700: #a21caf;
    --fuchsia-800: #86198f;
    --fuchsia-900: #701a75;
    --pink-50: #fdf2f8;
    --pink-100: #fce7f3;
    --pink-200: #fbcfe8;
    --pink-300: #f9a8d4;
    --pink-400: #f472b6;
    --pink-500: #ec4899;
    --pink-600: #db2777;
    --pink-700: #be185d;
    --pink-800: #9d174d;
    --pink-900: #831843;
    --rose-50: #fff1f2;
    --rose-100: #ffe4e6;
    --rose-200: #fecdd3;
    --rose-300: #fda4af;
    --rose-400: #fb7185;
    --rose-500: #f43f5e;
    --rose-600: #e11d48;
    --rose-700: #be123c;
    --rose-800: #9f1239;
    --rose-900: #881337;
    --SmallShadow: 0 2px 4px hsla(0, 0%, 0%, .05), 0 3px 6px hsla(0, 0%, 0%, .1);
    --MediumShadow: 0 4px 8px hsla(0, 0%, 0%, .05), 0 6px 12px hsla(0, 0%, 0%, .1);
    --LargeShadow: 0 8px 16px hsla(0, 0%, 0%, .05), 0 12px 24px hsla(0, 0%, 0%, .1);
    --DefaultTransition: ease-in-out .2s;
    --BorderRadius: 4px;
    --PillBorderRadius: 50px;
    --CircleBorderRadius: 50%;
    --Opacity50: 0.5;
    --ButtonPadding: 8px;
    --ButtonCompactPadding: 2px 4px;
    --ButtonBorderRadius: var(--BorderRadius);
    --ButtonTextSize: 14px;
    --ButtonFontWeight: 500;
    --ButtonPrimaryBackgroundColor: var(--blue-800);
    --ButtonPrimaryHoverBackgroundColor: var(--blue-700);
    --ButtonPrimaryActiveBackgroundColor: var(--blue-900);
    --ButtonPrimaryTextColor: var(--white);
    --ButtonPrimaryHoverTextColor: var(--white);
    --ButtonPrimaryActiveTextColor: var(--white);
    --ButtonSecondaryBackgroundColor: var(--grey-300);
    --ButtonSecondaryHoverBackgroundColor: var(--grey-200);
    --ButtonSecondaryActiveBackgroundColor: var(--grey-400);
    --ButtonSecondaryTextColor: var(--slate-900);
    --ButtonSecondaryHoverTextColor: var(--slate-900);
    --ButtonSecondaryActiveTextColor: var(--slate-900);
    --ButtonLinkBackgroundColor: transparent;
    --ButtonLinkHoverBackgroundColor: var(--blue-50);
    --ButtonLinkActiveBackgroundColor: var(--blue-100);
    --ButtonLinkTextColor: var(--ButtonPrimaryBackgroundColor);
    --ButtonLinkHoverTextColor: var(--ButtonPrimaryBackgroundColor);
    --ButtonLinkActiveTextColor: var(--ButtonPrimaryBackgroundColor);
    --ButtonGhostBackgroundColor: transparent;
    --ButtonGhostHoverBackgroundColor: var(--grey-300);
    --ButtonGhostActiveBackgroundColor: var(--grey-400);
    --ButtonGhostBorder: 1px solid var(--grey-300);
    --ButtonGhostHoverBorder: 1px solid var(--grey-300);
    --ButtonGhostActiveBorder: 1px solid var(--grey-400);
    --ButtonGhostTextColor: var(--slate-900);
    --ButtonGhostHoverTextColor: var(--slate-900);
    --ButtonGhostActiveTextColor: var(--slate-900);
    --ButtonWarnBackgroundColor: var(--amber-400);
    --ButtonWarnHoverBackgroundColor: var(--amber-300);
    --ButtonWarnActiveBackgroundColor: var(--amber-500);
    --ButtonWarnTextColor: var(--slate-900);
    --ButtonWarnHoverTextColor: var(--slate-900);
    --ButtonWarnActiveTextColor: var(--slate-900);
    --ButtonDangerBackgroundColor: var(--red-800);
    --ButtonDangerHoverBackgroundColor: var(--red-700);
    --ButtonDangerActiveBackgroundColor: var(--red-900);
    --ButtonDangerTextColor: var(--white);
    --ButtonDangerHoverTextColor: var(--white);
    --ButtonDangerActiveTextColor: var(--white);
    --BadgeDefaultBackgroundColor: var(--slate-300);
    --BadgeDefaultTextColor: var(--slate-900);
    --BadgePrimaryBackgroundColor: var(--ButtonPrimaryBackgroundColor);
    --BadgePrimaryTextColor: var(--ButtonPrimaryTextColor);
    --BadgeInverseBackgroundColor: var(--white);
    --BadgeInverseTextColor: var(--ButtonPrimaryBackgroundColor);
    --BadgeCriticalBackgroundColor: var(--red-800);
    --BadgeCriticalTextColor: var(--white);
    --BadgeAddedBackgroundColor: var(--green-100);
    --BadgeAddedTextColor: var(--slate-900);
    --BadgeRemovedBackgroundColor: var(--red-100);
    --BadgeRemovedTextColor: var(--slate-900);
    --BannerDefaultBackgroundColor: var(--slate-300);
    --BannerDefaultTextColor: var(--slate-900);
    --BannerWarnBackgroundColor: var(--amber-400);
    --BannerWarnTextColor: var(--slate-900);
    --BannerErrorBackgroundColor: var(--red-700);
    --BannerErrorTextColor: var(--white);
    --BannerInfoBackgroundColor: var(--blue-100);
    --BannerInfoTextColor: var(--slate-900);
    --BannerCloseButtonBackgroundColor: var(--slate-900);
    --BannerCloseButtonHoverBackgroundColor: var(--red-700);
    --InputBackgroundColor: var(--white);
    --InputBorderColor: var(--grey-300);
    --InputBorderHoverColor: var(--grey-400);
    --InputBorderFocusColor: var(--ButtonPrimaryBackgroundColor);
    --InputBorderActiveColor: var(--ButtonPrimaryBackgroundColor);
    --InputBorderRadius: var(--BorderRadius);
    --InputTextColor: var(--slate-600);
    --InputDisabledBackgroundColor: var(--grey-300);
    --InputDisabledBorderColor: var(--grey-400);
    --InputDisabledTextColor: var(--slate-600);
    --InputPlaceholderColor: var(--grey-400);
    --InputCaretColor: var(--ButtonPrimaryBackgroundColor);
    --InputInvalidBorderColor: var(--red-700);
    --InputInvalidCaretColor: var(--InputInvalidBorderColor);
    --CalendarPadding: 20px;
    --CalendarBackground: var(--white);
    --CalendarDaysOfWeekTextColor: var(--slate-400);
    --CalendarDayCellBackground: var(--white);
    --CalendarDayCellBorderColor: var(--slate-300);
    --CalendarDayCellTextColor: var(--slate-600);
    --CalendarTodayBackground: var(--slate-200);
    --CalendarTodayTextColor: var(--slate-600);
    --CalendarBorderColor: var(--slate-300);
    --CalendarBorderRadius: var(--BorderRadius);
}

.svg-major-container {
    height: 20px;
    width: 20px;
}

.svg-minor-container {
    height: 16px;
    width: 16px;
}

.hidden {
    display: none;
}


.h500 {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 0.01em;
    line-height: 30px;
}

.h400 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.01em;
    line-height: 25.5px;
}

.h300 {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0.02em;
    line-height: 25px;
}

.h200 {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.02em;
    line-height: 22.5px
}

.h100 {
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 0.02em;
    line-height: 20px;
}

.h500,
.h400,
.h300,
.h200,
.h100 {
    color: var(--slate-900);
}

.a300 {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 23.5px;
}

.b300 {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 23.5px;
}

.c300 {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 23.5px;
}

.a200 {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 20px;
}

.b200 {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 16px;
}

.c200 {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 16px;
}

.a100 {
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 14px;
}

.b100 {
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 14px;
}

.c100 {
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 14px;
}

.c100,
.c200,
.c300 {
    font-family: 'Consolas', 'Segoe UI', Courier, monospace;
}

.b300,
.b200,
.b100 {
    color: var(--slate-600);
}

.a300,
.a300:visited,
.a200,
.a200:visited,
.a100,
.a100:visited {
    color: var(--ButtonPrimaryBackgroundColor);
    text-decoration: none;
    cursor: pointer;
}

.a300:hover,
.a200:hover,
.a100:hover {
    text-decoration: underline;
}

.semibold {
    font-weight: 600;
}


.btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: relative;
    max-height: 40px;
    padding: var(--ButtonPadding);
    margin: 4px;
    border: none;
    border-radius: var(--ButtonBorderRadius);
    font-size: var(--ButtonTextSize);
    font-weight: var(--ButtonFontWeight);
    cursor: pointer;
}

.btn:disabled {
    opacity: var(--Opacity50);
    cursor: default;
}

.btn.is-loading {
    cursor: not-allowed;
}

.btn.is-loading:disabled {
    opacity: 1 !important;
    cursor: not-allowed;
}

.btnCompact {
    height: fit-content;
    padding: var(--ButtonCompactPadding) !important;
}

.btnPrimary,
button:disabled:hover.btnPrimary,
button:disabled:active.btnPrimary {
    background-color: var(--ButtonPrimaryBackgroundColor);
    border: 1px solid transparent;
    transition: var(--DefaultTransition);
}

.btnPrimary span,
.btnPrimary > .icon-container > .svg-minor-container > svg path,
.btnPrimary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonPrimaryTextColor);
    fill: var(--ButtonPrimaryTextColor);
    transition: var(--DefaultTransition);
}

.btnPrimary:hover {
    background-color: var(--ButtonPrimaryHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnPrimary:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnPrimary:hover span,
.btnPrimary > .icon-container > .svg-minor-container > svg path,
.btnPrimary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonPrimaryHoverTextColor);
    fill: var(--ButtonPrimaryHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnPrimary:active {
    background-color: var(--ButtonPrimaryActiveBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnPrimary:active span,
.btnPrimary > .icon-container > .svg-minor-container > svg path,
.btnPrimary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonPrimaryActiveTextColor);
    fill: var(--ButtonPrimaryActiveTextColor);
    transition: var(--DefaultTransition);
}

.btnSecondary,
button:disabled:hover.btnSecondary,
button:disabled:active.btnSecondary {
    background-color: var(--ButtonSecondaryBackgroundColor);
    border: 1px solid transparent;
    transition: var(--DefaultTransition);
}

.btnSecondary span,
.btnSecondary > .icon-container > .svg-minor-container > svg path,
.btnSecondary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonSecondaryTextColor);
    transition: var(--DefaultTransition);
}

.btnSecondary:hover {
    background-color: var(--ButtonSecondaryHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnSecondary:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnSecondary:hover span,
.btnSecondary > .icon-container > .svg-minor-container > svg path,
.btnSecondary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonSecondaryHoverTextColor);
    fill: var(--ButtonSecondaryHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnSecondary:active {
    background-color: var(--ButtonSecondaryActiveBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnSecondary:active span,
.btnSecondary > .icon-container > .svg-minor-container > svg path,
.btnSecondary > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonSecondaryActiveTextColor);
    fill: var(--ButtonSecondaryActiveTextColor);
    transition: var(--DefaultTransition);
}

.btnLink,
button:disabled:hover.btnLink,
button:disabled:active.btnLink {
    background-color: var(--ButtonLinkBackgroundColor);
    border: 1px solid transparent;
    transition: var(--DefaultTransition);
}

.btnLink span,
.btnLink > .icon-container > .svg-minor-container > svg path,
.btnLink > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonLinkTextColor);
    fill: var(--ButtonLinkTextColor);
    transition: var(--DefaultTransition);
}

.btnLink:hover {
    background-color: var(--ButtonLinkHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnLink:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnLink:hover span,
.btnLink > .icon-container > .svg-minor-container > svg path,
.btnLink > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonLinkHoverTextColor);
    fill: var(--ButtonLinkHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnLink:active {
    background-color: var(--ButtonLinkActiveBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnLink:active span,
.btnLink > .icon-container > .svg-minor-container > svg path,
.btnLink > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonLinkActiveTextColor);
    fill: var(--ButtonLinkActiveTextColor);
    transition: var(--DefaultTransition);
}

.btnGhost,
button:disabled:hover.btnGhost,
button:disabled:active.btnGhost {
    background-color: var(--ButtonGhostBackgroundColor);
    border: var(--ButtonGhostBorder);
    transition: var(--DefaultTransition);
}

.btnGhost span,
.btnGhost > .icon-container > .svg-minor-container > svg path,
.btnGhost > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonGhostTextColor);
    fill: var(--ButtonGhostTextColor);
    transition: var(--DefaultTransition);
}

.btnGhost:hover {
    background-color: var(--ButtonGhostHoverBackgroundColor);
    color: var(--ButtonGhostHoverTextColor);
    border: var(--ButtonGhostHoverBorder);
    transition: var(--DefaultTransition);
}

.btnGhost:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnGhost:hover span,
.btnGhost > .icon-container > .svg-minor-container > svg path,
.btnGhost > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonGhostHoverTextColor);
    fill: var(--ButtonGhostHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnGhost:active {
    background-color: var(--ButtonGhostActiveBackgroundColor);
    border: var(--ButtonGhostActiveBorder);
    transition: var(--DefaultTransition);
}

.btnGhost:active span,
.btnGhost > .icon-container > .svg-minor-container > svg path,
.btnGhost > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonGhostActiveTextColor);
    fill: var(--ButtonGhostActiveTextColor);
    transition: var(--DefaultTransition);
}

.btnWarn,
button:disabled:hover.btnWarn,
button:disabled:active.btnWarn {
    background-color: var(--ButtonWarnBackgroundColor);
    border: 1px solid transparent;
    transition: var(--DefaultTransition);
}

.btnWarn span,
.btnWarn > .icon-container > .svg-minor-container > svg path,
.btnWarn > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonWarnTextColor);
    transition: var(--DefaultTransition);
}

.btnWarn:hover {
    background-color: var(--ButtonWarnHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnWarn:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnWarn:hover span,
.btnWarn > .icon-container > .svg-minor-container > svg path,
.btnWarn > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonWarnHoverTextColor);
    fill: var(--ButtonWarnHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnWarn:active {
    background-color: var(--ButtonWarnActiveBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnWarn:active span,
.btnWarn > .icon-container > .svg-minor-container > svg path,
.btnWarn > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonWarnActiveTextColor);
    fill: var(--ButtonWarnActiveTextColor);
    transition: var(--DefaultTransition);
}

.btnDanger,
button:disabled:hover.btnDanger,
button:disabled:active.btnDanger {
    background-color: var(--ButtonDangerBackgroundColor);
    border: none;
    transition: var(--DefaultTransition);
}

.btnDanger span,
.btnDanger > .icon-container > .svg-minor-container > svg path,
.btnDanger > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonDangerTextColor);
    fill: var(--ButtonDangerTextColor);
    transition: var(--DefaultTransition);
}

.btnDanger:hover {
    background-color: var(--ButtonDangerHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnDanger:focus {
    outline: none;
    transition: var(--DefaultTransition);
}

.btnDanger:hover span,
.btnDanger > .icon-container > .svg-minor-container > svg path,
.btnDanger > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonDangerHoverTextColor);
    fill: var(--ButtonDangerHoverTextColor);
    transition: var(--DefaultTransition);
}

.btnDanger:active {
    background-color: var(--ButtonDangerActiveBackgroundColor);
    transition: var(--DefaultTransition);
}

.btnDanger:active span,
.btnDanger > .icon-container > .svg-minor-container > svg path,
.btnDanger > .icon-container > .svg-major-container > svg path {
    color: var(--ButtonDangerActiveTextColor);
    fill: var(--ButtonDangerActiveTextColor);
    transition: var(--DefaultTransition);
}

button.svg-minor-container.loading-spinner,
button.svg-major-container.loading-spinner {
    transform-origin: center center;
    animation: 0.86s cubic-bezier(0.4, 0.15, 0.6, 0.85) 0 infinite normal;
    display: inline-block;
    vertical-align: middle;
}

button > .loading-spinner-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

button > .loading-spinner-wrapper ~ span {
    opacity: 1;
    transition: var(--DefaultTransition);
}

button > .loading-spinner-wrapper.is-loading ~ span {
    opacity: 0;
    transition: var(--DefaultTransition);
}

.icon-before {
    margin-right: 4px;
    order: 0;
}

.icon-after {
    margin-left: 4px;
    order: 3;
}

.is-loading > .loading-spinner-container {
    opacity: 1;
    transition: var(--DefaultTransition);
}

.loading-spinner-wrapper {
    display: none;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.loading-spinner-wrapper.is-loading {
    display: flex;
    opacity: 1;
}

.loading-spinner-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    opacity: 0;
    transition: var(--DefaultTransition);
}

.loading-spinner {
    display: flex;
    justify-content: center;
    margin: 0 8px;
    animation: animation-spinner .75s infinite ease-in-out;
}

.loading-spinner svg path {
    fill: var(--slate-900);
}

.spinnerPrimary svg path {
    fill: var(--ButtonPrimaryBackgroundColor);
}

.spinnerSecondary svg path {
    fill: var(--ButtonSecondaryBackgroundColor);
}

.spinnerWarn svg path {
    fill: var(--ButtonWarnBackgroundColor);
}

.spinnerDanger svg path {
    fill: var(--ButtonDangerBackgroundColor);
}

.spinnerInverse svg path {
    fill: var(--white);
}

.tabs-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
}

.tabs-container {
    display: flex;
    flex-direction: row;
    border-bottom: 2px solid var(--slate-300);
}

.tab {
    position: relative;
    top: 2px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid transparent;
    background-color: transparent;
    color: var(--slate-900);
    margin: 0;
    transition: var(--DefaultTransition);
}

.tab > span {
    width: 100%;
    height: 100%;
    padding: 8px 16px;
    color: var(--slate-900);
    font-weight: 500;
    cursor: pointer;
}

.tab:hover,
.tab:focus {
    border-color: var(--sky-700);
    transition: var(--DefaultTransition);
    outline: none;
}

.tab:active {
    border-color: var(--ButtonPrimaryBackgroundColor);
    transition: var(--DefaultTransition);
}

.tab.active-tab {
    border-color: var(--ButtonPrimaryBackgroundColor);
    transition: var(--DefaultTransition)
}

.tab-content-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.tab-content-container {
    position: relative;
}

.badge {
    padding: 0 8px;
    background-color: var(--BadgeDefaultBackgroundColor);
    border-radius: 2em;
    color: var(--BadgeDefaultTextColor);
    text-align: center;
}

.badgePrimary {
    background-color: var(--BadgePrimaryBackgroundColor);
    color: var(--BadgePrimaryTextColor);
}

.badgeInverse {
    background-color: var(--BadgeInverseBackgroundColor);
    color: var(--BadgeInverseTextColor);
}

.badgeCritical {
    background-color: var(--BadgeCriticalBackgroundColor);
    color: var(--BadgeCriticalTextColor);
}

.badgeAdded {
    background-color: var(--BadgeAddedBackgroundColor);
    color: var(--BadgeAddedTextColor);
}

.badgeRemoved {
    background-color: var(--BadgeRemovedBackgroundColor);
    color: var(--BadgeRemovedTextColor);
}

.banner {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 4px;
    padding: 20px;
    background-color: var(--BannerDefaultBackgroundColor);
    border-radius: var(--BorderRadius);
}

.banner > span {
    color: var(--BannerDefaultTextColor);
    fill: var(--BannerDefaultTextColor);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.banner > .svg-major-container {
    margin-right: 8px;
}

.banner .svg-major-container svg path,
.banner .svg-minor-container svg path {
    fill: var(--BannerDefaultTextColor);
}

.bannerWarn {
    background-color: var(--BannerWarnBackgroundColor);
}

.bannerWarn > span
.bannerWarn .svg-major-container svg path,
.bannerWarn .svg-minor-container svg path {
    color: var(--BannerWarnTextColor);
    fill: var(--BannerWarnTextColor);
}

.bannerError {
    background-color: var(--BannerErrorBackgroundColor);
}

.bannerError > span,
.bannerError .svg-major-container svg path,
.bannerError .svg-minor-container svg path {
    color: var(--BannerErrorTextColor);
    fill: var(--BannerErrorTextColor);
}

.bannerInfo {
    background-color: var(--BannerInfoBackgroundColor);
}

.bannerInfo > span,
.bannerInfo .svg-major-container svg path,
.bannerInfo .svg-minor-container svg path {
    color: var(--BannerInfoTextColor);
    fill: var(--BannerInfoTextColor);
}

.banner-close-container {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    width: 24px;
    top: -10px;
    right: -10px;
    background-color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    outline: none;
    transition: var(--DefaultTransition);
}

.banner-close-container .svg-major-container svg path,
.banner-close-container .svg-minor-container svg path {
    fill: var(--BannerCloseButtonBackgroundColor);
    transition: var(--DefaultTransition);
}

.banner-close-container:hover .svg-major-container svg path,
.banner-close-container:hover .svg-minor-container svg path {
    fill: var(--BannerCloseButtonHoverBackgroundColor);
    transition: var(--DefaultTransition);
}

.checkbox-wrapper fieldset,
.radiobutton-wrapper fieldset,
.toggleswitch-wrapper fieldset {
    border: none;
}

.checkbox-wrapper fieldset legend,
.radiobutton-wrapper fieldset legend,
.toggleswitch-wrapper fieldset legend {
    padding-bottom: 8px;
}

.checkbox-container,
.radiobutton-container,
.toggleswitch-container {
    display: inline-flex;
    position: relative;
    margin: 4px 20px 4px 0;
}

fieldset.stacked > .checkbox-container,
fieldset.stacked > .radiobutton-container,
fieldset.stacked > .toggleswitch-container {
    display: flex;
    flex-direction: column;
}

.checkbox-container label,
.radiobutton-container label {
    display: block;
    padding-left: 28px;
}

.checkbox-container input,
.radiobutton-container input,
.toggleswitch-container input {
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    opacity: 0;
    outline: none;
}

.checkbox-container input + label,
.radiobutton-container input + label,
.toggleswitch-container input + label {
    cursor: pointer;
}

.checkbox-container input:disabled + label,
.radiobutton-container input:disabled + label,
.toggleswitch-container input:disabled + label {
    cursor: default;
}


.checkbox-container input + label::before,
.radiobutton-container input + label::before {
    position: absolute;
    top: 2px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: var(--InputBackgroundColor);
    border: 1px solid var(--InputBorderColor);
    border-radius: var(--BorderRadius);
    content: "";
    transition: var(--DefaultTransition);
    outline: none;
}

.checkbox-container input:hover + label::before,
.radiobutton-container input:hover + label::before {
    border-color: var(--InputBorderHoverColor);
    transition: var(--DefaultTransition);
}

.checkbox-container input:focus + label::before,
.radiobutton-container input:focus + label::before {
    outline: none;
    transition: var(--DefaultTransition);
}

.radiobutton-container input + label::before {
    border-radius: 50%;
}

.checkbox-container input:checked + label::before,
.radiobutton-container input:checked + label::before {
    border-color: var(--ButtonPrimaryBackgroundColor);
    background-color: var(--ButtonPrimaryBackgroundColor);
    border-radius: var(--BorderRadius);
    transition: var(--DefaultTransition);
}

.radiobutton-container input:checked + label::before {
    border-radius: 50%;
}

.checkbox-container input + label::after,
.radiobutton-container input + label::after {
    position: absolute;
    top: 1px;
    left: -1px;
    height: 20px;
    width: 20px;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.293 14.707l-3-3a.999.999 0 111.414-1.414l2.236 2.236 6.298-7.18a.999.999 0 111.518 1.3l-7 8a1 1 0 01-.72.35 1.017 1.017 0 01-.746-.292z' fill='%23FFFFFF'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    opacity: 0;
    content: "";
    transition: var(--DefaultTransition);
}

.radiobutton-container input + label::after {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse fill='%23FFFFFF' cx='10' cy='10' id='svg_2' rx='4' ry='4'/%3E%3C/svg%3E");
}

.checkbox-container input:indeterminate + label::before {
    background-color: var(--ButtonPrimaryBackgroundColor);
    border-color: var(--ButtonPrimaryBackgroundColor);
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 9H5a1 1 0 100 2h10a1 1 0 100-2z' fill='%23FFFFFF'/%3E%3C/svg%3E");
}

.checkbox-container input:checked + label::after,
.radiobutton-container input:checked + label::after {
    opacity: 1;
}

.checkbox-container input:focus + label::before,
.checkbox-container input:checked:focus + label::before,
.checkbox-container input:active + label::before,
.checkbox-container input:checked:active + label::before,
.radiobutton-container input:focus + label::before,
.radiobutton-container input:checked:focus + label::before,
.radiobutton-container input:active + label::before,
.radiobutton-container input:checked:active + label::before {
    border-color: var(--ButtonPrimaryBackgroundColor);
    outline: none;
    transition: var(--DefaultTransition);
}

.checkbox-container input:disabled + label::before,
.checkbox-container input:disabled:checked + label::before,
.radiobutton-container input:disabled + label::before,
.radiobutton-container input:disabled:checked + label::before {
    background-color: var(--InputDisabledBackgroundColor);
    border-color: var(--InputDisabledBorderColor);
}

.checkbox-container input:disabled + label::after {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.293 14.707l-3-3a.999.999 0 111.414-1.414l2.236 2.236 6.298-7.18a.999.999 0 111.518 1.3l-7 8a1 1 0 01-.72.35 1.017 1.017 0 01-.746-.292z' fill='%23475569'/%3E%3C/svg%3E");
}

.checkbox-container input:disabled + label::before {
    cursor: default;
}

.radiobutton-container input:disabled + label::after {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse fill='%23475569' cx='10' cy='10' id='svg_2' rx='4' ry='4'/%3E%3C/svg%3E");
}

.checkbox-container input:disabled:indeterminate + label::before {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 9H5a1 1 0 100 2h10a1 1 0 100-2z' fill='%23475569'/%3E%3C/svg%3E");
}


.toggleswitch-container label {
    position: relative;
    display: flex;
    align-items: center;
    height: 35px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--DefaultTransition);
}

.toggleswitch-container label:after {
    position: absolute;
    left: 4px;
    content: "";
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: var(--DefaultTransition);
    background-color: var(--white);
}

.toggleswitch-container input:disabled + label:after {
    background-color: var(--grey-500);
}

.toggleswitch-container input:checked:disabled + label:before {
    background-color: var(--grey-300);
}

.toggleswitch-container label::before {
    content: "";
    width: 34px;
    height: 18px;
    margin-right: 8px;
    background-color: var(--grey-300);
    border-radius: 50px;
    transition: var(--DefaultTransition);
}

.toggleswitch-container input + label:hover::before {
    box-shadow: inset 0 0 0 1px var(--grey-400);
    transition: var(--DefaultTransition);
}

.toggleswitch-container input + label:active::before {
    box-shadow: inset 0 0 0 1px var(--ButtonPrimaryBackgroundColor);
    transition: var(--DefaultTransition);
}

.toggleswitch-container input:focus + label::before {
    box-shadow: inset 0 0 0 1px var(--ButtonPrimaryBackgroundColor);
    outline: none;
    transition: var(--DefaultTransition);
}

.toggleswitch-container input:disabled + label:hover::before {
    box-shadow: none;
}

.toggleswitch-container label.hidden::after {
    content: "";
}


.toggleswitch-container input:checked + label::before {
    background-color: var(--ButtonPrimaryBackgroundColor);
}

.toggleswitch-container input:checked + label::after {
    left: 18px;
}

.avatar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    width: 48px;
    margin: 4px;
    background-color: var(--slate-800);
    border: 1px solid var(--slate-800);
    border-radius: 50%;
}

.avatar span {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: var(--blue-100);
}

.avatar > .status-indicator {
    position: absolute;
}

.status-indicator {
    position: relative;
    bottom: -2px;
    right: 0;
    height: 16px;
    width: 16px;
    background-color: var(--slate-300);
    border: 2px solid var(--white);
    border-radius: 50%;
}

.status-indicator.offline {
    background-color: var(--slate-300);
}

.status-indicator.available {
    background-color: var(--emerald-800);
}

.status-indicator.busy {
    background-color: var(--orange-500);
}

.status-indicator.unavailable {
    background-color: var(--red-700);
}

.avatargroup-wrapper {
    display: inline-flex;
}

.avatargroup-wrapper > .avatar {
    border: 2px solid var(--white);
}

.avatargroup-wrapper > .avatar:last-child {
    background-color: var(--slate-300);
}

.avatargroup-wrapper > .avatar:last-child span {
    color: var(--slate-600);
}

.avatargroup-wrapper > .avatar:not(:first-child) {
    margin-left: -16px;
}

.avatargroup-wrapper.stacked {
    display: flex;
    flex-direction: column;
}

.avatargroup-wrapper.stacked > .avatar:not(:first-child) {
    margin-left: 4px;
    margin-top: -16px;
}

.breadcrumb-wrapper {
    display: block;
}

.breadcrumb-list {
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;
}

.breadcrumb-item {
    display: flex;
    flex-direction: row;
}

.breadcrumb-link {
    display: inline-flex;
    flex-shrink: 1;
    white-space: nowrap;
    text-decoration: none;
}

.breadcrumb-link:hover {
    text-decoration: underline;
    text-decoration-color: var(--slate-600);
}

.breadcrumb-list > li::after {
    flex-shrink: 0;
    padding: 0 8px;
    width: 8px;
    color: var(--slate-600);
    content: "/";
}

.breadcrumb-list > li:last-of-type::after {
    content: "";
}

.breadcrumb-collapse > button {
    background-color: transparent;
    border: none;
    cursor: pointer;
}

.input-control {
    display: flex;
    flex-direction: column;
}

.input-control-inline {
    flex-direction: row;
}

.input-field {
    height: 32px;
    padding: 8px;
    background-color: var(--InputBackgroundColor);
    border: 1px solid var(--InputBorderColor);
    border-radius: var(--InputBorderRadius);
    color: var(--InputTextColor);
    caret-color: var(--ButtonPrimaryBackgroundColor);
    outline: none;
    transition: var(--DefaultTransition);
}

.input-field:hover {
    border-color: var(--InputBorderHoverColor);
}

.input-field:focus {
    border-color: var(--InputBorderFocusColor);
    outline: none;
}

.input-field:active {
    border-color: var(--InputBorderActiveColor);
}

.input-field:disabled {
    background-color: var(--InputDisabledBackgroundColor);
    border: 1px solid var(--InputDisabledBorderColor);
    color: var(--InputDisabledTextColor);
}

.input-field-icon-container {
    position: relative;
}

.input-field-icon-container.icon-before > .icon-container {
    position: absolute;
    top: 8px;
    left: 8px;
}

.input-field-icon-container.icon-after > .icon-container {
    position: absolute;
    top: 8px;
    right: 8px;
}

.input-field-icon-container > .icon-container > .svg-minor-container > svg path {
    fill: var(--InputPlaceholderColor);
    transition: var(--DefaultTransition);
}

.input-field:focus + .icon-container svg path {
    fill: var(--InputBorderActiveColor);
}

.input-field-icon-container.icon-before,
.input-field-icon-container.icon-after {
    margin: 0;
}

.input-field-icon-container.icon-before > .input-field {
    padding-left: 28px;
}

.input-field-icon-container.icon-after > .input-field {
    padding-right: 28px;
}

select.input-field {
    padding: 0 28px 0 8px;
    appearance: none;
    background-color: var(--InputBackgroundColor);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10.828' height='6.416' viewBox='0 0 10.828 6.416'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:none;stroke:%23475569;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;%7D%3C/style%3E%3C/defs%3E%3Cpath class='a' d='M-17652.326-23668.564l4,4,4-4' transform='translate(17653.74 23669.979)'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 8px) 51%;
}

select.input-field:disabled {
    background-color: var(--InputDisabledBackgroundColor);
    border: 1px solid var(--InputDisabledBorderColor);
    opacity: 1;
}

.calendar {
    min-width: 240px;
    max-width: 360px;
    width: 260px;
    padding: var(--CalendarPadding);
    background-color: var(--CalendarBackground);
    border: 1px solid var(--CalendarBorderColor);
    border-radius: var(--CalendarBorderRadius);
}

.calendar-range-wrapper {
    width: 100%;
    margin: 0 0 12px 0;
    gap: 8px;
}

.calendar-table {
    width: 100%;
}

.calendar-table th,
.calendar-table td {
    width: calc(100% / 7);
    text-align: center;
}

.calendar-table th {
    color: var(--CalendarDaysOfWeekTextColor);
}

.calendar-table td {
    height: 32px;
    color: var(--CalendarDayCellTextColor);
}

.calendar-table [data-date="today"] {
    background-color: var(--CalendarTodayBackground);
    border-radius: var(--BorderRadius);
}

.show-today-wrapper {
    margin: 8px 0 0 0;
}

.flex {
    display: flex;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.flex-grow {
    flex-grow: 1;
}

.full-width {
    max-width: 100%;
    min-width: 100%;
    width: 100%;
}

.ic24-simple-separator {
    margin: calc(var(--ic24-flex-gap) / 2) 0;
}

@keyframes animation-spinner {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}
