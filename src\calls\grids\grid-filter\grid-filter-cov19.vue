<template>
  <div>
    COV19
    <select
      v-model="selectedValue"
      v-on:change="onChange"
      :class="
        selectedValue === 'ALL' ? '' : 'grid-route-toolbar--filters-warn-value'
      "
    >
      <option value="ALL">All</option>
      <option value="NOT_COV19">Exclude PCV</option>
      <option value="COV19">ONLY PCV</option>
    </select>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { GRID_FILTER_NAME } from "@/calls/grids/grid-filter/grid-filter-models";

@Component({
  name: "grid-filter-cov19"
})
export default class GridFilterCov19 extends Vue {
  public selectedValue: GRID_FILTER_NAME | "ALL" = "ALL";

  public onChange() {
    this.$emit("change", this.selectedValue);
  }
}
</script>
