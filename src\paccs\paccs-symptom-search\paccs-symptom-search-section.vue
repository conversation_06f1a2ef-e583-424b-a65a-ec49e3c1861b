<template>
  <div
    class="paccs-symptom-search-section--wrapper"
    :class="isDisabled ? 'adapter--element-disabled' : ''"
  >
    <div class="paccs-symptom-search-section--header">
      <span class="paccs-symptom-search-section--search-label"
        >Symptom Search:</span
      >
      <PaccsSymptomSearch
        class="cleo-force-inline-block"
        v-on:onResults="onResults"
        v-on:reset="reset"
      ></PaccsSymptomSearch>

      <div class="paccs-symptom-search-section--header-buttons">
        <div
          v-for="paccsJump in addCaseRecordResponse.jumps"
          :key="paccsJump.orderNo"
          class="cleo-force-inline-block"
        >
          <PaccsButtonPwJump
            :title="paccsJump.label"
            :id="paccsJump.pathwayJump"
            :button-type="paccsJump.buttonType"
            class="adapter-button"
          ></PaccsButtonPwJump>
          <div class="adapter-button--separator"></div>
        </div>
      </div>
    </div>

    <div class="paccs-symptom-search-section--results">
      <div
        class="paccs-symptom-search-section--result-link"
        v-for="paccsSymptomModel in paccsSymptomModels"
        :key="paccsSymptomModel.templateId"
      >
        <a
          href="#"
          v-on:click.prevent="onSelected(paccsSymptomModel.templateId)"
          v-text="labelFunction(paccsSymptomModel)"
        ></a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  onBeforeUnmount,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import { PaccsService } from "@/paccs/paccs-service";
import { loggerInstance } from "@/common/Logger";
import PaccsSymptomSearch from "@/paccs/paccs-symptom-search/paccs-symptom-search.vue";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import { CommonService } from "@/common/common-service";

import PaccsButtonPwJump from "@/paccs/buttons/paccs-button-pw-jump.vue";
import { IAddCaseRecordResponse } from "@/paccs/paccs-data";
import { appStore } from "@/store/store";
import { PACCS_STORE_CONST } from "@/paccs/paccs-store";
import { PathwaysJumpToButtonType } from "@/paccs/pathways/pathways-models";

const paccsService: PaccsService = new PaccsService();

export default defineComponent({
  name: "paccs-symptom-search-section",
  components: {
    PaccsSymptomSearch,
    PaccsButtonPwJump
  },
  props: {
    addCaseRecordResponse: {
      required: true,
      type: Object as PropType<IAddCaseRecordResponse>
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  setup(
    props: {
      addCaseRecordResponse: IAddCaseRecordResponse;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const paccsSymptomModels = ref<Array<IPaccsSymptomModel>>([]);

    const store = appStore;
    const unsubscribeStore = store.subscribeAction((action, state) => {
      if (
        action.type ===
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_RESET
      ) {
        reset();
      }
    });

    onBeforeUnmount(() => {
      unsubscribeStore();
    });

    function reset() {
      paccsSymptomModels.value = [];
    }

    const onResults = (paccsSymptomModelsResults: IPaccsSymptomModel[]) => {
      paccsSymptomModels.value = paccsSymptomModelsResults;
    };

    const labelFunction = (paccsSymptomModel: IPaccsSymptomModel) => {
      return (
        paccsSymptomModel.templateId + " - " + paccsSymptomModel.templateName
      );
    };

    const onSelected = (templateId: string) => {
      const commonService: CommonService = new CommonService();
      const pred = (paccsSymptomModel: IPaccsSymptomModel) => {
        return paccsSymptomModel.templateId === templateId;
      };
      const paccsSymptomModel: IPaccsSymptomModel | null = commonService.findFirst(
        pred,
        paccsSymptomModels.value
      );
      if (!paccsSymptomModel) {
        return;
      }
      context.emit("onSelected", { ...paccsSymptomModel });
    };

    return {
      onResults,
      reset,
      paccsSymptomModels,
      labelFunction,
      onSelected
    };
  }
});
</script>

<style>
.paccs-symptom-search-section--wrapper {
  /*height: 2em;*/
  /*vertical-align: middle;*/
  /*padding: 0.5em;*/
}

.paccs-symptom-search-section--header {
  background-color: #2980b9;
  padding: 0.5em;
  height: 3em;
}

.paccs-symptom-search-section--header-buttons {
  float: right;
}

.paccs-symptom-search-section--search-label {
  color: black;
  margin-right: 5px;
  font-weight: 600;
}

.paccs-symptom-search-section--results {
  height: 7em;
  overflow: auto;
  padding: 1em;
}

.paccs-symptom-search-section--result-link {
  height: 1.5em;
}

.paccs-symptom-search-section--result-link:hover {
  background-color: #b7daea;
}

.paccs-symptom-search-section--results a {
  text-decoration: none;
  vertical-align: middle;
}

/*.paccs-symptom-search-section--results a:hover {*/
/*  background-color: #b7daea;*/
/*}*/
</style>
