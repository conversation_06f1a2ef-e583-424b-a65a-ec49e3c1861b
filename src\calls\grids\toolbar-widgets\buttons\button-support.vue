<template>
  <button
    class="toolbar--legacy-button"
    v-if="!userAppPermsShort[permissionNames.NO_SUPPORT]"
    v-on:click.prevent="$options.methods.launchSupport()"
  >
    Support
  </button>
</template>

<script lang="ts">
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import { CLEO_CALL_ACTIONS } from "@/common/cleo-common-models";

export default {
  props: ["userAppPermsShort", "permissionNames", "callSummaryController"],
  methods: {
    launchSupport: () => {
      new CallSummaryController().processAction(
        CLEO_CALL_ACTIONS.SUPPORT.id,
        []
      );
    },
  },
};
</script>
