{"name": "2test12", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-uat": "vue-cli-service build --mode uat", "test:unit": "vue-cli-service test:unit --watchAll", "test:int": "vue-cli-service test:int -c jest.config.int.js", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint"}, "dependencies": {"@ag-grid-community/all-modules": "24.1.0", "@googlemaps/js-api-loader": "1.12.10", "@googlemaps/markerclusterer": "1.0.20", "@microsoft/signalr": "5.0.2", "@vue/composition-api": "1.4.0", "@vue/reactivity": "3.0.5", "ag-grid-community": "24.1.0", "ag-grid-vue": "24.1.0", "autoprefixer": "9.8.6", "axios": "0.21.1", "core-js": "3.8.2", "date-fns": "2.16.1", "node-sass": "5.0.0", "portal-vue": "2.1.7", "postcss": "7.0.35", "sass-loader": "10.1.1", "splitpanes": "2.3.6", "vue": "2.6.14", "vue-class-component": "7.2.6", "vue-property-decorator": "9.1.2", "vue-router": "3.4.9", "vue-toasted": "1.1.28", "vuex": "3.6.2"}, "devDependencies": {"@types/google.maps": "3.47.0", "@types/jest": "26.0.20", "@types/splitpanes": "2.2.0", "@typescript-eslint/eslint-plugin": "4.13.0", "@typescript-eslint/parser": "4.13.0", "@vue/cli-plugin-babel": "4.5.10", "@vue/cli-plugin-e2e-cypress": "4.5.10", "@vue/cli-plugin-eslint": "4.5.10", "@vue/cli-plugin-router": "4.5.10", "@vue/cli-plugin-typescript": "4.5.10", "@vue/cli-plugin-unit-jest": "4.5.17", "@vue/cli-plugin-vuex": "4.5.10", "@vue/cli-service": "4.5.10", "@vue/eslint-config-prettier": "6.0.0", "@vue/eslint-config-typescript": "7.0.0", "@vue/test-utils": "1.1.2", "eslint": "7.17.0", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-vue": "7.4.1", "prettier": "1.19.1", "typescript": "4.1.3", "vue-template-compiler": "2.6.14"}}