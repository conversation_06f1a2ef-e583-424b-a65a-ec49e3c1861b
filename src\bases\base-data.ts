import { CLEO_CONFIG } from "@/common/config/config-";
import https from "@/common/https";
import { ILegacyDojoResponse } from "@/common/cleo-legacy-models";
import { IBaseLookupFullLegacy } from "@/bases/base-models";

// const mockData = require("bases-mock.json");
// import("@/bases/base-mock");

import { mockData } from "@/bases/base-mock";

export class BaseData {
  private adapterEndPoint = CLEO_CONFIG.ADAPTER_URL
    ? CLEO_CONFIG.ADAPTER_URL
    : "";

  public getCleoBases(): Promise<Record<string, IBaseLookupFullLegacy>> {
    if (process.env.NODE_ENV === "development") {
      return Promise.resolve(mockData);
    }

    return https.get(
      CLEO_CONFIG.CLEO.XCLEO_PATH +
        "/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDJSON&sid=base",
      {
        responseType: "json"
      }
    );
  }
}
