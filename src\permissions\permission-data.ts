import {
  CLeoPermissionServerResponse,
  ICleoPermission
} from "@/permissions/permission-models";
import { CLEO_CONFIG } from "@/common/config/config-";
// import https from "@/common/https";

//  TODO
import { userPermissionServerResponseMockData } from "@/permissions/permission-mock-data";

if (process.env.NODE_ENV === "development") {
  import("@/permissions/permission-mock-data");
}

export class PermissionData {
  public getUserPermissions(
    userRole: string,
    callIdentifier?: string | number
  ): Promise<Record<string, ICleoPermission>> {
    if (process.env.NODE_ENV === "development") {
      return new Promise(function(resolve) {
        setTimeout(function() {
          resolve(userPermissionServerResponseMockData.Permissions[userRole]);
        }, 1000); // Wait 3s then resolve.
      });
    } else {
      /*
      return https
        .get(
          CLEO_CONFIG.CLEO.XCLEO_PATH +
            "/xpbeaninterface.xsp?processformat=json&action=GETUSERCONFIGJSON&jobrole=" +
            encodeURI(userRole) +
            "&sid=" +
            (callIdentifier ? encodeURI(callIdentifier.toString()) : ""),
          {
            responseType: "json"
          }
        )
        .then(resp => {
          const serverResp = (resp as any) as CLeoPermissionServerResponse;
          return serverResp.Permissions[userRole];
        });
      */

      //////////////

      return new Promise(function(resolve) {
        // https://ash-tst-domino.sehnp.nhs.uk/stage/xcleo.nsf/xpbeaninterface.xsp?processformat=json&sid=905151201&spinerole=&action=GETDOCJSON&dojo.preventCache=1725608790737

        const s_path =
          window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
          "/" +
          window.MyGlobalSession.Global_DB_Paths.PATH_XCLEO +
          "/xpbeaninterface.xsp?processformat=json&action=GETUSERCONFIGJSON&jobrole=" +
          encodeURI(userRole) +
          "&sid=" +
          (callIdentifier ? encodeURI(callIdentifier.toString()) : "");

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        dojo.xhrGet({
          url: s_path,
          handleAs: "json",
          preventCache: true,
          content: {},
          timeout: 30000,

          load: function(resp: any) {
            const serverResp = (resp as any) as CLeoPermissionServerResponse;
            resolve(serverResp.Permissions[userRole]);
          },

          error: function(response: any) {
            console.error("Error: ", response);
            resolve({});
          }
        });
      });

      //////////////
    }
  }
}
