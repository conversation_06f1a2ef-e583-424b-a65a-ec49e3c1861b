<template>
  <!--  <div class="app-layout-default" oncontextmenu="javascript:return false;">-->
  <div class="app-layout-default" :class="getAppClass">
    <div class="app-layout--header">
      <slot name="header">
        <h3>App Header</h3>
        {{ $route.name }}
      </slot>
    </div>

    <div class="app-layout--body">
      <!--      {{ $route.name }}-->
      <slot name="body">
        <router-view :key="$route.fullPath"></router-view>
      </slot>
    </div>

    <div class="app-layout--footer">
      <slot name="footer">
        <h3>Footer</h3>
      </slot>
    </div>

    <Sesui v-if="getShouldLoadSesui" />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import { USER_STORE_CONST } from "@/user/user-store";
import { IUser } from "@/user/user-models";
import { LAUNCH_ROUTES_PATHS } from "@/router/router";
import Sesui from "@/sesui/sesui.vue";
import { mapState } from "vuex";

@Component({
  name: "app-layout-default",
  components: { Sesui },
  computed: {
    ...mapState<IPermissionStoreState>(
      PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME,
      {
        permissionStoreState: (state: IPermissionStoreState) => state
      }
    )
  }
})
export default class AppLayoutDefault extends Vue {
  public readonly permissionStoreState!: IPermissionStoreState;

  public created() {
    const promGetUser = this.$store
      .dispatch(
        USER_STORE_CONST.USER__CONST_MODULE_NAME +
          "/" +
          USER_STORE_CONST.USER__ACTIONS_GET_USER
      )
      .then((user: IUser) => {
        this.$store.dispatch(
          PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME +
            "/" +
            PERMISSION_STORE_CONST.PERMISSION__ACTIONS_GET_APP_PERMS,
          user.userRole
        );
      });
  }

  /**
   * The app is initially embedded in CLEO and as functionality moves into this
   * app will start to take over more ui.  Using portals to "inject" our
   * components into existing CLEO UI, so when opening a call, we need all of
   * the app "hidden".
   */
  public get getAppClass(): string {
    const cssMapHideApp = [
      LAUNCH_ROUTES_PATHS.WELCOME,
      LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE
    ];
    if (cssMapHideApp.indexOf(this.$route.name ?? "") > -1) {
      return process.env.NODE_ENV === "development"
        ? "app-layout-default--legacy-call-dev"
        : "app-layout-default--legacy-call";
    }

    // if (this.$route.name === LAUNCH_ROUTES_PATHS.WELCOME) {
    //   return process.env.NODE_ENV === "development"
    //     ? "app-layout-default--legacy-call-dev"
    //     : "app-layout-default--legacy-call";
    // }
    return "";
  }

  public get getShouldLoadSesui(): boolean {
    console.log("shouldLoadSesui() areWeInViewGrid: Start...");
    if (!window.CallControllerClient.areWeInViewGrid()) {
      console.log("shouldLoadSesui() areWeInViewGrid: Start...false");
      return false;
    }
    console.log("shouldLoadSesui() areWeInViewGrid: Start...true");

    if (!this.permissionStoreState.userAppPermsShort) {
      console.log("shouldLoadSesui() userAppPermsShort: false");
      return false;
    }

    const hasPerm = !!this.permissionStoreState.userAppPermsShort
      .SESUI_TELEPHPONE;
    console.log("shouldLoadSesui() hasPerm: ", hasPerm);

    return hasPerm;
  }

  // public shouldLoadSesui(): boolean {
  //   console.log("shouldLoadSesui() areWeInViewGrid: Start...");
  //   if (!window.CallControllerClient.areWeInViewGrid()) {
  //     console.log("shouldLoadSesui() areWeInViewGrid: Start...false");
  //     return false;
  //   }
  //   console.log("shouldLoadSesui() areWeInViewGrid: Start...true");
  //
  //   if (!this.permissionStoreState.userAppPermsShort) {
  //     console.log("shouldLoadSesui() userAppPermsShort: false");
  //     return false;
  //   }
  //
  //   const hasPerm = !!this.permissionStoreState.userAppPermsShort
  //     .SESUI_TELEPHPONE;
  //   console.log("shouldLoadSesui() hasPerm: ", hasPerm);
  //
  //   return hasPerm;
  // }
}
</script>
