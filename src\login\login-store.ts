import { ITokenResponse } from "@/login/login-models";
import { Module } from "vuex";
import { IRootState } from "@/store/store";
import { LoginService } from "@/login/login-service";

export interface ILoginStoreState {
  tokenResponse: ITokenResponse;
}

export enum LOGIN_STORE_STORE_CONST {
  LOGIN_STORE__CONST_MODULE_NAME = "LOGIN_STORE__CONST_MODULE_NAME",

  LOGIN_STORE__MUTATION_SET_TOKEN = "LOGIN_STORE__MUTATION_SET_TOKEN"
}

const mutations = {
  [LOGIN_STORE_STORE_CONST.LOGIN_STORE__MUTATION_SET_TOKEN](
    state: ILoginStoreState,
    tokenResponse: ITokenResponse
  ): void {
    state.tokenResponse = tokenResponse;
  }
};

const getters = {};

const actions = {};

export const loginStore: Module<ILoginStoreState, IRootState> = {
  namespaced: true,
  state: new LoginService().factoryLoginStoreState(),
  mutations,
  getters,
  actions
};
