import { CompleteStepName } from "@/calls/details/complete/complete-models";

export type BrisDocCompleteStepNameCas = Extract<
  CompleteStepName,
  | "END_ASSESSMENT_CONFIRMATION"
  | "CONTACT_MADE"
  | "READ_CODES"
  | "FAILED_CONTACT_RISK_ASSESSMENT"
  | "FAILED_CONTACT_REASON"
  | "INSUFFICIENT_CONTACT_ATTEMPTS"
  | "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
  | "BRISDOC_AUDIT_QUESTIONS"
  | "OUTCOMES"
  | "FURTHER_ACTION"
  | "UNKNOWN"
>;
