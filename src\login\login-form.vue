<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" for="username">User name</label>
        <input id="username" name="username" type="text" v-model="userName" />
      </div>
    </div>
    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" for="password">Password</label>
        <input id="password" name="password" type="text" v-model="password" />
      </div>
    </div>
    <div class="row">
      <div class="col s12 m12 l12">
        <button v-on:click.prevent="submit">Submit</button>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { CLEO_CONFIG } from "@/common/config/config-";
import { loggerInstance } from "@/common/Logger";

@Component({
  name: "login-form"
})
export default class LoginForm extends Vue {
  public userName = "";
  public password = "USERCREDS";

  public submit(): void {
    this.$emit("submit", {
      userName: this.userName,
      password: this.password
    });
  }
}
</script>
