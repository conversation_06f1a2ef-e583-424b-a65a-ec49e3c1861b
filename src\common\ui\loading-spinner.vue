<template>
  <img
    class="loading-icon"
    alt="Loading"
    src="./../../assets/Bars-1s-30px.gif"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";

@Component({
  name: "loading-spinner"
})
export default class LoadingSpinner extends Vue {}
</script>

<style scoped>
.loading-icon {
  vertical-align: middle;
  height: 25px;
  width: 25px;
}
</style>
