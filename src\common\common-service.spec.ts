import { CommonService } from "@/common/common-service";
import { differenceInSeconds, parseISO } from "date-fns";

const commonService: CommonService = new CommonService();

describe("CallSummary", () => {
  it("fromNow", () => {
    expect(
      commonService.fromNow(
        parseISO("2020-11-13T16:25:00+00:00"),
        parseISO("2020-11-13T16:27:00+00:00")
      )
    ).toBe("2 minutes ago");

    expect(
      commonService.fromNow(
        parseISO("2020-11-13T12:25:00+00:00"),
        parseISO("2020-11-13T16:27:00+00:00")
      )
    ).toBe("4 hours ago");

    expect(
      commonService.fromNow(
        parseISO("2020-11-13T16:27:00+00:00"),
        parseISO("2020-11-13T16:25:00+00:00")
      )
    ).toBe("in 2 minutes");

    expect(
      commonService.fromNow(
        parseISO("2020-11-13T16:27:00+00:00"),
        parseISO("2020-11-13T16:26:50+00:00")
      )
    ).toBe("in 10 seconds");
  });

  it("findFirst", () => {
    const someStuff = [
      {
        name: "bob",
        age: 33
      },
      {
        name: "Sue",
        age: 34
      },
      {
        name: "Jack",
        age: 34
      },
      {
        name: "Nick",
        age: 29
      }
    ];

    expect(
      commonService.findFirst((obj: any) => {
        return obj.age === 34;
      }, someStuff)?.name
    ).toBe("Sue");

    expect(
      commonService.findFirst((obj: any) => {
        return obj.age === 29;
      }, someStuff)?.name
    ).toBe("Nick");

    expect(
      commonService.findFirst((obj: any) => {
        return obj.age === 37;
      }, someStuff)
    ).toBe(null);
  });

  it("reachedThisTime", () => {
    expect(
      commonService.reachedThisTime(
        "2021-02-04T17:58:28+00:00",
        parseISO("2021-02-04T17:58:29+00:00")
      )
    ).toBe(false);

    expect(
      commonService.reachedThisTime(
        "2021-02-04T17:58:29+00:00",
        parseISO("2021-02-04T17:58:29+00:00")
      )
    ).toBe(true);

    expect(
      commonService.reachedThisTime(
        "2021-02-04T17:58:35+00:00",
        parseISO("2021-02-04T17:58:29+00:00")
      )
    ).toBe(true);

    expect(
      commonService.reachedThisTime(
        "2021-02-04T17:00:31+00:00",
        parseISO("2021-02-04T17:00:30+00:00")
      )
    ).toBe(true);
  });

  it("sortArray", () => {
    const someStuff = [
      {
        name: "bob",
        age: 33,
        address: {
          line1: "h"
        }
      },
      {
        name: "Sue",
        age: 34,
        address: {
          line1: "z"
        }
      },
      {
        name: "Jack",
        age: 37,
        address: {
          line1: "a"
        }
      },
      {
        name: "Nick",
        age: 23,
        address: {
          line1: "b"
        }
      },
      {
        name: "Alfie",
        age: 29,
        address: {
          line1: "h"
        }
      }
    ];

    expect(commonService.sortArray("name", someStuff)[0].name).toBe("Alfie");

    const pred = (obj: any) => {
      return obj.name;
    };
    expect(commonService.sortArray(pred, someStuff)[0].name).toBe("Alfie");

    const predAddress = (obj: any) => {
      return obj.address.line1;
    };
    expect(commonService.sortArray(predAddress, someStuff)[0].name).toBe("Jack");


    expect(commonService.sortArray("age", someStuff)[0].name).toBe("Nick");

    const predAge = (obj: any) => {
      return obj.age;
    }
    expect(commonService.sortArray(predAge, someStuff)[0].name).toBe("Nick");

    expect(commonService.sortArray(
      "name",
      someStuff,
      "DESC"
    )[0].name).toBe("Sue");


    expect(commonService.sortArray("age", someStuff, "DESC")[0].name).toBe("Jack");

  });
});
