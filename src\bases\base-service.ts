import { IBaseLookupFullLegacy, IBaseSummary } from "@/bases/base-models";

export class BaseService {
  public getValidBases(bases: IBaseLookupFullLegacy[]): IBaseSummary[] {
    return bases
      .filter(base => {
        const hasLong = base.GPSLong && base.GPSLong.length > 0;
        const hasLat = base.GPSLat && base.GPSLat.length > 0;
        return hasLong && hasLat;
      })
      .map(base => {
        return this.mapLegacyBase(base);
      });
  }

  public mapLegacyBase(base: IBaseLookupFullLegacy): IBaseSummary {
    return {
      Id: Number(base.AdapterId),
      Name: base.description,
      legacyKey: base.permalink,
      lat: parseFloat(base.GPSLat),
      long: parseFloat(base.GPSLong),
      ccg: base.CCG_Code,
      inIc24Area: base.InIc24Area === "1",
      address: base.keyAddress,
      town: base.keyTown,
      postCode: base.keyPostCode,
      inOut: base.InOut === "IN"
    };
  }
}
