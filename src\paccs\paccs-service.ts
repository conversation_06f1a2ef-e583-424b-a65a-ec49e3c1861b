import {
  IPaccsForm,
  IPaccsFormState,
  IPaccsFormStatePathways,
  IPathwaysDataset,
  IPathwaysDatasetLegacy,
  ITriageRecord,
  ITriageRecordCondition,
  ITriageRecordTemplate,
  PaccsAgeGroup,
  PATHWAYS_SKILL_SET__PACCS
} from "@/paccs/paccs-models";
import { IPaccsSymptomModel } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import { IConditionTab } from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs-models";
import {
  IPaccsStoreState,
  TriageRecordConditionUpdate
} from "@/paccs/paccs-store";
import { differenceInHours } from "date-fns";
import { IPaccsCondition } from "@/paccs/paccs-condition-tab-body/paccs-condition-models";
import {
  IPathwaysJumpToPayload,
  IPathwaysPayloadBase
} from "@/paccs/pathways/pathways-models";

export class PaccsService {
  public factoryPaccsStoreState(): IPaccsStoreState {
    return {
      showSection: "PACCS",
      addCaseRecordResponse: {
        caseId: "",
        jumps: []
      },
      triageRecords: [],
      pathwaysDataset: this.factoryPathwaysDataset(),
      pathwaysJumpToId: "",
      pathwaysPayload: this.factoryPathwaysJumpToPayload(),
      reportTriageClicked: this.factoryTriageRecord(
        "",
        this.factoryPathwaysDataset()
      ),
      pathways: {
        showMask: false,
        showMaskMessage: ""
      }
    };
  }

  public factoryPaccsForm(): IPaccsForm {
    return {
      // globalConfig: this.factoryPaccsGlobalConfig(),
      patientInfo: {
        ageGroup: "Adult",
        gender: "Male"
      },
      conditionTabsArray: []
    };
  }

  public factoryConditionTab(
    paccsSymptomModel: IPaccsSymptomModel
  ): IConditionTab {
    return {
      templateId: paccsSymptomModel.templateId,
      tabName: paccsSymptomModel.templateName,
      isLoading: false,
      isSelected: false,
      paccsSymptomModel: Object.assign({}, paccsSymptomModel),
      paccsConditionOrder: []
    };
  }

  public factoryTriageRecord(
    caseId: string,
    pathwaysDataset: IPathwaysDataset
  ): ITriageRecord {
    return {
      caseId: caseId,
      userId: pathwaysDataset.userId,
      timeIn: new Date().toISOString(), //   formatISO(new Date())
      pathwayId: "",
      actionId: 2,
      // hostSystem: "CLEO",
      includeInReport: true,
      interfaceId: 15,
      skillSet: pathwaysDataset.skillSet,
      siteId: "WHAT_VALUE_SHOULD_GO_HERE",
      symptomGroup: 0
    };
  }

  public factoryTriageRecordTemplate(
    caseId: string,
    pathwaysDataset: IPathwaysDataset,
    paccsSymptomModel: IPaccsSymptomModel
  ): ITriageRecordTemplate {
    return {
      ...this.factoryTriageRecord(caseId, pathwaysDataset),
      symptomGroup: paccsSymptomModel.symptomGroup,
      dispositionRationale: ""
    };
  }

  public factoryTriageRecordCondition(
    caseId: string,
    pathwaysDataset: IPathwaysDataset,
    paccsSymptomModel: IPaccsSymptomModel
  ): ITriageRecordCondition {
    return {
      ...this.factoryTriageRecord(caseId, pathwaysDataset),
      symptomGroup: paccsSymptomModel.symptomGroup,
      userComment: "",
      quId: "",
      answerNumber: 1,
      reportText: ""
    };
  }

  public factoryPathwaysDataset(): IPathwaysDataset {
    return {
      addressBuildingIdentifier: "",
      addressBuildingPrefix: "",
      addressCounty: "",
      addressDescription: "",
      addressEasting: 0,
      addressKey: "",
      addressNorthing: 0,
      addressStreetName1: "",
      addressStreetName2: "",
      addressTown: "",
      ageInHours: 0,
      callerTelephoneNumber: "",
      caseStartTime: "",
      clinicianCallStartTime: "",
      forename: "",
      gender: "Male",
      hostCaseId: "",
      hostDocId: "",
      nhsId: "",
      party: "First",
      patientId: "",
      postCode: "",
      presentingCondition: "",
      previousEncounters: 0,
      serviceId: "",
      skillSet: 0,
      surgeryId: "",
      surname: "",
      traceVerified: false,
      userId: "",
      dob: ""
    };
  }

  public factoryPathwaysPayloadBase(): IPathwaysPayloadBase {
    return {
      entryType: "",
      skillset: 14
    };
  }

  public factoryPathwaysJumpToPayload(): IPathwaysJumpToPayload {
    return {
      ...this.factoryPathwaysPayloadBase(),
      pwId: "",
      caseId: "",
      sg: null,
      entryType: "JUMPTO",
      quId: "",
      presentingCondition: ""
    };
  }

  public factoryPaccsFormState(): IPaccsFormState {
    return {
      tabRemove: {
        showModal: false,
        message: ""
      },
      restartTriage: {
        showModal: false,
        isLoading: false
      },
      pathways: this.factoryPaccsFormStatePathways()
    };
  }

  public factoryPaccsFormStatePathways(): IPaccsFormStatePathways {
    return {
      changingAnswer: false,
      changingAnswerGetConfirmation: false,
      caseId: "",
      reportHtml: "",
      pathwaysReturnData: null
    };
  }

  // public createPathwaysJumpToPayload(
  //   caseId: string,
  //   pathwaysJumpToId: string,
  //   symptomGroup: null | number,
  //   buttonType: PathwaysJumpToButtonType,
  //   paccsSymptomModelTemplateId: PaccsSymptomModelTemplateId,
  //   paccsConditionId: PaccsConditionId
  // ): IPathwaysJumpToPayload {
  //   const pathwaysJumpToPayload = this.factoryPathwaysJumpToPayload();
  //   pathwaysJumpToPayload.caseId = caseId;
  //   pathwaysJumpToPayload.pwId = pathwaysJumpToId;
  //   pathwaysJumpToPayload.sg = symptomGroup;
  //   pathwaysJumpToPayload.quId =
  //     buttonType + "|" + paccsSymptomModelTemplateId + "|" + paccsConditionId;
  //
  //   return pathwaysJumpToPayload;
  // }

  /**
   * Can't think for now why you wouldn't be able to switch tabs...but stubbed anyway.
   * @param paccsForm
   * @param destinationConditionTab
   */
  public canSwitchConditionTab(
    paccsForm: IPaccsForm,
    destinationConditionTab: IConditionTab
  ): { isOK: boolean; message: string } {
    return {
      isOK: true,
      message: ""
    };
  }

  public getLatestTriageRecordCondition(
    triageRecords: ITriageRecord[],
    triageRecordConditionUpdate: TriageRecordConditionUpdate
  ): ITriageRecordCondition | null {
    return triageRecords.reduce((accum, triageRecord) => {
      if (this.isTriageRecordCondition(triageRecord)) {
        if (
          triageRecord.pathwayId === triageRecordConditionUpdate.pathwayId &&
          triageRecord.quId === triageRecordConditionUpdate.quId
        ) {
          accum = triageRecord as ITriageRecordCondition;
        }
      }
      return accum;
    }, null as ITriageRecordCondition | null);
  }

  public getLatestStateForEachTriageRecordCondition(
    triageRecords: ITriageRecordCondition[]
  ): Record<string, ITriageRecordCondition> {
    return triageRecords.reduce(
      (accum, triageRecord: ITriageRecordCondition) => {
        accum[triageRecord.quId] = triageRecord;
        return accum;
      },
      {} as Record<string, ITriageRecordCondition>
    );
  }

  public filterTriageRecordCondition(
    triageRecords: ITriageRecord[]
  ): ITriageRecordCondition[] {
    return triageRecords.filter(triageRecord => {
      return this.isTriageRecordCondition(triageRecord);
    }) as ITriageRecordCondition[];
  }

  public getActiveTriageRecordConditionsForTab(
    conditionTab: IConditionTab,
    triageRecords: ITriageRecord[]
  ): ITriageRecordCondition[] {
    const triageRecordConditions = this.filterTriageRecordCondition(
      triageRecords
    ).filter(triageRecord => {
      return (
        triageRecord.pathwayId === conditionTab.paccsSymptomModel.templateId
      );
    });
    const latestState = this.getLatestStateForEachTriageRecordCondition(
      triageRecordConditions
    );

    return Object.values(latestState).filter(
      (triageRecord: ITriageRecordCondition) => {
        return triageRecord.answerNumber > 0;
      }
    );
  }

  /**
   * For all TriageRecords only return those that have a "true" answer and return the latest one.
   * @param triageRecords
   */
  public getLatestStateForEachTriageRecord(
    triageRecords: ITriageRecord[]
  ): ITriageRecord[] {
    //  Go in, in time order, so start from last.
    const triageRecordsMap: Record<string, ITriageRecord> = triageRecords
      .slice()
      .reverse()
      .reduce((accum, triageRecord) => {
        const key: string | undefined = this.isTriageRecordCondition(
          triageRecord
        )
          ? triageRecord.quId
          : triageRecord.pathwayId;
        if (key && !accum[key]) {
          accum[key] = triageRecord;
        }
        return accum;
      }, {} as Record<string, ITriageRecord>);

    return Object.values(triageRecordsMap);
  }

  public getLatestActiveStateForEachTriageRecord(
    triageRecords: ITriageRecord[]
  ): ITriageRecord[] {
    return this.getLatestStateForEachTriageRecord(triageRecords).filter(
      triageRecord => {
        return this.isTriageRecordActive(triageRecord);
      }
    );
  }

  public isTriageRecordActive(triageRecord: ITriageRecord): boolean {
    return this.isTriageRecordCondition(triageRecord)
      ? this.isTriageRecordConditionActive(
          triageRecord as ITriageRecordCondition
        )
      : this.isTriageRecordTemplateActive(
          triageRecord as ITriageRecordTemplate
        );
  }

  public isTriageRecordTemplateActive(
    triageRecordTemplate: ITriageRecordTemplate
  ): boolean {
    return triageRecordTemplate.actionId === 2;
  }

  public isTriageRecordConditionActive(
    triageRecordCondition: ITriageRecordCondition
  ): boolean {
    return triageRecordCondition.answerNumber > 0;
  }

  public canConditionTabBeRemoved(
    conditionTab: IConditionTab,
    triageRecords: ITriageRecord[]
  ): {
    isOK: boolean;
    simpleMessage: string;
    data: { activeRecordsCount: number; activeRecords: ITriageRecord[] };
  } {
    const activeTriageRecords = this.getActiveTriageRecordConditionsForTab(
      conditionTab,
      triageRecords
    );

    const message: string = activeTriageRecords
      .reduce((accum, triageRecordCondition: ITriageRecordCondition) => {
        accum.push(triageRecordCondition.reportText);
        return accum;
      }, [] as string[])
      .join(" | ");

    return {
      isOK: activeTriageRecords.length === 0,
      simpleMessage: message,
      data: {
        activeRecordsCount: activeTriageRecords.length,
        activeRecords: activeTriageRecords
      }
    };
  }

  public createTriageRecordTemplate(
    caseId: string,
    pathwaysDataset: IPathwaysDataset,
    paccsSymptomModel: IPaccsSymptomModel
  ): ITriageRecordTemplate {
    const triageRecordTemplate: ITriageRecordTemplate = this.factoryTriageRecordTemplate(
      caseId,
      pathwaysDataset,
      paccsSymptomModel
    );
    triageRecordTemplate.pathwayId = paccsSymptomModel.templateId;
    triageRecordTemplate.dispositionRationale = paccsSymptomModel.templateName;
    // triageRecordTemplate.symptomGroup = paccsSymptomModel.symptomGroup;
    return triageRecordTemplate;
  }

  public createTriageRecordCondition(
    paccsSymptomModel: IPaccsSymptomModel,
    paccsCondition: IPaccsCondition,
    // paccsGlobalConfig: IPaccsGlobalConfig,
    caseId: string,
    pathwaysDataset: IPathwaysDataset,
    data: {
      considered: boolean;
      suspected: boolean;
      specify: string;
    }
  ): ITriageRecordCondition {
    const triageRecordCondition = this.factoryTriageRecordCondition(
      caseId,
      pathwaysDataset,
      paccsSymptomModel
    );
    triageRecordCondition.answerNumber = data.suspected
      ? 2
      : data.considered
      ? 1
      : 0;

    triageRecordCondition.includeInReport =
      triageRecordCondition.answerNumber > 0;
    triageRecordCondition.pathwayId = paccsSymptomModel.templateId;
    triageRecordCondition.quId = paccsCondition.conditionId;
    triageRecordCondition.userComment =
      triageRecordCondition.answerNumber === 0 ? "" : data.specify;
    triageRecordCondition.reportText =
      paccsSymptomModel.templateName +
      " / " +
      paccsCondition.conditionName +
      ", " +
      (data.suspected
        ? "Suspected"
        : data.considered
        ? "Considered"
        : "Deselected");
    return triageRecordCondition;
  }

  public isTriageRecordTemplate(triageRecord: ITriageRecord): boolean {
    return !!(
      triageRecord.dispositionRationale &&
      triageRecord.dispositionRationale.length > 0
    );
  }

  public isTriageRecordCondition(triageRecord: ITriageRecord): boolean {
    return !!(triageRecord.quId && triageRecord.quId.length > 0);
  }

  public getTriageRecordTitle(triageRecord: ITriageRecord): string {
    if (this.isTriageRecordTemplate(triageRecord)) {
      return (triageRecord as ITriageRecordTemplate).dispositionRationale;
    }
    return (triageRecord as ITriageRecordCondition).reportText;
  }

  public getOutputForHostSystem(triageRecords: ITriageRecord[]): string {
    return this.getLatestActiveStateForEachTriageRecord(triageRecords)
      .map(triageRecord => {
        return this.getTriageRecordTitle(triageRecord);
      })
      .join(".  ");
  }

  // function to decodeURIComponent, if fails, return original string.
  public decodeURIComponentSafe(value: string): string {
    try {
      return decodeURIComponent(value);
    } catch (e) {
      return value;
    }
  }

  public mapPathwaysDatasets(
    pathwaysDatasetLegacy: IPathwaysDatasetLegacy
  ): IPathwaysDataset {
    const pathwaysDataset = this.factoryPathwaysDataset();

    const keysLegacy = Object.keys(pathwaysDatasetLegacy);
    const keys = Object.keys(pathwaysDataset);
    const keysSimilar: (keyof IPathwaysDataset)[] = keys.reduce(
      (accum, key) => {
        if (keysLegacy.indexOf(key) > -1) {
          accum.push(key as keyof IPathwaysDataset);
        }
        return accum;
      },
      [] as (keyof IPathwaysDataset)[]
    );

    keysSimilar.forEach((key: keyof IPathwaysDataset) => {
      if (!(key === "previousEncounters" || key === "traceVerified")) {
        const keyValue: unknown =
          pathwaysDatasetLegacy[key as keyof IPathwaysDatasetLegacy];
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        pathwaysDataset[(key as any) as keyof IPathwaysDataset] =
          typeof keyValue === "string"
            ? this.decodeURIComponentSafe(keyValue)
            : keyValue;
      }
    });

    pathwaysDataset.skillSet = PATHWAYS_SKILL_SET__PACCS;
    pathwaysDataset.previousEncounters = Number(
      pathwaysDatasetLegacy.previousEncounters
    );
    pathwaysDataset.traceVerified = pathwaysDatasetLegacy.TraceVerified;

    //  TODO why removing tel number space?
    pathwaysDataset.callerTelephoneNumber = decodeURIComponent(
      pathwaysDatasetLegacy.CallerTelephoneNumber
    ).replace(" ", "");

    pathwaysDataset.addressEasting =
      typeof pathwaysDataset.addressEasting === "string"
        ? Number(pathwaysDataset.addressEasting)
        : pathwaysDataset.addressEasting;
    pathwaysDataset.addressNorthing =
      typeof pathwaysDataset.addressNorthing === "string"
        ? Number(pathwaysDataset.addressNorthing)
        : pathwaysDataset.addressNorthing;

    return pathwaysDataset;
  }

  public getPaccsAgeGroup(
    dateOfBirth: Date,
    seedNow: Date = new Date()
  ): PaccsAgeGroup {
    /*
    1 Adult 140160 1753164
    2 Child 43800 140160
    3 Toddler 8760 43800
    4 Infant 0 8760
    */

    const ageInHours = differenceInHours(seedNow, dateOfBirth);
    let result: PaccsAgeGroup = "Infant";

    //  1yr
    if (ageInHours > 8760) {
      result = "Toddler";
    }
    //  5 years
    if (ageInHours > 43800) {
      result = "Child";
    }
    //  16 years
    if (ageInHours > 140160) {
      result = "Adult";
    }
    return result;
  }
}
