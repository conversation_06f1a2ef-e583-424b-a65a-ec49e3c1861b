import { ICleoBase } from "@/keywords/keywords-models";

export interface IBaseLookupFullLegacy {
  permalink: string; //  NSTS-8DWEFJ
  description: string; // Name of base
  CCG_Code: string; //  CCG
  GPSLong: string;
  GPSLat: string;
  InIc24Area: "" | "0" | "1";
  keyAddress: string;
  keyTown: string;
  keyPostCode: string;
  InOut: "IN" | "OUT";
  AdapterId: string;
}

export interface IBaseSummary extends ICleoBase {
  legacyKey: string; //  Until fully switched over, this is what links old and new
  lat: number;
  long: number;
  ccg: string;
  inIc24Area: boolean;
  address: string;
  town: string;
  postCode: string;
  inOut: boolean;
}
