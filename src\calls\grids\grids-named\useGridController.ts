import { computed, reactive, ref, SetupContext } from "@vue/composition-api";
import {
  IGridControllerStackMessage,
  IGridControllerState
} from "@/calls/grids/grid-controller/grid-controller";
import { StackCollection } from "@/common/collections/StackCollection";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { IGridDefinition } from "@/calls/grids/grid-models";
import {
  CleoSocketWrapperController,
  SOCKET_ACTIONS,
  SocketActionType
} from "@/socket/socket-controller";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { loggerInstance } from "@/common/Logger";
import { IAdapterPagedResponse } from "@/common/common-models";
import { format, parseISO } from "date-fns";
import { CommonService } from "@/common/common-service";
import { IUser } from "@/user/user-models";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import { Store } from "vuex";
import { IRootState } from "@/store/store";
import {
  factoryGridFilters,
  IGridFilter
} from "@/calls/grids/grid-filter/grid-filter-models";
import { debounce } from "@/common/debounce";
import { PermissionData } from "@/permissions/permission-data";
import { IUseRightClickControllerShow } from "@/calls/grids/contextmenu/useRightClickController";
import { getLegacyGridData } from "@/calls/grids/grids-named/legacy/api/grid-legacy-api";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";
import { IGridFilterUserInput } from "@/calls/grids/grid-filter/grid-filter-models";
import { toIsoStringWithLocalOffsetWithoutTemporal } from "@/common/common-utils";
import { GridFilterUserInputMyCasesParaMedicOnScene } from "@/calls/grids/grid-filter/grid-filter-models";

const callSummaryService: CallSummaryService = new CallSummaryService();
const commonService: CommonService = new CommonService();
const callSummaryController = new CallSummaryController();
const cleoCommonService = new CleoCommonService();
const permissionData = new PermissionData();
const gridFilterService = new GridFilterService();

export interface IGridControllerAppConfig {
  store: Store<IRootState>;
  headers: {
    Authorization: string;
    HeaderAuthorization: string;
  };
  user: IUser;
  longPollingRefreshRateMs: number;
  breachRefreshRateMs: number;
  stackMessagesMaxToShow: number;
}

export function useGridController(
  context: SetupContext,
  gridDefinition: IGridDefinition,
  gridControllerAppConfig: IGridControllerAppConfig
) {
  const targetGridContainerId =
    "target-grid-container-" +
    Math.random()
      .toString(36)
      .substring(2);

  const state = reactive<IGridControllerState>({
    simpleTriggerLongPoll: { timeIso: "" },
    simpleTriggerBreach: { timeIso: "" },
    socketLoadingTrigger: { timeIso: "" },
    isLoading: false,
    longPollingCounter: 0,
    gridResponse: callSummaryService.factoryAdapterGridResponse(),
    gridData: [],
    gridDataExported: [],
    newCalls: [],
    updatedCalls: [],
    removedCalls: [],
    lastUpdatedHumanReadTime: "",
    lastLongPollHumanReadTime: "",
    lastSocketMessageHumanReadTime: "",
    simpleGetData: { timeIso: "" },
    selectedCall: callSummaryService.factoryCleoCallSummary(),
    isLoadingPermsForSelectedCall: false,
    permsForSelectedCall: {},
    selectedCalls: [],
    quickFilterText: { timeIso: "", data: "" },
    filters: [],
    filterUserInput: gridFilterService.factoryGridFilterUserInput(),
    showFilterDialog: false,
    agFilters: {},
    callGridCount: 0,
    callFilterCount: 0,
    stackCollectionMessages: new StackCollection<IGridControllerStackMessage>(
      100
    ),
    stackMessages: [],
    showStackMessages: false,
    rightClick: {
      showMenu: false,
      coords: {
        x: 0,
        y: 0
      }
    }
  });

  const cleoCallSummarySelected = ref<ICleoCallSummary>(
    callSummaryService.factoryCleoCallSummary()
  );
  const showConfirmOpeningCall = ref<boolean>(false);

  let breachRecalcTimer: any;
  let longPollingTimer: any;

  const cleoSocketWrapperController: CleoSocketWrapperController = new CleoSocketWrapperController(
    gridControllerAppConfig.headers
  );
  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_NEW,
    (cleoCallSummary: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_NEW +
          ", Grid: " +
          gridDefinition.identifier +
          ", Call: " +
          cleoCallSummary.CallNo,
        cleoCallSummary
      );
      handleNew(cleoCallSummary);
    }
  );
  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_UPDATED,
    (cleoCallSummary: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_UPDATED +
          ", Grid: " +
          gridDefinition.identifier +
          ", Call: " +
          cleoCallSummary.CallNo,
        cleoCallSummary
      );
      handleUpdated(cleoCallSummary);
    }
  );

  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_LOCKED,
    (cleoCallSummary: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_LOCKED +
          ", Grid: " +
          gridDefinition.identifier +
          ", Call: " +
          cleoCallSummary.CallNo,
        cleoCallSummary
      );
      handleUpdated(cleoCallSummary);
    }
  );

  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_UNLOCKED,
    (cleoCallSummary: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_UNLOCKED +
          ", Grid: " +
          gridDefinition.identifier +
          ", Call: " +
          cleoCallSummary.CallNo,
        cleoCallSummary
      );
      handleUpdated(cleoCallSummary);
    }
  );

  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_REMOVED,
    (removedCallNumber: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_REMOVED +
          ", Grid: " +
          gridDefinition.identifier,
        removedCallNumber
      );
      handleRemoved(removedCallNumber);
    }
  );

  cleoSocketWrapperController.socket.on(
    SOCKET_ACTIONS.ON_CASE_CLOSED,
    (removedCallNumber: ICleoCallSummary) => {
      loggerInstance.log(
        "useGridController socket.on: " +
          SOCKET_ACTIONS.ON_CASE_CLOSED +
          ", Grid: " +
          gridDefinition.identifier,
        removedCallNumber
      );
      handleRemoved(removedCallNumber);
    }
  );

  cleoSocketWrapperController.socket.onreconnecting(() => {
    loggerInstance.log(
      "useGridController socket.on: " +
        "onreconnecting" +
        ", Grid: " +
        gridDefinition.identifier
    );
  });

  cleoSocketWrapperController.socket.onreconnected(() => {
    loggerInstance.log(
      "useGridController socket.on: " +
        "onreconnected" +
        ", Grid: " +
        gridDefinition.identifier
    );
  });

  cleoSocketWrapperController.socket.onclose(() => {
    loggerInstance.log(
      "useGridController socket.on: " +
        "onclose" +
        ", Grid: " +
        gridDefinition.identifier
    );
  });

  if (gridDefinition.legacyOptions) {
    getDataFromLegacyServer().then(() => {
      if (gridControllerAppConfig.longPollingRefreshRateMs > 0) {
        longPolling();
        recalcBreach();
      }
    });
  } else {
    cleoSocketWrapperController.startSocket().then(() => {
      joinSocketGroup().then(() => {
        if (gridControllerAppConfig.longPollingRefreshRateMs > 0) {
          longPolling();
          recalcBreach();
        }
      });
    });
  }

  /**
   *
   */
  function getDataFromLegacyServer(): Promise<void> {
    return getLegacyGridData(gridDefinition).then((response: any) => {
      setData(response);
    });
  }

  /**
   *
   */
  function joinSocketGroup(): Promise<void> {
    if (
      !cleoSocketWrapperController ||
      cleoSocketWrapperController.status !== "Connected"
    ) {
      return Promise.resolve();
    }

    let params = "";
    const paramKeys = Object.keys(gridDefinition.params);
    if (paramKeys.length > 0) {
      params = paramKeys.reduce((accum, key) => {
        accum +=
          (accum.length > 0 ? "&" : "") +
          key +
          "=" +
          gridDefinition.params[key];
        return accum;
      }, "");
    }

    const groupName =
      gridDefinition.identifier + (params.length > 0 ? "?" + params : "");

    state.isLoading = true;
    return cleoSocketWrapperController.socket
      .invoke("RegisterForEpisodeOfCareQueue", groupName)
      .then((adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary>) => {
        loggerInstance.log(
          "onSocketStatusChanged>>>>>> invoke() invokeResponse",
          adapterPagedResponse
        );

        if (gridDefinition.legacyOptions) {
          // return getDataFromLegacyServer();
        } else {
          setData(adapterPagedResponse);
        }
        // setData(adapterPagedResponse);
      })
      .catch((err: Error) => {
        console.error(err);
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function setData(
    adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary>
  ): void {
    //  TODO only here when Adapter returning incorrect data.
    // const responseNew = commonService.simpleObjectClone(adapterPagedResponse);
    // responseNew.Records = gridService.filterAdapterPagedResponse(
    //   adapterPagedResponse
    // );
    const responseNew = adapterPagedResponse;

    state.gridResponse = responseNew;
    state.gridData = responseNew.Records;

    state.simpleGetData = { timeIso: new Date().toISOString() };

    state.lastLongPollHumanReadTime = format(
      parseISO(new Date().toISOString()),
      "HH:mm:ss"
    );
    state.lastUpdatedHumanReadTime = state.lastLongPollHumanReadTime;
    state.callGridCount = state.gridData.length;
  }

  function handleNew(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("useGridController.handleNew()...");
    state.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    state.newCalls = [cleoCallSummary];
    setLastSocketMessage([cleoCallSummary], SOCKET_ACTIONS.ON_CASE_NEW);
  }

  function handleUpdated(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("useGridController.handleUpdated()...");
    state.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    const updatedCalls = [cleoCallSummary];

    state.updatedCalls = updatedCalls;
    setLastSocketMessage([cleoCallSummary], SOCKET_ACTIONS.ON_CASE_UPDATED);
    checkIfSummaryDisplayNeedUpdating(updatedCalls);
  }

  function handleRemoved(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("useGridController.handleRemoved()...");
    state.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    state.removedCalls = [cleoCallSummary];
    setLastSocketMessage([cleoCallSummary], SOCKET_ACTIONS.ON_CASE_REMOVED);
    checkIfSummaryDisplayNeedUpdating([cleoCallSummary]);
  }

  function quickFilterTextChanged(filterText: string): void {
    state.quickFilterText = {
      timeIso: new Date().toISOString(),
      data: filterText
    };
    // this.quickFilterTrigger1 = {
    //   timeIso: new Date().toISOString(),
    //   data: this.quickFilterText1
    // };
  }

  // const filterValueClassification = ref("");
  // const filterValueCov19 = ref("");

  function onClassificationFilter(filterValue: string): void {
    // filterValueClassification.value = filterValue;
    state.filterUserInput.CLASSIFICATION = filterValue;
    setFilters();
  }

  function onCov19Filter(filterName: "COV19" | "NOT_COV19" | ""): void {
    // filterValueCov19.value = filterName;
    state.filterUserInput.COV19 = filterName;
    setFilters();
  }

  function filterDialogClosed(gridFilterUserInput: IGridFilterUserInput): void {
    state.showFilterDialog = false;
    onGridFilterUserInputChanged(gridFilterUserInput);
  }

  function onGridFilterUserInputChanged(
    gridFilterUserInput: IGridFilterUserInput
  ) {
    state.filterUserInput = gridFilterUserInput;
    setFilters();
  }

  function setFilters(): void {
    const filters: IGridFilter[] = [];

    if (state.quickFilterText.data.length > 0) {
      const quickFilter = factoryGridFilters.QUICK;
      quickFilter.filterValue = state.quickFilterText.data;
      filters.push(quickFilter);
    }

    // if (filterValueClassification.value.length > 0) {
    if (state.filterUserInput.CLASSIFICATION.length > 0) {
      console.error(
        "state.filterUserInput.CLASSIFICATION: " +
          state.filterUserInput.CLASSIFICATION
      );
      const cleoFilter = factoryGridFilters.CLASSIFICATION;
      cleoFilter.filterValue = state.filterUserInput.CLASSIFICATION;
      filters.push(cleoFilter);
    }

    if (state.filterUserInput.COV19 === "COV19") {
      console.error(
        "state.filterUserInput.COV19: " + state.filterUserInput.COV19
      );
      filters.push(factoryGridFilters.COV19);
    }
    // TODO
    if (state.filterUserInput.COV19 === "NOT_COV19") {
      console.error(
        "state.filterUserInput.COV19: " + state.filterUserInput.COV19
      );
      filters.push(factoryGridFilters.COV19);
    }

    if (state.filterUserInput.CLEO_CLIENT_SERVICE.length > 0) {
      console.error(
        "state.filterUserInput.CLEO_CLIENT_SERVICE: " +
          state.filterUserInput.CLEO_CLIENT_SERVICE
      );
      const cleoClientServiceFilter = factoryGridFilters.CLEO_CLIENT_SERVICE;
      cleoClientServiceFilter.filterValue = state.filterUserInput;
      filters.push(cleoClientServiceFilter);
    }

    if (state.filterUserInput.DX.dxCodes.length > 0) {
      console.error(
        "state.filterUserInput.DX.dxCodes: " + state.filterUserInput.DX.dxCodes
      );
      const dxFilter = factoryGridFilters.DX;
      dxFilter.filterValue = state.filterUserInput.DX;
      filters.push(dxFilter);
    }

    if (state.filterUserInput.PDS_TRACED_AND_VERIFIED !== null) {
      console.error(
        "state.filterUserInput.PDS_TRACED_AND_VERIFIED: " +
          state.filterUserInput.PDS_TRACED_AND_VERIFIED
      );
      const pdsFilter = factoryGridFilters.PDS_TRACED_AND_VERIFIED;
      pdsFilter.filterValue = state.filterUserInput.PDS_TRACED_AND_VERIFIED;
      filters.push(pdsFilter);
    }

    if (state.filterUserInput.ASSIGNED_TO !== null) {
      console.error(
        "state.filterUserInput.ASSIGNED_TO: " +
          state.filterUserInput.ASSIGNED_TO
      );
      const assignedToFilter = factoryGridFilters.ASSIGNED_TO;
      assignedToFilter.filterValue = state.filterUserInput.ASSIGNED_TO;
      filters.push(assignedToFilter);
    }

    if (state.filterUserInput.BREACHED !== null) {
      console.error(
        "state.filterUserInput.BREACHED: " + state.filterUserInput.BREACHED
      );
      const breachedFilter = factoryGridFilters.BREACHED;
      const isoTimeNow = toIsoStringWithLocalOffsetWithoutTemporal(new Date());
      console.error("isoTimeNow: " + isoTimeNow);
      breachedFilter.filterValue = isoTimeNow;
      filters.push(breachedFilter);
    }

    if (state.filterUserInput.TOXIC_INGESTION_AND_EMPTY) {
      console.error(
        "state.filterUserInput.TOXIC_INGESTION_AND_EMPTY: " +
          state.filterUserInput.TOXIC_INGESTION_AND_EMPTY
      );
      filters.push(factoryGridFilters.TOXIC_INGESTION_AND_EMPTY);
    }

    if (state.filterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES) {
      console.error(
        "state.filterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES: " +
          state.filterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES
      );
      filters.push(factoryGridFilters.AMBULANCE_CLEO_CLIENT_SERVICES);
    }

    if (state.filterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES) {
      console.error(
        "state.filterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES: " +
          state.filterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES
      );
      filters.push(factoryGridFilters.ED_VALIDATION_CLEO_CLIENT_SERVICES);
    }

    if (state.filterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled) {
      console.error(
        "state.filterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled: " +
          state.filterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled
      );
      factoryGridFilters.MY_CASE_AND_PARAMEDIC_ON_SCENE.filterValue =
        state.filterUserInput;
      filters.push(factoryGridFilters.MY_CASE_AND_PARAMEDIC_ON_SCENE);
    }

    if (state.filterUserInput.REQUIRES_VALIDATION) {
      console.error(
        "state.filterUserInput.REQUIRES_VALIDATION: " +
          state.filterUserInput.REQUIRES_VALIDATION
      );
      filters.push(factoryGridFilters.REQUIRES_VALIDATION);
    }

    // if (state.filterUserInput.FOLLOW_UP) {
    //   filters.push(factoryGridFilters.FOLLOW_UP);
    // }

    state.filters = filters;
  }

  function onRowClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("useGridController.onRowClicked();", cleoCallSummary);
    cleoCallSummarySelected.value = commonService.simpleObjectClone(
      cleoCallSummary
    );
    context.emit("onRowClicked", cleoCallSummary);
    debounceGetSelectedPerms();
  }

  /**
   *
   * @param cleoCallSummary
   */
  function onRowDoubleClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log(
      "useGridController.onRowDoubleClicked();",
      cleoCallSummary
    );

    if (
      cleoCallSummarySelected.value &&
      cleoCallSummarySelected.value.CallNo !== cleoCallSummary.CallNo
    ) {
      onRowClicked(cleoCallSummary);
    }

    if (isCallLockedByAnotherUser()) {
      showConfirmOpeningCall.value = true;
      return;
    }
    proceedToOpeningCall(cleoCallSummary);
    context.emit("openCall", cleoCallSummary);
  }

  /**
   *
   * @param payload
   */
  function onCellContextMenu(payload: {
    data: ICleoCallSummary;
    coords: { x: number; y: number };
  }): void {
    loggerInstance.log("useGridController.onCellContextMenu();", payload.data);
    cleoCallSummarySelected.value = commonService.simpleObjectClone(
      payload.data
    );
    state.rightClick.showMenu = true;
    state.rightClick.coords = payload.coords;
    state.permsForSelectedCall = {};

    const useRightClickControllerShow: IUseRightClickControllerShow = {
      ...payload,
      targetGridContainerId
    };

    context.emit("onRowRightClick", useRightClickControllerShow);
    onRowClicked(payload.data);
    // debounceGetSelectedPerms();
  }

  const debounceGetSelectedPerms = debounce(() => {
    loggerInstance.log("call-summary-section.debounceGetSelectedPerms()...");
    getPermsForSelectedCall();
  }, 1000);

  /**
   *
   */
  function getPermsForSelectedCall(): void {
    if (cleoCallSummarySelected.value) {
      state.isLoadingPermsForSelectedCall = true;
      context.emit("loadingSelectedCallPerms", true);
      context.emit("selectedCallPerms", {});
      permissionData
        .getUserPermissions(
          gridControllerAppConfig.user.userRole,
          cleoCallSummarySelected.value.CallNo
        )
        .then(perms => {
          state.permsForSelectedCall = commonService.simpleObjectClone(perms);
          context.emit(
            "selectedCallPerms",
            commonService.simpleObjectClone(perms)
          );
        })
        .finally(() => {
          state.isLoadingPermsForSelectedCall = false;
          context.emit("loadingSelectedCallPerms", false);
        });
    }
  }

  /**
   *
   */
  function isCallLockedByAnotherUser(): boolean {
    if (!cleoCallSummarySelected.value) {
      return false;
    }
    if (!gridControllerAppConfig.user) {
      return false;
    }

    return callSummaryService.isCallLockedByAnotherUser(
      cleoCallSummarySelected.value,
      gridControllerAppConfig.user.userName
    );
  }

  /**
   *
   */
  function proceedToOpeningLockedCall(): void {
    showConfirmOpeningCall.value = false;
    if (cleoCallSummarySelected.value) {
      proceedToOpeningCall(cleoCallSummarySelected.value);
      context.emit("openCall", cleoCallSummarySelected.value);
    }
  }

  const getOpenCallMessage = computed<string>(() => {
    if (
      cleoCallSummarySelected.value &&
      cleoCallSummarySelected.value.CallNo > 0
    ) {
      const message =
        "Call " +
        cleoCallSummarySelected.value.CallNo +
        (isCallLockedByAnotherUser()
          ? " is locked by: " +
            cleoCommonService.formatUserDominoName(
              cleoCallSummarySelected.value.IsLocked
            ) +
            ", continue to open?"
          : "");
      return message;
    }
    return "";
  });

  /**
   *
   * @param cleoCallSummary
   */
  function proceedToOpeningCall(cleoCallSummary: ICleoCallSummary): void {
    callSummaryController.openCall(
      cleoCallSummary,
      gridControllerAppConfig.user
    );
  }

  /**
   *
   * @param updatedCalls
   */
  function checkIfSummaryDisplayNeedUpdating(
    updatedCalls: ICleoCallSummary[]
  ): void {
    if (!cleoCallSummarySelected.value) {
      return;
    }

    const pred = (callSummary: ICleoCallSummary) => {
      return callSummary.CallNo === cleoCallSummarySelected.value.CallNo;
    };

    const callMatchUpdate: ICleoCallSummary | null = commonService.findFirst(
      pred,
      updatedCalls
    );
    if (callMatchUpdate) {
      cleoCallSummarySelected.value = commonService.simpleObjectClone(
        callMatchUpdate
      );
      context.emit("onRowClicked", cleoCallSummarySelected.value);
    }
  }

  function longPolling(): void {
    loggerInstance.log("useGridController.longPolling()...a at: " + new Date());

    longPollingTimer = window.setTimeout(() => {
      loggerInstance.log(
        "useGridController.longPolling()...run again at " +
          new Date().toISOString() +
          ", which is every: " +
          gridControllerAppConfig.longPollingRefreshRateMs / 1000 +
          " seconds"
      );
      clearTimeout(longPollingTimer);
      if (state.isLoading) {
        loggerInstance.log(
          "useGridController.longPolling() already loading..."
        );
      } else {
        state.simpleTriggerLongPoll = {
          timeIso: new Date().toISOString()
        };

        if (gridDefinition.legacyOptions) {
          // Even if there is error, need to keep polling.
          getDataFromLegacyServer()
            .then(() => {
              console.log("getDataFromLegacyServer() done...");
            })
            .catch((err: Error) => {
              console.log("getDataFromLegacyServer() error...");
              console.error(err);
            })
            .finally(() => {
              console.log("getDataFromLegacyServer() finally...");
              longPolling();
            });
          // getLegacyGridData(gridDefinition).then((response: any) => {
          //   setData(response);
          // });
        } else {
          joinSocketGroup().finally(() => {
            longPolling();
          });
        }
      }
    }, gridControllerAppConfig.longPollingRefreshRateMs);
  }

  function setLastSocketMessage(
    cleoCallSummaries: ICleoCallSummary[] | number[],
    messageType: SocketActionType
  ): void {
    state.lastSocketMessageHumanReadTime = format(
      parseISO(new Date().toISOString()),
      "HH:mm:ss"
    );
    state.lastUpdatedHumanReadTime = state.lastSocketMessageHumanReadTime;

    //  Every time we get a socket message, socket working, so reset the "back up"
    //  long polling.  On a really busy day, socket messages might come in
    //  faster than the refresh rate, so a long poll may never get triggered...
    //  which is fine, because all messages should be coming thru socket.  We have
    //  this mechanism in place just in case Socket Back Plane falls over...
    //  which it has done before.
    stopLongPollingTimer();
    longPolling();

    cleoCallSummaries.forEach((cleoCallSummary: ICleoCallSummary | number) => {
      const message: IGridControllerStackMessage = {
        id: "",
        time: new Date().toISOString(),
        callNo: 0,
        message: "",
        messageType: messageType,
        serviceName: ""
      };

      if (typeof cleoCallSummary === "number") {
        message.callNo = cleoCallSummary;
      } else {
        message.callNo = cleoCallSummary.CallNo;
        message.serviceName =
          cleoCallSummary.CallService.Type +
          (cleoCallSummary.CallService.Description
            ? ": " + cleoCallSummary.CallService.Description
            : "");
      }
      message.message = messageType + "";
      message.id = message.time + "~" + message.callNo + "~" + messageType;
      state.stackCollectionMessages.push(message);
    });
    state.stackMessages = state.stackCollectionMessages.getStorage();
  }

  function stopLongPollingTimer(): void {
    clearTimeout(longPollingTimer);
  }

  function recalcBreach(): void {
    breachRecalcTimer = window.setTimeout(() => {
      clearTimeout(breachRecalcTimer);

      loggerInstance.log(
        "useGridController.recalcBreach()...a at: " + new Date()
      );
      //  The grid is "watching" this
      state.simpleTriggerBreach = { timeIso: new Date().toISOString() };
      recalcBreach();
    }, gridControllerAppConfig.breachRefreshRateMs);
  }

  function stopRecalcBreachTimer(): void {
    clearTimeout(breachRecalcTimer);
  }

  const socketStatus = computed(() => {
    return cleoSocketWrapperController.status;
  });

  function setExportGridData(callData: ICleoCallSummary[]) {
    state.gridDataExported = commonService.simpleObjectClone(callData);
  }

  // are any filters active?  Include external filters and quick text
  const areAnyFiltersActive = computed(() => {
    return state.filters.length > 0 || state.quickFilterText.data.length > 0;
  });

  function destroy() {
    cleoSocketWrapperController.stopSocket();
    stopLongPollingTimer();
    stopRecalcBreachTimer();
  }

  return {
    targetGridContainerId,
    state,

    showConfirmOpeningCall,
    getOpenCallMessage,
    socketStatus,
    cleoSocketWrapperController,
    debounceGetSelectedPerms,
    cleoCallSummarySelected,
    areAnyFiltersActive,

    destroy,
    quickFilterTextChanged,
    onGridFilterUserInputChanged,
    onRowClicked,
    onRowDoubleClicked,
    onCellContextMenu,
    proceedToOpeningLockedCall,
    proceedToOpeningCall,
    filterDialogClosed,

    joinSocketGroup,
    getDataFromLegacyServer,

    onClassificationFilter,
    onCov19Filter,
    setExportGridData
  };
}
