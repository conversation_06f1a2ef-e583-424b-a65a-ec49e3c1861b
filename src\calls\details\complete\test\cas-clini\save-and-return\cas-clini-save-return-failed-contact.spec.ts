import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import { IStep } from "@/calls/details/complete/complete-models";

describe("CAS Clini useCompleteController Save-Return", () => {
  /**
   *
   */
  it("CAS Clini Save and Return - Failed Contact: RETURN_TO_OPEN_CASE", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "SAVE_AND_RETURN_PROCESS"
    );
    expect(controller.state.processName).toBe("SAVE_AND_RETURN_CAS_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(false);
    expect(controller.state.autoProgress).toBe(true);
    expect(controller.state.userOptions.exitReason.forUser.length).toBe(3);

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(5);

    expect(stepNames[0]).toBe("EXIT_REASON");
    expect(stepNames[1]).toBe("HOW_WAS_CASE_MANAGED");
    expect(stepNames[2]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[3]).toBe("FAILED_CONTACT_WARNING");
    expect(stepNames[4]).toBe("UNKNOWN");

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    // controller.onExitReasonSelected();
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("EXIT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "EXIT_REASON__NOT_SELECTED"
    );

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FAILED_CONTACT",
      description: "Failed Contact",
      value: "FAILED_CONTACT"
    });

    expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");

    expect((controller.state.steps.EXIT_REASON as IStep<any>).isValid).toBe(
      true
    );

    expect(controller.state.isProcessComplete).toBe(false);

    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onHowMangedSelected({
      id: "VIRTUAL_CONSULT_TEL_AND_VIDEO",
      description: "Virtual Consult Tel and Video",
      value: "2-VirtualConsultTelVideo"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    expect(controller.state.isProcessComplete).toBe(false);

    controller.onFailedContactWarningSelected({
      id: "RETURN_TO_OPEN_CASE",
      description: "Return to Open Case",
      value: "RETURN_TO_OPEN_CASE"
    });
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    expect(controller.state.validationMessages.length).toBe(0);

    expect(controller.state.isProcessComplete).toBe(true);
    expect(controller.state.finalAction).toBe("RETURN_TO_OPEN_CASE");
  });

  it("CAS Clini Save and Return - Failed Contact: RETURN_TO_OPEN_CASE", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "SAVE_AND_RETURN_PROCESS"
    );
    expect(controller.state.processName).toBe("SAVE_AND_RETURN_CAS_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(false);
    expect(controller.state.autoProgress).toBe(true);
    expect(controller.state.userOptions.exitReason.forUser.length).toBe(3);

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(5);

    expect(stepNames[0]).toBe("EXIT_REASON");
    expect(stepNames[1]).toBe("HOW_WAS_CASE_MANAGED");
    expect(stepNames[2]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[3]).toBe("FAILED_CONTACT_WARNING");
    expect(stepNames[4]).toBe("UNKNOWN");

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    // controller.onExitReasonSelected();
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("EXIT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "EXIT_REASON__NOT_SELECTED"
    );

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FAILED_CONTACT",
      description: "Failed Contact",
      value: "FAILED_CONTACT"
    });

    expect(controller.state.userResponse.exitReason.id).toBe("FAILED_CONTACT");

    expect((controller.state.steps.EXIT_REASON as IStep<any>).isValid).toBe(
      true
    );

    expect(controller.state.isProcessComplete).toBe(false);

    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onHowMangedSelected({
      id: "VIRTUAL_CONSULT_TEL_AND_VIDEO",
      description: "Virtual Consult Tel and Video",
      value: "2-VirtualConsultTelVideo"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    expect(controller.state.isProcessComplete).toBe(false);

    controller.onFailedContactWarningSelected({
      id: "SAVE_AND_RETURN_TO_QUEUE",
      description: "Save and Return to Queue",
      value: "SAVE_AND_RETURN_TO_QUEUE"
    });
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_WARNING");
    expect(controller.state.validationMessages.length).toBe(0);

    expect(controller.state.isProcessComplete).toBe(true);
    expect(controller.state.finalAction).toBe("SAVE_AND_RETURN_TO_QUEUE");
  });
});
