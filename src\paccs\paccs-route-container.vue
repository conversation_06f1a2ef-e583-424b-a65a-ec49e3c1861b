<template>
  <div>
    <div>PACCS Route Container</div>
    <PaccsForm :paccs-form-data="paccsFormData" v-if="isReady"></PaccsForm>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";

import PaccsForm from "./paccs-form.vue";
import { IPaccsForm } from "@/paccs/paccs-models";
import { PaccsService } from "@/paccs/paccs-service";
// import { PaccsData } from "@/paccs/paccs-data";

@Component({
  name: "paccs-route-container",
  components: { PaccsForm }
})
export default class PaccsRouteContainer extends Vue {
  public paccsService: PaccsService = new PaccsService();
  public paccsFormData: IPaccsForm = this.paccsService.factoryPaccsForm();
  public isLoading = false;
  public isReady = false;

  public created() {
    this.isLoading = true;
    // new PaccsData()
    //   .getGlobalConfig()
    //   .then(resp => {
    //     this.paccsFormData.globalConfig = Object.assign({}, resp);
    //     this.isReady = true;
    //   })
    //   .finally(() => {
    //     this.isLoading = false;
    //   });
  }
}
</script>
