<template>
  <div class="autocomplete-simple">
    <input :disabled="false" v-model="searchTerm" v-on:keyup="debounceSearch" />
    <span slot="links">
      <a
        href="#"
        v-on:click.prevent="onSearchTermChanged"
        class="autocomplete-simple--link autocomplete-simple--search-link"
      >
        Search
      </a>
      <a
        href="#"
        v-on:click.prevent="reset"
        class="autocomplete-simple--link autocomplete-simple--reset-link"
      >
        Clear
      </a>
    </span>
    <LoadingSpinner v-show="isLoading" class="autocomplete-simple--loading"></LoadingSpinner>
    <div v-if="getShowItems">
      <div v-for="(item, index) in items" :key="index">
        <div v-text="getLabel(item)" v-on:click="onSelected(index)"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { debounce } from "@/common/debounce";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";

@Component({
  name: "autocomplete-simple",
  components: { LoadingSpinner }
})
export default class AutocompleteSimple extends Vue {
  @Prop({
    default: () => {
      return [];
    }
  })
  public readonly items!: any[];

  @Prop({
    default: 1
  })
  public minCharsTrigger!: number;

  @Prop({
    default: false
  })
  public isLoading!: boolean;

  public searchTerm = "";
  public debounceSearch: any;

  public created(): void {
    this.debounceSearch = debounce(() => {
      this.onSearchTermChanged();
    }, 200);
  }

  @Prop({ default: "" })
  public readonly labelProp!: string;

  @Prop({ default: null })
  public readonly labelPropFunction!: any;

  public get getShowItems(): boolean {
    return false;
  }

  public shouldShowItems(): boolean {
    return this.items.length > 0;
  }

  public reset(): void {
    this.searchTerm = "";
    this.$emit("onReset");
  }

  public getLabel(item: any): string {
    if (this.labelPropFunction) {
      return this.labelPropFunction(item);
    }

    if (this.labelProp.length > 0) {
      return item[this.labelProp];
    }
    return "NA";
  }

  public onSearchTermChanged(): void {
    if (this.searchTerm.length >= this.minCharsTrigger) {
      this.$emit("onSearchChanged", this.searchTerm);
    }
  }

  public onSelected(index: number): void {
    const item = this.items[index];
    this.$emit("onSelected", { ...item });
  }
}
</script>

<style>
.autocomplete-simple {
  height: 20px;
}
.autocomplete-simple--link {
  color: white;
  text-decoration: none;
}
.autocomplete-simple--reset-link {
  text-decoration: none;
}
.autocomplete-simple--loading {
  height: 20px;
}
</style>
