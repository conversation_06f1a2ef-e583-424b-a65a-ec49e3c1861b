/**
 * This file contains the models for the grids.
 *
 * Example of inserting custom column order:
 * colDefintions: {
 *       onlyIncludeTheseFields: [],
 *       excludeTheseFields: ["WARM_TRANSFERRED"],
 *       columnOrder: (() => {
 *         const customOrder = [...DEFAULT_COLUMN_ORDER];
 *         const serviceIndex = customOrder.indexOf("SERVICE");
 *         if (serviceIndex !== -1) {
 *           customOrder.splice(serviceIndex, 1);
 *           customOrder.unshift("SERVICE");
 *         }
 *         return customOrder;
 *       })()
 *     }
 *
 *     Example of inserting custom column
 *     colDefintions: {
 *       onlyIncludeTheseFields: [],
 *       excludeTheseFields: ["WARM_TRANSFERRED"],
 *       customColumns: {
 *         "CUSTOM_COLUMN": {
 *           headerName: "Custom",
 *           field: "customField",
 *           width: 100,
 *           valueGetter: (params) => {
 *             return "Custom value";
 *           }
 *         }
 *       }
 *     }
 *
 *     Example of moving a column
 *     // Get the current column order
 *   const currentOrder = gridDefaultInstance.getCurrentColumnOrder();
 *   console.log("Current column order:", currentOrder);
 *
 *   // You can now use this order to create a modified version
 *   const customOrder = [...currentOrder];
 *   // For example, move PATIENT to the beginning
 *   const patientIndex = customOrder.indexOf("PATIENT");
 *   if (patientIndex !== -1) {
 *     customOrder.splice(patientIndex, 1);
 *     customOrder.unshift("PATIENT");
 *   }
 *
 */

import { CLEO_GRID_COLUMN_NAME } from "@/calls/summary/call-summarry-models";
import { ColDef } from "@ag-grid-community/core/dist/cjs/entities/colDef";
import {
  getDefaultColumnOrder,
  insertColumnAfter,
} from "@/calls/grids/grid-column-utils";

export const SocketGroupNames = [
  "CatchAllCalls",
  "111calls",
  "PcasCalls",
  "FcmsCalls",
  "NavBase",
  "CasCalls",
  "OverSight",
  "OverSightFollowUp",
  "ClassificationBase",
  "ClassificationVisit",
  "PLS",
] as const;
export type SocketGroup = typeof SocketGroupNames[number];

export interface IGridDefinition {
  identifier: SocketGroup;
  title: string;
  description: string;
  params: Record<string, any>;
  legacy?: boolean;
  legacyOptions?: {
    enabled: boolean;
    url: string; // relative to cleo server, e.g./dev/livecalld.nsf/getViewData?openagent&vn=CAS_FILTER_NEW
  };
  colDefintions: {
    onlyIncludeTheseFields: CLEO_GRID_COLUMN_NAME[];
    excludeTheseFields: CLEO_GRID_COLUMN_NAME[];
    // Add these new properties for column customization
    columnOrder?: CLEO_GRID_COLUMN_NAME[]; // Define explicit column order
    customColumns?: Record<string, ColDef>; // Allow custom column definitions
  };
}

export interface IGridParamsBase {
  DutyBaseId: number;
}

/**
 * Constructs the base URL for accessing legacy data using global session properties.
 *
 * The `baseLegacyUrl` is a string that is dynamically generated by concatenating
 * several global session properties. It consists of:
 * - The `HOST_PATH` from `Global_DB_Paths`, which represents the root host URL.
 * - The `PATH_CALL` from `Global_DB_Paths`, which provides the path to necessary resources.
 * - A fixed query string `getViewDataGrid?openagent` to specify the desired operation.
 *
 * This variable is used as the starting point for legacy system API calls or data fetches.
 *
 * Note:
 * Ensure that `window.MyGlobalSession.Global_DB_Paths` is properly defined in the global session
 * before accessing this variable to avoid runtime errors.
 */
const baseLegacyUrl =
  window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
  "/" +
  window.MyGlobalSession.Global_DB_Paths.PATH_CALL +
  "/getViewDataGrid?openagent";

const DEFAULT_COLUMN_ORDER = getDefaultColumnOrder();

export const GRID_DEFINITIONS: Record<SocketGroup, IGridDefinition> = {
  CatchAllCalls: {
    identifier: "CatchAllCalls",
    title: "Catch All cases",
    description: "Any cases that do not fit into any other 'views.'",
    params: {},
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: [],
    },
  },
  "111calls": {
    identifier: "111calls",
    title: "111 cases",
    description: "",
    params: {},
    legacy: true,
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: [],
    },
  },
  CasCalls: {
    identifier: "CasCalls",
    title: "Cas Queue",
    description: "",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=CAS_FILTER_NEW",
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "FOLLOW_UP_Active"],
      columnOrder: DEFAULT_COLUMN_ORDER,
    },
  },
  OverSight: {
    identifier: "OverSight",
    title: "OverSight",
    description: "Base, Visit",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=OVERSIGHT_FOLLOW_UP",
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "FOLLOW_UP_Active"],
      columnOrder: DEFAULT_COLUMN_ORDER,
    },
  },
  OverSightFollowUp: {
    identifier: "OverSightFollowUp",
    title: "OverSight Follow Up",
    description: "Advice",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=OVERSIGHT_FOLLOW_UP_URGENT",
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "BREACH"],
    },
  },
  ClassificationBase: {
    identifier: "ClassificationBase",
    title: "Face to Face",
    description: "",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=OOH_CLASS&CLASS=Base",
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "FOLLOW_UP_Active"],
      columnOrder: DEFAULT_COLUMN_ORDER,
    },
  },
  ClassificationVisit: {
    identifier: "ClassificationVisit",
    title: "Home Visit",
    description: "",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=OOH_CLASS&CLASS=Visit",
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "FOLLOW_UP_Active"],
      columnOrder: DEFAULT_COLUMN_ORDER,
    },
  },
  PLS: {
    identifier: "PLS",
    title: "PLS",
    description: "PLS",
    params: {},
    legacy: true,
    legacyOptions: {
      enabled: true,
      url: baseLegacyUrl + "&vn=PLS",
    },
    colDefintions: {
      onlyIncludeTheseFields: [
        "LOCKED",
        "CALL_PRACTICE",
        "NHS_NO",
        "PATIENT",
        "PATIENT_AGE",
        "DOB",
        "CALL_NO",
        "FURTHER_ACTION",
        "FURTHER_ACTION_GP",
        "PLS_ACTION",
        "PLS",
        "MORE_INFO",
      ],
      excludeTheseFields: [],
      columnOrder: DEFAULT_COLUMN_ORDER,
    },
  },
  PcasCalls: {
    identifier: "PcasCalls",
    title: "PCAS cases",
    description: "",
    params: {},
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: [],
    },
  },
  FcmsCalls: {
    identifier: "FcmsCalls",
    title: "FCMS cases",
    description: "",
    params: {},
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: [],
    },
  },
  NavBase: {
    identifier: "NavBase",
    title: "Base cases",
    description: "",
    params: <IGridParamsBase>{
      DutyBaseId: 0,
    },
    colDefintions: {
      onlyIncludeTheseFields: [],
      excludeTheseFields: ["WARM_TRANSFERRED", "FOLLOW_UP_Active"],
    },
  },
};
