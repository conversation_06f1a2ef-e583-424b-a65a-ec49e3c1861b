<template>
  <div class="dropdown" v-if="hasAnyActionPerms">
    <!--    <button class="dropbtn">-->
    <!--      <div-->
    <!--        class="standard-icon dijit-editor-icon dijit-editor-icon&#45;&#45;paste"-->
    <!--      ></div>-->
    <!--      Actions-->
    <!--    </button>-->
    <Ic24Button title="Actions" class="dropbtn" />
    <div class="dropdown-content">
      <CallSummaryToolbarLink
        v-if="keysPerm[permissionNames.ADD_CONSULTATION]"
        :key-perms="userAppPermsShort"
        :cleo-action="cleoCallActions.ADD_CONSULTATION"
        v-on:onLinkClick="onLinkClick"
      ></CallSummaryToolbarLink>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { ICleoPermission } from "@/permissions/permission-models";
import CallSummaryToolbarLink from "@/calls/summary/call-summary-toolbar/call-summary-toolbar-link.vue";
import { CLEO_CALL_ACTIONS, ICleoAction } from "@/common/cleo-common-models";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import { PERMISSION_NAMES } from "@/permissions/permission-models";
import { PermissionService } from "@/permissions/permission-service";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";

@Component({
  name: "toolbar-widget-actions",
  components: { Ic24Button, CallSummaryToolbarLink, LoadingSpinner }
})
export default class ToolbarWidgetActions extends Vue {
  @Prop({
    default: () => {
      return {};
    }
  })
  public readonly userAppPermsShort!: Record<string, ICleoPermission>;

  public permissionNames = PERMISSION_NAMES;

  public keysPerm: Record<string, boolean> = {};
  public cleoCallActions = CLEO_CALL_ACTIONS;
  public callSummaryController: CallSummaryController = new CallSummaryController();
  public permissionService: PermissionService = new PermissionService();

  public created() {
    this.keysPerm = this.getKeysPerm();
  }

  @Watch("userAppPermsShort")
  public onUserAppPermsShortChanged() {
    this.keysPerm = this.getKeysPerm();
  }

  public getKeysPerm(): Record<string, boolean> {
    if (Object.keys(this.userAppPermsShort).length === 0) {
      return {};
    }
    const permsToCheck = [this.permissionNames.ADD_CONSULTATION];
    return Object.values(permsToCheck).reduce((accum, permName) => {
      const hasPerm = this.userAppPermsShort[permName];
      if (hasPerm) {
        accum[permName] = true;
      }
      return accum;
    }, {} as Record<string, boolean>);
  }

  public get hasAnyActionPerms() {
    return Object.keys(this.keysPerm).length > 0;
  }

  public onLinkClick(cleoAction: ICleoAction) {
    this.callSummaryController.processAction(cleoAction.id, []);
  }
}
</script>
