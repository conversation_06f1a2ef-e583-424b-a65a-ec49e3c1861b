import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export interface BrisDocAuditQuestionsInput {
  cleoClientService: CLEO_CLIENT_SERVICE;
  classification: CALL_CLASSIFICATION;
}

export interface BrisdocAuditQuestionsState {
  input: {
    answers: Record<string, unknown>;
  };
  data: {
    isLoading: boolean;
    questions: Question[];
  };
}
