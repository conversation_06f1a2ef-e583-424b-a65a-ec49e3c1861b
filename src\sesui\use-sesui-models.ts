import { IsoDateTimeWithOffset } from "@/common/common-models";

export interface ISesuiConfig {
  url: string;
  user: string;
  password: string;
  operatorPhoneNumber: string;
  configId: number;
}

export interface ISesuiCommand {
  command: ISesuiCommandPayload;
}

export interface ISesuiCommandPayload {
  command_type:
    | "session_start"
    | "provisioning_request_telephony_services"
    | "session_ping"
    | "call_terminate"
    | "op_update_target"
    | "session_end"
    | "call_start_outdialler";
}

export interface ISesuiCommandPayloadLogin extends ISesuiCommandPayload {
  user: string;
  pass: string;
  config_id: number;
}

export interface ISesuiCommandPayloadSessionEnd extends ISesuiCommandPayload {
  reason: string;
}

export interface ISesuiCommandPayloadMakeCall extends ISesuiCommandPayload {
  operator_id: string;
  phone_number_to_call: string;
}

export interface ISesuiCommandPayloadCallTerminate
  extends ISesuiCommandPayload {
  call_id: number;
  reason: string;
}

export interface ISesuiEvent {
  event: ISesuiEventMessage;
}

export type SesuiEventType =
  | "session_start_result"
  | "call_new_state"
  | "call_end"
  | "op_status";

export interface ISesuiEventMessage {
  event_type: SesuiEventType;
}

export interface ISesuiEventOperatorSpecificMessage extends ISesuiEventMessage {
  operator_id: number;
}

export interface ISesuiEventCallSpecificMessage {
  call_id: number;
}

export interface ISesuiEventMessageNewCallState
  extends ISesuiEventOperatorSpecificMessage,
    ISesuiEventCallSpecificMessage {
  event_type: "call_new_state";
  new_state: 0 | 502 | 200 | 116; // 502 = connecting etc., 200 = conected, 116 = some disconnecting status.
  status_text: string;
}

export interface ISesuiOpStatusMessage
  extends ISesuiEventOperatorSpecificMessage {
  event_type: "op_status";
  customer_id: number;
  name: string;
  email: string;
  login_state: number; //  e.g.  1, etc.  what do they map to?  1 = logged in?
  op_state_code: number; //  no idea.
  op_state_desc: "Waiting" | "Unreachable" | string;
  target: string; //  The telephone number attempting to call?
}

export interface ISesuiCallEndMessage
  extends ISesuiEventMessage,
    ISesuiEventCallSpecificMessage {
  event_type: "call_end";
  call_id: number;
}

export interface ISesuiLoginResult extends ISesuiEventMessage {
  event_type: "session_start_result";
  result_code: number;
  result_text: "Logon success" | string;
  session_id: number;
  authority_table: string;
  authority_id: number;
  config_id: number;
  api_version: string;
}

export interface ISesuiState {
  eventTimes: {
    onopen: IsoDateTimeWithOffset | "";
    onclose: IsoDateTimeWithOffset | "";
    onerror: IsoDateTimeWithOffset | "";
  };
  session_start_result: ISesuiLoginResult | null;
  call_new_state: ISesuiEventMessageNewCallState | null;
  // user: {
  //   session_start_result: ISesuiLoginResult | null;
  // };
  user: number | string; //  The user's id, e.g. 51002367
}

export const broadcastChannelNameForSocketController =
  "sesui-socket-controller";
export const broadcastChannelNameForController = "sesui-telephone-channel";
export type BroadcastActionsForController =
  | "MAKE_CALL"
  | "END_CALL"
  | "FILTER_MESSAGE";

export interface IBroadcastAction {
  action: BroadcastActionsForController;
  payload: unknown;
}

export interface IBroadcastActionMakeCall extends IBroadcastAction {
  payload: {
    telephoneNumber: string;
  };
}

export interface IBroadcastActionEndCall extends IBroadcastAction {
  payload: {
    telephoneNumber: string;
    reason: string;
  };
}

export interface IBroadcastActionFilterMessage extends IBroadcastAction {
  payload: ISesuiEventMessage;
}
