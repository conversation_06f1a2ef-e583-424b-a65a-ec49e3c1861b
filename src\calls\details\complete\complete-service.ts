import {
  CompleteStepName,
  IBaseC<PERSON>rollerState,
  ICompleteControllerState,
  IStep,
  IUserResponse,
  IValidationMessage,
  IVulnerability,
  INonClinicalReason,
  IFailedContactComplete
} from "./complete-models";
import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import { ISimpleButtonInputValue } from "@/calls/details/complete/simple-button-selector-models";

import { factoryUserStepsConfigSaveAndReturnCas } from "@/calls/details/complete/steps-factories/step-factory-cas-save-and-return";
export { factoryUserStepsConfigSaveAndReturnCas };

import { factoryUserStepsEndAssessmentCas } from "@/calls/details/complete/steps-factories/step-factory-cas-end-assessment";
export { factoryUserStepsEndAssessmentCas };

import { factoryUserStepsConfigSaveAndReturn111 } from "@/calls/details/complete/steps-factories/step-factory-111-save-and-return";
export { factoryUserStepsConfigSaveAndReturn111 };

import { factoryUserStepsEndAssessment111 } from "@/calls/details/complete/steps-factories/step-factory-111-end-assessment";
import { IFailedContact } from "@/calls/details/call-details-models";
import { factoryBrisDocNonClinicalAndPrescribingState } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical=and-prescribing-service";
import {
  CALL_CLASSIFICATION_UPPERCASE,
  IsoDateTimeWithOffset
} from "@/common/common-models";
export { factoryUserStepsEndAssessment111 };

export function factoryBaseControllerState(): IBaseControllerState<string> {
  return {
    debug: false,
    isReady: false,
    isLoading: false,
    processName: "",
    currentStep: "",
    validationMessages: [],
    steps: {},
    autoProgress: true,
    isProcessComplete: false,
    hasProcessBeenCancelled: false,
    cacheStepState: false
  };
}

export function factoryCompleteControllerState(): ICompleteControllerState {
  return {
    ...factoryBaseControllerState(),
    currentStep: "END_ASSESSMENT_CONFIRMATION",
    steps: factorySteps(),
    completeUserStepsConfig: {
      steps: {},
      validateMap: {},
      gotoNextMap: {},
      gotoBackMap: {}
    },
    saveAndReturnControllerState: null,
    userResponse: factoryUserResponse() as IUserResponse,
    userOptions: {
      END_ASSESSMENT_CONFIRMATION: {
        options: {
          messages: [
            "Further action required - refer to dispatch - Select cancel and then return to queue.",
            "Maintain within our service - Pass to Clinical Queue - Select cancel  and then return to queue.",
            "No Further Action Required - Select Next and Complete Process."
          ]
        }
      },
      exitReason: {
        options: [
          {
            id: "FAILED_CONTACT",
            description: "Failed Contact",
            value: "FAILED_CONTACT"
          },
          {
            id: "NO_ACTION_TAKEN",
            description: "No Action Taken",
            value: "NO_ACTION_TAKEN"
          },
          {
            id: "FURTHER_ACTION_REQUIRED",
            description: "Further Action Required to consultation",
            value: "FURTHER_ACTION_REQUIRED"
          }
        ],
        forUser: []
      },
      OUTCOMES: {
        overRideKey: ""
      }
    },
    failedContactConfig: {
      config: {
        attemptsRequired: 2,
        minsInterval: 20
      }
    },
    doesPassFailedContactValidation: true,
    dx: {
      usingDx: "",
      isDxValidationRequired: false
    },
    ui: {
      disableBack: false,
      disableNext: false,
      disableCancel: false,
      disableComplete: false,
      buttons: {
        complete: {
          text: "Complete"
        }
      }
    },
    finalAction: ""
  };
}

/**
 * This is the factory for the user response state.
 * TODO - This is a bit of a mess.  Need to refactor this.  What is the purpose of this function
 * TODO ...could be doine inline in the controller.
 * @param completeStepName
 */
export function factoryUserResponse(
  completeStepName?: CompleteStepName
): unknown {
  function genericState(): ISimpleButtonInputValue<"", ""> {
    return { id: "", description: "", value: "" };
  }

  // This is map of the state for each tep
  const factoryStateMap: Record<CompleteStepName, () => unknown> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      return undefined;
    },
    NON_CLINICAL_REASON: (): INonClinicalReason => {
      return {
        reason: genericState(),
        comment: ""
      };
    },
    HOW_WAS_CASE_MANAGED: () => {
      return genericState();
    },
    CONTACT_MADE: () => {
      return genericState();
    },
    FAILED_CONTACT_REASON: () => {
      return genericState();
    },
    PATIENT_REFERRED_TO: () => {
      return {
        referredTo: genericState(),
        referredText: "",
        furtherActionGP: genericState(),
        furtherActionGPText: ""
      };
    },
    READ_CODES: () => {
      return {
        readCodesSelected: []
      };
    },
    OUTCOMES: () => {
      return {
        outcome: "",
        subOutcome: "",
        otherOutcome: "",
        outcomeOptions: {}
      };
    },
    FAILED_CONTACT_SAFEGUARDING: () => {
      return {
        risk: "",
        furtherAction: null,
        welfareAction: ""
      };
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      return {
        completeFinalAction: ""
      };
    },
    PATIENT_RISK_ASSESSMENT: () => {
      return {
        risk: genericState(),
        actionTaken: ""
      };
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      return genericState();
    },
    CLINICAL_VALIDATION: () => {
      return genericState();
    },
    EXIT_REASON: () => {
      return genericState();
    },
    FAILED_CONTACT_WARNING: () => {
      return genericState();
    },
    TAXI: () => {
      return genericState();
    },
    VULNERABILITY: () => {
      const vulnerability: IVulnerability = {
        adult: genericState(),
        child: genericState()
      };
      return vulnerability;
    },
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: () => {
      return factoryBrisDocNonClinicalAndPrescribingState();
    },
    BRISDOC_AUDIT_QUESTIONS: () => {
      return {
        questions: [],
        answers: {},
        isValid: false,
        questionsThatNeedAnswer: []
      };
    },
    FURTHER_ACTION: () => {
      return undefined;
    },
    UNKNOWN: () => {
      return undefined;
    }
  };

  if (completeStepName) {
    return factoryStateMap[completeStepName]();
  }

  //  TODO why returning contactMade instead of CONTACT_MADE!?!?!!?!? Refactor.
  return {
    howManaged: factoryStateMap.HOW_WAS_CASE_MANAGED(),
    contactMade: factoryStateMap.CONTACT_MADE(),
    failedContactReason: factoryStateMap.FAILED_CONTACT_REASON(),
    nonClinicalReason: factoryStateMap.NON_CLINICAL_REASON(),
    patientReferredTo: factoryStateMap.PATIENT_REFERRED_TO(),
    outcomes: factoryStateMap.OUTCOMES(),
    safeguarding: factoryStateMap.FAILED_CONTACT_SAFEGUARDING(),
    patientRiskAssessment: factoryStateMap.PATIENT_RISK_ASSESSMENT(),
    insufficientContactAttempts: factoryStateMap.INSUFFICIENT_CONTACT_ATTEMPTS(),
    clinicalValidation: factoryStateMap.CLINICAL_VALIDATION(),
    exitReason: factoryStateMap.EXIT_REASON(),
    failedContactRiskAssessment: factoryStateMap.FAILED_CONTACT_RISK_ASSESSMENT(),
    failedContactWarning: factoryStateMap.FAILED_CONTACT_WARNING(),
    readCodes: factoryStateMap.READ_CODES(),
    taxi: factoryStateMap.TAXI(),
    vulnerability: factoryStateMap.VULNERABILITY(),
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: factoryStateMap.BRISDOC_NON_CLINICAL_AND_PRESCRIBING(),
    BRISDOC_AUDIT_QUESTIONS: factoryStateMap.BRISDOC_AUDIT_QUESTIONS()
  };
}

export function replaceUserResponse(
  state: ICompleteControllerState,
  completeStepName: CompleteStepName
): void {
  const mapStateProp: Partial<Record<CompleteStepName, keyof IUserResponse>> = {
    HOW_WAS_CASE_MANAGED: "howManaged",
    CONTACT_MADE: "contactMade",
    NON_CLINICAL_REASON: "nonClinicalReason",
    PATIENT_REFERRED_TO: "patientReferredTo",
    OUTCOMES: "outcomes",
    FAILED_CONTACT_REASON: "failedContactReason",
    PATIENT_RISK_ASSESSMENT: "patientRiskAssessment",
    INSUFFICIENT_CONTACT_ATTEMPTS: "insufficientContactAttempts",
    FAILED_CONTACT_SAFEGUARDING: "safeguarding",
    FAILED_CONTACT_RISK_ASSESSMENT: "failedContactRiskAssessment",
    CLINICAL_VALIDATION: "clinicalValidation",
    EXIT_REASON: "exitReason",
    FAILED_CONTACT_WARNING: "failedContactWarning",
    READ_CODES: "readCodes",
    TAXI: "taxi",
    VULNERABILITY: "vulnerability",
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING:
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING",
    BRISDOC_AUDIT_QUESTIONS: "BRISDOC_AUDIT_QUESTIONS",
    FURTHER_ACTION: "patientReferredTo"
  };

  const userResponseProp: keyof IUserResponse | undefined =
    mapStateProp[completeStepName];

  if (!userResponseProp) {
    console.error(
      "replaceUserResponse: completeStepName not found in mapStateProp: " +
        completeStepName
    );
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const userResponse: any = factoryUserResponse(completeStepName!);

  if (userResponse) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    state.userResponse[userResponseProp!] = userResponse;
  }
}

export function factoryStep(
  stepId: CompleteStepName,
  title: string
): IStep<CompleteStepName> {
  return {
    stepId,
    title,
    isValid: false,
    requiresValidation: true,
    validationMessages: []
  };
}

/**
 * this just builds the state for the steps.
 * @see step factories for the logic.
 */
export function factorySteps(): Record<
  CompleteStepName,
  IStep<CompleteStepName>
> {
  const steps: Record<CompleteStepName, IStep<CompleteStepName>> = {
    END_ASSESSMENT_CONFIRMATION: {
      ...factoryStep(
        "END_ASSESSMENT_CONFIRMATION",
        "End Assessment Confirmation"
      ),
      requiresValidation: false
    },
    NON_CLINICAL_REASON: factoryStep(
      "NON_CLINICAL_REASON",
      "Non Clinical Reason"
    ),
    HOW_WAS_CASE_MANAGED: factoryStep(
      "HOW_WAS_CASE_MANAGED",
      "How was Case Managed"
    ),
    CONTACT_MADE: factoryStep(
      "CONTACT_MADE",
      "Has Successful contact been made"
    ),
    PATIENT_REFERRED_TO: factoryStep(
      "PATIENT_REFERRED_TO",
      "Patient Referred To"
    ),
    OUTCOMES: factoryStep("OUTCOMES", "Outcomes"),
    READ_CODES: factoryStep("READ_CODES", "Read Codes"),
    FAILED_CONTACT_REASON: factoryStep(
      "FAILED_CONTACT_REASON",
      "Failed Contact Reason"
    ),
    FAILED_CONTACT_SAFEGUARDING: factoryStep(
      "FAILED_CONTACT_SAFEGUARDING",
      "Safeguarding Review Failed Contact"
    ),
    FAILED_CONTACT_WARNING: factoryStep(
      "FAILED_CONTACT_WARNING",
      "Failed Contact Warning"
    ),
    FAILED_CONTACT_RISK_ASSESSMENT: factoryStep(
      "FAILED_CONTACT_RISK_ASSESSMENT",
      "Failed Contact Risk Assessment"
    ),
    PATIENT_RISK_ASSESSMENT: factoryStep(
      "PATIENT_RISK_ASSESSMENT",
      "Patient Risk Assessment"
    ),
    INSUFFICIENT_CONTACT_ATTEMPTS: factoryStep(
      "INSUFFICIENT_CONTACT_ATTEMPTS",
      "Insufficient Contact Attempts"
    ),
    CLINICAL_VALIDATION: factoryStep(
      "CLINICAL_VALIDATION",
      "Clinical Validation"
    ),
    EXIT_REASON: factoryStep("EXIT_REASON", "Exit Reasons"),
    TAXI: factoryStep("TAXI", "Taxi"),
    VULNERABILITY: factoryStep("VULNERABILITY", "Vulnerability"),
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: factoryStep(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING",
      "Non Clinical and Prescribing"
    ),
    BRISDOC_AUDIT_QUESTIONS: factoryStep(
      "BRISDOC_AUDIT_QUESTIONS",
      "Audit Questions"
    ),
    FURTHER_ACTION: factoryStep("FURTHER_ACTION", "Further Action"),
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
  };
  return steps;
}

/**
 * Validation map for each step.
 * @param state
 * @param completeControllerInput
 */
export function validationMapDefault(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): Record<CompleteStepName, () => IValidationMessage[]> {
  return {
    END_ASSESSMENT_CONFIRMATION: () => {
      return [];
    },
    NON_CLINICAL_REASON: () => {
      const messages: IValidationMessage[] = [];

      if (state.userResponse.nonClinicalReason.reason.id === "") {
        messages.push({
          id: "NON_CLINICAL_REASON__NOT_SELECTED",
          message: "Please select a reason"
        });
      }

      if (
        state.userResponse.nonClinicalReason.reason.id.toUpperCase() ===
          "OTHER" &&
        state.userResponse.nonClinicalReason.comment.length === 0
      ) {
        messages.push({
          id: "NON_CLINICAL_REASON__COMMENT_REQUIRED",
          message: "Please enter a comment"
        });
      }

      return messages;
    },
    HOW_WAS_CASE_MANAGED: () => {
      return state.userResponse.howManaged.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    CONTACT_MADE: () => {
      return state.userResponse.contactMade.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    PATIENT_REFERRED_TO: () => {
      if (state.userResponse.patientReferredTo.referredTo.id === "") {
        return [validationMessageNotSelected()];
      }
      if (
        state.userResponse.patientReferredTo.referredTo.id ===
          "17-Other Secondary Care" &&
        state.userResponse.patientReferredTo.referredText === ""
      ) {
        return [
          {
            id: "PATIENT_REFERRED_TO__OTHER_SECONDARY_CARE_TEXT",
            message:
              "Secondary Care Referral: Other - please provide more details"
          }
        ];
      }
      if (
        state.userResponse.patientReferredTo.furtherActionGP.id !== "" &&
        state.userResponse.patientReferredTo.furtherActionGPText === ""
      ) {
        return [
          {
            id: "PATIENT_REFERRED_TO__FURTHER_ACTION_GP_TEXT",
            message: "Further Action: GP - please provide more details"
          }
        ];
      }
      return [];
    },
    READ_CODES: () => {
      return state.userResponse.readCodes.readCodesSelected.length === 0
        ? [
            {
              id: "READ_CODES__NOT_SELECTED",
              message: "Please select a Read Code"
            }
          ]
        : [];
    },
    OUTCOMES: () => {
      const outcome = state.userResponse.outcomes.outcome;
      const subOutcome = state.userResponse.outcomes.subOutcome;
      const outcomesOptions = state.userResponse.outcomes.outcomeOptions;
      if (outcome.length === 0) {
        return [
          {
            id: "OUTCOMES__SELECT_OUTCOME",
            message: "Please select an outcome"
          }
        ];
      }
      if (outcomesOptions[outcome] && outcomesOptions[outcome].length > 0) {
        if (subOutcome.length === 0) {
          return [
            {
              id: "OUTCOMES__SELECT_SUB_OUTCOME",
              message: "Please select a sub-outcome"
            }
          ];
        }
      }
      if (
        (outcome.toUpperCase() === "OTHER" ||
          subOutcome.toUpperCase() === "OTHER") &&
        state.userResponse.outcomes.otherOutcome.length === 0
      ) {
        return [
          {
            id: "OUTCOMES__SELECT_OUTCOME_OTHER",
            message: "Please enter a description for the outcome"
          }
        ];
      }
      return [];
    },
    FAILED_CONTACT_REASON: () => {
      return state.userResponse.failedContactReason.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    FAILED_CONTACT_WARNING: () => {
      return state.userResponse.failedContactWarning.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    FAILED_CONTACT_SAFEGUARDING: () => {
      return [
        {
          id: "VALIDATION_UNDER_CONSTRUCTION__FAILED_CONTACT_SAFEGUARDING",
          message: "VALIDATION_UNDER_CONSTRUCTION__FAILED_CONTACT_SAFEGUARDING"
        }
      ];
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      return state.userResponse.failedContactRiskAssessment
        .completeFinalAction === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    PATIENT_RISK_ASSESSMENT: () => {
      return state.userResponse.patientRiskAssessment.risk.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      //  user can not move off this step.
      return state.userResponse.insufficientContactAttempts.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    CLINICAL_VALIDATION: () => {
      return state.userResponse.clinicalValidation.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    EXIT_REASON: () => {
      return state.userResponse.exitReason.id === ""
        ? [
            {
              id: "EXIT_REASON__NOT_SELECTED",
              message: "Please select an exit reason"
            }
          ]
        : [];
    },
    TAXI: () => {
      return state.userResponse.taxi.id === ""
        ? [validationMessageNotSelected()]
        : [];
    },
    VULNERABILITY: () => {
      const messages: IValidationMessage[] = [];

      if (state.userResponse.vulnerability.adult.id === "") {
        messages.push({
          id: "NOT_SELECTED",
          message: "Please select an option for Adult Vulnerability"
        });
      }

      if (state.userResponse.vulnerability.child.id === "") {
        messages.push({
          id: "NOT_SELECTED",
          message: "Please select an option for Child Vulnerability"
        });
      }

      return messages;
    },
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: () => {
      const messages: IValidationMessage[] = [];

      const isCasCase =
        completeControllerInput.callDetail.Service.serviceType === "CAS";
      const classification: CALL_CLASSIFICATION_UPPERCASE = completeControllerInput.callDetail.Classification.Description.toUpperCase() as CALL_CLASSIFICATION_UPPERCASE;

      // if (isCasCase || classification.toUpperCase() === "BASE") {
      if (
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .nonClinicalSupportToCompleteCaseRequired === null
      ) {
        messages.push({
          id:
            "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__NON_CLINICAL_SUPPORT_REQUIRED",
          message: "Please select an option for Non Clinical Support Required"
        });
      }
      if (
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .nonClinicalSupportToCompleteCaseRequired &&
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .supportTypeRequired === ""
      ) {
        messages.push({
          id: "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__SUPPORT_TYPE_REQUIRED",
          message: "Please select an option for Support Type Required"
        });
      }
      // }

      if (
        completeControllerInput.callDetail.cleoClientService ===
          "MENTAL_HEALTH" &&
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .mhClinicianSignOffRequired === null
      ) {
        messages.push({
          id:
            "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MENTAL_HEALTH_SIGN_OFF_REQUIRED",
          message: "Mental Health Clinician Sign Off Required"
        });
      }

      // medicationIssuedFromStock
      // if classification is BASE or VISIT
      if (
        !isCasCase &&
        ["BASE", "VISIT"].indexOf(classification.toUpperCase()) > -1
      ) {
        if (
          state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
            .medicationIssuedFromStock === null
        ) {
          messages.push({
            id:
              "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MEDICATION_ISSUED_FROM_STOCK",
            message: "Medication Issued From Stock"
          });
        }
      }

      // if support is OTHER and comments is empty, create error
      if (
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .supportTypeRequired === "OTHER" &&
        state.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING
          .supportTypeRequiredComments === ""
      ) {
        messages.push({
          id:
            "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__SUPPORT_TYPE_REQUIRED_COMMENTS",
          message: "Please enter a comment for Support Type Required"
        });
      }

      return messages;
    },
    BRISDOC_AUDIT_QUESTIONS: () => {
      const messages: IValidationMessage[] = [];
      if (!state.userResponse.BRISDOC_AUDIT_QUESTIONS.isValid) {
        messages.push({
          id: "BRISDOC_AUDIT_QUESTIONS__NOT_VALID",
          message: "Please answer all the questions"
        });
      }
      return messages;
    },
    FURTHER_ACTION: () => {
      const messages: IValidationMessage[] = [];
      if (
        state.userResponse.patientReferredTo.furtherActionGP.id !== "" &&
        state.userResponse.patientReferredTo.furtherActionGPText === ""
      ) {
        messages.push({
          id: "PATIENT_REFERRED_TO__FURTHER_ACTION_GP_TEXT",
          message: "Further Action: GP - please provide more details"
        });
      }
      return messages;
    },
    UNKNOWN: () => {
      return [
        {
          id: "VALIDATION_UNDER_CONSTRUCTION__UNKNOWN",
          message: "VALIDATION_UNDER_CONSTRUCTION__UNKNOWN"
        }
      ];
    }
  };
}

export function validationMessageNotSelected(): IValidationMessage {
  return {
    id: "NOT_SELECTED",
    message: "Please select an option"
  };
}

export function getFailedContactMessage(
  failedContactConfig: IFailedContactComplete,
  failedContacts: IFailedContact[],
  seedTimeIso: IsoDateTimeWithOffset
): string {
  const actualAttempts = failedContacts.length;

  // if (actualAttempts === 0) {
  //   return (
  //     "In accordance with organisation policy, there has only been  " +
  //     actualAttempts +
  //     " of the " +
  //     failedContactConfig.config.attemptsRequired +
  //     " required attempts for this patient"
  //   );
  // }

  const haveEnoughAttemptsBeenMade =
    actualAttempts >= failedContactConfig.config.attemptsRequired;

  if (haveEnoughAttemptsBeenMade) {
    return (
      "In accordance with organisation policy, there has been the required " +
      failedContactConfig.config.attemptsRequired +
      " attempts for this patient"
    );
  }

  //  Was an attempt made at the last interval older than the required interval
  //  Get the failed contact with the newest attempt time.

  let isOlderThanInterval = false;

  if (actualAttempts > 0) {
    const newestFailedContact = getNewestFailedContact(failedContacts);
    isOlderThanInterval = isFailedContactOlderThanInterval(
      newestFailedContact,
      failedContactConfig.config.minsInterval,
      seedTimeIso || ""
    );
  }

  if (isOlderThanInterval) {
    return (
      "In accordance with organisation policy, there has only been  " +
      actualAttempts +
      " of the " +
      failedContactConfig.config.attemptsRequired +
      " required attempts for this patient"
    );
  }
  return (
    "In accordance with organisation policy, there has only been  " +
    actualAttempts +
    " of the " +
    failedContactConfig.config.attemptsRequired +
    " required attempts for this patient and the last attempt was less than " +
    failedContactConfig.config.minsInterval +
    " minutes ago."
  );
}

/**
 * Got to have tried at least x times and the last attempt must be older than y mins
 * @param state
 * @param failedContacts
 * @param seedTimeIso
 */
export function doesPassFailedContactValidation(
  state: ICompleteControllerState,
  failedContacts: IFailedContact[],
  seedTimeIso?: string
): boolean {
  const haveEnoughAttemptsBeenMade =
    failedContacts.length >= state.failedContactConfig.config.attemptsRequired;

  if (!haveEnoughAttemptsBeenMade) {
    return false;
  }

  // return haveEnoughAttemptsBeenMade;

  //  Was an attempt made at the last interval older than the required interval
  //  Get the failed contact with the newest attempt time.
  const newestFailedContact = getNewestFailedContact(failedContacts);

  return isFailedContactOlderThanInterval(
    newestFailedContact,
    state.failedContactConfig.config.minsInterval,
    seedTimeIso || ""
  );
}

export function isFailedContactOlderThanInterval(
  failedContact: IFailedContact,
  intervalMins: number,
  seedTimeIso: string
): boolean {
  const seedTime = seedTimeIso.length > 0 ? new Date(seedTimeIso) : new Date();
  const failedContactTime = new Date(failedContact.time);
  const diff = seedTime.getTime() - failedContactTime.getTime();
  const diffMins = diff / (1000 * 60);
  return diffMins > intervalMins;
}

export function getNewestFailedContact(
  failedContacts: IFailedContact[]
): IFailedContact {
  if (failedContacts.length === 1) {
    return failedContacts[0];
  }

  const newest = failedContacts.reduce((acc, current) => {
    return acc.time > current.time ? acc : current;
  }, failedContacts[0]);

  return newest;
}
