import { <PERSON><PERSON><PERSON>x<PERSON>, Module } from "vuex";
import { IRootState } from "@/store/store";
import {
  IPathwaysDataset,
  IPathwaysDatasetContinue,
  IPathwaysDatasetLegacy,
  ITriageRecord,
  ITriageRecordCondition
} from "@/paccs/paccs-models";
import { IAddCaseRecordResponse, PaccsData } from "@/paccs/paccs-data";
import { PaccsService } from "@/paccs/paccs-service";
import { PaccsLegacyFormController } from "@/paccs/paccs-legacy-form-controller";
import {
  IPathwaysEarlyExitPayload,
  IPathwaysJumpToPayload
} from "@/paccs/pathways/pathways-models";
import { CommonService } from "@/common/common-service";

const paccsData: PaccsData = new PaccsData();
const paccsService: PaccsService = new PaccsService();
const commonService: CommonService = new CommonService();

const paccsLegacyFormController = new PaccsLegacyFormController();

export type FORM_SECTION = "PACCS" | "PATHWAYS" | "PATHWAYS_REPORT" | "NONE";

export interface IPaccsStoreState {
  showSection: FORM_SECTION;
  addCaseRecordResponse: IAddCaseRecordResponse;
  triageRecords: ITriageRecord[];
  pathwaysDataset: IPathwaysDataset;
  pathwaysJumpToId: string;
  pathwaysPayload: IPathwaysJumpToPayload | IPathwaysEarlyExitPayload;
  reportTriageClicked: ITriageRecord;
  pathways: {
    showMask: boolean;
    showMaskMessage: string;
  };
}

export type TriageRecordConditionUpdate = Pick<
  ITriageRecordCondition,
  "pathwayId" | "quId" | "userComment"
>;

export enum PACCS_STORE_CONST {
  PACCS__CONST_MODULE_NAME = "PACCS__CONST_MODULE_NAME",

  PACCS__MUTATION_SET_ADD_CASE_RECORD = "PACCS__MUTATION_SET_ADD_CASE_RECORD",
  PACCS__MUTATION_SET_PATHWAYS_DATASET = "PACCS__MUTATION_SET_PATHWAYS_DATASET",
  PACCS__MUTATION_SET_PACCS_QUESTION = "PACCS__MUTATION_SET_PACCS_QUESTION",
  PACCS__MUTATION_UPDATE_PACCS_QUESTION_SPECIFY = "PACCS__MUTATION_UPDATE_PACCS_QUESTION_SPECIFY",
  PACCS__MUTATION_SET_FORM_SECTION = "PACCS__MUTATION_SET_FORM_SECTION",
  PACCS__MUTATION_SET_PATHWAYS_JUMP_TO_ID = "PACCS__MUTATION_SET_PATHWAYS_JUMP_TO_ID",
  PACCS__MUTATION_SET_PATHWAYS_EARLY_EXIT = "PACCS__MUTATION_SET_PATHWAYS_EARLY_EXIT",
  PACCS__MUTATION_SET_PATHWAYS_PAYLOAD = "PACCS__MUTATION_SET_PATHWAYS_PAYLOAD",
  PACCS__MUTATION_RESET_CASE_RECORD = "PACCS__MUTATION_RESET_CASE_RECORD",
  PACCS__MUTATION_SET_REPORT_TRIAGE_CLICKED = "PACCS__MUTATION_SET_REPORT_TRIAGE_CLICKED",
  PACCS__MUTATION_SET_PATHWAYS_MASK = "PACCS__MUTATION_SET_PATHWAYS_MASK",

  PACCS__ACTION_INIT_PACCS = "PACCS__ACTION_INIT_PACCS",
  PACCS__ACTION_ADD_TRIAGE_QUESTION = "PACCS__ACTION_ADD_TRIAGE_QUESTION",
  PACCS__ACTION_ADD_QUESTION_SPECIFY = "PACCS__ACTION_ADD_QUESTION_SPECIFY",
  PACCS__ACTION_JUMP_TO_ID_PATHWAYS = "PACCS__ACTION_JUMP_TO_ID_PATHWAYS",
  PACCS__ACTION_RESET = "PACCS__ACTION_RESET",
  PACCS__ACTION_EARLY_EXIT = "PACCS__ACTION_EARLY_EXIT",
  PACCS__ACTION_POST_NOTES = "PACCS__ACTION_POST_NOTES"
}

const mutations = {
  [PACCS_STORE_CONST.PACCS__MUTATION_SET_ADD_CASE_RECORD](
    state: IPaccsStoreState,
    addCaseRecordResponse: IAddCaseRecordResponse
  ): void {
    state.addCaseRecordResponse = Object.assign({}, addCaseRecordResponse);
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_DATASET](
    state: IPaccsStoreState,
    pathwaysDataset: IPathwaysDataset
  ): void {
    state.pathwaysDataset = Object.assign({}, pathwaysDataset);
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PACCS_QUESTION](
    state: IPaccsStoreState,
    paccsQuestion: ITriageRecord
  ): void {
    //  IMPORTANT!!! Since going in time order, newest first, oldest last.
    state.triageRecords.push(paccsQuestion);
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_UPDATE_PACCS_QUESTION_SPECIFY](
    state: IPaccsStoreState,
    triageRecordConditionUpdate: TriageRecordConditionUpdate
  ): void {
    let foundMatch = false;
    for (let index = state.triageRecords.length - 1; index >= 0; index--) {
      const element = state.triageRecords[index];
      if (
        element.pathwayId === triageRecordConditionUpdate.pathwayId &&
        element.quId === triageRecordConditionUpdate.quId
      ) {
        if (!foundMatch) {
          element.userComment = triageRecordConditionUpdate.userComment;
          foundMatch = true;
        }
      }
    }
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION](
    state: IPaccsStoreState,
    showSection: FORM_SECTION
  ): void {
    state.showSection = showSection;
    if (showSection === "PATHWAYS") {
      //  Pathways starting, so whatever user has entered, push into following "legacy" form field.
      // const messageHostSystem = paccsService.getOutputForHostSystem(
      //   state.triageRecords
      // );
      //
      // //  "Presenting complaint"
      // const chubPresenting = window.document.getElementById(
      //   "CHUB_Presenting"
      // ) as HTMLTextAreaElement;
      // if (chubPresenting) {
      //   chubPresenting.value =
      //     chubPresenting.value +
      //     (messageHostSystem.length > 0 ? " PACCS: " + messageHostSystem : "");
      // }
    }

    paccsLegacyFormController.pathwaysHideLegacyDomElements(
      showSection === "PATHWAYS"
    );
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_JUMP_TO_ID](
    state: IPaccsStoreState,
    payload: IPathwaysJumpToPayload
  ): void {
    state.pathwaysJumpToId = payload.pwId;
    state.pathwaysPayload = payload;

    state.pathways.showMask = false;
    state.pathways.showMaskMessage = "";
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_EARLY_EXIT](
    state: IPaccsStoreState
  ): void {
    const pathwaysEarlyExitPayload: IPathwaysEarlyExitPayload = {
      entryType: "EARLYEXIT",
      caseId: state.addCaseRecordResponse.caseId,
      skillset: 14
    };
    state.pathwaysPayload = pathwaysEarlyExitPayload;
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_PAYLOAD](
    state: IPaccsStoreState,
    payload: IPathwaysJumpToPayload | IPathwaysEarlyExitPayload
  ): void {
    state.pathwaysPayload = payload;
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_RESET_CASE_RECORD](
    state: IPaccsStoreState
  ): void {
    state.triageRecords = [];
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_REPORT_TRIAGE_CLICKED](
    state: IPaccsStoreState,
    triageRecord: ITriageRecord
  ): void {
    state.reportTriageClicked = Object.assign({}, triageRecord);
  },

  [PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_MASK](
    state: IPaccsStoreState,
    payload: {
      showMask: boolean;
      showMaskMessage: string;
    }
  ): void {
    state.pathways.showMask = payload.showMask;
    state.pathways.showMaskMessage = payload.showMaskMessage;
  }
};

const getters = {};

const actions = {
  [PACCS_STORE_CONST.PACCS__ACTION_INIT_PACCS](
    context: ActionContext<IPaccsStoreState, IRootState>
  ): Promise<any> {
    //  Promise here as when switch in to new app
    return Promise.resolve()
      .then(() => {
        const pathwaysDatasetLegacy: IPathwaysDatasetLegacy = window.CallControllerClient.createNHSPathwaysDataSet();
        const pathwaysDataSet: IPathwaysDataset = paccsService.mapPathwaysDatasets(
          pathwaysDatasetLegacy
        );
        context.commit(
          PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_DATASET,
          pathwaysDataSet
        );
        return pathwaysDataSet;
      })
      .then(pathwaysDataSet => {
        let prom;

        //  The value that came back from this may be different due to rehydration hacks.
        const pathwaysCaseId: string = paccsLegacyFormController.getPaccsPathwaysCaseIdFromCall();
        paccsLegacyFormController.setPathwaysCaseIdOnCallForm(pathwaysCaseId);

        //  If another user has done a PW, "continue" with the same CaseId.
        //  We still had to call api to get the "jump" ID data.
        if (pathwaysCaseId.toString().length > 0) {
          const pathwaysDatasetContinue = (pathwaysDataSet as unknown) as IPathwaysDatasetContinue;
          pathwaysDatasetContinue.caseId = pathwaysCaseId;
          prom = paccsData.restartCaseRecord(pathwaysDatasetContinue);
        } else {
          prom = paccsData.addCaseRecord(pathwaysDataSet);
        }

        return prom.then(addCaseRecordResponse => {
          const pathwaysCaseId: string = CallControllerClient.getFieldValue(
            "PathwaysCaseId"
          ) as string;
          if (pathwaysCaseId && pathwaysCaseId.toString().length > 0) {
            addCaseRecordResponse.caseId = pathwaysCaseId;
          }

          context.commit(
            PACCS_STORE_CONST.PACCS__MUTATION_SET_ADD_CASE_RECORD,
            addCaseRecordResponse
          );
          paccsLegacyFormController.setPaccsHasStarted(true, pathwaysCaseId);
        });
      });
  },

  [PACCS_STORE_CONST.PACCS__ACTION_ADD_TRIAGE_QUESTION](
    context: ActionContext<IPaccsStoreState, IRootState>,
    triageRecord: ITriageRecord
  ): Promise<any> {
    return paccsData.submitTriageRecord(triageRecord).then(() => {
      context.commit(
        PACCS_STORE_CONST.PACCS__MUTATION_SET_PACCS_QUESTION,
        triageRecord
      );
    });
  },

  [PACCS_STORE_CONST.PACCS__ACTION_ADD_QUESTION_SPECIFY](
    context: ActionContext<IPaccsStoreState, IRootState>,
    triageRecordConditionUpdate: TriageRecordConditionUpdate
  ): Promise<any> {
    let foundMatch = false;

    context.commit(
      PACCS_STORE_CONST.PACCS__MUTATION_UPDATE_PACCS_QUESTION_SPECIFY,
      triageRecordConditionUpdate
    );

    let triageRecord: ITriageRecord | null = null;
    for (
      let index = context.state.triageRecords.length - 1;
      index >= 0;
      index--
    ) {
      const element = context.state.triageRecords[index];
      if (
        element.pathwayId === triageRecordConditionUpdate.pathwayId &&
        element.quId === triageRecordConditionUpdate.quId
      ) {
        if (!foundMatch) {
          triageRecord = commonService.simpleObjectClone(element);
          foundMatch = true;
        }
      }
    }

    if (triageRecord) {
      triageRecord.reportText += triageRecord.userComment
        ? " - " + triageRecord.userComment
        : "";
      return paccsData.submitTriageRecord(triageRecord).then(() => {
        context.commit(
          PACCS_STORE_CONST.PACCS__MUTATION_SET_PACCS_QUESTION,
          triageRecord
        );
      });
    }
    return Promise.resolve();
  },

  [PACCS_STORE_CONST.PACCS__ACTION_JUMP_TO_ID_PATHWAYS](
    context: ActionContext<IPaccsStoreState, IRootState>,
    payload: IPathwaysJumpToPayload
  ): void {
    CallControllerClient.submitDocumentPromise(
      CallControllerClient.m_call_id,
      false
    ).then(() => {
      // payload: { pathwaysJumpToId: string; symptomGroup: null | number }

      //  Required by legacy code, it's looking for perms when writing back to call.
      CallControllerClient.initPathwaysPermissions();

      context.commit(
        PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_JUMP_TO_ID,
        payload
      );
      context.commit(
        PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
        "PATHWAYS"
      );

      // const messageHostSystem = paccsService.getOutputForHostSystem(
      //   context.state.triageRecords
      // );
      //
      // //  "Presenting complaint"
      // const chubPresenting = window.document.getElementById(
      //   "CHUB_Presenting"
      // ) as HTMLTextAreaElement;
      // if (chubPresenting) {
      //   chubPresenting.value =
      //     chubPresenting.value +
      //     (messageHostSystem.length > 0 ? " PACCS: " + messageHostSystem : "");
      // }
    });
  },

  [PACCS_STORE_CONST.PACCS__ACTION_RESET](
    context: ActionContext<IPaccsStoreState, IRootState>
  ): Promise<any> {
    const caseId = context.state.addCaseRecordResponse.caseId;
    return paccsData.restartTriage(caseId).then(() => {
      context.commit(PACCS_STORE_CONST.PACCS__MUTATION_RESET_CASE_RECORD);
      context.commit(
        PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
        "PACCS"
      );
      return;
    });
  },

  [PACCS_STORE_CONST.PACCS__ACTION_EARLY_EXIT](
    context: ActionContext<IPaccsStoreState, IRootState>
  ): void {
    context.commit(PACCS_STORE_CONST.PACCS__MUTATION_SET_PATHWAYS_EARLY_EXIT);
    context.commit(
      PACCS_STORE_CONST.PACCS__MUTATION_SET_FORM_SECTION,
      "PATHWAYS"
    );
  },

  [PACCS_STORE_CONST.PACCS__ACTION_POST_NOTES](
    context: ActionContext<IPaccsStoreState, IRootState>,
    caseNotes: string
  ): Promise<unknown> {
    const caseId = context.state.addCaseRecordResponse.caseId;
    return paccsData.postNotes(caseId, caseNotes);
  }
};

export const paccsStore: Module<IPaccsStoreState, IRootState> = {
  namespaced: true,
  state: paccsService.factoryPaccsStoreState(),
  mutations,
  getters,
  actions
};
