import { IConfigStoreState } from "@/common/config/config-store";

export class ConfigService {
  public factoryConfigStoreState(): IConfigStoreState {
    return {
      debug: false,
      isLocalDevServer: process.env.NODE_ENV === "development",
      legacyGridResize: {
        timeIso: ""
      },
      adapterCleoAction: {
        payload: {
          actionType: "",
          data: null
        }
      },
      priorityDxCodes: ["DX333", "DX334", "DX337", "DX338"],
      failedContactWarnMinutes: {
        urgent: 15,
        notUrgent: 15
      },
      splitPanes: {
        gridContentDefault: {
          size: 95,
          minSize: 50
        },
        callSummaryDefault: {
          size: 5,
          minSize: 5,
          maxSize: 75
        },
        panes: []
      }
    };
  }
}
