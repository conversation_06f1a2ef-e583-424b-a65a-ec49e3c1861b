<template>
  <div>
<!--    BASES: {{bases}}-->
    <div v-for="base in bases" :key="base.Id">
<!--      >>>{{base}} ===<br>-->

      <MapBaseTable :base="base" v-on:baseClicked="baseClicked"></MapBaseTable>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  PropType
} from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IBaseSummary } from "@/bases/base-models";
import MapBaseTable from "@/calls/maps/bases/map-base.vue";
const commonService: CommonService = new CommonService();

export default defineComponent({
  // type inference enabled
  name: "map-bases-table",
  components: { MapBaseTable },
  props: {
    bases: {
      default: () => {
        return [];
      },
      type: Array as PropType<IBaseSummary[]>
    }
  },
  setup(props: { bases: IBaseSummary[] }, context: SetupContext) {
    function baseClicked(base: IBaseSummary) {
      context.emit("baseClicked", base);
    }
    return {
      baseClicked
    };
  }
});
</script>

<style scoped>

</style>
