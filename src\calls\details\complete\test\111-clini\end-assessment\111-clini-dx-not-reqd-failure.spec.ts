import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import { factoryCallDetail } from "@/calls/details/call-detail-service";
import * as CallDetailService from "@/calls/details/call-detail-service";

describe("111 Clini useCompleteController Dx validation REQUIRED", () => {
  /**
   *
   */
  it("111 Clini contact MADE flow", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx111",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const caseDx = callDetail.dx.clini.dxCode || callDetail.dx.ch.dxCode;

    expect(caseDx).toBe("Dx111");

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: CallDetailService.factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    completeControllerInput.callDetail.failedContacts = [
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T04:29:46"
      },
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T05:29:46"
      }
    ];

    //  Even though failed contacts...this is contact made route
    completeControllerInput.failedContactConfig.config.seedTime =
      "2024-06-28T05:39:46";

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.processName).toBe("COMPLETE_111_CLINICAL");

    expect(controller.state.dx.isDxValidationRequired).toBe(false);

    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_FAILURE",
      description: "Contact Failure",
      value: "CONTACT_FAILURE"
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_RISK_ASSESSMENT");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_RISK_ASSESSMENT");

    /*
    controller.onOutcomesSelected({
      outcome: "Other",
      subOutcome: "",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME_OTHER"
    );

    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    expect(controller.state.currentStep).toBe("OUTCOMES");
    */

    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_RISK_ASSESSMENT");

    // controller.goto("NEXT");
    // expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    // expect(controller.state.validationMessages.length).toBe(1);
    // expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    //  Can't go back from here
    // controller.goto("BACK");
    // expect(controller.state.currentStep).toBe("OUTCOMES");
    // controller.goto("NEXT");
    // expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    // expect(controller.state.ui.disableBack).toBe(false);

    // controller.onInsufficientContactAttemptTypeSelected({
    //   id: "SAVE_AND_RETURN",
    //   description: "Save and Return to Queue",
    //   value: "SAVE_AND_RETURN"
    // });

    // expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    // expect(controller.state.isProcessComplete).toBe(true);
  });
});
