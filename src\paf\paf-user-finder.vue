<template>
  <input v-model="postCodeInternal" v-on:keyup="debounceSearch" />
</template>

<script lang="ts">
import {
  defineComponent,
  watch,
  ref,
  SetupContext
} from "@vue/composition-api";
import { PafData } from "@/paf/paf-data";
import { IPafAddress } from "@/paf/paf-models";
import { ILegacyDojoResponse } from "@/common/cleo-legacy-models";
import { debounce } from "@/common/debounce";
import { PafService } from "@/paf/paf-service";
export default defineComponent({
  name: "paf-user-finder",
  components: {},
  props: {
    postCode: {
      type: String,
      default: ""
    }
  },
  setup(props: { postCode: string }, context: SetupContext) {
    const isLoading = ref(false);
    const postCodeInternal = ref("");
    const pafData = new PafData();
    const pafAddresses = ref<IPafAddress[]>([]);
    const pafService = new PafService();

    watch(
      () => props.postCode,
      (newValue: string) => {
        if (postCodeInternal.value !== newValue) {
          postCodeInternal.value = newValue;
          getAddresses();
        }
      }
    );

    const debounceSearch = debounce(() => {
      getAddresses();
    }, 250);

    function getAddresses() {
      const isValid = pafService.isValidPostCode(postCodeInternal.value);
      if (!isValid) {
        return;
      }
      isLoading.value = true;
      context.emit("isLoading", isLoading.value);
      pafData
        .getPafData(postCodeInternal.value)
        .then(resp => {
          //  TODO   fix this cack
          pafAddresses.value = ((resp.DATA as any)[0] as ILegacyDojoResponse<
            IPafAddress[]
          >).items;
          context.emit("onSearchFinished", pafAddresses.value);
        })
        .finally(() => {
          isLoading.value = false;
          context.emit("isLoading", isLoading.value);
        });
    }

    return {
      postCodeInternal,
      debounceSearch,
      pafAddresses,
      isLoading
    };
  }
});
</script>
