<template>
  <input
    v-model="selectedValue"
    v-on:keyup="debounceOnChange"
    :placeholder="placeHolder"
  />
</template>

<script lang="ts">
import { defineComponent, ref } from "@vue/composition-api";
import { debounce } from "@/common/debounce";
import { loggerInstance } from "@/common/Logger";

export default defineComponent({
  name: "input-debounce",
  props: {
    value: {
      type: String,
      default: ""
    },
    placeHolder: {
      type: String,
      default: ""
    },
    debounceTime: {
      type: Number,
      default: 250
    }
  },
  setup(props, { emit }) {
    const selectedValue = ref(props.value);

    const debounceOnChange = debounce(() => {
      loggerInstance.log(
        "InputDebounce.debounceOnChange()...selectedValue: " +
          selectedValue.value
      );
      onChange();
    }, props.debounceTime);

    function onChange(): void {
      emit("input", selectedValue.value);
      emit("onChange", selectedValue.value);
    }

    return {
      selectedValue,
      debounceOnChange
    };
  }
});
</script>
