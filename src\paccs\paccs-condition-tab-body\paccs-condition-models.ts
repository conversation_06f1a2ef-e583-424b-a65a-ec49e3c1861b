export interface IPaccsConditionOrder {
  orderNo: number;
  condition: IPaccsCondition;
}

export type PaccsConditionId = string;

export interface IPaccsCondition {
  conditionId: string; //  E.g. "Cn000002";
  conditionName: string; //  E.g. "Poor Nutritional Status";
  importance: number; //  E.g. 2;
  ambJump: string; //  E.g. "PW1848.0";
  homeMgmtJump: string; //  E.g. "PW1849.0";
  servicesJump: string; //  E.g. "PW1846.0";
  etcJump: string; //  E.g. "PW1847.0";
  gender: "male" | "female"; //  E.g. "male|female"   different values to PaccsGender
  age: "adult" | "child" | "toddler" | "infant"; //  E.g. "adult|child|toddler|infant"; different values to  PaccsAgeGroup
  conditionSupportingInformation: IPaccsConditionSupportingInformation[];
}

export interface IPaccsConditionSupportingInformation {
  conditionId: string; //  E.g. "Cn010010";
  categoryTitle: string; //  E.g. "Overview";
  categoryId: string; //  E.g. "O";
  orderNo: number; //  E.g. 1;
  text: string; //  HTML
}

export interface IPaccsConditionUserDataEntry {
  considered: boolean;
  suspected: boolean;
  specify: string;
}
