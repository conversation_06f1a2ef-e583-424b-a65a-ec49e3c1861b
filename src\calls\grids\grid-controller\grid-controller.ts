import {
  ICleoCallSummary,
  IS<PERSON><PERSON><PERSON><PERSON>ger,
  ISimpleTriggerData
} from "@/calls/summary/call-summarry-models";
import {
  GridCallsData,
  IAdapterQueueParams
} from "@/calls/grids/grid-calls-data";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CommonService } from "@/common/common-service";
import { loggerInstance } from "@/common/Logger";

import { format, parseISO } from "date-fns";
import {
  GRID_FILTER_NAME,
  IGridFilter,
  IGridFilterUserInput
} from "@/calls/grids/grid-filter/grid-filter-models";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";
import { IAgGridFilter } from "@/calls/grids/ag-grid-specific/ag-grid-models";
import {
  IAdapterPagedResponse,
  IsoDateTimeWithOffset
} from "@/common/common-models";
import { SOCKET_ACTIONS, SocketActionType } from "@/socket/socket-controller";
import { StackCollection } from "@/common/collections/StackCollection";
import { GridService } from "@/calls/grids/grid-service";
import { ICleoPermission } from "@/permissions/permission-models";

const commonService: CommonService = new CommonService();
const gridService: GridService = new GridService();

export interface IGridControllerConfig {
  viewTitle: string;
  divTimer: string;
  adapterQueueParams: IAdapterQueueParams;
  breachRefreshRateMs: number;
  longPollingRefreshRateMs: number | -1; //  -1 = don't long poll
  allowMultiSelect: boolean;
  stackCollectionMessagesCount: number;
}

export interface IGridControllerStackMessage {
  id: string;
  time: IsoDateTimeWithOffset;
  callNo: number;
  message: string;
  messageType: SocketActionType;
  serviceName: string;
}

export interface IGridControllerState {
  simpleTriggerLongPoll: ISimpleTrigger<unknown>;
  simpleTriggerBreach: ISimpleTrigger<unknown>;
  socketLoadingTrigger: ISimpleTrigger<{ timeIso: IsoDateTimeWithOffset }>;
  isLoading: boolean;
  longPollingCounter: number;
  gridResponse: IAdapterPagedResponse<ICleoCallSummary>;
  gridData: ICleoCallSummary[];
  gridDataExported: ICleoCallSummary[];
  newCalls: ICleoCallSummary[];
  updatedCalls: ICleoCallSummary[];
  removedCalls: ICleoCallSummary[];
  lastUpdatedHumanReadTime: string;
  lastLongPollHumanReadTime: string;
  lastSocketMessageHumanReadTime: string;
  simpleGetData: ISimpleTrigger<unknown>;
  selectedCall: ICleoCallSummary;
  isLoadingPermsForSelectedCall: boolean;
  permsForSelectedCall: Record<string, ICleoPermission>;
  selectedCalls: ICleoCallSummary[];
  quickFilterText: ISimpleTriggerData<string>;
  filters: IGridFilter[];
  showFilterDialog: boolean;
  filterUserInput: IGridFilterUserInput;
  agFilters: IAgGridFilter;
  callGridCount: number;
  callFilterCount: number;
  stackCollectionMessages: StackCollection<IGridControllerStackMessage>;
  stackMessages: IGridControllerStackMessage[];
  showStackMessages: boolean;
  rightClick: {
    showMenu: boolean;
    coords: {
      x: number;
      y: number;
    };
  };
}

export class GridController {
  public callSummaryService: CallSummaryService = new CallSummaryService();
  public gridFilterService: GridFilterService = new GridFilterService();

  public config: IGridControllerConfig = {
    viewTitle: "",
    divTimer: "",
    adapterQueueParams: {
      PageNumber: 1,
      RecordsPerPage: 100,
      QueryName: "",
      Criteria: {}
    },
    breachRefreshRateMs:
      process.env.NODE_ENV === "development" ? 60_000 : 3_000,
    longPollingRefreshRateMs:
      process.env.NODE_ENV === "development" ? 3_000_000 : 3_000_000, //  TODO for production?
    allowMultiSelect: false,
    stackCollectionMessagesCount: 100
  };

  public SUFFIX = Math.random()
    .toString(36)
    .substring(2);

  public state: IGridControllerState = {
    simpleTriggerLongPoll: { timeIso: "" },
    simpleTriggerBreach: { timeIso: "" },
    socketLoadingTrigger: { timeIso: "" },
    isLoading: false,
    longPollingCounter: 0,
    gridResponse: this.callSummaryService.factoryAdapterGridResponse(),
    gridData: [],
    gridDataExported: [],
    newCalls: [],
    updatedCalls: [],
    removedCalls: [],
    lastUpdatedHumanReadTime: "",
    lastLongPollHumanReadTime: "",
    lastSocketMessageHumanReadTime: "",
    simpleGetData: { timeIso: "" },
    selectedCall: this.callSummaryService.factoryCleoCallSummary(),
    isLoadingPermsForSelectedCall: false,
    permsForSelectedCall: {},
    selectedCalls: [],
    quickFilterText: { timeIso: "", data: "" },
    filters: [],
    filterUserInput: new GridFilterService().factoryGridFilterUserInput(),
    showFilterDialog: false,
    agFilters: {},
    callGridCount: 0,
    callFilterCount: 0,
    stackCollectionMessages: new StackCollection<IGridControllerStackMessage>(
      this.config.stackCollectionMessagesCount
    ),
    stackMessages: [],
    showStackMessages: false,
    rightClick: {
      showMenu: false,
      coords: {
        x: 0,
        y: 0
      }
    }
  };

  private breachRecalcTimer: any;
  private longPollingTimer: any;

  public init(): void {
    if (this.config.longPollingRefreshRateMs > -1) {
      // this.getData().then(() => {
      //   this.longPolling();
      //   this.recalcBreach();
      // });
      this.longPolling();
      this.recalcBreach();
      return;
    }
    this.recalcBreach();
  }

  public getData(): Promise<void> {
    loggerInstance.log(
      "GridController.getData() DEPRECATED???  Join socket group."
    );
    this.state.isLoading = true;
    return new GridCallsData()
      .getQueueApi(this.config.adapterQueueParams)
      .then((response: IAdapterPagedResponse<ICleoCallSummary>) => {
        loggerInstance.log(
          "GridController.getData() DEPRECATED???  Join socket group."
        );
      })
      .catch(err => {
        console.log("Error", err);
      })
      .finally(() => {
        this.state.isLoading = false;
      });
  }

  public setData(
    adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary>
  ): void {
    const responseNew = commonService.simpleObjectClone(adapterPagedResponse);
    responseNew.Records = gridService.filterAdapterPagedResponse(
      adapterPagedResponse
    );

    this.state.gridResponse = responseNew;
    this.state.gridData = responseNew.Records;

    this.state.simpleGetData = { timeIso: new Date().toISOString() };

    this.state.lastLongPollHumanReadTime = format(
      parseISO(new Date().toISOString()),
      "HH:mm:ss"
    );
    this.state.lastUpdatedHumanReadTime = this.state.lastLongPollHumanReadTime;
  }

  public getLastLongPollReloaded(): string {
    return this.state.lastLongPollHumanReadTime;
  }

  public setFilters(filters: IGridFilter[]): void {
    this.state.filters = filters;
    this.applyFilters();
  }

  public applyFilters(): void {
    if (this.state.filters.length === 0) {
      this.state.gridData = commonService.simpleObjectClone(
        this.state.gridResponse.Records
      );
      return;
    }
    const gridData = this.gridFilterService.applyFilters(
      this.state.gridResponse.Records,
      this.state.filters
    );
    this.state.gridData = commonService.simpleObjectClone(gridData);
  }

  public newCalls(cleoCallSummaries: ICleoCallSummary[]): void {
    this.state.newCalls = cleoCallSummaries;
    this.setLastSocketMessage(cleoCallSummaries, SOCKET_ACTIONS.ON_CASE_NEW);
  }

  public updateCalls(cleoCallSummaries: ICleoCallSummary[]): void {
    this.state.updatedCalls = cleoCallSummaries;
    this.setLastSocketMessage(
      cleoCallSummaries,
      SOCKET_ACTIONS.ON_CASE_UPDATED
    );
  }

  public removeCalls(cleoCallSummaries: ICleoCallSummary[]): void {
    this.state.removedCalls = cleoCallSummaries;
    this.setLastSocketMessage(
      cleoCallSummaries,
      SOCKET_ACTIONS.ON_CASE_REMOVED
    );
  }

  // public canTimerTasksContinue(): boolean {
  //   return !!document.getElementById(this.config.divTimer);
  // }

  public recalcBreach(): void {
    this.breachRecalcTimer = window.setTimeout(() => {
      clearTimeout(this.breachRecalcTimer);

      loggerInstance.log("GridController.recalcBreach()...a at: " + new Date());
      //  The grid is "watching" this
      this.state.simpleTriggerBreach = { timeIso: new Date().toISOString() };
      this.recalcBreach();
    }, this.config.breachRefreshRateMs);
  }

  public longPolling(): void {
    loggerInstance.log("GridController.longPolling()...a at: " + new Date());

    // if (!this.canTimerTasksContinue()) {
    //   loggerInstance.log(
    //     "GridController.longPolling() can not find div: " +
    //       this.config.divTimer +
    //       ", stop loop."
    //   );
    // }
    loggerInstance.log(
      "GridController.longPolling() found div: " +
        this.config.divTimer +
        ", keep looping...."
    );

    this.longPollingTimer = window.setTimeout(() => {
      loggerInstance.log(
        "GridController.longPolling()...run again at " +
          new Date().toISOString() +
          ", which is every: " +
          this.config.longPollingRefreshRateMs / 1000 +
          " seconds"
      );
      clearTimeout(this.longPollingTimer);
      if (this.state.isLoading) {
        loggerInstance.log("GridController.longPolling() already loading...");
        // this.longPolling();
      } else {
        this.state.simpleTriggerLongPoll = {
          timeIso: new Date().toISOString()
        };

        this.longPolling();

        // this.getData().then(() => {
        //   this.state.longPollingCounter++;
        //   this.longPolling();
        // });
      }
    }, this.config.longPollingRefreshRateMs);
  }

  public setLastSocketMessage(
    cleoCallSummaries: ICleoCallSummary[] | number[],
    messageType: SocketActionType
  ): void {
    this.state.lastSocketMessageHumanReadTime = format(
      parseISO(new Date().toISOString()),
      "HH:mm:ss"
    );
    this.state.lastUpdatedHumanReadTime = this.state.lastSocketMessageHumanReadTime;

    //  Every time we get a socket message, socket working, so reset the "back up"
    //  long polling.  On a really busy day, socket messages might come in
    //  faster than the refresh rate, so a long poll may never get triggered...
    //  which is fine, because all messages should be coming thru socket.  We have
    //  this mechanism in place just in case Socket Back Plane falls over...
    //  which it has done before.
    this.stopLongPollingTimer();
    this.longPolling();

    cleoCallSummaries.forEach((cleoCallSummary: ICleoCallSummary | number) => {
      const message: IGridControllerStackMessage = {
        id: "",
        time: new Date().toISOString(),
        callNo: 0,
        message: "",
        messageType: messageType,
        serviceName: ""
      };

      if (typeof cleoCallSummary === "number") {
        message.callNo = cleoCallSummary;
      } else {
        message.callNo = cleoCallSummary.CallNo;
        message.serviceName =
          cleoCallSummary.CallService.Type +
          (cleoCallSummary.CallService.Description
            ? ": " + cleoCallSummary.CallService.Description
            : "");
      }
      message.message = messageType + "";
      message.id = message.time + "~" + message.callNo + "~" + messageType;

      this.state.stackCollectionMessages.push(message);
    });
    this.state.stackMessages = this.state.stackCollectionMessages.getStorage();
  }

  public stopRecalcBreachTimer(): void {
    clearTimeout(this.breachRecalcTimer);
  }

  public stopLongPollingTimer(): void {
    clearTimeout(this.longPollingTimer);
  }

  public stopTimers(): void {
    this.stopRecalcBreachTimer();
    this.stopLongPollingTimer();
  }

  public destroy(): void {
    loggerInstance.log("GridController.destroy()");
    this.stopTimers();
  }
}
