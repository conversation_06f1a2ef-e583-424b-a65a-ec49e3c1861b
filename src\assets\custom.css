:root {
    --header-height: 0px;
    --grid--header-height: 30px;
    --grid--call-summary-toolbar-height: 30px;
    --pathways--standard-width: 955px;
}

/*body {*/
/*    !*font: 12px Myriad, Helvetica, Tahoma, Arial, clean, sans-serif;*!*/
/*    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;*/
/*}*/

.dat-content-wrapper {
    max-width: 1600px;
    width: 100%;
    margin: 10px;
    gap: 20px;
}

.dat-card {
    display: flex;
    background: linear-gradient(to top, #f8fafc -100%,  #ffffff 30%);
    border: 1px solid #e2e8f0;
    /*border-color: #e2e8f0;*/
    border-radius: 8px;
}

.dat-card--generic {
    flex-direction: column;
    width: 100%;
    padding: 8px;
    background: linear-gradient(to top, #f8fafc -100%,  #ffffff 30%);
}

.dat-vertical-spacer {
    margin-top: 16px;
}

.dat-flex {
    display: flex;
}

.dat-flex-column {
    display: flex;
    flex-direction: column;
}

.dat-flex-row {
    display: flex;
    flex-direction: row;
}

.dat-flex-wrap {
    flex-wrap: wrap;
}

.dat-flex-nowrap {
    flex-wrap: nowrap;
}

.dat-flex-grow {
    flex-grow: 1;
}

.dat-flex-start {
    align-items: flex-start;
}

.dat-flex-center {
    align-items: center;
}

.dat-flex-end {
    align-items: flex-end;
}

.dat-align-self-flex-start {
    align-self: flex-start;
}

.dat-align-self-flex-end {
    align-self: flex-end;
}

.dat-align-self-flex-center {
    align-self: center;
}

.dat-justify-flex-start {
    justify-content: flex-start;
}

.dat-justify-flex-center {
    justify-content: center;
}

.dat-justify-flex-end {
    justify-content: flex-end;
}

.dat-justify-flex-space-between {
    justify-content: space-between;
}

.dat-justify-flex-space-around {
    justify-content: space-around;
}

.dat-justify-flex-space-evenly {
    justify-content: space-evenly;
}

.dat-full-width {
    width: 100%;
}

.dat-full-height {
    height: 100%;
}

.dat-info--success {
    background: #f0fdfa;
    color: #134e4a;
    border-color: #072523;
}

.dat-info--info {
    background: #eff6ff;
    color: #1e3a8a;
    border-color: #93c5fd;
}

.dat-info--warn {
    background: #fff7ed;
    color: #7c2d12;
    border-color: #fed7aa;
}

.dat-info--error {
    background: #fef2f2;
    color: #7f1d1d;
    border-color: #fca5a5;
}

.dat-local-dev-server {
    background: hotpink;
}


.disabled-link {
    pointer-events: none;
}

.app-layout-default {
    width: 100%;
    height: 98vh;
    /*overflow: hidden;*/
    /*background-color: darkorange;*/
}

.app-layout-default--legacy-call {
    width: 0;
    height: 0;
    overflow: hidden;
    background-color: #9abefd;
}

.app-layout-default--legacy-call-dev {
    width: 100%;
    height: 50%;
    overflow: hidden;
    background-color: #2980b9;
}

.app-layout--header {
    /*background-color: red;*/
    height: var(--header-height);
    width: 100%;
    display: none;
}

.app-layout--side-bar {
    /*background-color: green;*/
    display: none;
}

.app-layout--body {
    height: calc( 100% - var(--header-height));
    /*background-color: deeppink;*/
}

.app-layout--footer {
    /*background-color: saddlebrown;*/
    position:absolute;
    /*height: 50px;*/
    height: 0;
    left:0;
    bottom:0;
    width:100%;
    display: none;
}

.app-div--cell-like {
    display: inline-block;
    vertical-align: top;
}

.cleo-float-left {
    float: left;
}

.cleo-float-right {
    float: right;
}

.cleo-force-inline-block {
    display: inline-block;
}

.splitpanes__pane {
    background-color: white !important;
}

.splitpanes__splitter {
    background-color: #E1EBFB !important;
    border-top: 1px solid #d2d1d1;
    border-bottom: 1px solid #d2d1d1;
}


.custom-tooltip {
    position: absolute;
    width: 600px;
    height: 300px;
    border: 1px solid lightgrey;
    background-color: white;
    overflow: hidden;
    pointer-events: none;
    transition: opacity 1s;
}

.custom-tooltip.ag-tooltip-hiding {
    opacity: 0;
}

.custom-tooltip p {
    margin: 5px;
    white-space: nowrap;
}

.custom-tooltip p:first-of-type {
    font-weight: bold;
}

.grid-cleo-row--icon {
    width: 16px;
    height: 16px;
}

.cleo-ag-hack-hide-menu-item {
    height: 0;
    display: none;
}


.ag-menu--menu-separator {
    color: red;
    background-color: yellow;
}
.ag-menu--perms-loading {
    color: red;
    font-weight: bold;
}

.ag-menu--perms-loaded {
    color: green;
    font-weight: bold;
}


#grid--content {
    /*background-color: #ffe5ff;*/
    height: 100%;
}

.grid--header {
    height: var(--grid--header-height);
    background: transparent linear-gradient(180deg, #E9EFFD 0%, #CBDFF6 64%, #BCD5F0 65%, #BCD5F0 100%) 0 0 no-repeat padding-box;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
}

/*Toolbar*/
.grid-route-toolbar--view-title-wrapper {
    /*float: left;*/
    margin-left: 5px;
    display: table-cell;
    height: var(--grid--header-height);
    line-height: var(--grid--header-height);
    vertical-align: middle;
}

.grid-route-toolbar--view-title {
    margin-right: 1em;
}

.grid-route-toolbar--header-right {
    /*float: right;*/
    /*margin-right: 1em;*/
    line-height: calc(var(--grid--header-height) - 1px);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    position: relative;
    padding: 0 16px 0 0;
    align-items: center;
    margin: 0;
}

.grid-route-toolbar--reload-spinner {
    display: inline-block;
    margin-right: 1em;
}

.grid-route-toolbar--refresh-link {
    margin-right: 1em;
    color: #3a66dd;
    text-decoration: none;
}

.grid-route-toolbar--refresh-link:hover {
    color: red;
    text-decoration: underline;
}

.grid-route-toolbar--filters-sep {
    margin-left: 1em;
}

.grid-route-toolbar--filters-warn {
    /*color: red;*/
}

.grid-route-toolbar--filters-warn-value {
    background-color: darkorange;
}


/*/Toolbar*/

.grid--content-pane {
    width: 100%;
    height: calc(100% - var(--grid--header-height));
}
.grid--call-summary-toolbar {
    height: var(--grid--call-summary-toolbar-height);
    /*background-color: #f5f3f3;*/
    background: transparent linear-gradient(180deg, #E9EFFD 0%, #CBDFF6 64%, #BCD5F0 65%, #BCD5F0 100%) 0 0 no-repeat padding-box;
    border-bottom: 1px solid #eee;
}

.grid--call-summary-header-text {
    line-height: var(--grid--call-summary-toolbar-height);
    vertical-align: middle;
    padding-left: 5px;
}

.grid--call-summary-display {
    height: calc(100% - var(--grid--call-summary-toolbar-height));
    overflow-y: auto;
    width: 100%
}

.grid--call-summary-display-form {
    width: 100%;
}

.grid--call-summary-display-label {
    width: 25%;
    text-align: left;
    font: italic normal normal 12px/14px Arial;
    letter-spacing: 0px;
    color: #000000;
    opacity: 1;
}

.grid--call-summary-display-data {
    width: 25%;
    text-align: left;
    font: normal normal bold 12px/14px Arial;
    letter-spacing: 0px;
    color: #000000;
    opacity: 1;
}

.adapter-button--separator {
    width: 0.5em;
    display: inline-block;
}


.adapter-button  {
    outline: 0;
    border: none;
    border-radius: 2px;
    text-align: center;
    /*text-transform: uppercase;*/
    color: white;
    text-decoration: none;
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    vertical-align: middle;
    z-index: 1;
    transition: .3s ease-out;
    font-size: 14px;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,0.14), 0 3px 1px -2px rgba(0,0,0,0.12), 0 1px 5px 0 rgba(0,0,0,0.2);
    height: 30px;
    padding: 5px;
}

.adapter-width-5 {
    width: 5rem;
}

.adapter-width-8 {
    width: 8rem;
}

.adapter-width-10 {
    width: 10rem;
}

.adapter-button:disabled {
    pointer-events: none;
    background-color: #DFDFDF !important;
    box-shadow: none;
    color: #9F9F9F !important;
    cursor: default;
}

.adapter-button--red {
    background-color: #f10505;
    color: white;
}

.adapter-button--green {
    background-color: limegreen;
    color: white;
}

.adapter-button--orange {
    background-color: orange;
    color: white;
}

.adapter-button--blue {
    background-color: blue;
    color: white;
}

.adapter-button--grey {
    background-color: #a5a8a8;
    color: black;
}

.adapter--element-disabled {
    pointer-events: none;
    opacity: 0.4;
}

/* Reusable card component. */
:root {
    --primary-button-color: #005eb8;
    --primary-button-text-color: #ffffff;
    --wrapper-border-color: 1px solid #46596b;
    --border-radius: 4px;
    --transition-default: all 0.25s ease-in-out;
    --shadow-small: 0 3px 6px hsla(0, 0%, 0%, .10), 0 2px 4px hsla(0, 0%, 0%, .07);
}
.card--wrapper {
    display: flex;
    flex-direction: column;
    margin: 4px;
    padding: 16px;
    border: var(--wrapper-border-color);
    border-radius: var(--border-radius);
}

.card--container {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border: 1px solid #c6cdd4;
    border-radius: var(--border-radius);
}

.card-title--container {
    display: flex;
    justify-content: space-between;
}

.card-text-type--sub {
    font-size: 10px;
    font-weight: 600;
    color: #96a3af;
    font-style: italic;
}

.card-text-type--title,
.card-text-type--title-small {
    font-size: 14px;
    font-weight: 600;
    color: #46596b;
    margin: 0;
}
.card-text-type--title-small {
    font-size: 12px;
}

/* End of: Reusable card component .*/
