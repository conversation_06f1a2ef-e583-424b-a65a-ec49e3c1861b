<template>
  <div class="google-addresses-picker-cleo">
    <div
      class="google-addresses-picker-cleo--address"
      v-for="geocoderResult in geocoderResults"
      :key="geocoderResult.place_id"
    >
      <span v-text="geocoderResult.formatted_address"></span>
      <div v-text="geocoderResult.types.join(', ')"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";

declare const google: any;

export default defineComponent({
  name: "google-addresses-picker-cleo",
  props: {
    geocoderResults: {
      default: () => {
        return [];
      }
    }
  },
  setup(props: { geocoderResults: unknown[] }) {
    return {};
  }
});
</script>

<style>
.google-addresses-picker-cleo {
  width: 100%;
}

.google-addresses-picker-cleo--address {
  line-height: 2;
  font-size: 15px;
  padding-left: 5px;
  border-top: 1px solid #e1e1e1;
}

.google-addresses-picker-cleo--address:hover {
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
