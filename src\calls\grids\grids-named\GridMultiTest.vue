<template>
  <div style="height: 100%" v-if="isReady" id="grids-wrapper">
    <div style="height: 20%">
      <GridStandard2
        :grid-definition="gridDefinition111"
        v-on:onRowClicked="onRowClicked"
        v-on:onRowRightClick="onRowRightClick"
        v-on:loadingSelectedCallPerms="setIsSelectedCallPermsLoading"
        v-on:selectedCallPerms="setSelectedCallPerms"
      />
    </div>
    <div style="height: 20%;margin-top: 30px;">
      <GridStandard2
        :grid-definition="gridDefinitionFcms"
        v-on:onRowClicked="onRowClicked"
        v-on:onRowRightClick="onRowRightClick"
        v-on:loadingSelectedCallPerms="setIsSelectedCallPermsLoading"
        v-on:selectedCallPerms="setSelectedCallPerms"
      />
    </div>
    <div style="height: 20%;margin-top: 30px;">
      <GridStandard2
        :grid-definition="gridDefinitionPcas"
        v-on:onRowClicked="onRowClicked"
        v-on:onRowRightClick="onRowRightClick"
        v-on:loadingSelectedCallPerms="setIsSelectedCallPermsLoading"
        v-on:selectedCallPerms="setSelectedCallPerms"
      />
    </div>
    <div style="height: 20%;margin-top: 30px">
      <CallSummarySection
        :cleo-call-summary="cleoCallSummary"
        :is-loading-perms="isLoadingPermsForCall"
        :cleo-call-summary-perms="permsForSelectedCall"
      />
    </div>

    <!--    rightClickController.rightClickTargetDiv.value-->
    <CleoContextMenu2
      :show-menu="rightClickController.showRightClickMenu.value"
      contain-with-dom-id="grids-wrapper"
      :perms-loading="isLoadingPermsForCall"
      :perms-for-call="permsForSelectedCall"
      :cleo-call-summary="rightClickController.cleoCallSummary.value"
      :clicked-element-coords="rightClickController.rightClickCoords.value"
      v-on:hideMenu="rightClickController.showRightClickMenu.value = false"
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from "@vue/composition-api";
import { GRID_DEFINITIONS } from "../grid-models";
// import GridStandard from "@/calls/grids/GridStandard.vue";
import CallSummarySection from "@/calls/summary/CallSummarySection.vue";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CommonService } from "@/common/common-service";
import GridStandard2 from "@/calls/grids/grids-named/GridStandard2.vue";
import { useRightClickController } from "@/calls/grids/contextmenu/useRightClickController";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import { useStore } from "@/store/store";
import { ICleoPermission } from "@/permissions/permission-models";
import CleoContextMenu2 from "@/calls/grids/contextmenu/cleo-context-menu2.vue";

const callSummaryService: CallSummaryService = new CallSummaryService();
const commonService: CommonService = new CommonService();

export default defineComponent({
  name: "grid-multi-test",
  components: { CleoContextMenu2, CallSummarySection, GridStandard2 },
  setup() {
    const gridDefinition111 = GRID_DEFINITIONS["111calls"];
    const gridDefinitionFcms = GRID_DEFINITIONS.FcmsCalls;
    const gridDefinitionPcas = GRID_DEFINITIONS.PcasCalls;

    const store = useStore();

    const userStoreState = computed<IUserStoreState>(() => {
      return store.state[USER_STORE_CONST.USER__CONST_MODULE_NAME];
    });

    const cleoCallSummary = ref<ICleoCallSummary>(
      callSummaryService.factoryCleoCallSummary()
    );

    const rightClickController = useRightClickController();

    function onRowClicked(cleoCall: ICleoCallSummary) {
      cleoCallSummary.value = commonService.simpleObjectClone(cleoCall);
    }

    const isReady = computed(() => {
      return userStoreState.value.user.userName.length > 0;
    });

    function onRowRightClick(payload: {
      data: ICleoCallSummary;
      coords: { x: number; y: number };
      targetGridContainerId: string;
    }) {
      rightClickController.startMenu(payload);

      onRowClicked(payload.data);
    }

    const isLoadingPermsForCall = ref(false);
    const permsForSelectedCall = ref({});
    function setIsSelectedCallPermsLoading(isLoading: boolean): void {
      isLoadingPermsForCall.value = isLoading;
    }

    function setSelectedCallPerms(
      perms: Record<string, ICleoPermission>
    ): void {
      permsForSelectedCall.value = commonService.simpleObjectClone(perms);
    }

    return {
      cleoCallSummary,
      gridDefinition111,
      gridDefinitionFcms,
      gridDefinitionPcas,
      onRowClicked,
      rightClickController,
      isReady,
      onRowRightClick,
      setIsSelectedCallPermsLoading,
      setSelectedCallPerms,
      isLoadingPermsForCall,
      permsForSelectedCall
    };
  }
});
</script>
