import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import {
  differenceInDays,
  differenceInSeconds,
  format,
  parseISO,
} from "date-fns";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CommonService } from "@/common/common-service";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import { simpleObjectClone, toLocalISOWithOffset } from "@/common/common-utils";

const callSummaryService: CallSummaryService = new CallSummaryService();
const commonService: CommonService = new CommonService();
const cleoCommonService: CleoCommonService = new CleoCommonService();

type COMFORTCALL_ICONS =
  | "CLEO_SENT"
  | "CLEO_RECEIVED"
  | "CLEO_SENT2"
  | "CLEO_RECEIVED2"
  | "MANUAL_UNSUCCESSFUL"
  | "MANUAL_SUCCESS"
  | "NONE";

interface IComfortCallOutput {
  count: number;
  user: string;
  icon: COMFORTCALL_ICONS;
  iconPath: string;
  cleoSentSms: boolean;
  cleoReceivedSms: boolean;
  hasManualCourtesy: boolean;
  title: string;
  timeDesc: string;
  autoManualDiffInSeconds: number;
  debug: any;
}

export class CallSummaryHtml {
  public getStandardDateTimeFormat(): string {
    return "MMM do ccc hh:mm a";
  }

  public getStandardDateDayTimeFormat(): string {
    return "MMM do ccc hh:mm a";
  }

  public isLocked(cleoCallSummary: ICleoCallSummary): string {
    const padlock = require("./../../assets/padlock3.gif");

    if (cleoCallSummary.IsLocked && cleoCallSummary.IsLocked) {
      return (
        "<img alt='' class='grid-cleo-row--icon' title='" +
        cleoCommonService.formatUserDominoName(cleoCallSummary.IsLocked, "CN") +
        ".' src='" +
        padlock +
        "'/>"
      );
    }
    return "";
  }

  public callHcp(cleoCallSummary: ICleoCallSummary): string {
    const userBlue = require("../../assets/user_blue.png");

    if (cleoCallSummary.Call_HCP === "1") {
      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' src='" +
        userBlue +
        "' alt='HCP'/>" +
        "</div>"
      );
    }
    return "";
  }

  public callCallBack(cleoCallSummary: ICleoCallSummary): string {
    const callbackImg = require("../../assets/callback3.gif");
    const callCallback: number = cleoCallSummary.CallCallback;
    if (!callCallback || callCallback === 0) {
      return "";
    }
    return (
      "<div style='text-align: center; padding-top:2px;'>" +
      "<img class='grid-cleo-row--icon' src='" +
      callbackImg +
      "' " +
      " alt='Patient has called back " +
      callCallback +
      " time(s)' " +
      " class='grid-cleo-row--icon' " +
      " title='Patient has called back " +
      callCallback +
      " time(s)' />" +
      "</div>"
    );
    // return cleoCallSummary.CallCallback;
  }

  public walkIn(cleoCallSummary: ICleoCallSummary): string {
    const ambulance = require("../../assets/ambulance.gif");
    const walkIn = require("../../assets/walkin.gif");

    const dispatchVehicle =
      cleoCallSummary.DispatchVehicle &&
      cleoCallSummary.DispatchVehicle.length > 0
        ? cleoCallSummary.DispatchVehicle
        : "";
    if (dispatchVehicle.length > 0) {
      return (
        "<div id='walk-in' title='Dispatched to vehicle: " +
        cleoCommonService.formatUserDominoName(dispatchVehicle, "CN") +
        "' style='text-align: center; padding-top:2px;'>" +
        "<img src='" +
        ambulance +
        "' alt='Vehicle'/>" +
        "</div>"
      );
    }
    if (cleoCallSummary.WalkIn) {
      return (
        "<div id='walk-in' title='Walk In' style='text-align: center; padding-top:2px;'>" +
        "<img src='" +
        walkIn +
        "' alt='Walk In'/>" +
        "</div>"
      );
    }
    return "";
  }

  public callWarmTransferred(cleoCallSummary: ICleoCallSummary): string {
    const transmitDeleteImg = require("../../assets/icon_transmit_delete.png");
    const transmitGoImg = require("../../assets/icon_transmit_go.png");

    const callWarmTransferred = cleoCallSummary.CallWarmTransferred;

    if (callWarmTransferred === null) {
      return "";
    }

    if (callWarmTransferred) {
      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' title='Warm transfer: " +
        callWarmTransferred +
        "' src='" +
        transmitGoImg +
        "' alt=''/>" +
        "</div>"
      );
    }
    if (!callWarmTransferred) {
      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' title='Warm transfer: " +
        callWarmTransferred +
        "' src='" +
        transmitDeleteImg +
        "' alt=''/>" +
        "</div>"
      );
    }
    return "";
  }

  public breachLevelHuman(cleoCallSummary: ICleoCallSummary): string {
    function timeConvert(n: number) {
      // Store the input number of minutes in a variable
      const num = n;

      // Calculate the total hours by dividing the number of minutes by 60
      const hours = num / 60;

      // Round down the total hours to get the number of full hours
      const rhours = Math.floor(hours);

      // Calculate the remaining minutes after subtracting the full hours from the total hours
      const minutes = (hours - rhours) * 60;

      // Round the remaining minutes to the nearest whole number
      const rminutes = Math.round(minutes);

      if (hours > 24) {
        return Math.round(hours / 24) + "d";
      }

      // Construct and return a string representing the conversion result
      return hours + "h " + (rminutes > 0 ? rminutes + "m" : "");
    }

    if (cleoCallSummary.BreachLevel1Mins) {
      const BreachLevel1Mins = cleoCallSummary.BreachLevel1Mins;
      if (isNaN(BreachLevel1Mins)) {
        return "";
      }
      const numberMins = Number(BreachLevel1Mins);
      if (numberMins > 60) {
        return timeConvert(numberMins);
      } else {
        return numberMins + "m";
      }
    }
    return "";
  }

  public breachActualTime(
    cleoCallSummary: ICleoCallSummary,
    dxCodes: string[],
    failedContactWarnMinutesUrgent: number,
    failedContactWarnMinutesNotUrgent: number,
    dateTimeToCalcAgainst: Date
  ): string {
    const breachIso: string = cleoCallSummary.BreachActualTime
      ? cleoCallSummary.BreachActualTime
      : "";
    const warnIso: string = cleoCallSummary.BreachWarnActualTime
      ? cleoCallSummary.BreachWarnActualTime
      : "";
    if (breachIso.length > 0 && warnIso.length > 0) {
      const dateTimeNow = new Date();
      const breachDateTime = parseISO(breachIso);
      const diffInDays = differenceInDays(breachDateTime, dateTimeNow);
      if (diffInDays > 30) {
        return "";
      }
      if (cleoCallSummary.ApplyBreach) {
        const diffInSecondsBreach = differenceInSeconds(
          breachDateTime,
          dateTimeNow
        );
        const warnDateTime = parseISO(warnIso);
        const diffInSecondsWarn = differenceInSeconds(
          warnDateTime,
          dateTimeNow
        );

        let backGroundColor = "#ccffcc"; //	green

        if (diffInSecondsWarn <= 0) {
          backGroundColor = "#ffff99"; //	amber
        }
        if (diffInSecondsBreach <= 0) {
          backGroundColor = "#ffd6cc"; //	red
        }

        const humanDiff = callSummaryService.timeHumanShort(
          Math.abs(diffInSecondsBreach)
        );

        let inLineStyle =
          "color: black;background-color: " + backGroundColor + ";";

        const rowStyle = this.getRowStyle(
          cleoCallSummary,
          dxCodes,
          failedContactWarnMinutesUrgent,
          failedContactWarnMinutesNotUrgent,
          dateTimeToCalcAgainst
        );

        if (rowStyle.message.substring(0, 16) === "LEVEL_1_PRIORITY") {
          inLineStyle =
            "color: " +
            rowStyle.style.color +
            ";background-color: " +
            rowStyle.style.backgroundColor +
            ";";
        }

        return (
          "<div style='text-align: center;" +
          inLineStyle +
          "' title='" +
          " Warn: " +
          format(warnDateTime, "yyyy MMM do, HH:mm") +
          " | Breach: " +
          format(breachDateTime, "yyyy MMM do, HH:mm") +
          "'>" +
          (format(warnDateTime, "yyyy") === "2099"
            ? ""
            : (diffInSecondsBreach <= 0 ? "-" : "") + humanDiff) +
          "</div>"
        );
      }
      return "";
    }
    return "";
  }

  public getFollowUpActiveTime(cleoCallSummary: ICleoCallSummary): string {
    if (cleoCallSummary.FOLLOW_UP_Active) {
      // FOLLOW_UP_Active in format 17/03/2025 08:35:00
      // return 17th Mar 08:35
      // return format(parseISO(cleoCallSummary.FOLLOW_UP_Active), "do MMM HH:mm");
      // also get 17/03/2025.

      try {
        let FOLLOW_UP_Active = cleoCallSummary.FOLLOW_UP_Active;

        if (FOLLOW_UP_Active.length === 10) {
          FOLLOW_UP_Active = FOLLOW_UP_Active + " 00:00:00";
        }

        const isoFollowUpActive = toLocalISOWithOffset(FOLLOW_UP_Active);
        if (isoFollowUpActive.error.length > 0) {
          return FOLLOW_UP_Active;
        }
        return format(parseISO(isoFollowUpActive.iso), "do MMM HH:mm");
      } catch (e) {
        console.log(e);
        return cleoCallSummary.FOLLOW_UP_Active;
      }

      // return cleoCallSummary.FOLLOW_UP_Active;
    }
    return "";
  }

  public pdsTracedAndVerified(cleoCallSummary: ICleoCallSummary): string {
    const ballGreenImg = require("../../assets/ball-green.png");
    const ballRedImg = require("../../assets/ball-red.png");
    // const ballYellowImg = require("../../assets/ball-yellow.png");
    const ballGreyImg = require("../../assets/ball-grey.gif");
    const pdsTracedAndVerified = cleoCallSummary.PdsTracedAndVerified;

    let imgToUse = ballRedImg;
    const PDSAdminTrace = cleoCallSummary.PDSAdminTrace || "";

    if (pdsTracedAndVerified) {
      imgToUse = ballGreenImg;
    } else if (PDSAdminTrace.length > 0) {
      imgToUse = ballGreyImg;
    } else {
      imgToUse = ballRedImg;
    }

    return (
      '<div style="text-align: center; padding-top:2px;">' +
      "<img style='width: 10px;height: 10px;' " +
      "title=' Traced and Verified: " +
      pdsTracedAndVerified +
      (PDSAdminTrace.length > 0 ? " - " + PDSAdminTrace : "") +
      "' " +
      "src='" +
      imgToUse +
      "' " +
      "alt='PDS'/>" +
      "</div>"
    );
  }

  public casValidation(cleoCallSummary: ICleoCallSummary): string {
    const ballGreenImg = require("../../assets/ball-green.png");
    const ballRedImg = require("../../assets/ball-red.png");
    const hasBeenValidated = cleoCallSummary.CasValidationCount > 0;

    if (!hasBeenValidated) {
      return (
        '<div style="text-align: center; padding-top:2px;">' +
        "<img style='width: 10px;height: 10px;' " +
        "title='Not Validated' " +
        "src='" +
        ballRedImg +
        "' " +
        "alt='PDS'/>" +
        "</div>"
      );
    }

    return (
      '<div style="text-align: center; padding-top:2px;">' +
      "<img style='width: 10px;height: 10px;' " +
      "title='Last Validated at: " +
      cleoCallSummary.CasValidationTime +
      " by " +
      cleoCallSummary.CasValidationUser.split("/")[0].replace("CN=", "") +
      " " +
      cleoCallSummary.CasValidationReason +
      "' " +
      "src='" +
      ballGreenImg +
      "' " +
      "alt='Cas Validated'/>" +
      "</div>"
    );
  }

  public patientName(cleoCallSummary: ICleoCallSummary): string {
    const isRealCall = callSummaryService.isRealCall(cleoCallSummary);
    const atSymbol = require("../../assets/at-symbol.png");

    let html;
    const patientName = callSummaryService.getPatientName(cleoCallSummary);

    if (cleoCallSummary.Itk111Online) {
      html =
        "<span title='111 on line call." +
        patientName +
        "' style='text-align: center; padding-top:2px;margin-right: 5px;'>" +
        "<img alt='Patient' src='" +
        atSymbol +
        "'  class='grid-cleo-row--icon'/>" +
        "&nbsp" +
        patientName +
        "</span>";
    } else {
      html = patientName;
    }
    return (
      "<div class = '" +
      (isRealCall ? "" : "grid-cleo-row--not-real-call") +
      "' style='overflow:hidden;' title='" +
      (cleoCallSummary.Itk111Online ? "111 on line call. " : "") +
      patientName +
      "'>" +
      html +
      "</div>"
    );
  }

  public callCRel(cleoCallSummary: ICleoCallSummary): string {
    let html;
    const relationship = cleoCallSummary.CallCRel.toUpperCase();
    const subClassification =
      cleoCallSummary.CallSubClassification &&
      cleoCallSummary.CallSubClassification.Description
        ? cleoCallSummary.CallSubClassification.Description.toUpperCase()
        : "";

    const userGreenImg = require("../../assets/user_green.png");
    const userBlueImg = require("../../assets/user_blue.png");
    const userRedImg = require("../../assets/user_red.png");

    // TODO need "serviceID" to come up in grid data.
    const isBrisDoc = true;

    if (
      (relationship === "AMBULANCE PARAMEDIC ON SCENE" ||
        relationship === "PARAMEDIC ON SCENE") &&
      (subClassification === "CLINICIAN AWAITING CALLBACK" || isBrisDoc)
    ) {
      html =
        "<div title=' Relationship: " +
        cleoCallSummary.CallCRel +
        "' style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' src='" +
        userGreenImg +
        "' alt='User'/>" +
        "</div>";
    } else if (
      relationship === "DISTRICT NURSE" &&
      subClassification === "CLINICIAN AWAITING CALLBACK"
    ) {
      html =
        "<div title=' Relationship: " +
        cleoCallSummary.CallCRel +
        "' style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' src='" +
        userBlueImg +
        "' alt='User'/>" +
        "</div>";
    } else if (
      relationship === "AMBULANCE TECHNICIAN" &&
      subClassification === "CLINICIAN AWAITING CALLBACK"
    ) {
      html =
        "<div title=' Relationship: " +
        cleoCallSummary.CallCRel +
        "' style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' src='" +
        userRedImg +
        "' alt='User'/>" +
        "</div>";
    } else {
      html = "";
    }
    return html;
  }

  public callAltTel(cleoCallSummary: ICleoCallSummary): string {
    const userPhoneImg = require("../../assets/phone_sound.png");

    let message = "";
    if (
      cleoCallSummary.CallTelNoAlt1 &&
      cleoCallSummary.CallTelNoAlt1.length > 0
    ) {
      //  Don't show the number, the clini will bypass "start consult"
      message =
        "Alternative number available: " +
        (cleoCallSummary.CallTelNoAltType1 === ""
          ? "Type not specified."
          : cleoCallSummary.CallTelNoAltType1);
      return (
        "<div title='" +
        message +
        '\' style="text-align: center; background-color:white; padding-top:1px;">' +
        "<img class='grid-cleo-row--icon' src='" +
        userPhoneImg +
        "' alt=\"" +
        message +
        '"/>' +
        "</div>"
      );
    } else {
      return "";
    }
  }

  public call1stContact(cleoCallSummary: ICleoCallSummary): string {
    const letterpImg = require("../../assets/letterp.png");
    const blueCircleImg = require("../../assets/blue-circle-I.png");
    const yellowCircleImg = require("../../assets/yellow-circle-II.png");
    const redCircleImg = require("../../assets/red-circle-III.png");
    const blackcircleImg = require("../../assets/black-circle-caution.png");
    const tickImg = require("../../assets/tick.gif");
    // const crossImg = require("../../../images/cross.gif");

    const failedCount = Number(cleoCallSummary.PatientContactCodeCount);
    let failedContactImage;

    if (cleoCallSummary?.PatientContactCode?.length > 0) {
      // if (failedCount > 0) {
      //  Dr has indicated failed contact, don't show green icon.
      if (failedCount === 1) {
        failedContactImage = blueCircleImg;
      } else if (failedCount === 2) {
        failedContactImage = yellowCircleImg;
      } else if (failedCount === 3) {
        failedContactImage = redCircleImg;
      } else {
        failedContactImage = blackcircleImg;
      }

      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img alt='1st' src='" +
        failedContactImage +
        "' title='Failed contact: " +
        cleoCallSummary.PatientContactCode +
        ", count: " +
        cleoCallSummary.PatientContactCodeCount +
        "'/>" +
        "</div>"
      );
    }

    if (
      cleoCallSummary.Call1StContact &&
      cleoCallSummary.Call1StContact.length > 0
    ) {
      return (
        "<div style='text-align: center; padding-top:2px;'><img alt='1st' src='" +
        tickImg +
        "'" +
        " title='1st contact: " +
        format(
          parseISO(cleoCallSummary.Call1StContact),
          this.getStandardDateTimeFormat()
        ) +
        "' class='grid-cleo-row--icon'/></div>"
      );
    }

    if (
      cleoCallSummary.Call1StContactPathways &&
      cleoCallSummary.Call1StContactPathways.length > 0
    ) {
      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img alt='1st' class='grid-cleo-row--icon' src='" +
        letterpImg +
        "' title='Pathways Contact: " +
        format(
          parseISO(cleoCallSummary.Call1StContactPathways),
          this.getStandardDateTimeFormat()
        ) +
        "'/>" +
        "</div>"
      );
    }

    return "";
  }

  public media(cleoCallSummary: ICleoCallSummary): string {
    const photosRequested = require("../../assets/photos_requested_grey.jpg");
    const photosAvailableToView = require("../../assets/photos_available_to_view.png");
    const photosRequestedAgain = require("../../assets/photos_requested_again.gif");

    /*
    		var p = "";
		var gsImage = "";
		if ( s === "RECEIVED" ){
			gsImage = "images/photos_attached.jpg";
		}
		if ( s === "REQUESTED" ){
			gsImage = "images/photos_requested.jpg";
		}
		if ( gsImage !== "" ){
			p = "<div style=\"text-align: center; padding-top:2px;\"><img src=\"" + gsImage + "\" title=\"Media Status\"></div>";
		}
		return p;
     */

    let imgToUse = "";
    let title = "";

    if (cleoCallSummary.GOODSAM_IMAGE_STATUS === "RECEIVED") {
      imgToUse = photosAvailableToView;
      title = "Photos/Media Available";
    }

    if (cleoCallSummary.GOODSAM_IMAGE_STATUS === "REQUESTED_AGAIN") {
      imgToUse = photosRequestedAgain;
      title = "Photos/Media Requested Again";
    }

    if (cleoCallSummary.GOODSAM_IMAGE_STATUS === "REQUESTED") {
      imgToUse = photosRequested;
      title = "Photos/Media Requested";
    }

    if (imgToUse.length > 0) {
      return (
        "<div style='text-align: center; padding-top:2px;'>" +
        "<img class='grid-cleo-row--icon' src='" +
        imgToUse +
        "' title='" +
        title +
        "'/>" +
        "</div>"
      );
    }
    return "";

    // if (cleoCallSummary.Cpl_supportTypeRequired) {
    //   return (
    //     "<div title='Media' style='text-align: center; padding-top:2px;'>" +
    //     "<img class='grid-cleo-row--icon' src='" +
    //     mediaImg +
    //     "' alt='Media'/>" +
    //     "</div>"
    //   );
    // }
    // return "";
  }

  public getCaseComments(cleoCallSummary: ICleoCallSummary): string {
    const commentsGif = require("../../assets/comments.gif");

    const hasCaseComments = cleoCallSummary.CaseComments.length > 0;

    if (!hasCaseComments) {
      return "";
    }

    return (
      '<div style="text-align: center; padding-top:2px;">' +
      "<img style='width: 10px;height: 10px;' " +
      "src='" +
      commentsGif +
      "' " +
      "alt='Case Comments'/>" +
      "</div>"
    );
  }

  /**
   * E.g. "2025-04-15T11:27:51 01:00~:~rewr3ere~~~2025-04-15T11:27:31 01:00~:~ vb gfb gbnghnghng",
   * @param cleoCallSummary
   */
  public getCaseCommentsToolTip(
    cleoCallSummary: ICleoCallSummary | undefined
  ): string {
    if (cleoCallSummary === undefined) {
      return "";
    }
    const caseCommentsSource = cleoCallSummary.CaseComments;
    try {
      if (caseCommentsSource && caseCommentsSource.length > 0) {
        let caseComments = caseCommentsSource.replace(/~n~/g, "<br>"); // replace newline delimeter
        caseComments = caseComments.replace(/<br><br>/g, "<br>"); // remove double lines
        const commentsArr = caseComments.split("~~~");
        const html: string[] = [];

        html.push("<table>");

        for (const commentLine of commentsArr) {
          const commentLineSplit = commentLine.split("~:~");
          html.push('<tr><td style="vertical-align: top;width:140px;"><b>');
          // encode turns + into space. put it back
          commentLineSplit[0] = commentLineSplit[0].replace(" ", "+");

          // It's got this odd space....like it hasn't done the offset correct..
          // Just get the date time part and assume local time.
          let isoIsh = commentLineSplit[0]; // E.g. 2025-03-27T17:29:54 00:00
          isoIsh = isoIsh.slice(0, 19); // E.g. 2025-03-27T17:29:54

          html.push(format(parseISO(isoIsh), "dd MMM yyyy hh:mm")); // time
          html.push("</b></td><td>");
          html.push(commentLineSplit[1]); // comment
          html.push("</td>");
        }
        html.push("</table>");

        return html.join("");
      }

      return "";
    } catch (e) {
      console.log(e);
      return caseCommentsSource;
    }
  }

  public getGenericDateTime(dateTime: string): string {
    if (dateTime && dateTime.length > 0) {
      // If dateTime string pattern matchs HH:mm, then return the time only
      if (dateTime.match(/^[0-9]{2}:[0-9]{2}$/)) {
        return "<div style='text-align: center;'>" + dateTime + "</div>";
      }
      const dtParsed = parseISO(dateTime);
      return (
        "<div title='" +
        format(dtParsed, this.getStandardDateTimeFormat()) +
        "' style='text-align: center;'>" +
        format(dtParsed, "HH:mm") +
        "</div>"
      );
    }
    return "";
  }

  public callAppointmentTime(cleoCallSummary: ICleoCallSummary): string {
    const callAppointmentTime =
      cleoCallSummary.DateAppointmentStart &&
      cleoCallSummary.DateAppointmentStart.length > 0
        ? cleoCallSummary.DateAppointmentStart
        : "";
    const AFT_UNABLE_REASON =
      cleoCallSummary.AFT_UNABLE_REASON &&
      cleoCallSummary.AFT_UNABLE_REASON.length > 0
        ? cleoCallSummary.AFT_UNABLE_REASON
        : "";

    const clockRed = require("../../assets/clock_red.png");

    if (callAppointmentTime.length === 0 && AFT_UNABLE_REASON.length === 0) {
      return "";
    }
    if (callAppointmentTime.length > 0) {
      // return callAppointmentTime; //  e.g.  11:45
      // return this.getGenericDateTime(callAppointmentTime);
      // time in format 03/07/2025 07:30:00, convert to ISO with offset
      const isoWithOffset = toLocalISOWithOffset(callAppointmentTime);
      if (isoWithOffset.error === "") {
        return this.getGenericDateTime(isoWithOffset.iso);
      }
    }
    if (AFT_UNABLE_REASON.length > 0) {
      return (
        "<div title='" +
        AFT_UNABLE_REASON +
        "' style='text-align: center; padding-top:2px;'>" +
        "<img src='" +
        clockRed +
        "' class='grid-cleo-row--icon' alt=''/>" +
        "</div>"
      );
    }
    return "";
  }

  public callClassification(cleoCallSummary: ICleoCallSummary): string {
    return (
      cleoCallSummary.CallClassification.Description +
      (cleoCallSummary.CallSubClassification
        ? "(" + cleoCallSummary.CallSubClassification.Description + ")"
        : "")
    );
  }

  /**
   * Primarily a "Manual" SMS, but could also be an auto on ITK inbound.
   * @param cleoCallSummary
   */
  public smsSent(cleoCallSummary: ICleoCallSummary): string {
    const iconSms = require("../../assets/icon-sms.png");
    const iconSmsGreen = require("../../assets/icon-sms-green.png");
    const iconSmsRed = require("../../assets/icon-sms-red.png");

    if (!cleoCallSummary.SMS_HAS) {
      return "";
    }
    if (!cleoCallSummary.SMS_LATEST_AT) {
      return "";
    }

    // console.log("SMS_LATEST_AT: ", cleoCallSummary.SMS_LATEST_AT);

    // if SMS_LATEST_AT is in pattern 02/05/2025 18:02:15, create a local ISO datetime with offset
    if (cleoCallSummary.SMS_LATEST_AT.includes("/")) {
      const isoWithOffset = toLocalISOWithOffset(cleoCallSummary.SMS_LATEST_AT);
      if (isoWithOffset.error === "") {
        cleoCallSummary.SMS_LATEST_AT = isoWithOffset.iso;
      }

      // cleoCallSummary.SMS_LATEST_AT = toLocalISOWithOffset(
      //   cleoCallSummary.SMS_LATEST_AT
      // );
    }

    // format SMS_LATEST_AT as Do MMM, HH:mm
    const dateTimeFormat = "do MMM, h:mm a";
    const formattedDateTime = format(
      parseISO(cleoCallSummary.SMS_LATEST_AT),
      dateTimeFormat
    );

    const titleFinal =
      "[" +
      formattedDateTime +
      "] " +
      (cleoCallSummary.SMS_SENT ? "Sent: " : "Not Sent: ") +
      cleoCommonService.formatUserDominoName(cleoCallSummary.SMS_LATEST_USER) +
      ": " +
      cleoCallSummary.SMS_LATEST_MESSAGE;

    const html =
      "<div title='" +
      titleFinal +
      "'" +
      ' style="text-align: center; padding-top:1px;">' +
      "<img class='grid-cleo-row--icon' src='" +
      (cleoCallSummary.SMS_SENT ? iconSmsGreen : iconSmsRed) +
      "' alt=\"SMS Sent" +
      '"/>' +
      "</div>";

    return html;
  }

  public smsSentText(cleoCallSummary: ICleoCallSummary): string {
    if (!cleoCallSummary.SMS_HAS) {
      return "";
    }
    if (!cleoCallSummary.SMS_LATEST_AT) {
      return "";
    }

    // console.log("SMS_LATEST_AT: ", cleoCallSummary.SMS_LATEST_AT);

    // if SMS_LATEST_AT is in pattern 02/05/2025 18:02:15, create a local ISO datetime with offset
    if (cleoCallSummary.SMS_LATEST_AT.includes("/")) {
      const isoWithOffset = toLocalISOWithOffset(cleoCallSummary.SMS_LATEST_AT);

      if (isoWithOffset.error === "") {
        cleoCallSummary.SMS_LATEST_AT = isoWithOffset.iso;
      }

      // cleoCallSummary.SMS_LATEST_AT = toLocalISOWithOffset(
      //   cleoCallSummary.SMS_LATEST_AT
      // );
    }

    // format SMS_LATEST_AT as Do MMM, HH:mm
    const dateTimeFormat = "do MMM, h:mm a";
    const formattedDateTime = format(
      parseISO(cleoCallSummary.SMS_LATEST_AT),
      dateTimeFormat
    );

    return (
      "[" +
      formattedDateTime +
      "] " +
      (cleoCallSummary.SMS_SENT ? "Sent: " : "Not Sent: ") +
      cleoCommonService.formatUserDominoName(cleoCallSummary.SMS_LATEST_USER) +
      ": " +
      cleoCallSummary.SMS_LATEST_MESSAGE
    );
  }

  public comfortCall(cleoCallSummary: ICleoCallSummary): string {
    const comfortCallOutput = this.comfortCallOutput(cleoCallSummary);

    const titleFinal =
      "[" +
      comfortCallOutput.count +
      "] " +
      cleoCommonService.formatUserDominoName(comfortCallOutput.user) +
      " " +
      comfortCallOutput.timeDesc +
      comfortCallOutput.title;

    const html =
      "<div title='" +
      titleFinal +
      "'" +
      ' style="text-align: center; padding-top:1px;">' +
      "<img class='grid-cleo-row--icon' src='" +
      comfortCallOutput.iconPath +
      "' alt=\"" +
      '"/>' +
      "</div>";

    return html;
  }

  public comfortCallOutput(
    cleoCallSummary: ICleoCallSummary
  ): IComfortCallOutput {
    const output: IComfortCallOutput = {
      count: 0,
      user: "",
      icon: "NONE",
      iconPath: "",
      cleoSentSms: false,
      cleoReceivedSms: false,
      hasManualCourtesy: false,
      title: "",
      timeDesc: "",
      autoManualDiffInSeconds: 0,
      debug: {
        ComfortSentServiceTime: "",
        ComfortSmsTime: "",
        CourtesyTime: "",
        diffInSecsAutoManual: 0,
        manual: null,
        auto: null,
      },
    };

    const cleoSent: string = require("../../assets/heart_grey.png");
    const cleoReceived = require("../../assets/heart_green.png");

    const cleoSent2: string = require("../../assets/heart_grey2.png");
    const cleoReceived2 = require("../../assets/heart_green2.png");

    const manualSuccess = require("../../assets/heart.png");
    const manualUnsuccessful = require("../../assets/heart_blue.png");
    const iconPaths: Record<COMFORTCALL_ICONS, any> = {
      CLEO_SENT: cleoSent,
      CLEO_RECEIVED: cleoReceived,
      CLEO_SENT2: cleoSent2,
      CLEO_RECEIVED2: cleoReceived2,
      MANUAL_UNSUCCESSFUL: manualUnsuccessful,
      MANUAL_SUCCESS: manualSuccess,
      NONE: "",
    };

    try {
      const dateTimeFormat = this.getStandardDateTimeFormat();

      const cleoSmsRequestedTime: Date | null =
        cleoCallSummary.ComfortSentServiceTime &&
        cleoCallSummary.ComfortSentServiceTime.length > 0
          ? parseISO(cleoCallSummary.ComfortSentServiceTime)
          : null;

      const smsSentConfTime: Date | null =
        cleoCallSummary.ComfortSmsTime &&
        cleoCallSummary.ComfortSmsTime.length > 0
          ? parseISO(cleoCallSummary.ComfortSmsTime)
          : null;

      const manualTime: Date | null =
        cleoCallSummary.CourtesyTime.length > 0
          ? parseISO(cleoCallSummary.CourtesyTime)
          : null;

      if (cleoSmsRequestedTime) {
        output.count++;
        output.cleoSentSms = true;
        if (smsSentConfTime) {
          output.cleoReceivedSms = true;
        }
      }

      if (manualTime) {
        output.count += cleoCallSummary.CourtesyCount;
        output.hasManualCourtesy = true;
      }

      let autoTime: Date | null = null;
      if (cleoSmsRequestedTime) {
        output.icon = "CLEO_SENT";
        output.user = "System";

        autoTime = cleoSmsRequestedTime;
        output.timeDesc += " Sent : " + format(autoTime, dateTimeFormat);
        if (smsSentConfTime) {
          autoTime = smsSentConfTime;
          output.icon = "CLEO_RECEIVED";
          output.timeDesc +=
            " Received : " + format(smsSentConfTime, dateTimeFormat);
        }

        const cleoSmsRequestedTime2: Date | null =
          cleoCallSummary.ComfortSentService2Time &&
          cleoCallSummary.ComfortSentService2Time.length > 0
            ? parseISO(cleoCallSummary.ComfortSentService2Time)
            : null;

        const smsSentConfTime2: Date | null =
          cleoCallSummary.ComfortSmsTime2 &&
          cleoCallSummary.ComfortSmsTime2.length > 0
            ? parseISO(cleoCallSummary.ComfortSmsTime2)
            : null;

        if (cleoSmsRequestedTime2) {
          output.icon = "CLEO_SENT2";
          output.user = "System 2";
          autoTime = cleoSmsRequestedTime2;
          output.timeDesc +=
            " Sent second: " + format(cleoSmsRequestedTime2, dateTimeFormat);
          if (smsSentConfTime2) {
            autoTime = smsSentConfTime2;
            output.icon = "CLEO_RECEIVED2";
            output.timeDesc +=
              " Received Second: " + format(smsSentConfTime2, dateTimeFormat);
          }
        }

        if (manualTime) {
          output.debug.manual = manualTime.toISOString();
          output.debug.auto = autoTime.toISOString();
          output.autoManualDiffInSeconds = differenceInSeconds(
            manualTime,
            autoTime
          );
        }
      }

      if (
        cleoCallSummary.CourtesyUser &&
        cleoCallSummary.CourtesyUser.length > 0
      ) {
        if (output.autoManualDiffInSeconds >= 0 && manualTime) {
          output.icon = "MANUAL_UNSUCCESSFUL";
          output.user = cleoCallSummary.CourtesyUser;
          if (cleoCallSummary.CourtesyContact) {
            output.icon = "MANUAL_SUCCESS";
          }

          output.timeDesc = manualTime
            ? commonService.fromNow(manualTime) +
              " at " +
              format(manualTime, dateTimeFormat)
            : "";
        }
      }

      output.iconPath = iconPaths[output.icon];
    } catch (e) {
      // console.log(e);
      // console.error(
      //   "Error in comfortCallOutput for case: ",
      //   simpleObjectClone(cleoCallSummary)
      // );
      output.icon = "NONE";
      output.title = (e as any).message;
      output.iconPath = iconPaths["MANUAL_UNSUCCESSFUL"];
    }
    return output;
  }

  public moreInfo(cleoCallSummary: ICleoCallSummary): string {
    const infoBlue = require("../../assets/icon_2finfoblue.png");
    const infoRedImg = require("../../assets/icon_2finfored.png");
    const infoGreen = require("../../assets/icon_2finfogreen.png");
    // const infoPurple = require("../../assets/icon_2finfpurple.svg");
    // const infoDarkGrey = require("../../assets/icon_2finfodarkgrey.svg");
    const infoPurple = require("../../assets/thumbnail_purple_info_icon_16x16.png");
    const infoDarkGrey = require("../../assets/thumbnail_dark_grey_info_icon_16x16.png");

    let iconToUse = infoBlue;

    if (cleoCallSummary.LinkedCallID.length > 0) {
      iconToUse = infoRedImg;
    }
    if (cleoCallSummary.COMPLETE_PREVENT === "1") {
      iconToUse = infoGreen;
    }

    if (cleoCallSummary.OversightValidationType === "Approve") {
      iconToUse = infoPurple;
    }
    if (
      cleoCallSummary.OversightValidationType ===
      "Further Clinical Input Required"
    ) {
      iconToUse = infoDarkGrey;
    }

    return (
      "<div " +
      ' style="text-align: center; padding-top:1px;">' +
      "<img class='grid-cleo-row--icon' src='" +
      iconToUse +
      "' alt=\"" +
      '"/>' +
      "</div>"
    );
  }

  /**
   * Not time dependant, if call is E.g. a DX333, then true. (these get displayed as black in grid)
   * @param cleoCallSummary
   * @param dxCodes
   */
  public isBreachPriority(
    cleoCallSummary: ICleoCallSummary,
    dxCodes: string[]
  ): boolean {
    //  TODO
    // if (!cleoCallSummary.ChFinalDispositionCode) {
    //   return false;
    // }
    // const CHFinalDispositionCode = cleoCallSummary.ChFinalDispositionCode.toUpperCase();

    const dxToUse = callSummaryService.getDxCode(cleoCallSummary).toUpperCase();

    return dxCodes.indexOf(dxToUse) > -1;
  }

  // | (keyof ICleoCallSummary)
  // | "BreachActualTime"
  // | "BreachPreActualTime"
  // | "BreachWarnActualTime",

  /**
   * Calls without Dx codes are ignored.  A preset number of Dx Codes for Black\ Orange.
   * TODO
   * Pick<
   ICleoCallSummary,
   "BreachActualTime" | "BreachPreActualTime" | "BreachWarnActualTime"
   >
   * @param cleoCallSummary
   * @param prop
   * @param dxCodes
   * @param reachedThisDateTime
   */
  public reachedBreachPriority(
    cleoCallSummary: ICleoCallSummary,
    prop: "BreachActualTime" | "BreachPreActualTime" | "BreachWarnActualTime",
    dxCodes: string[],
    pointInTime: Date
  ): boolean {
    // qwer
    const dxToUse = callSummaryService.getDxCode(cleoCallSummary).toUpperCase();

    // if (!cleoCallSummary.ChFinalDispositionCode) {
    //   return false;
    // }
    // const CHFinalDispositionCode = cleoCallSummary.ChFinalDispositionCode.toUpperCase();
    // if (dxCodes.indexOf(CHFinalDispositionCode) > -1) {
    if (dxCodes.indexOf(dxToUse) > -1) {
      const breachTimeToCheck = cleoCallSummary[prop] as string;
      if (!breachTimeToCheck || breachTimeToCheck.length === 0) {
        return false;
      }
      return commonService.reachedThisTime(
        pointInTime,
        parseISO(breachTimeToCheck)
      );
    }
    return false;
  }

  public reachedFailedContactWarn(
    cleoCallSummary: ICleoCallSummary,
    failedContactWarnMinutesUrgent: number,
    failedContactWarnMinutesNotUrgent: number,
    dateTimeToCalcAgainst: Date
  ): any {
    try {
      if (
        !cleoCallSummary.PatientContactCode ||
        !cleoCallSummary.LastFailedContactTime ||
        cleoCallSummary.LastFailedContactTime.length === 0
      ) {
        return false;
      }

      if (cleoCallSummary.PatientContactCode.length > 0) {
        dateTimeToCalcAgainst = dateTimeToCalcAgainst
          ? dateTimeToCalcAgainst
          : new Date();

        const dateTimeLastFailedContacted = parseISO(
          cleoCallSummary.LastFailedContactTime
        );

        const diffInSeconds = differenceInSeconds(
          dateTimeLastFailedContacted,
          dateTimeToCalcAgainst
        );

        //  This will always be a -negative number, since PatientContactCode_Current_ForView was in the past
        const minsSinceLastFailedContact = Math.abs(diffInSeconds / 60);
        const warnMins = cleoCallSummary.CallUrgentYn
          ? failedContactWarnMinutesUrgent
          : failedContactWarnMinutesNotUrgent;
        return minsSinceLastFailedContact >= warnMins;
      }
      return false;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  /**
   * Style whole row in grid to highlight various states.
   * @param cleoCallSummary
   * @param dxCodes
   * @param failedContactWarnMinutesUrgent
   * @param failedContactWarnMinutesNotUrgent
   * @param dateTimeToCalcAgainst
   */
  public getRowStyle(
    cleoCallSummary: ICleoCallSummary,
    dxCodes: string[],
    failedContactWarnMinutesUrgent: number,
    failedContactWarnMinutesNotUrgent: number,
    dateTimeToCalcAgainst: Date
  ): {
    style: {
      backgroundColor: string;
      color?: string;
    };
    message:
      | "LEVEL_1_PRIORITY__STANDARD"
      | "LEVEL_1_PRIORITY__BREACH"
      | "LEVEL_1_PRIORITY__PRE_BREACH"
      | "LEVEL_1_PRIORITY__WARN"
      | "CLINI_HIGH_PRIORITY"
      | "FAILED_CONTACT__WARN"
      | "FAILED_CONTACT__STANDARD"
      | "OVERSIGHT_VALIDATION__REVIEW_IN_PROGRESS"
      | "DEFAULT";
  } {
    if (this.isBreachPriority(cleoCallSummary, dxCodes)) {
      if (
        this.reachedBreachPriority(
          cleoCallSummary,
          "BreachActualTime",
          dxCodes,
          dateTimeToCalcAgainst
        )
      ) {
        return {
          style: {
            backgroundColor: "black",
            color: "white",
          },
          message: "LEVEL_1_PRIORITY__BREACH",
        };
      }

      // level-1-breach-priority--breachPre
      if (
        this.reachedBreachPriority(
          cleoCallSummary,
          "BreachPreActualTime",
          dxCodes,
          dateTimeToCalcAgainst
        )
      ) {
        return {
          style: {
            backgroundColor: "black",
            color: "white",
          },
          message: "LEVEL_1_PRIORITY__PRE_BREACH",
        };
      }

      // level-1-breach-priority--warn
      if (
        this.reachedBreachPriority(
          cleoCallSummary,
          "BreachWarnActualTime",
          dxCodes,
          dateTimeToCalcAgainst
        )
      ) {
        return {
          style: {
            backgroundColor: "red",
            color: "white",
          },
          message: "LEVEL_1_PRIORITY__WARN",
        };
      }

      return {
        style: {
          backgroundColor: "#FF8C00",
          color: "black",
        },
        message: "LEVEL_1_PRIORITY__STANDARD",
      };
    }

    //  row-clini-high-priority
    if (cleoCallSummary.CliniHighPriority) {
      return {
        style: {
          backgroundColor: "#ffe5ff",
          color: "black",
        },
        message: "CLINI_HIGH_PRIORITY",
      };
    }

    // failed-contact
    if (
      cleoCallSummary.PatientContactCode &&
      cleoCallSummary.PatientContactCode.length > 0
    ) {
      // failed-contact--warn
      if (
        this.reachedFailedContactWarn(
          cleoCallSummary,
          failedContactWarnMinutesUrgent,
          failedContactWarnMinutesNotUrgent,
          dateTimeToCalcAgainst
        )
      ) {
        return {
          style: {
            backgroundColor: "#fff2e5",
            color: "#000",
          },
          message: "FAILED_CONTACT__WARN",
        };
      }

      // failed-contact--standard
      return {
        style: {
          backgroundColor: "white",
          color: "#b0b0b0",
        },
        message: "FAILED_CONTACT__STANDARD",
      };
    }

    // Clinical Co-Ordinator Review in progress
    if (
      cleoCallSummary.OversightValidationType ===
      "Clinical Co-Ordinator Review in progress"
    ) {
      return {
        style: {
          backgroundColor: "#e5e7eb",
          color: "#000",
        },
        message: "OVERSIGHT_VALIDATION__REVIEW_IN_PROGRESS",
      };
    }

    return {
      style: {
        backgroundColor: "white",
        color: "black",
      },
      message: "DEFAULT",
    };
  }
}
