import https from "@/common/https";
// import { CLEO_CONFIG } from "@/common/config/config-";
import axios, { AxiosRequestConfig } from "axios";

export interface IConnectTokenPayload {
  grantType: string;
  clientId: string;
  clientSecret: string;
  userName: string;
  password: string;
  scope: string;
}

export function getJwtFromIdentityServer(
  connectTokenPayload: IConnectTokenPayload
): Promise<any> {
  const axiosConfig: AxiosRequestConfig = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    responseType: "json"
  };
  const params: string[] = [];
  params.push("grant_type=" + connectTokenPayload.grantType);
  params.push("client_id=" + connectTokenPayload.clientId);
  params.push("client_secret=" + connectTokenPayload.clientSecret);
  params.push("username=" + connectTokenPayload.userName);
  params.push("password=" + connectTokenPayload.password);
  params.push("scope=" + connectTokenPayload.scope);

  const payload = params.join("&");

  const url = "https://identityserver.sta.apps.ic24.nhs.uk:890" + "/connect/token";

  return axios.post(url, payload, axiosConfig);

  // return https.post(url, payload, axiosConfig);
}
