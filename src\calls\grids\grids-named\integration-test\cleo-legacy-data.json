{"Count": 11, "Returned": 11, "identifier": "unid", "label": "name", "Limit": 500, "items": [{"unid": "FC5F122974E1B7898025878A004FAF73", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01d7gje1jXoBLTdAdfTiEvIb", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1966-06-06", "CallAddress1": "335 VICTORIA ROAD", "CallAddress2": "LOWESTOFT", "CallAddress3": "", "CallAddress4": "", "CallTown": "", "CallPostCode": "NR33 9LS", "CallClassification": "Nurse Advice", "CC": "Nurse Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 14:33:06", "Call1stContactPathways": "11/11/2021 14:31:40", "PathwaysCaseId": "5f4be7f3-7c7b-4e67-b261-f4e810ade6bd", "CallCreatedBy": "Disp Cont1", "CallCName": "", "CallCRel": "Patient", "BreachKey": "NurseAdviceNo", "ApplyBreach": "1", "CallReceivedISO": "2021-11-11T14:32:22", "CallReceivedTimeISO": "2021-11-11T14:30:22", "BreachWarnActualTime": "2021-11-11T14:52:22", "BreachPreActualTime": "2021-11-11T14:32:22", "BreachActualTime": "2021-11-11T15:02:22", "BreachPriority": "3", "BreachPriorityGroup": "", "BreachPriorityLabel": "<PERSON>rgent 30 mins", "CallWithBaseAckTime": "", "CallReceivedTime": "14:30", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "55 yrs", "CallDoctorNameCN": "Disp Cont1", "PatientName": "NW_0003, Terst", "CallTriaged": "No", "CallSymptoms": "", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "00000 000000", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "NW_0003", "CallForename": "<PERSON>rst", "CallDoctorName": "CN=Disp Cont1/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "Z10000", "CallWarmTransferred": "Yes", "CHFinalDispositionCode": "Dx335", "CHFinalDispositionDescription": "Speak to a Clinician from our service Immediately - Other Disposition Validation", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "D2FF98EA29E4A0C88025878B0038C2FD", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01nfWDf5yA0Yo6LXSwWVi0vvl", "CallNHSNo": "**********", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1985-03-12", "CallAddress1": "INTEGRATED CARE 24", "CallAddress2": "KINGSTON HOUSE", "CallAddress3": "THE LONG BARROW", "CallAddress4": "ORBITAL PARK", "CallTown": "ASHFORD", "CallPostCode": "TN24 0GP", "CallClassification": "Nurse Advice", "CC": "Nurse Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "12/11/2021 10:23:58", "Call1stContactPathways": "12/11/2021 10:20:31", "PathwaysCaseId": "4864d910-173d-4223-8669-cab4f73b3565", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "NurseAdviceNo", "ApplyBreach": "1", "CallReceivedISO": "2021-11-12T10:23:12", "CallReceivedTimeISO": "2021-11-12T10:19:59", "BreachWarnActualTime": "2021-11-12T10:33:12", "BreachPreActualTime": "2021-11-12T10:23:12", "BreachActualTime": "2021-11-12T10:38:12", "BreachPriority": "1", "BreachPriorityGroup": "", "BreachPriorityLabel": "<PERSON>rgent 15 mins", "CallWithBaseAckTime": "", "CallReceivedTime": "10:19", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "36 yrs", "CallDoctorNameCN": "<PERSON>", "PatientName": "HOMAN, Amos", "CallTriaged": "No", "CallSymptoms": "Test Call Please Ignore.", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": "", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "HOMAN", "CallForename": "<PERSON>", "CallDoctorName": "CN=<PERSON>/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "A20047", "CallWarmTransferred": "Yes", "CHFinalDispositionCode": "Dx32", "CHFinalDispositionDescription": "Speak to a Clinician from our service Immediately", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "Yes", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Main Base Advice", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "451E6E728A9D48E880258784003BA64F", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01LSzDxkEST104D3idzi70aMd", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1966-06-06", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "LOWESTOFT", "CallAddress3": "", "CallAddress4": "", "CallTown": "", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "Yes", "Call1stContact": "05/11/2021 10:52:40", "Call1stContactPathways": "", "PathwaysCaseId": "47e98706-991e-4247-9b8e-08d9a4646a81", "CallCreatedBy": "Test Doctor", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-05T10:51:32", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:51", "CallAppointmentTime": "", "CallArrivedTime": "10:27", "dtArrivedTime": "12/11/2021 10:27:57", "CallAge": "55 yrs", "CallDoctorNameCN": "Test Doctor", "PatientName": "WEFWEFW, Qedrqw", "CallTriaged": "No", "CallSymptoms": "", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "00000 000000", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "WEFWEFW", "CallForename": "Qedrqw", "CallDoctorName": "CN=Test Doctor/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "Z10000", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "0", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "4F43458E58B65CA68025878A0039905C", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01f73Axzh3s6xVt9eY5bPs2BR", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1996-03-25", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 10:30:13", "Call1stContactPathways": "", "PathwaysCaseId": "ab10e278-4909-4642-1509-08d9a51c556e", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-11T10:28:45", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:28", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "25 yrs", "CallDoctorNameCN": "Test Doctor", "PatientName": "ITK TEST, Test", "CallTriaged": "No", "CallSymptoms": "TEST", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "ITK TEST", "CallForename": "Test", "CallDoctorName": "CN=Test Doctor/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "G00234", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "3457268983AC46588025878A0039D3DA", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01f73Axzh3s6xVt9eY5bPs2BR", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1996-03-25", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 10:30:13", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-11T10:28:45", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:28", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "25 yrs", "CallDoctorNameCN": "<PERSON>", "PatientName": "ITK TEST, Test", "CallTriaged": "No", "CallSymptoms": "TEST", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": "", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "ITK TEST", "CallForename": "Test", "CallDoctorName": "CN=<PERSON>/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "G00234", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "F488BB63749B7EAD8025878A003CD108", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01f73Axzh3s6xVt9eY5bPs2BR", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1996-03-25", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 10:30:13", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-11T10:28:45", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:28", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "25 yrs", "CallDoctorNameCN": "Test Doctor", "PatientName": "ITK TEST, Test", "CallTriaged": "No", "CallSymptoms": "TEST", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "ITK TEST", "CallForename": "Test", "CallDoctorName": "CN=Test Doctor/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "G00234", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "7A82F7846E98FEB38025878A003CDDA9", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01f73Axzh3s6xVt9eY5bPs2BR", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1996-03-25", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 10:30:13", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-11T10:28:45", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:28", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "25 yrs", "CallDoctorNameCN": "Test Doctor", "PatientName": "ITK TEST, Test", "CallTriaged": "No", "CallSymptoms": "TEST", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "ITK TEST", "CallForename": "Test", "CallDoctorName": "CN=Test Doctor/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "G00234", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "C92AC7AE24F721D38025878A004D864C", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01f73Axzh3s6xVt9eY5bPs2BR", "CallNHSNo": "", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1996-03-25", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "11/11/2021 10:30:13", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-11T10:28:45", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "10:28", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "25 yrs", "CallDoctorNameCN": "Test Doctor", "PatientName": "ITK TEST, Test", "CallTriaged": "No", "CallSymptoms": "TEST", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": " ", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "ITK TEST", "CallForename": "Test", "CallDoctorName": "CN=Test Doctor/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "G00234", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "No", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "44E6754898A7ACC68025878B00364379", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01v2D2Q9ZY8wnCtcflfDHCUf0", "CallNHSNo": "**********", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Male", "CallDobIso": "1985-03-12", "CallAddress1": "313 VICTORIA ROAD", "CallAddress2": "", "CallAddress3": "", "CallAddress4": "", "CallTown": "LOWESTOFT", "CallPostCode": "NR33 9LS", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "12/11/2021 09:59:16", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-12T09:52:42", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "09:52", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "36 yrs", "CallDoctorNameCN": "<PERSON>", "PatientName": "HOMAN, Amos", "CallTriaged": "No", "CallSymptoms": "Test Call Please Ignore.", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": "", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "HOMAN", "CallForename": "<PERSON>", "CallDoctorName": "CN=<PERSON>/O=staging", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "A20047", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "Yes", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "1", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Lowestoft", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "CAACCE697BAD08AB8025878B003660BD", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01lWvvhaxYNdeKnNhAr78Avd2", "CallNHSNo": "**********", "CallService": "Norfolk and Wisbech 111", "CallServiceOriginal": "Norfolk and Wisbech 111", "CallServiceAlt": "", "CallMF": "Female", "CallDobIso": "1995-01-18", "CallAddress1": "Integrated Care 24 Kingston House", "CallAddress2": "The Long Barrow Orbital Park", "CallAddress3": "ASHFORD", "CallAddress4": "", "CallTown": "", "CallPostCode": "TN24 0GP", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-12T09:53:57", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "09:53", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "26 yrs", "CallDoctorNameCN": "", "PatientName": "EWART, Faye", "CallTriaged": "No", "CallSymptoms": "Test Call Please Ignore.", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": "", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "EWART", "CallForename": "<PERSON>", "CallDoctorName": "", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "A20047", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "Yes", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Main Base Advice", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}, {"unid": "DAAF560395142BD68025878B00368897", "name": "**********", "Info": "", "IsLocked": "", "CallNo": "**********", "CallID": "01xvzLVbgTaZg27q92REt3i", "CallNHSNo": "**********", "CallService": "South Essex 111", "CallServiceOriginal": "South Essex 111", "CallServiceAlt": "", "CallMF": "Female", "CallDobIso": "1995-01-18", "CallAddress1": "ONICH HOTEL", "CallAddress2": "PRITTLEWELL CHASE", "CallAddress3": "", "CallAddress4": "", "CallTown": "WESTCLIFF-ON-SEA", "CallPostCode": "SS0 0RY", "CallClassification": "Advice", "CC": "Advice", "CSC": "", "WalkIn": "0", "CallUrgentYN": "No", "Call1stContact": "", "Call1stContactPathways": "", "PathwaysCaseId": "", "CallCreatedBy": "<PERSON>", "CallCName": "", "CallCRel": "Patient", "BreachKey": "AdviceNo", "ApplyBreach": "1", "CallReceivedISO": "", "CallReceivedTimeISO": "2021-11-12T09:55:39", "BreachWarnActualTime": "", "BreachPreActualTime": "", "BreachActualTime": "", "BreachPriority": "", "BreachPriorityGroup": "", "BreachPriorityLabel": "", "CallWithBaseAckTime": "", "CallReceivedTime": "09:55", "CallAppointmentTime": "", "CallArrivedTime": "", "dtArrivedTime": "", "CallAge": "26 yrs", "CallDoctorNameCN": "", "PatientName": "EWART, Faye", "CallTriaged": "No", "CallSymptoms": "Test Call Please Ignore.", "CallPtcl": "", "CallStatus": "New", "CallStatusValue": "1", "PatientContactCode": "", "PatientContactCode_count": "", "PatientContactCode_Initial": "", "PatientContactCode_Current_ForView": "", "CallInformationalOutcomes": "", "CallInformationalOutcomesComment": "", "DutyBase": "", "Dispatch_Vehicle": "", "CallCompletedYN": "No", "CallCompleted": "", "CallCallback": "0", "CallTelNo_R": "01233 123123", "CallTelNoAlt_1": "", "CallTelNoAltType_1": "", "CallSurname": "EWART", "CallForename": "<PERSON>", "CallDoctorName": "", "CallSecondOpenCall": "", "CallAdastraSent": "", "CallAdastraLock": "0", "TomTomOrderID": "", "Linked_Call_ID": "", "CallPracticeOCS": "A20047", "CallWarmTransferred": "", "CHFinalDispositionCode": "", "CHFinalDispositionDescription": "", "FinalDispositionCode": "", "FinalDispositionDescription": "", "FLAG_REMOVE_FIRST_CONTACT": "", "PDSTracedAndVerified": "Yes", "PDSTraced": "true", "CliniHighPriority": "", "StartConsultationPerformed": "", "ClinicalHub_111ToHubReason": "", "Courtesy_User": "", "Courtesy_Time": "", "Courtesy_Count": "", "Courtesy_Contact": "", "CAS_TRANSFER_ERROR": "", "Pathways_ITK_Send": "", "ITK_111_Online": "", "AFT_appt_id": "", "AFT_datetime_start": "", "AFT_UNABLE_REASON": "", "DAB_Id": "", "DAB_StartDateTime": "", "DAB_EndDateTime": "", "AFT_time_start": "", "AFT_datetime_start_ORIG": "", "AFT_CANCELLED_REASON": "", "ED_arrived": "", "txtAppointmentBase": "", "CareConnectAppointmentStart": "", "IUC_CAS_AT_ONE_TIME": "", "IUC_Contract": "", "SetBaseInfo_Base": "Southend PCC", "Comfort_SENT_SERVICE": "", "Comfort_SENT_SERVICE_TIME": "", "Comfort_SMS": "", "Comfort_SMS_TIME": "", "Cov19Priority": "", "dateAppointmentStart": "", "EConsult_UTC": "", "EConsult_code": "", "EConsult_Priority": "", "EConsult_PriorityLabel": "", "EConsult_ITK_IN": "", "EConsult_Status": "", "EConsult_Source": "", "EConsult_LivingArrangement": "", "EConsult_AttendanceSource": "", "EConsult_LiveOverseas": "", "EConsult_ArrivalMethod": "", "EConsult_SameProblem": "", "EConsult_XRay": "", "EMIS_CDB": "", "EMIS_PatientId": "", "EMIS_UserId": "", "KMS_SC_MESSAGE": ""}], "Page": {"Enabled": 1, "PageNumber": 1, "PageSize": 100, "getRowCount": 11, "getStartRowNumber": 1, "TotalSearchRowCount": 11, "getTotalNumberOfPages": 1}}