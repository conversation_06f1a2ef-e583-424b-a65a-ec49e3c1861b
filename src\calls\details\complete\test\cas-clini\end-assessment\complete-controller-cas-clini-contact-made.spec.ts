import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { patientReferredToFurtherActionButtonOption } from "@/calls/details/complete/components/patient-referred-to/patient-referred-to-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import {
  CompleteStepName,
  IStep
} from "@/calls/details/complete/complete-models";

const callDetail: ICallDetail = factoryCallDetail();

describe("CAS Clini useCompleteController", () => {
  it("CAS Clini contact made flow", () => {
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    // const step: IStep<CompleteStepName>;

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  will fail as not valid and stay on same step
    const step: IStep<CompleteStepName> =
      controller.state.steps[controller.state.currentStep];
    expect(step.stepId).toBe("HOW_WAS_CASE_MANAGED");
    expect(step.requiresValidation).toBe(true);

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  can move forward from HOW_WAS_CASE_MANAGED to CONTACT_MADE as already answered
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_MADE",
      description: "Contact Made",
      value: "CONTACT_MADE"
    });

    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onPatientReferredTo({
      referredTo: { id: "", description: "", value: "" },
      referredText: "",
      furtherActionGP: { id: "", description: "", value: "" },
      furtherActionGPText: ""
    });
    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onPatientReferredTo({
      referredTo: {
        id: "17-Other Secondary Care",
        description: "Other Secondary Care",
        value: "17-Other Secondary Care"
      },
      referredText: "",
      furtherActionGP: { id: "", description: "", value: "" },
      furtherActionGPText: ""
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "PATIENT_REFERRED_TO__OTHER_SECONDARY_CARE_TEXT"
    );

    controller.onPatientReferredTo({
      referredTo: {
        id: "17-Other Secondary Care",
        description: "Other Secondary Care",
        value: "17-Other Secondary Care"
      },
      referredText: "Some text",
      furtherActionGP: { id: "", description: "", value: "" },
      furtherActionGPText: ""
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");
    expect(controller.state.validationMessages.length).toBe(0);

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");

    controller.onPatientReferredTo({
      referredTo: {
        id: "17-Other Secondary Care",
        description: "Other Secondary Care",
        value: "17-Other Secondary Care"
      },
      referredText: "Some text",
      furtherActionGP:
        patientReferredToFurtherActionButtonOption[
          "1-Patient Advised Contact Own GP"
        ],
      furtherActionGPText: ""
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "PATIENT_REFERRED_TO__FURTHER_ACTION_GP_TEXT"
    );

    controller.onPatientReferredTo({
      referredTo: {
        id: "17-Other Secondary Care",
        description: "Other Secondary Care",
        value: "17-Other Secondary Care"
      },
      referredText: "Some text",
      furtherActionGP:
        patientReferredToFurtherActionButtonOption[
          "1-Patient Advised Contact Own GP"
        ],
      furtherActionGPText: "Some text"
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");
    expect(controller.state.validationMessages.length).toBe(0);

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "READ_CODES__NOT_SELECTED"
    );
  });
});
