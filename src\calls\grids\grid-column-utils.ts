import { CLEO_GRID_COLUMN_NAME } from "@/calls/summary/call-summarry-models";
import GridDefault from "@/calls/grids/grid-default.vue";
import { IGridDefinition } from "@/calls/grids/grid-models";

// Basic grid definition for initialization
const basicGridDef: IGridDefinition = {
  identifier: "CatchAllCalls",
  title: "",
  description: "",
  params: {},
  colDefintions: {
    onlyIncludeTheseFields: [],
    excludeTheseFields: []
  }
};

// Get the default column order
export function getDefaultColumnOrder(): CLEO_GRID_COLUMN_NAME[] {
  const gridDefaultInstance = new GridDefault({
    propsData: {
      gridDefinition: basicGridDef
    }
  });

  return gridDefaultInstance.getCurrentColumnOrder();
}

// Move a column to the front of the order
export function moveColumnToFront(
  columnName: CLEO_GRID_COLUMN_NAME,
  baseOrder: CLEO_GRID_COLUMN_NAME[] = getDefaultColumnOrder()
): CLEO_GRID_COLUMN_NAME[] {
  const customOrder = [...baseOrder];
  const columnIndex = customOrder.indexOf(columnName);

  if (columnIndex !== -1) {
    customOrder.splice(columnIndex, 1);
    customOrder.unshift(columnName);
  }

  return customOrder;
}

// Move a column to a specific position
export function moveColumnToPosition(
  columnName: CLEO_GRID_COLUMN_NAME,
  position: number,
  baseOrder: CLEO_GRID_COLUMN_NAME[] = getDefaultColumnOrder()
): CLEO_GRID_COLUMN_NAME[] {
  const customOrder = [...baseOrder];
  const columnIndex = customOrder.indexOf(columnName);

  if (columnIndex !== -1) {
    customOrder.splice(columnIndex, 1);
    customOrder.splice(position, 0, columnName);
  }

  return customOrder;
}

// Insert a column after another column
export function insertColumnAfter(
  columnToInsert: CLEO_GRID_COLUMN_NAME,
  afterColumnName: CLEO_GRID_COLUMN_NAME,
  baseOrder: CLEO_GRID_COLUMN_NAME[] = getDefaultColumnOrder()
): CLEO_GRID_COLUMN_NAME[] {
  const customOrder = [...baseOrder];

  // First remove the column to insert if it already exists in the array
  const columnToInsertIndex = customOrder.indexOf(columnToInsert);
  if (columnToInsertIndex !== -1) {
    customOrder.splice(columnToInsertIndex, 1);
  }

  // Find the position of the reference column
  const afterColumnIndex = customOrder.indexOf(afterColumnName);

  if (afterColumnIndex !== -1) {
    // Insert the column right after the reference column
    customOrder.splice(afterColumnIndex + 1, 0, columnToInsert);
  } else {
    // If reference column not found, append to the end
    customOrder.push(columnToInsert);
  }

  return customOrder;
}
