import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { getKeywordsStandard } from "@/common/api/keywords-api";
import { SERVICE_NAME } from "@/common/services/services-models";
import { mockOutcomes } from "@/calls/details/complete/components/outcomes/api/mock/mockOutcomes";
import { UserOptionsOutcomesOverrideKey } from "@/calls/details/complete/complete-models";

// E.g. https://ash-brisdoc-cleouat.sehnp.nhs.uk/dev/livexcleolock.nsf/
// xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=CALL_INFORMATIONAL_OUTCOMES~CAS
export function getInformationalOutcomes(options: {
  cleoClientService: CLEO_CLIENT_SERVICE;
  classification: CALL_CLASSIFICATION;
  serviceName: SERVICE_NAME;
  userOptionsOutcomesOverrideKey: UserOptionsOutcomesOverrideKey;
}): Promise<string[] | Record<string, string[]>> {
  let keywordLookup = "";
  let keywordLookupOther = "";

  if (options.userOptionsOutcomesOverrideKey.length > 0) {
    // Override...ignore everything.
    keywordLookup = options.userOptionsOutcomesOverrideKey;
    keywordLookupOther = "OTHER";
  } else {
    if (options.serviceName === "CAS") {
      //  See if keywords for specific CLEO_CLIENT_SERVICE...
      keywordLookup = options.cleoClientService;

      // ...if not, use the generic keyword
      keywordLookupOther = "OTHER";
    } else {
      // OOH...or 111, but this so far is BrisDoc only
      keywordLookup = options.classification;

      // ...if OOH + Advice, shouldn't happen...so default to BASE
      keywordLookupOther = "BASE";
    }
  }

  keywordLookup = keywordLookup.toUpperCase();

  const urlParams = {
    action: "GETKEYWORDSTANDARDJSON",
    sid:
      "CALL_INFORMATIONAL_OUTCOMES" +
      (options.serviceName && options.serviceName.length > 0
        ? "~" + options.serviceName
        : "")
  };

  if (process.env.NODE_ENV === "development") {
    return new Promise(resolve => {
      setTimeout(() => {
        const resp = mockOutcomes;

        const data = resp[keywordLookup] || resp[keywordLookupOther];
        const codeID1: string = data.codeID1 as string;
        const codeID2: string = data.codeID2 as string;
        if (codeID2 && codeID2.length > 0) {
          resolve(JSON.parse(codeID2) as Record<string, string[]>);
          return;
        }

        resolve(codeID1.split(";") as string[]);
      }, 2000);
    });
  }

  return getKeywordsStandard(urlParams).then(resp => {
    const data = resp[keywordLookup] || resp[keywordLookupOther];
    if (data && data.codeID1) {
      const codeID1: string = data.codeID1 as string;
      const codeID2: string = data.codeID2 as string;
      if (codeID2 && codeID2.length > 0) {
        return JSON.parse(codeID2) as Record<string, string[]>;
      }
      return codeID1.split(";") as string[];
    }
    return [];
  });
}
