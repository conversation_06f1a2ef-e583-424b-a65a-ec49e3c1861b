import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import { doesPassFailedContactValidation } from "@/calls/details/complete/complete-service";
import * as CallDetailService from "@/calls/details/call-detail-service";

describe("111 Clini useCompleteController Dx validation REQUIRED", () => {
  /**
   *
   */
  it("111 Clini contact made flow", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: CallDetailService.factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "COMPLETE_PROCESS"
    );
    expect(controller.state.processName).toBe("COMPLETE_111_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(true);
    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(10);

    expect(stepNames[0]).toBe("END_ASSESSMENT_CONFIRMATION");
    expect(stepNames[1]).toBe("CONTACT_MADE");
    expect(stepNames[2]).toBe("OUTCOMES");
    expect(stepNames[3]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[4]).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(stepNames[5]).toBe("FAILED_CONTACT_RISK_ASSESSMENT");
    expect(stepNames[6]).toBe("CLINICAL_VALIDATION");
    expect(stepNames[7]).toBe("TAXI");
    expect(stepNames[8]).toBe("VULNERABILITY");
    expect(stepNames[9]).toBe("UNKNOWN");

    /*
        END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
    CONTACT_MADE: stepsDefault.CONTACT_MADE,
    OUTCOMES: stepsDefault.OUTCOMES,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    INSUFFICIENT_CONTACT_ATTEMPTS: stepsDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
    CLINICAL_VALIDATION: stepsDefault.CLINICAL_VALIDATION,
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
     */

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_MADE",
      description: "Contact Made",
      value: "CONTACT_MADE"
    });
    controller.goto("NEXT");
    // expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");
    expect(controller.state.currentStep).toBe("OUTCOMES");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");

    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME"
    );

    // controller.onClinicalValidationSelected({
    //   id: "LACK_OF_CLINICAL_CAPACITY",
    //   description: "Lack of Clinical Capacity",
    //   value: "LACK_OF_CLINICAL_CAPACITY"
    // });
    //
    // expect(controller.state.currentStep).toBe("OUTCOMES");

    //  but not any further as not answered CONTACT_MADE
    // controller.goto("NEXT");
    // expect(controller.state.validationMessages.length).toBe(1);
    // expect(controller.state.validationMessages[0].id).toBe(
    //   "OUTCOMES__SELECT_OUTCOME"
    // );

    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    expect(controller.state.currentStep).toBe("OUTCOMES");

    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe("OUTCOMES");

    // controller.goto("NEXT");
    // expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    // expect(controller.state.validationMessages.length).toBe(1);
    // expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");
    //
    // controller.onInsufficientContactAttemptTypeSelected({
    //   id: "SAVE_AND_RETURN",
    //   description: "Save and Return to Queue",
    //   value: "SAVE_AND_RETURN"
    // });

    expect(controller.state.isProcessComplete).toBe(true);
  });

  it("111 Clini contact FAILURE but NOT FAILURE made flow", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "COMPLETE_PROCESS"
    );
    expect(controller.state.processName).toBe("COMPLETE_111_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(true);
    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(10);

    expect(stepNames[0]).toBe("END_ASSESSMENT_CONFIRMATION");
    expect(stepNames[1]).toBe("CONTACT_MADE");
    expect(stepNames[2]).toBe("OUTCOMES");
    expect(stepNames[3]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[4]).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(stepNames[5]).toBe("FAILED_CONTACT_RISK_ASSESSMENT");
    expect(stepNames[6]).toBe("CLINICAL_VALIDATION");
    expect(stepNames[7]).toBe("TAXI");
    expect(stepNames[8]).toBe("VULNERABILITY");
    expect(stepNames[9]).toBe("UNKNOWN");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_FAILURE",
      description: "Contact Failure",
      value: "CONTACT_FAILURE"
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onClinicalValidationSelected({
      id: "LACK_OF_CLINICAL_CAPACITY",
      description: "Lack of Clinical Capacity",
      value: "LACK_OF_CLINICAL_CAPACITY"
    });

    expect(controller.state.currentStep).toBe("OUTCOMES");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME"
    );

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME"
    );

    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    controller.goto("NEXT");

    //  the user has issued a FAILED CONTACT (LACK_OF_CLINICAL_CAPACITY)...but we have no failed contacts
    //  so this case does NOT need to stay open and they can complete the process.
    expect(
      controller.completeControllerInput.callDetail.failedContacts.length
    ).toBe(0);

    expect(controller.state.currentStep).toBe("OUTCOMES");

    // controller.goto("NEXT");
    // expect(controller.state.validationMessages.length).toBe(1);
    // expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");
    //
    // expect(controller.state.isProcessComplete).toBe(false);
    //
    // controller.onInsufficientContactAttemptTypeSelected({
    //   id: "SAVE_AND_RETURN",
    //   description: "Save and Return to Queue",
    //   value: "SAVE_AND_RETURN"
    // });

    expect(controller.state.isProcessComplete).toBe(true);
  });

  it("111 Clini contact FAILURE but NOT FAILURE made flow LOADS of failed contacts", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    //  Add a load of failed contacts
    callDetail.failedContacts = [
      {
        userName: "Bob",
        type: "NO_ANSWER",
        time: "2020-01-01T12:00:00"
      },
      {
        userName: "Bob",
        type: "NO_ANSWER",
        time: "2020-01-01T12:00:01"
      },
      {
        userName: "Bob",
        type: "NO_ANSWER",
        time: "2020-01-01T12:00:02"
      },
      {
        userName: "Bob",
        type: "NO_ANSWER",
        time: "2020-01-01T12:00:03"
      },
      {
        userName: "Bob",
        type: "NO_ANSWER",
        time: "2020-01-01T12:00:04"
      }
    ];

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20,
          seedTime: "2020-01-01T12:25:00"
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "COMPLETE_PROCESS"
    );
    expect(controller.state.processName).toBe("COMPLETE_111_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(true);
    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(10);

    expect(stepNames[0]).toBe("END_ASSESSMENT_CONFIRMATION");
    expect(stepNames[1]).toBe("CONTACT_MADE");
    expect(stepNames[2]).toBe("OUTCOMES");
    expect(stepNames[3]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[4]).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(stepNames[5]).toBe("FAILED_CONTACT_RISK_ASSESSMENT");
    expect(stepNames[6]).toBe("CLINICAL_VALIDATION");
    expect(stepNames[7]).toBe("TAXI");
    expect(stepNames[8]).toBe("VULNERABILITY");
    expect(stepNames[9]).toBe("UNKNOWN");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.onContactMade({
      id: "CONTACT_FAILURE",
      description: "Contact Failure",
      value: "CONTACT_FAILURE"
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onClinicalValidationSelected({
      id: "LACK_OF_CLINICAL_CAPACITY",
      description: "Lack of Clinical Capacity",
      value: "LACK_OF_CLINICAL_CAPACITY"
    });

    expect(controller.state.currentStep).toBe("OUTCOMES");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("CLINICAL_VALIDATION");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME"
    );

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("OUTCOMES");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "OUTCOMES__SELECT_OUTCOME"
    );

    expect(controller.state.isProcessComplete).toBe(false);

    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    controller.goto("NEXT");

    //  the user has issued a FAILED CONTACT...but we have lots of failed contacts
    //  so this case needs to stay open and they need to try x more times.
    expect(
      controller.completeControllerInput.callDetail.failedContacts.length
    ).toBe(5);

    expect(
      doesPassFailedContactValidation(
        controller.state,
        callDetail.failedContacts,
        completeControllerInput.failedContactConfig.config.seedTime
      )
    ).toBe(true);

    expect(controller.state.currentStep).toBe("OUTCOMES");

    expect(controller.state.isProcessComplete).toBe(true);

    expect(controller.state.ui.disableComplete).toBe(false);
  });
});
