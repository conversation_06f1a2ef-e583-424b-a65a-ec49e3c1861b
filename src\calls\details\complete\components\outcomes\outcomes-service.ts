export type OutcomeName = string;
export type SubOutcomeName = string;

export type OutcomesOptions = Record<OutcomeName, SubOutcomeName[]>;

export function convertServerResponse(
  data: Record<string, string>
): OutcomesOptions {
  // Convert data to OutcomesOptions. Split the key using a "-" and assign the first part to Outcome and the second part the suboutcome
  // If the outcome does not exist in the outcomesOptions object, create a new array for the outcome
  // Push the suboutcome to the outcome array if the suboutcome is not already in the array and it's length is greater than 0.
  // Return the outcomesOptions object
  return Object.keys(data).reduce<OutcomesOptions>((outcomesOptions, key) => {
    const outcomeAndSubOutcome = key.split("-");
    const outcome = outcomeAndSubOutcome[0];
    const subOutcome = outcomeAndSubOutcome[1];
    if (!outcomesOptions[outcome]) {
      outcomesOptions[outcome] = [];
    }
    if (subOutcome && subOutcome.length > 0) {
      outcomesOptions[outcome].push(subOutcome);
    }
    return outcomesOptions;
  }, {});
}
