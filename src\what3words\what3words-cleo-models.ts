import {
  What3WordsCoordinates,
  What3WordsThreeWordsPartial,
  What3WordsThreeWords,
  What3WordsResponse,
  What3WordsSuggestionResponse,
  What3WordsSuggestion
} from "@/typings/unknown-lib";
import { IPafAddress } from "@/paf/paf-models";
import QueryAutocompletePrediction = google.maps.places.QueryAutocompletePrediction;
import PlaceResult = google.maps.places.PlaceResult;
import { ICallDetail } from "@/calls/details/call-details-models";
import { ICleoPermission } from "@/permissions/permission-models";
import { ITomTomVehicle, IVehicle } from "@/vehicles/vehicles-models";

export interface IWhat3wordsControllerState {
  apiKey: string;
  version: string;
  isLoading: boolean;
  map: {
    apiKey: string;
    gridOverlayData: any;
    debounceApplyOverlay: any;
    isZoomOkForOverlay: boolean;
    uiOptions: {
      mapStyle: "map" | "satellite";
    };
  };
  userInput: {
    divSuffix: string;
    coordinates: What3WordsCoordinates;
    words: What3WordsThreeWords;
    autoSuggest: What3WordsThreeWordsPartial;
    pafPostCode: string;
    mobileNumber: string;
    what3WordsConfirmed: boolean;
    addressConfirmed: boolean;
    googleAutoPredictionQuery: string;
    autoSuggestSearchType: "w3w" | "google";
    defaultLocation: {
      addressString: string;
    };
    callDetail: ICallDetail;
    userPermissions: Record<string, ICleoPermission>;
  };
  results: {
    what3WordsResponse: What3WordsResponse | null;
    suggestions: What3WordsSuggestionResponse | null;
    suggestion: What3WordsSuggestion | null;
    geocoderResults: google.maps.GeocoderResult[];
    geocoderResult: google.maps.GeocoderResult | null;
    geocoderResultsPostCodes: string[];
    pafResults: IPafAddress[];
    pafSelected: IPafAddress | null;
    mapMarkers: google.maps.Marker[];
    nearestAddress: google.maps.GeocoderResult | null;
    smsCount: number;
    googleAutoPredictions: QueryAutocompletePrediction[];
    googleAutoPrediction: QueryAutocompletePrediction | null;
    googlePlaceResult: PlaceResult | null;
    vehicles: IVehicle[];
    tomTomVehicles: ITomTomVehicle[];
  };
  display: {
    pafPicker: boolean;
    pafIsLoading: boolean;
    smsIsLoading: boolean;
  };
}

export interface IGeocoderResult extends google.maps.GeocoderResult {
  bs: boolean;
}

export interface IWhat3WordsMapResponse {
  what3WordsResponse: What3WordsResponse | null;
  nearestAddress: google.maps.GeocoderResult | null;
  smsCount: number;
  cleoAddress: IWhat3WordsMapCleoAddress | null;
}

export interface IWhat3WordsMapCleoAddress {
  address1: string;
  address2: string;
  address3: string;
  address4: string;
  town: string;
  postCode: string;
  GPS: string;
}
