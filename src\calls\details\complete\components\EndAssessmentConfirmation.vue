<template>
  <div>
    <CompleteStepHeader :step="step" />

    <div class="ic24-flex-column ic24-flex-gap">
      <span class="complete-step--subheader">
        Are you sure there is no further action required on the case?
      </span>

      <div class="ic24-vertical-spacer-large"></div>

      <div
        class="ic24-flex-column ic24-flex-gap-large end-assessment-confirmation"
      >
        <span
          v-for="(message, index) in messages"
          :key="index"
          class="complete-step--subheader end-assessment-confirmation"
          v-text="message"
        >
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "EndAssessmentConfirmation",
  components: { CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<"END_ASSESSMENT_CONFIRMATION">,
      required: true
    },
    messages: {
      type: Array as PropType<string[]>,
      required: true
    }
  }
});
</script>

<style>
.end-assessment-confirmation {
  color: var(--red-500) !important;
}
</style>
