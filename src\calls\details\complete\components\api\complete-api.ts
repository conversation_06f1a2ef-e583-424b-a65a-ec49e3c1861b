import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";
import { completeOutcomesMockOptions } from "@/calls/details/complete/components/api/complete-api-mock";

export class CompleteApi {
  public getOutcomes(service: string): Promise<Record<string, string>> {
    if (process.env.NODE_ENV === "development") {
      return new Promise(resolve => {
        resolve(completeOutcomesMockOptions);
      });
    } else {
      const url =
        CallControllerClient.PathCleoBeanInterface +
        "?processformat=json&action=GETPATIENTOUTCOMES" +
        "&service=" +
        service;

      return localCache.getUrlDataWithCache(url, true) as Promise<
        Record<string, string>
      >;

      /*
      return https.get(
        CLEO_CONFIG.CLEO.XCLEO_PATH +
          "/xpbeaninterface.xsp?processformat=json&action=GETPATIENTOUTCOMES&service=" +
          service,
        {
          responseType: "json"
        }
      );
      */
    }
  }
}
