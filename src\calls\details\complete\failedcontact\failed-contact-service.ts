import { factoryBaseControllerState } from "../complete-service";
import { IFailedContactControllerState } from "./IFailedContactControllerState";

export function factoryFailedContactControllerState(): IFailedContactControllerState {
  return {
    ...factoryBaseControllerState(),
    count: 1,
    currentStep: "END_ASSESSMENT_CONFIRMATION",
    steps: {
      END_ASSESSMENT_CONFIRMATION: {
        stepId: "END_ASSESSMENT_CONFIRMATION",
        title: "End Assessment Confirmation",
        isValid: false,
        requiresValidation: false,
        validationMessages: []
      },
      HOW_WAS_CASE_MANAGED: {
        stepId: "HOW_WAS_CASE_MANAGED",
        title: "How was Case Managed",
        isValid: false,
        requiresValidation: true,
        validationMessages: []
      },
      CONTACT_MADE: {
        stepId: "CONTACT_MADE",
        title: "Has Successful contact been made",
        isValid: false,
        requiresValidation: true,
        validationMessages: []
      },
      FAILED_CONTACT_SAFEGUARDING: {
        stepId: "FAILED_CONTACT_SAFEGUARDING",
        title: "Safeguarding Review Failed Contact",
        isValid: false,
        requiresValidation: true,
        validationMessages: []
      },
      UNKNOWN: {
        stepId: "UNKNOWN",
        title: "UNKNOWN",
        isValid: false,
        requiresValidation: true,
        validationMessages: []
      }
    },
    userResponse: {
      howManaged: { id: "", description: "", value: "" },
      contactMade: null,
      failedContactReason: { id: "", description: "", value: "" },
      safeguarding: {
        risk: "",
        furtherAction: null,
        welfareAction: ""
      }
    }
  };
}
