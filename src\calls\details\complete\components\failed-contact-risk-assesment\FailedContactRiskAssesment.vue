<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <p
      class="complete-step--subheader"
      :class="
        doesPassFailedContactValidation
          ? 'sufficient-contact-attempts'
          : 'insufficient-contact-attempts'
      "
      v-text="getUserMessage"
    ></p>

    <div class="ic24-vertical-spacer-large"></div>

    <div class="failed-contact-risk-assesment--button-label">
      By clicking Complete below you agree that you have followed the
      organisational policy for completing this case, including the
      documentation of a risk assessment
    </div>
    <Ic24Button
      @click="buttonClick('COMPLETE')"
      title="Complete Case"
      class="complete-step--select-button failed-contact-risk-assesment--button"
    />

    <div class="ic24-vertical-spacer-large"></div>

    <div class="failed-contact-risk-assesment--button-label">
      Alternatively:
    </div>
    <div class="failed-contact-risk-assesment--button-label">
      To save the case to the clinical queue, please click below:
    </div>
    <Ic24Button
      @click="buttonClick('SAVE_AND_RETURN_TO_QUEUE')"
      title="Save & Return to Queue"
      class="complete-step--select-button failed-contact-risk-assesment--button"
    />

    <div class="ic24-vertical-spacer-large"></div>

    <div class="failed-contact-risk-assesment--button-label">
      To return to the active case, please click below:
    </div>
    <Ic24Button
      @click="buttonClick('RETURN_TO_OPEN_CASE')"
      title="Return to Open case"
      class="complete-step--select-button failed-contact-risk-assesment--button"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import {
  IFailedContactComplete,
  IFailedContactRiskAssessment,
  IStep
} from "@/calls/details/complete/complete-models";

import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { IFailedContact } from "@/calls/details/call-details-models";
import { failedContactRiskAssesmentAction } from "@/calls/details/complete/components/failed-contact-risk-assesment/failed-contact-rosk-assesment-models";
import { getFailedContactMessage } from "@/calls/details/complete/complete-service";

export default defineComponent({
  name: "FailedContactRiskAssesment",
  components: { Ic24Button, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"FAILED_CONTACT_RISK_ASSESSMENT">>,
      required: true
    },
    value: {
      type: Object as PropType<IFailedContactRiskAssessment>,
      required: true
    },
    doesPassFailedContactValidation: {
      type: Boolean as PropType<boolean>,
      required: true
    },
    failedContacts: {
      type: Array as PropType<IFailedContact[]>,
      required: true
    },
    failedContactConfig: {
      type: Object as PropType<IFailedContactComplete>,
      required: true
    }
  },
  setup(
    props: {
      step: PropType<IStep<"FAILED_CONTACT_RISK_ASSESSMENT">>;
      value: IFailedContactRiskAssessment;
      doesPassFailedContactValidation: boolean;
      failedContacts: IFailedContact[];
      failedContactConfig: IFailedContactComplete;
    },
    context: SetupContext
  ) {
    const failedContactRiskAssessmentInternal = ref<
      IFailedContactRiskAssessment
    >(simpleObjectClone(props.value));

    watch(
      () => props.value,
      newValue => {
        failedContactRiskAssessmentInternal.value = simpleObjectClone(newValue);
      }
    );

    function buttonClick(action: failedContactRiskAssesmentAction) {
      context.emit("input", action);
    }

    // const getUserMessage = computed(() => {
    //   return contactMadeMessage(
    //     props.failedContactConfig.config.attemptsRequired,
    //     props.failedContacts.length,
    //     props.doesPassFailedContactValidation
    //   );
    // });

    const getUserMessage = computed(() => {
      return getFailedContactMessage(
        props.failedContactConfig,
        props.failedContacts,
        ""
      );
    });

    return { getUserMessage, buttonClick };
  }
});
</script>

<style scoped>
.insufficient-contact-attempts {
  color: #c9372c;
}

.sufficient-contact-attempts {
  color: #22a06b;
}

.failed-contact-risk-assesment--button-label {
  font-size: 14px;
}

.failed-contact-risk-assesment--button {
  width: 300px;
}
</style>
