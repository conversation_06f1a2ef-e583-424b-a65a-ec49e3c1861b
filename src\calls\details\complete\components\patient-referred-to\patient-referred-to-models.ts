import { ISimpleButtonInputValue } from "@/calls/details/complete/simple-button-selector-models";

export type PatientReferredToType =
  | "1-Advice Only"
  | "2-Advice Prescription"
  | "3-Appointment Declined"
  | "4-Duplicate Case"
  | "5-Patient Deceased"
  | "6-Called 999"
  | "7-Base Appointment"
  | "8-UTC"
  | "9-OOH Home Visit"
  | "10-Emergency Department"
  | "11-SDEC"
  | "12-Surgical"
  | "13-Medical"
  | "14-Paediatric"
  | "15-OG Ward"
  | "16-Early Pregnancy Unit"
  | "17-Other Secondary Care"
  | "18-PCN contracted bookable 111 slot"
  | "19-Community Nurses"
  | "20-Palliative Care"
  | "21-Mental Health Team"
  | "22-Dental Service"
  | "23-Other Community Services";

export const patientReferredToButtonOption: Record<
  PatientReferredToType,
  ISimpleButtonInputValue<PatientReferredToType, PatientReferredToType>
> = {
  "1-Advice Only": {
    id: "1-Advice Only",
    description: "Advice Only",
    value: "1-Advice Only"
  },
  "2-Advice Prescription": {
    id: "2-Advice Prescription",
    description: "Advice Prescription",
    value: "2-Advice Prescription"
  },
  "3-Appointment Declined": {
    id: "3-Appointment Declined",
    description: "Appointment Declined",
    value: "3-Appointment Declined"
  },
  "4-Duplicate Case": {
    id: "4-Duplicate Case",
    description: "Duplicate Case",
    value: "4-Duplicate Case"
  },
  "5-Patient Deceased": {
    id: "5-Patient Deceased",
    description: "Patient Deceased",
    value: "5-Patient Deceased"
  },
  "6-Called 999": {
    id: "6-Called 999",
    description: "Called 999",
    value: "6-Called 999"
  },
  "7-Base Appointment": {
    id: "7-Base Appointment",
    description: "Base Appointment",
    value: "7-Base Appointment"
  },
  "8-UTC": {
    id: "8-UTC",
    description: "UTC",
    value: "8-UTC"
  },
  "9-OOH Home Visit": {
    id: "9-OOH Home Visit",
    description: "OOH Home Visit",
    value: "9-OOH Home Visit"
  },
  "10-Emergency Department": {
    id: "10-Emergency Department",
    description: "Emergency Department",
    value: "10-Emergency Department"
  },
  "11-SDEC": {
    id: "11-SDEC",
    description: "SDEC",
    value: "11-SDEC"
  },
  "12-Surgical": {
    id: "12-Surgical",
    description: "Surgical",
    value: "12-Surgical"
  },
  "13-Medical": {
    id: "13-Medical",
    description: "Medical",
    value: "13-Medical"
  },
  "14-Paediatric": {
    id: "14-Paediatric",
    description: "Paediatric",
    value: "14-Paediatric"
  },
  "15-OG Ward": {
    id: "15-OG Ward",
    description: "OG Ward",
    value: "15-OG Ward"
  },
  "16-Early Pregnancy Unit": {
    id: "16-Early Pregnancy Unit",
    description: "Early Pregnancy Unit",
    value: "16-Early Pregnancy Unit"
  },
  "17-Other Secondary Care": {
    id: "17-Other Secondary Care",
    description: "Other Secondary Care",
    value: "17-Other Secondary Care"
  },
  "18-PCN contracted bookable 111 slot": {
    id: "18-PCN contracted bookable 111 slot",
    description: "PCN contracted bookable 111 slot",
    value: "18-PCN contracted bookable 111 slot"
  },
  "19-Community Nurses": {
    id: "19-Community Nurses",
    description: "Community Nurses",
    value: "19-Community Nurses"
  },
  "20-Palliative Care": {
    id: "20-Palliative Care",
    description: "Palliative Care",
    value: "20-Palliative Care"
  },
  "21-Mental Health Team": {
    id: "21-Mental Health Team",
    description: "Mental Health Team",
    value: "21-Mental Health Team"
  },
  "22-Dental Service": {
    id: "22-Dental Service",
    description: "Dental Service",
    value: "22-Dental Service"
  },
  "23-Other Community Services": {
    id: "23-Other Community Services",
    description: "Other Community Services",
    value: "23-Other Community Services"
  }
};

export type PatientReferredToFurtherActionType =
  | "1-Patient Advised Contact Own GP"
  | "2-GP to contact Patient";

export const patientReferredToFurtherActionButtonOption: Record<
  PatientReferredToFurtherActionType,
  ISimpleButtonInputValue<
    PatientReferredToFurtherActionType,
    PatientReferredToFurtherActionType
  >
> = {
  "1-Patient Advised Contact Own GP": {
    id: "1-Patient Advised Contact Own GP",
    description: "Patient Advised Contact Own GP",
    value: "1-Patient Advised Contact Own GP"
  },
  "2-GP to contact Patient": {
    id: "2-GP to contact Patient",
    description: "GP to contact Patient",
    value: "2-GP to contact Patient"
  }
};
