<template>
  <button class="ic24-card-content" v-on:click="selected" :disabled="isDisabled">
    <div
      class="ic24-card"
      style="flex-grow: 1"
      :class="hasContent ? 'ic24-qa--inline-form' : 'ic24-card--clickable'"
    >
      <div class="ic24-card-grid--title-code">
        <h5 v-text="title"></h5>
        <span v-text="uniqueIdentifier"></span>
      </div>
      <sub v-text="(hasContent ? '(i) ' : '') + description"></sub>

      <slot name="content"></slot>
    </div>
  </button>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "ic24-card-button",
  components: {},
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      }
    },
    uniqueIdentifier: {
      type: [String, Number],
      default: () => {
        return "";
      }
    },
    description: {
      type: String,
      default: () => {
        return "";
      }
    },
    hasContent: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  setup(
    props: {
      hasContent: boolean;
    },
    context: SetupContext
  ) {
    function selected() {
      if (props.hasContent) {
        return;
      }
      context.emit("selected");
    }

    return { selected };
  }
});
</script>

<style>
button.ic24-card-content {
  align-items: unset;
  justify-content: unset;
  height: fit-content;
  padding: unset;
  margin: unset;
  background: none;
  border: none;
  text-align: unset;
}

button.ic24-card-content>.ic24-card {
  width: 100%;
}

.ic24-card-grid--title-code,
button.ic24-card-content>.ic24-card-grid--title-code {
  display: grid;
  grid-template-columns: 1fr 200px;
  align-items: flex-start;
}

.ic24-card-grid--title-code h4,
button.ic24-card-content>.ic24-card-grid--title-code h4 {
  font: var(--ic24-header--400);
  text-align: left;
  text-transform: uppercase;
}

.ic24-card-grid--title-code span,
button.ic24-card-content>.ic24-card-grid--title-code span,
button.ic24-card-content:disabled:active>.ic24-card--clickable>.ic24-card-grid--title-code span {
  font: var(--ic24-caption--200);
  font-weight: 600;
  text-align: right;
  color: var(--ic24-card-well--tertiary-text-color);
}

.ic24-card-grid--title-code h5,
button.ic24-card-content>.ic24-card-grid--title-code h5 {
  font: var(--ic24-header--400);
  text-align: left;
  color: var(--ic24-card-title--text-color);
}

.ic24-card-well sub,
button.ic24-card-content .ic24-card-well sub {
  font: var(--ic24-body--100);
  font-weight: 500;
  letter-spacing: 0.012em;
}

.ic24-qa--inline-form>.ic24-card-well p,
.ic24-qa--address-container,
button.ic24-card-content>.ic24-card-well p,
button.ic24-card-content>.ic24-qa--address-container {
  color: var(--slate-700);
}

.ic24-qa--inline-form h5,
.ic24-qa--inline-form label,
button.ic24-card-content>.ic24-qa--inline-form h5,
button.ic24-card-content>.ic24-qa--inline-form label {
  color: var(--slate-600);
}

.ic24-card--clickable,
button.ic24-card-content>.ic24-card--clickable {
  cursor: pointer;
  transition: all var(--ic24-transition--ease-in-out);
}

.ic24-qa--container,
button.ic24-card-content>.ic24-qa--container {
  gap: 4px;
}

.ic24-qa--container>.ic24-card>.ic24-card-grid--title-code h5,
.ic24-qa--container>button.ic24-card-content>.ic24-card>.ic24-card-grid--title-code h5 {
  font: var(--ic24-subheader--300);
}

.ic24-qa--container>.ic24-card--clickable>.ic24-card-grid--title-code h5,
.ic24-qa--container>.ic24-qa--inline-form>.ic24-card-grid--title-code h5,
.ic24-qa--container>button.ic24-card-content>.ic24-card--clickable>.ic24-card-grid--title-code h5,
.ic24-qa--container>button.ic24-card-content>.ic24-qa--inline-form>.ic24-card-grid--title-code h5,
button.ic24-card-content:disabled:active>.ic24-card--clickable>.ic24-card-grid--title-code h5 {
  color: var(--ic24-button-primary--background);
}

.ic24-qa--container>.ic24-card>sub,
.ic24-qa--container>button.ic24-card-content>.ic24-card>sub,
button.ic24-card-content:disabled:active>.ic24-card>sub {
  font: var(--ic24-body--100);
  color: var(--slate-500);
}

.ic24-qa-step--container {
  display: flex;
  flex-wrap: nowrap;
  color: var(--ic24-button-primary--background);
}

.ic24-qa-step--container>h5 {
  font: var(--ic24-subheader--300);
}

button.ic24-card-content:disabled:hover>.ic24-card--clickable,
button.ic24-card-content:disabled:focus>.ic24-card--clickable,
button.ic24-card-content:disabled:active>.ic24-card--clickable {
  border-color: var(--ic24-card--border-color);
  cursor: default;
}

.ic24-card--clickable:hover,
.ic24-card--clickable:focus,
.ic24-card--clickable:active {
  border-color: var(--ic24-button-primary--background);
}

button.ic24-card-content:disabled:active>.ic24-card--clickable {
  background: var(--ic24-card--background);
}

.ic24-card--clickable:active {
  background: var(--ic24-button-primary--background);
}

.ic24-card--clickable:active>.ic24-card-grid--title-code h5,
.ic24-card--clickable:active>.ic24-card-grid--title-code span,
.ic24-card--clickable:active>sub,
.ic24-card--clickable:active>.ic24-card-grid--title-code>div h5,
button.ic24-card-content:active>.ic24-card--clickable>.ic24-card-grid--title-code h5,
button.ic24-card-content:active>.ic24-card--clickable>.ic24-card-grid--title-code span,
button.ic24-card-content:active>.ic24-card--clickable>sub,
button.ic24-card-content:active>.ic24-card--clickable>.ic24-card-grid--title-code>div h5 {
  color: var(--ic24-button-primary--text-color);
}

.ic24-qa-section-title--container>h5 {
  margin: 0 12px 0 0;
}

.ic24-qa-section-title--container>.ic24-icon-text--container {
  gap: 4px;
}

.ic24-icon-text--container {
  font: var(--ic24-body--100);
}

.ic24-qa--address-wrapper {
  margin: 12px 0 0 0;
}

.ic24-qa--input-container {
  gap: 4px;
}

.ic24-qa--input-container>textarea {
  height: 60px;
}

/*************************************************************************************************************************************
Media queries to target specific screen sizes
*************************************************************************************************************************************/

@media screen and (max-width: 450px) {

  .ic24-card-grid--title-code {
    grid-template-columns: 1fr 60px;
  }

  .ic24-section-wrapper button {
    width: 100%;
  }
}
</style>
