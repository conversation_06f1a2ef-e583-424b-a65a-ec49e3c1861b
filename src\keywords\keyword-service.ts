import {
  IAcvpuType,
  IClassification,
  ICleoBase, ICleoRole,
  IClientDevice, IFailedContactCode,
  IKeywordsRefData,
  IKeywordsRefDataKey, IPatientRepresentative, ITemperatureScale
} from "@/keywords/keywords-models";
import { CommonService } from "@/common/common-service";
const commonService: CommonService = new CommonService();

export class KeywordService {

  public factoryClientDevice(): IClientDevice {
    return {
      Id: 0,
      Active: false,
      DeviceName: "",
      MacAddress: "",
      MachineSid: ""
    };
  }

  public factoryKeywordsRefData(): IKeywordsRefData {
    return {
      bases: [],
      classifications: [],
      roles: [],
      acvputypes: [],
      patientrepresentatives: [],
      temperaturescaletypes: [],
      clientdevices: [],
      failedcontactcodes: []
    }
  }

  public mapKeywordsRefData(
    keywordsRefData: IKeywordsRefData
  ): IKeywordsRefDataKey {
    const keywordsRefDataKey: IKeywordsRefDataKey = {};
    const keys: (keyof IKeywordsRefData)[] = Object.keys(
      keywordsRefData
    ) as (keyof IKeywordsRefData)[];
    keys.reduce(
      (accum: IKeywordsRefDataKey, keyName: keyof IKeywordsRefData) => {
        if (keywordsRefData[keyName]) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //  @ts-ignore
          accum[keyName] = commonService.convertArrayToObject("Id",keywordsRefData[keyName] );
        }
        return accum;
      },
      keywordsRefDataKey
    );
    return keywordsRefDataKey;
  }

  public getKeywordsRefObject<ObjectType>(
    keywordsRefDataKey: IKeywordsRefDataKey,
    keywordGroupName: keyof IKeywordsRefDataKey,
    key: string | number
  ): ObjectType | null {

    if (keywordsRefDataKey && keywordsRefDataKey[keywordGroupName]) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const records: Record<string, ObjectType> =
        keywordsRefDataKey[keywordGroupName];
      return records[key.toString()];
    }
    return null;
  }
}
