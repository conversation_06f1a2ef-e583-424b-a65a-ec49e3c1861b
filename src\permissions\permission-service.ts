import {
  CLEO_PERMISSION_FORM_TYPE,
  ICleoPermission,
  IJwtClaims,
  PERMISSION_NAMES
} from "@/permissions/permission-models";
import { IPermissionStoreState } from "@/permissions/permisssion-store";
import { ITokenResponse } from "@/login/login-models";

export class PermissionService {
  public factoryPermissionStoreState(): IPermissionStoreState {
    return {
      isLoadingPerms: false,
      userAppPerms: {},
      userAppPermsShort: {}
    };
  }

  public upperCasePerms(
    perms: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    if (perms && Object.keys(perms).length > 0) {
      return Object.keys(perms).reduce(
        (accum: Record<string, ICleoPermission>, permKey: string) => {
          const cleoPermission = { ...perms[permKey] };

          let permissionAction = cleoPermission.PermissionAction
            ? cleoPermission.PermissionAction.toUpperCase()
            : permKey;
          if (permissionAction.indexOf(".") > -1) {
            permissionAction = permissionAction.split(".")[1];
          }

          cleoPermission.PermissionAction = permissionAction;
          accum[permKey.toUpperCase()] = cleoPermission;
          return accum;
        },
        {} as Record<string, ICleoPermission>
      );
    }
    return {};
  }

  public simpleKeyPerms(
    perms: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    if (perms && Object.keys(perms).length > 0) {
      const permsUpperCase = this.upperCasePerms(perms);
      return Object.keys(permsUpperCase).reduce(
        (accum: Record<string, ICleoPermission>, permKey: string) => {
          let permKeySimple = permKey;
          if (permKeySimple.indexOf(".") > -1) {
            permKeySimple = permKeySimple.split(".")[1];
          }
          accum[permKeySimple] = permsUpperCase[permKey];
          return accum;
        },
        {} as Record<string, ICleoPermission>
      );
    }
    return {};
  }

  public getUserPermission(
    perms: Record<string, ICleoPermission>,
    permissionName: string,
    formType?: CLEO_PERMISSION_FORM_TYPE
  ): ICleoPermission | null {
    const permsInternal = this.upperCasePerms(perms);
    permissionName = permissionName.toUpperCase();

    const formTypes: CLEO_PERMISSION_FORM_TYPE[] = [
      "CALL",
      "ADMIN",
      "GLOBAL",
      "PATIENT",
      "WEBUI_DOCVIEW"
    ];

    let key = "";
    if (formType) {
      key = (formType + "." + permissionName).toUpperCase();
      return permsInternal[key] ? permsInternal[key] : null;
    }
    //  Any match will result in a pass.
    return formTypes.reduce((acc, formT) => {
      if (permsInternal[formT + "." + permissionName]) {
        acc = permsInternal[formT + "." + permissionName];
      }
      return acc;
    }, null as ICleoPermission | null);
  }

  public getUserTimeGroupPermissions(
    userPermissions: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    const timeGroupPermNames = [
      PERMISSION_NAMES.MAKE_APPOINTMENT,
      PERMISSION_NAMES.ARRIVED,
      PERMISSION_NAMES.ACKNOWLEDGE_RECEIPT_BASE,
      PERMISSION_NAMES.COMFORT_COURTESY_CALL
    ];

    return this.pluckTheseUserPermissions(userPermissions, timeGroupPermNames);
  }

  public getAssignGroupPermissions(
    userPermissions: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    const timeGroupPermNames = [
      PERMISSION_NAMES.ASSIGN_CALL,
      PERMISSION_NAMES.ASSIGN_CALL_BASE,
      PERMISSION_NAMES.ASSIGN_CALL_SECONDARY
    ];

    return this.pluckTheseUserPermissions(userPermissions, timeGroupPermNames);
  }

  public getDispatchGroupPermissions(
    userPermissions: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    const timeGroupPermNames = [
      PERMISSION_NAMES.DISPATCH_VEHICLE,
      PERMISSION_NAMES.RETRIEVE_VEHICLE
    ];

    return this.pluckTheseUserPermissions(userPermissions, timeGroupPermNames);
  }

  public getMessageGroupPermissions(
    userPermissions: Record<string, ICleoPermission>
  ): Record<string, ICleoPermission> {
    const timeGroupPermNames = [
      PERMISSION_NAMES.RESEND_INDIV,
      PERMISSION_NAMES.RESEND_SUMMARY,
      PERMISSION_NAMES.RESEND_DTS,
      PERMISSION_NAMES.RESEND_PEMS
    ];

    return this.pluckTheseUserPermissions(userPermissions, timeGroupPermNames);
  }

  public pluckTheseUserPermissions(
    userPermissions: Record<string, ICleoPermission>,
    cleoPermissionNames: string[]
  ): Record<string, ICleoPermission> {
    const simpleKeyPerms = this.simpleKeyPerms(userPermissions);

    return cleoPermissionNames.reduce((accum, cleoPermissionName: string) => {
      if (simpleKeyPerms[cleoPermissionName]) {
        // console.log(
        //   "getUserTimeGroupPermissions() cleoPermissionName: " +
        //     cleoPermissionName,
        //   simpleKeyPerms[cleoPermissionName]
        // );
        accum[cleoPermissionName] = Object.assign(
          {},
          simpleKeyPerms[cleoPermissionName]
        );
      }
      return accum;
    }, {} as Record<string, ICleoPermission>);
  }

  // public getJwtPersmission(
  //   tokenResponse: ITokenResponse,
  //   permission: string
  // ): boolean {
  //   for (var key in claims) {
  //     if (claims.hasOwnProperty(key)) {
  //       console.log(key, claims[key]);
  //       if (claims.hasOwnProperty(key)) {
  //         //  console.log(key + ">>>", claims[key]);
  //         var claimValue = claims[key];
  //         var hasActivityCode =
  //           typeof claimValue === "string"
  //             ? claimValue.indexOf(activityCode) > -1
  //             : false;
  //         if (hasActivityCode) {
  //           return true;
  //         }
  //       }
  //     }
  //   }
  //   return false;
  // }

  public getJwtTokenClaims(tokenResponse: ITokenResponse): unknown | null {
    if (tokenResponse.access_token) {
      const tokenParts = tokenResponse.access_token.split(".");
      let claims = tokenParts[1];
      claims = window.atob(claims);
      claims = JSON.parse(claims);
      return claims;
    } else {
      return null;
    }
  }

  public doesJwtHaveClaimActivityCode(
    claims: IJwtClaims,
    activityCode: string
  ): boolean {
    return Object.keys(claims).reduce((accum, prop) => {
      const claim = claims[prop];
      const hasActivityCode =
        typeof claim === "string" ? claim.indexOf(activityCode) > -1 : false;
      if (hasActivityCode) {
        accum = true;
      }
      return accum;
    }, false as boolean);
  }

  public doesJwtHaveClaimRole(
    claims: IJwtClaims,
    activityCode: string
  ): boolean {
    return Object.keys(claims).reduce((accum, prop) => {
      const hasActivityCode = prop.indexOf(activityCode) > -1;
      if (hasActivityCode) {
        accum = true;
      }
      return accum;
    }, false as boolean);
  }
}
