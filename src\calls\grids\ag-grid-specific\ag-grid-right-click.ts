import { PermissionService } from "@/permissions/permission-service";
import { UserService } from "@/user/user-service";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import {
  GetContextMenuItems,
  MenuItemDef
} from "@ag-grid-community/core/dist/cjs/entities/gridOptions";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CLEO_CALL_ACTIONS } from "@/common/cleo-common-models";
import { PERMISSION_NAMES } from "@/permissions/permission-models";
import { PermissionData } from "@/permissions/permission-data";
import { CommonService, getElementPosition } from "@/common/common-service";
import { loggerInstance } from "@/common/Logger";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { IUser } from "@/user/user-models";

const userService: UserService = new UserService();
const callSummaryController: CallSummaryController = new CallSummaryController();
const permissionData: PermissionData = new PermissionData();
const commonService: CommonService = new CommonService();
const callSummaryService: CallSummaryService = new CallSummaryService();

export interface CleoContextMenuDefinition {
  menuItemDef: MenuItemDef;
  permName: string;
  allowedMultiSelect?: boolean;
}

export class CleoRightClickAgGridService {
  public LOADING_ICON = "LOADING_ICON";
  public MENU_SEPARATOR = "MENU_SEPARATOR";
  public cleoCallSummary: ICleoCallSummary = callSummaryService.factoryCleoCallSummary();
  public cleoCallSummaries: ICleoCallSummary[] = [];
  public user: IUser = new UserService().factoryUser();

  public getContextMenuItems(
    cleoCallSummary: ICleoCallSummary,
    cleoCallSummaries: ICleoCallSummary[],
    user: IUser,
    gridAllowsMultiSelect: boolean
  ): GetContextMenuItems {
    this.cleoCallSummary = commonService.simpleObjectClone(cleoCallSummary);

    const cleoCallSummariesUnique = [
      ...cleoCallSummaries.filter(cleoCall => {
        return cleoCall.CallNo !== cleoCallSummary.CallNo;
      }),
      commonService.simpleObjectClone(cleoCallSummary)
    ];
    this.cleoCallSummaries = cleoCallSummariesUnique;

    const callNumbers = cleoCallSummariesUnique.map(
      cleoCall => cleoCall.CallNo
    );
    const multiSelected = gridAllowsMultiSelect && callNumbers.length > 1;

    const cleoMenuItemDefinitions = this.getContextMenuConfiguration(
      cleoCallSummary
    );

    //  N.B. If for whatever wwe want to conditionally alter the right click menu options
    //  that are displayed, regardless of whether user has permission, do it here.

    const menuItems: (MenuItemDef | string)[] | string[] = [
      cleoMenuItemDefinitions[this.LOADING_ICON].menuItemDef,
      "separator"
    ];

    function addMenusToArray(menyKeys: string[]): boolean {
      return menyKeys.reduce((accum: boolean, menuKey: string) => {
        const cleoContextMenuDefinition: CleoContextMenuDefinition =
          cleoMenuItemDefinitions[menuKey];
        if (multiSelected) {
          if (cleoContextMenuDefinition.allowedMultiSelect === true) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            menuItems.push(cleoContextMenuDefinition.menuItemDef);
            accum = true;
          }
        } else {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          menuItems.push(cleoContextMenuDefinition.menuItemDef);
          accum = true;
        }
        return accum;
      }, false);
    }

    let anythingAdded = addMenusToArray([
      CLEO_CALL_ACTIONS.MAKE_APPOINTMENT.id,
      CLEO_CALL_ACTIONS.ARRIVED.id,
      CLEO_CALL_ACTIONS.ACKNOWLEDGE_RECEIPT_BASE.id,
      CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL.id
    ]);
    if (anythingAdded) {
      // menuItems.push("separator");
      // menuItems.push(cleoMenuItemDefinitions[this.MENU_SEPARATOR].menuItemDef);
    }

    anythingAdded = addMenusToArray([
      CLEO_CALL_ACTIONS["EDIT CLASSIFICATION"].id
    ]);
    if (anythingAdded) {
      // menuItems.push(cleoMenuItemDefinitions[this.MENU_SEPARATOR].menuItemDef);
    }

    anythingAdded = addMenusToArray([
      CLEO_CALL_ACTIONS.DOWNGRADE.id,
      CLEO_CALL_ACTIONS.ASSIGN.id,
      CLEO_CALL_ACTIONS.ASSIGN_BASE.id,
      CLEO_CALL_ACTIONS.SECONDARY_ASSIGN.id,
      CLEO_CALL_ACTIONS.PRIORITY.id
    ]);
    // if (anythingAdded) {
    //   menuItems.push("separator");
    // }

    anythingAdded = addMenusToArray([
      CLEO_CALL_ACTIONS.DISPATCH_VEHICLE.id,
      CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE.id
    ]);
    // if (anythingAdded) {
    //   menuItems.push("separator");
    // }

    anythingAdded = addMenusToArray([CLEO_CALL_ACTIONS.PRINT.id]);
    // if (anythingAdded) {
    //   menuItems.push("separator");
    // }

    anythingAdded = addMenusToArray([CLEO_CALL_ACTIONS["UNLOCK CALL"].id]);
    // if (anythingAdded) {
    //   menuItems.push("separator");
    // }

    return (menuItems as any) as GetContextMenuItems;
  }

  public getHackClassName(key: string) {
    return "hack-ag-grid-no-async-context-menus--" + key.replace(/ /g, "-");
  }

  public getContextMenuConfiguration(
    cleoCallSummary: ICleoCallSummary | null
  ): Record<string, CleoContextMenuDefinition> {
    const loadingIcon = require("../../../assets/Bars-1s-30px.gif");
    cleoCallSummary = commonService.simpleObjectClone(cleoCallSummary);

    return {
      [this.LOADING_ICON]: {
        permName: this.LOADING_ICON,
        menuItemDef: {
          name: "Menu Permissions Loading...",
          cssClasses: [
            this.getHackClassName(this.LOADING_ICON),
            "ag-menu--perms-loading"
          ],
          icon: "<img class='grid-cleo-row--icon' src='" + loadingIcon + "'/>",
          disabled: true
        }
      },
      [this.MENU_SEPARATOR]: {
        permName: this.MENU_SEPARATOR,
        menuItemDef: {
          name: "x",
          cssClasses: ["ag-menu--menu-separator"],
          disabled: true
        }
      },
      [CLEO_CALL_ACTIONS.MAKE_APPOINTMENT.id]: {
        permName: CLEO_CALL_ACTIONS.MAKE_APPOINTMENT.permissionName,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.MAKE_APPOINTMENT.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.MAKE_APPOINTMENT),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.MAKE_APPOINTMENT.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.ARRIVED.id]: {
        permName: PERMISSION_NAMES.ARRIVED,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.ARRIVED.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.ARRIVED),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.ARRIVED.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.ACKNOWLEDGE_RECEIPT_BASE.id]: {
        permName: PERMISSION_NAMES.ACKNOWLEDGE_RECEIPT_BASE,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.ACKNOWLEDGE_RECEIPT_BASE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.ACKNOWLEDGE_RECEIPT_BASE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.ACKNOWLEDGE_RECEIPT_BASE.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL.id]: {
        permName: PERMISSION_NAMES.COMFORT_COURTESY_CALL,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.COMFORT_COURTESY_CALL),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS["EDIT CLASSIFICATION"].id]: {
        permName: PERMISSION_NAMES["EDIT CLASSIFICATION"],
        menuItemDef: {
          name: CLEO_CALL_ACTIONS["EDIT CLASSIFICATION"].title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES["EDIT CLASSIFICATION"]),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS["EDIT CLASSIFICATION"].id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.DOWNGRADE.id]: {
        permName: PERMISSION_NAMES.DOWNGRADE,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.DOWNGRADE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.DOWNGRADE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.DOWNGRADE.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.ASSIGN.id]: {
        permName: PERMISSION_NAMES.ASSIGN_CALL,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.ASSIGN.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.ASSIGN_CALL),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.ASSIGN.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.ASSIGN_BASE.id]: {
        permName: PERMISSION_NAMES.ASSIGN_CALL_BASE,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.ASSIGN_BASE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.ASSIGN_CALL_BASE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.ASSIGN_BASE.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.SECONDARY_ASSIGN.id]: {
        permName: PERMISSION_NAMES.ASSIGN_CALL_SECONDARY,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.SECONDARY_ASSIGN.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.ASSIGN_CALL_SECONDARY),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.SECONDARY_ASSIGN.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.PRIORITY.id]: {
        permName: PERMISSION_NAMES.PRIORITY,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.PRIORITY.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.PRIORITY),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.PRIORITY.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.DISPATCH_VEHICLE.id]: {
        permName: PERMISSION_NAMES.DISPATCH_VEHICLE,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.DISPATCH_VEHICLE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.DISPATCH_VEHICLE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.DISPATCH_VEHICLE.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE.id]: {
        permName: PERMISSION_NAMES.RETRIEVE_VEHICLE,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RETRIEVE_VEHICLE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.PRINT.id]: {
        permName: PERMISSION_NAMES.PRINT,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.PRINT.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.PRINT),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.PRINT.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS["UNLOCK CALL"].id]: {
        permName: PERMISSION_NAMES["UNLOCK CALL"],
        menuItemDef: {
          name: CLEO_CALL_ACTIONS["UNLOCK CALL"].title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES["UNLOCK CALL"]),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS["UNLOCK CALL"].id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_INDIV.id]: {
        permName: PERMISSION_NAMES.RESEND_INDIV,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_INDIV.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_INDIV),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_INDIV.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_SUMMARY.id]: {
        permName: PERMISSION_NAMES.RESEND_SUMMARY,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_SUMMARY.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_SUMMARY),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_SUMMARY.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_DTS.id]: {
        permName: PERMISSION_NAMES.RESEND_DTS,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_DTS.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_DTS),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_DTS.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_PEMS.id]: {
        permName: PERMISSION_NAMES.RESEND_PEMS,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_PEMS.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_PEMS),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_PEMS.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_RESOLVED.id]: {
        permName: PERMISSION_NAMES.RESEND_RESOLVED,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_RESOLVED.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_RESOLVED),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_RESOLVED.id,
              this.cleoCallSummaries
            );
          }
        }
      },
      [CLEO_CALL_ACTIONS.RESEND_CAN_NOT_RESOLVE.id]: {
        permName: PERMISSION_NAMES.RESEND_CAN_NOT_RESOLVE,
        allowedMultiSelect: true,
        menuItemDef: {
          name: CLEO_CALL_ACTIONS.RESEND_CAN_NOT_RESOLVE.title,
          cssClasses: [
            this.getHackClassName(PERMISSION_NAMES.RESEND_CAN_NOT_RESOLVE),
            "cleo-ag-hack-hide-menu-item"
          ],
          action: () => {
            callSummaryController.processAction(
              CLEO_CALL_ACTIONS.RESEND_CAN_NOT_RESOLVE.id,
              this.cleoCallSummaries
            );
          }
        }
      }
    };
  }

  disableMenuItems(callNo: string | number, userRole: string) {
    Array.from(document.getElementsByClassName("ag-menu-option")).forEach(
      function(el) {
        loggerInstance.log(
          "CleoRightClickAgGridService.disableMenuItems().............",
          el
        );

        const classList = el.classList;

        // const ignoreTheseClasses = [
        //   "ag-menu--menu-separator",
        //   "hack-ag-grid-no-async-context-menus--LOADING_ICON"
        // ].reduce((accum, className) => {
        //
        // }, )

        if (
          !(
            classList.contains(
              "hack-ag-grid-no-async-context-menus--LOADING_ICON"
            ) || classList.contains("ag-menu--menu-separator")
          )
        ) {
          el.classList.add("ag-menu-option-disabled");
          el.classList.add("cleo-ag-hack-hide-menu-item");
        }
      }
    );
    this.enableMenuItems(callNo, userRole);
  }

  enableMenuItems(callNo: string | number, userRole: string) {
    permissionData
      .getUserPermissions(userRole, callNo)
      .then(userPermissions => {
        const menuItems = this.getContextMenuConfiguration(null);
        const permissionService: PermissionService = new PermissionService();

        this.styleLoadingIcon(false);

        //  Show context menus.
        Object.keys(menuItems).forEach(actionName => {
          const cleoContextMenuDefinition: CleoContextMenuDefinition =
            menuItems[actionName];
          const hasPerm = permissionService.getUserPermission(
            userPermissions,
            cleoContextMenuDefinition.permName,
            "WEBUI_DOCVIEW"
          );
          if (hasPerm) {
            const classToLookFor: string = cleoContextMenuDefinition.menuItemDef
              .cssClasses
              ? cleoContextMenuDefinition.menuItemDef.cssClasses[0]
              : "";
            Array.from(document.getElementsByClassName(classToLookFor)).forEach(
              function(el) {
                const classList = el.classList;
                if (
                  classList.contains(
                    "hack-ag-grid-no-async-context-menus--LOADING_ICON"
                  )
                ) {
                  el.innerHTML = "Permissions Loaded";
                } else {
                  el.classList.remove("ag-menu-option-disabled");
                  el.classList.remove("cleo-ag-hack-hide-menu-item");
                }
              }
            );

            //  TODO Brittle and weak. Reliant on DOM id\class names.
            const gridTopPane = document.getElementById("grid--top-pane");

            const menuItems = Array.from(
              document.getElementsByClassName("ag-popup-child")
            );
            if (menuItems.length > 0 && gridTopPane) {
              const menu = menuItems[0] as HTMLElement;
              const menuCoords = getElementPosition(menu);
              const menuContentHeight = menu.offsetHeight;

              const gridTopPaneCoords = getElementPosition(gridTopPane);
              const gridTopPaneHeight = gridTopPane.offsetHeight;
              const gridTopPaneBottom =
                gridTopPaneCoords.top + gridTopPaneHeight;

              const menuBottom = menuCoords.top + menuContentHeight;

              if (menuBottom > gridTopPaneBottom) {
                const diffBottom = menuBottom - gridTopPaneBottom;
                const newTop =
                  Number(menu.style.top.replace("px", "")) - diffBottom;
                menu.style.top = newTop + "px";
              }
            }
          }
        });
      });
  }

  public styleLoadingIcon(isLoading: boolean): void {
    const el = this.getMenuItemElement("LOADING_ICON");
    if (el) {
      const menuDomElement = el.getElementsByClassName(
        "ag-menu-option-text"
      )[0];

      if (isLoading) {
        // menuDomElement.innerHTML = "Permissions Loading";
      } else {
        menuDomElement.innerHTML = "Menu Permissions Loaded";
        menuDomElement.classList.add("ag-menu--perms-loaded");
        // menuDomElement.classList.remove("ag-menu-option-disabled");
        // menuDomElement.classList.remove("cleo-ag-hack-hide-menu-item");

        //  Hide icon
        const iconDomElement = el.getElementsByClassName(
          "ag-menu-option-icon"
        )[0];
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //  @ts-ignore
        iconDomElement.style.visibility = "hidden";
      }

      // //  Hide icon
      // const iconDomElement = el.getElementsByClassName(
      //   "ag-menu-option-icon"
      // )[0];
      // //  @ts-ignore
      // iconDomElement.style.visibility = "hidden";
      //
      // if (isLoading) {
      //   el.innerHTML = "Permissions Loading a";
      // } else {
      //   el.innerHTML = "Permissions Loaded b";
      //   //     //  @ts-ignore
      //   //     iconDomElement.style.visibility = "hidden";
      //   el.classList.remove("ag-menu-option-disabled");
      //   el.classList.remove("cleo-ag-hack-hide-menu-item");
      // }
    }

    // const classToLookForLoading = this.getHackClassName(this.LOADING_ICON);
    // Array.from(document.getElementsByClassName(classToLookForLoading)).forEach(
    //   function(el) {
    //     const menuDomElement = el.getElementsByClassName(
    //       "ag-menu-option-text"
    //     )[0];
    //     menuDomElement.innerHTML = "Permissions Loaded";
    //     menuDomElement.classList.remove("ag-menu-option-disabled");
    //     menuDomElement.classList.remove("cleo-ag-hack-hide-menu-item");
    //
    //     //  Hide icon
    //     const iconDomElement = el.getElementsByClassName(
    //       "ag-menu-option-icon"
    //     )[0];
    //     //  @ts-ignore
    //     iconDomElement.style.visibility = "hidden";
    //   }
    // );
  }

  public getMenuItemElement(menuItemName: string): Element | null {
    let elFound = null;
    Array.from(
      document.getElementsByClassName(
        "hack-ag-grid-no-async-context-menus--" + menuItemName.toUpperCase()
      )
    ).forEach(function(el) {
      elFound = el;
    });
    return elFound;
  }
}
