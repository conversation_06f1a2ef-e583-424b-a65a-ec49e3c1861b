// import { shallowMount } from "@vue/test-utils";
// import CallSummaryToolbar from "@/calls/summary/call-summary-toolbar/call-summary-toolbar.vue";
// import { CallSummaryService } from "@/calls/summary/call-summary-service";
// import CallSummaryToolbarLink from "@/calls/summary/call-summary-toolbar/call-summary-toolbar-link.vue";
// import { ICleoAction } from "@/common/cleo-common-models";
// import { PERMISSION_NAMES } from "@/permissions/permission-models";
// import LoadingSpinner from "@/common/ui/loading-spinner.vue";
//
// const permissionNames = PERMISSION_NAMES;

it("CallSummaryToolbarLink", () => {
  expect(true).toBe(true);
});

// describe("CallSummaryToolbar.vue", () => {
// it("renders props.msg when passed", () => {
//   const cleoCallSummary = new CallSummaryService().factoryCleoCallSummary();
//   const permsForCall = {};
//
//   expect(process.env.NODE_ENV).toBe("test");
//   expect(process.env.VUE_APP_CLEO_BASE_URL).toBe(
//     "http://dr-sta-dom01.sehnp.nhs.uk"
//   );
//
//   //
//   const wrapper = shallowMount(CallSummaryToolbar, {
//     propsData: {
//       cleoCallSummary,
//       permsForCall,
//       isLoading: false
//     }
//   });
//   const spinnerDiv = wrapper.find(".call-summary-toolbar--loading-spinner");
//   expect(spinnerDiv.exists()).toBe(true);
//
//   const loadingSpinner = wrapper.findComponent(LoadingSpinner); // => finds Bar by component instance
//   expect(loadingSpinner.exists()).toBe(true);
// });
//
// it("CallSummaryToolbarLink", () => {
//   const cleoCallSummary = new CallSummaryService().factoryCleoCallSummary();
//   const permsForCall = {};
//
//   const cleoAction: ICleoAction = {
//     id: "ARRIVED",
//     permissionName: "ARRIVED",
//     title: "Arrived"
//   };
//
//   const keyPerms = {
//     ARRIVED: cleoAction
//   };
//
//   const wrapper = shallowMount(CallSummaryToolbarLink, {
//     propsData: {
//       keyPerms,
//       cleoAction
//     }
//   });
//   expect(wrapper.text()).toMatch("Arrived");
// });
// });
