<template>
  <div class="ic24-card--wrapper">
    <h4>
      <span v-text="title"></span>
    </h4>

    <div class="ic24-simple-separator"></div>

    <slot name="content"></slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "ic24-card-generic-layout",
  components: {},
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      }
    }
  }
});
</script>
