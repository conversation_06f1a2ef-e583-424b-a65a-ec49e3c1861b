import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";

export interface IEnvironmentData {
  thisPage: string;
  referrer: string;
  browser: string;
  os: string;
  userAgent: string;
  lang: string;
  resolution: string;
}

export class ErrorData {
  /**
   *
   * @param error
   * @param moreInfo        A map of any data you want to submit {name: "joe bloggs", age: 34, etc...}
   * @param vueInfo         TODO Vue specific, if in component, what is "info"
   * @param userMessage     Maybe get feedback off user: "what were you doing when error occurred"
   */
  public submitError(
    error: Error,
    moreInfo: Record<string, string>,
    vueInfo: string,
    userMessage?: string
  ): Promise<any> {
    const url =
      CLEO_CONFIG.CLEO.ERROR_LOG_PATH + "/JavascriptErrorReport?OpenAgent";

    const environmentData = {
      thisPage: location.href,
      referrer: document.referrer,
      browser: navigator.appName + " " + navigator.appVersion,
      os: navigator.platform,
      userAgent: navigator.userAgent,
      lang: navigator.language,
      resolution: screen.width + " x " + screen.height
    };

    let data: string[] = Object.keys(environmentData).map(key => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const propValue = environmentData[key].toString();
      return escape(key) + "=" + escape(propValue);
    });

    data.push(escape("message") + "=" + escape(error.message));
    data.push(escape("name") + "=" + escape(error.name));
    if (error.stack) {
      data.push(escape("stack") + "=" + escape(error.stack));
    }

    if (userMessage) {
      data.push(escape("user-message") + "=" + escape(userMessage));
    }

    data = Object.keys(moreInfo).reduce((accum: string[], key) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const propValue = moreInfo[key].toString();
      accum.push(escape(key) + "=" + escape(propValue));
      return accum;
    }, data);

    return https.post(url, data.join("&"));
  }
}
