<template>
  <div>
    <div class="cleo-force-inline-block">
      <div class="grid--call-summary-header-text">
        Call Summary
        <span v-show="isLoading" class="call-summary-toolbar--loading-spinner">
          Loading permissions...
          <LoadingSpinner class="inline-block"></LoadingSpinner>
        </span>
      </div>
    </div>

    <!--    <div class="cleo-float-right" v-show="isLoading">-->
    <!--      Loading permissions...-->
    <!--      <LoadingSpinner class="inline-block"></LoadingSpinner>-->
    <!--    </div>-->

    <!--    v-show="getHasAnyPerms"-->
    <div
      class="cleo-force-inline-block call-summary-toolbar--menu"
      v-if="false"
    >
      <div class="dropup" v-if="getHasTimeGroupPerms">
        <button
          class="dropbtn"
          id="time-option--button"
          v-on:mouseover="setMenuContentPosition"
        >
          Time Options
        </button>
        <div class="dropup-content" id="time-option--content">
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.MAKE_APPOINTMENT]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.MAKE_APPOINTMENT"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.ARRIVED]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.ARRIVED"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.ACKNOWLEDGE_RECEIPT_BASE]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.ACKNOWLEDGE_RECEIPT_BASE"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.COMFORT_COURTESY_CALL]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.COMFORT_CALL"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
        </div>
      </div>

      <button
        class="dropbtn"
        v-if="keyPerms[permissionNames['EDIT CLASSIFICATION']]"
        v-on:click="onLinkClick(cleoCallActions['EDIT CLASSIFICATION'])"
      >
        <span v-text="cleoCallActions['EDIT CLASSIFICATION'].title"></span>
      </button>

      <div class="dropup" v-if="getHasAssignGroupPerms">
        <button
          class="dropbtn"
          id="assign-option--button"
          v-on:mouseover="setMenuContentPosition"
        >
          Assign
        </button>
        <div class="dropup-content" id="assign-option--content">
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.ASSIGN_CALL]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.ASSIGN"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.ASSIGN_CALL_BASE]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.ASSIGN_BASE"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.ASSIGN_CALL_SECONDARY]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.SECONDARY_ASSIGN"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
        </div>
      </div>

      <div class="dropup" v-if="getHasDispatchGroupPerms">
        <button
          class="dropbtn"
          id="dispatch-option--button"
          v-on:mouseover="setMenuContentPosition"
        >
          Dispatch
        </button>
        <div class="dropup-content" id="dispatch-option--content">
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.DISPATCH_VEHICLE]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.DISPATCH_VEHICLE"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.RETRIEVE_VEHICLE]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.RETRIEVE_VEHICLE"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
        </div>
      </div>

      <button
        v-if="keyPerms[permissionNames.PRINT]"
        class="dropbtn"
        v-on:click="onLinkClick(cleoCallActions.PRINT)"
      >
        <span v-text="cleoCallActions.PRINT.title"></span>
      </button>

      <button
        v-if="keyPerms[permissionNames['UNLOCK CALL']]"
        class="dropbtn"
        v-on:click="onLinkClick(cleoCallActions.UNLOCK)"
      >
        <span v-text="cleoCallActions.UNLOCK.title"></span>
      </button>

      <div class="dropup" v-if="getHasMessageGroupPerms">
        <button
          class="dropbtn"
          id="messaging-option--button"
          v-on:mouseover="setMenuContentPosition"
        >
          Messaging
        </button>
        <div class="dropup-content" id="messaging-option--content">
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.RESEND_INDIV]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.RESEND_INDIV"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.RESEND_SUMMARY]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.RESEND_SUMMARY"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.RESEND_DTS]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.RESEND_DTS"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
          <CallSummaryToolbarLink
            v-if="keyPerms[permissionNames.RESEND_PEMS]"
            :key-perms="keyPerms"
            :cleo-action="cleoCallActions.RESEND_PEMS"
            v-on:onLinkClick="onLinkClick"
          ></CallSummaryToolbarLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import { PermissionService } from "@/permissions/permission-service";
import { ICleoPermission } from "@/permissions/permission-models";
import { PERMISSION_NAMES } from "@/permissions/permission-models";
import CallSummaryToolbarLink from "@/calls/summary/call-summary-toolbar/call-summary-toolbar-link.vue";
import { CLEO_CALL_ACTIONS, ICleoAction } from "@/common/cleo-common-models";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
  ISplitPane
} from "@/common/config/config-store";
import { getElementPosition } from "@/common/common-service";

const callSummaryService: CallSummaryService = new CallSummaryService();
const callSummaryController: CallSummaryController = new CallSummaryController();
const permissionService: PermissionService = new PermissionService();

@Component({
  name: "call-summary-toolbar",
  components: { LoadingSpinner, CallSummaryToolbarLink },
  computed: {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    ...mapState(CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME, {
      splitPanes: (state: IConfigStoreState) => state.splitPanes
    })
  }
})
export default class CallSummaryToolbar extends Vue {
  public readonly configStoreState!: IConfigStoreState;

  @Prop({
    default: () => {
      return callSummaryService.factoryCleoCallSummary();
    }
  })
  public readonly cleoCallSummary!: ICleoCallSummary;

  @Prop({
    default: false
  })
  public readonly isLoading!: boolean;

  @Prop({
    default: () => {
      return {};
    }
  })
  public readonly permsForCall!: Record<string, ICleoPermission>;

  public permissionNames = PERMISSION_NAMES;
  public permissionService: PermissionService = permissionService;
  public keyPerms: Record<string, ICleoPermission> = {};
  public keyPermsTimeGroup: Record<string, ICleoPermission> = {};
  public keyPermsAssignGroup: Record<string, ICleoPermission> = {};
  public keyPermsDispatchGroup: Record<string, ICleoPermission> = {};
  public keyPermsMessageGroup: Record<string, ICleoPermission> = {};

  public cleoCallActions = CLEO_CALL_ACTIONS;
  public PREFIX = Math.random()
    .toString(36)
    .substring(2);

  // public shouldMenuGoDown = false;

  @Watch("cleoCallSummary")
  public onCleoCallSummaryChanged() {
    //  Don't bother checking newvalue vs oldValue, the CallModel on back end may have changed, need to check each time.
    this.keyPerms = {};
  }

  @Watch("permsForCall")
  public onPermsForCallChanged() {
    //  Don't bother checking newvalue vs oldValue, the CallModel on back end may have changed, need to check each time.
    this.calcPermissions();
  }

  // @Watch("splitPanes.panes")
  // public onSplitPanesPanesChanged(
  //   newValue: ISplitPane[],
  //   oldValue: ISplitPane[]
  // ): void {
  //   if (newValue.length !== 2) {
  //     return;
  //   }
  //
  //
  //   const newPane = newValue[1];
  //   this.shouldMenuGoDown = newPane.size >= 20;
  //   // this.shouldMenuGoDown = oldValue[1].size >= 20;
  // }

  public get getHasAnyPerms(): boolean {
    return Object.keys(this.keyPerms).length > 0;
  }

  public get getPermKeys(): string[] {
    return Object.keys(this.keyPerms);
  }

  public calcPermissions(): void {
    const keyPerms = Object.values(this.permissionNames).reduce(
      (accum, permName) => {
        const hasPerm = permissionService.getUserPermission(
          this.permsForCall,
          permName,
          "WEBUI_DOCVIEW"
        );
        if (hasPerm) {
          accum[permName] = { ...hasPerm };
        }
        return accum;
      },
      {} as Record<string, ICleoPermission>
    );

    this.keyPerms = keyPerms;
    this.keyPermsTimeGroup = permissionService.getUserTimeGroupPermissions(
      keyPerms
    );
    this.keyPermsAssignGroup = permissionService.getAssignGroupPermissions(
      keyPerms
    );
    this.keyPermsDispatchGroup = permissionService.getDispatchGroupPermissions(
      keyPerms
    );
    this.keyPermsMessageGroup = permissionService.getMessageGroupPermissions(
      keyPerms
    );
  }

  public get getHasTimeGroupPerms(): boolean {
    return Object.keys(this.keyPermsTimeGroup).length > 0;
  }

  public get getHasAssignGroupPerms(): boolean {
    return Object.keys(this.keyPermsAssignGroup).length > 0;
  }

  public get getHasDispatchGroupPerms(): boolean {
    return Object.keys(this.keyPermsDispatchGroup).length > 0;
  }

  public get getHasMessageGroupPerms(): boolean {
    return Object.keys(this.keyPermsMessageGroup).length > 0;
  }

  public onLinkClick(cleoAction: ICleoAction): void {
    callSummaryController.processAction(cleoAction.id, [this.cleoCallSummary]);
  }

  /**
   * One optio is to dynamically see how much space is in CallSummary panel
   * then decide whether to display up or down...or as below, make all
   * display up.
   * @param optionButt
   */
  public setMenuContentPosition(optionButt: HTMLElement): void {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    const id: string = optionButt.srcElement.id;
    const optionButton = document.getElementById(
      id.split("--")[0] + "--button"
    );
    if (optionButton) {
      const optionContent = document.getElementById(
        id.split("--")[0] + "--content"
      );
      if (optionContent) {
        const buttonCoords = getElementPosition(optionButton);
        const optionContentHeight = optionContent.offsetHeight;
        const heightOfNavBar = 0;
        const newTop =
          (buttonCoords ? buttonCoords.top : 0) -
          (optionContentHeight + heightOfNavBar);

        optionContent.style.position = "fixed";
        optionContent.style.top = newTop + "px";
      }
    }
  }
}
</script>

<style scoped>
.call-summary-toolbar--loading-spinner {
  padding-left: 1em;
  color: grey;
}

.call-summary-toolbar--menu {
  padding-left: 1em;
}
</style>
