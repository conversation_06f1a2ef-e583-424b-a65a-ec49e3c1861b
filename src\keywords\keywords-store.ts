import { IKeywordsRefD<PERSON><PERSON><PERSON> } from "@/keywords/keywords-models";
import { <PERSON><PERSON>ontext, Module } from "vuex";
import { IRootState, useStore } from "@/store/store";
import { KeywordData } from "@/keywords/keyword-data";
import { KeywordService } from "@/keywords/keyword-service";

const keywordData: KeywordData = new KeywordData();
const keywordService: KeywordService = new KeywordService();
export interface IKeywordsStoreState {
  keywordsRefDataKey: IKeywordsRefDataKey;
}

export enum KEYWORD_STORE_STORE_CONST {
  KEYWORD_STORE__CONST_MODULE_NAME = "KEYWORD_STORE__CONST_MODULE_NAME",

  KEYWORD_STORE__MUTATION_SET_REF_DATA_KEY = "KEYWORD_STORE__MUTATION_SET_REF_DATA_KEY",

  KEYWORD_STORE__ACTIONS_GET_REF_DATA = "KEYWORD_STORE__ACTIONS_GET_REF_DATA"
}

const mutations = {
  [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__MUTATION_SET_REF_DATA_KEY](
    state: IKeywordsStoreState,
    keywordsRefDataKey: IKeywordsRefDataKey
  ): void {
    state.keywordsRefDataKey = keywordsRefDataKey;
  }
};

const getters = {};

const actions = {
  [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__ACTIONS_GET_REF_DATA](
    context: ActionContext<IKeywordsStoreState, IRootState>
  ): Promise<IKeywordsRefDataKey> {
    return keywordData.getRefData(["classifications"]).then(resp => {
      const keywordsRefDataKey: IKeywordsRefDataKey = keywordService.mapKeywordsRefData(
        resp
      );
      context.commit(
        KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__MUTATION_SET_REF_DATA_KEY,
        keywordsRefDataKey
      );
      return keywordsRefDataKey;
    });
  }
};

export const keywordStore: Module<IKeywordsStoreState, IRootState> = {
  namespaced: true,
  state: {
    keywordsRefDataKey: {}
  },
  mutations,
  getters,
  actions
};
