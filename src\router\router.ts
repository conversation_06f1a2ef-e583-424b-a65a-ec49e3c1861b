import Vue from "vue";
import VueRouter, { RouteConfig } from "vue-router";
import Welcome from "@/welcome/welcome.vue";
// import GridsLayout from "@/calls/grids/GridsLayout.vue";
// import gridRoutes from "@/calls/grids/grid-routes";

Vue.use(VueRouter);

/**
 * These need mapping to CLEO legacy function.  E.g. adapterMenu.setView('grid-111');
 */
export const LAUNCH_ROUTES_PATHS = {
  WELCOME: "welcome",
  LOGIN: "login-domino",
  PACCS: "paccs",
  CALL_DETAIL_ROUTE: "call-detail-route",
  GRIDS: "grids",
  GRID_CATCH_ALL: "catchall",
  GRID_111: "grid-111",
  GRID_CAS: "grid-Cas",
  GRID_OVERSIGHT: "grid-oversight",
  GRID_OVERSIGHT_FOLLOW_UP: "grid-oversight-follow-up",
  GRID_CLASSIFICATION_BASE: "grid-classification-base",
  GRID_CLASSIFICATION_VISIT: "grid-classification-visit",
  GRID_111_OLD: "grid-111-old",
  GRID_FCMS: "grid-fcms",
  GRID_PCAS: "grid-pcas",
  GRID_BASE: "grid-base",
  GRID_PLS: "grid-pls",
  WHAT_3_WORDS: "what3words"
};

const routes: Array<RouteConfig> = [
  {
    path: "/",
    name: LAUNCH_ROUTES_PATHS.WELCOME,
    component: Welcome
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.WELCOME,
    name: LAUNCH_ROUTES_PATHS.WELCOME,
    component: Welcome
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE + "/:id",
    name: LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE,
    component: () =>
      import(
        /* webpackChunkName: "call-detail-route" */ "@/calls/details/call-detail-route.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.LOGIN,
    name: LAUNCH_ROUTES_PATHS.LOGIN,
    component: () =>
      import(/* webpackChunkName: "login-domino" */ "@/login/login-domino.vue")
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.PACCS,
    name: LAUNCH_ROUTES_PATHS.PACCS,
    component: () =>
      import(
        /* webpackChunkName: "paccs-route-container" */ "@/paccs/paccs-route-container.vue"
      )
  },
  // "@/calls/grids/grid-111.vue"
  // {
  //   path: "/grids",
  //   // component: GridsLayout,
  //   children: gridRoutes
  // }
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_111_OLD,
    name: LAUNCH_ROUTES_PATHS.GRID_111_OLD,
    component: () =>
      import(
        /* webpackChunkName: "grid-111-old" */ "@/calls/grids/grid-111.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_111,
    name: LAUNCH_ROUTES_PATHS.GRID_111,
    component: () =>
      import(
        /* webpackChunkName: "grid-111" */ "@/calls/grids/grids-named/Grid111.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_CAS,
    name: LAUNCH_ROUTES_PATHS.GRID_CAS,
    component: () =>
      import(
        /* webpackChunkName: "grid-Cas" */ "@/calls/grids/grids-named/GridCas.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_OVERSIGHT,
    name: LAUNCH_ROUTES_PATHS.GRID_OVERSIGHT,
    component: () =>
      import(
        /* webpackChunkName: "grid-oversight" */ "@/calls/grids/grids-named/GridOverSight.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_OVERSIGHT_FOLLOW_UP,
    name: LAUNCH_ROUTES_PATHS.GRID_OVERSIGHT_FOLLOW_UP,
    component: () =>
      import(
        /* webpackChunkName: "grid-oversight-follow-up" */ "@/calls/grids/grids-named/GridOverSightFollowUp.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_CLASSIFICATION_BASE,
    name: LAUNCH_ROUTES_PATHS.GRID_CLASSIFICATION_BASE,
    component: () =>
      import(
        /* webpackChunkName: "grid-classification-base" */ "@/calls/grids/grids-named/GridClassificationBase.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_CLASSIFICATION_VISIT,
    name: LAUNCH_ROUTES_PATHS.GRID_CLASSIFICATION_VISIT,
    component: () =>
      import(
        /* webpackChunkName: "grid-classification-visit" */ "@/calls/grids/grids-named/GridClassificationVisit.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_PLS,
    name: LAUNCH_ROUTES_PATHS.GRID_PLS,
    component: () =>
      import(
        /* webpackChunkName: "grid-pls" */ "@/calls/grids/grids-named/GridPls.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_FCMS,
    name: LAUNCH_ROUTES_PATHS.GRID_FCMS,
    component: () =>
      import(
        /* webpackChunkName: "grid-fcms" */ "@/calls/grids/grids-named/GridFcms.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_PCAS,
    name: LAUNCH_ROUTES_PATHS.GRID_PCAS,
    component: () =>
      import(
        /* webpackChunkName: "grid-pcas" */ "@/calls/grids/grids-named/GridPcas.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.GRID_BASE + "/:id",
    name: LAUNCH_ROUTES_PATHS.GRID_BASE,
    component: () =>
      import(
        /* webpackChunkName: "grid-base" */ "@/calls/grids/grids-named/GridBase.vue"
      )
  },
  {
    path: "/grid-multi-test",
    name: "grid-multi-test",
    component: () =>
      import(
        /* webpackChunkName: "grid-multi-test" */ "@/calls/grids/grids-named/GridMultiTest.vue"
      )
  },
  {
    path: "/" + LAUNCH_ROUTES_PATHS.WHAT_3_WORDS,
    name: LAUNCH_ROUTES_PATHS.WHAT_3_WORDS,
    component: () =>
      import(
        /* webpackChunkName: "what-3-words" */ "@/what3words/what3words-route.vue"
      )
  }
];

const router = new VueRouter({
  routes
});

export default router;
