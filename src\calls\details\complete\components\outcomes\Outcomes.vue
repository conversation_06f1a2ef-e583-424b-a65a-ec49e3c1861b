<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" :title-end="getTitleEnd" />

    <div class="complete-step--subheader">Please confirm the outcome</div>

    <div class="ic24-vertical-spacer-large"></div>

    <!--    userOptionsOutcomesOverrideKey {{ userOptionsOutcomesOverrideKey }}-->
    <div v-if="isLoading">
      Loading options...
    </div>
    <div v-if="hasOptions" class="ic24-flex-column ic24-flex-gap">
      <div class="ic24-flex-row">
        <label class="outcomes--label">Outcome</label>
        <select v-model="state.outcome" @change="onUserChangeOutcome">
          <option></option>
          <option v-for="(outcome, prop) in state.outcomeOptions" :key="prop">
            {{ prop }}
          </option>
        </select>
      </div>

      <div class="ic24-flex-row" v-if="getSubOutcomes.length > 0">
        <label class="outcomes--label">Sub Outcome</label>
        <select v-model="state.subOutcome" @change="onUserChange">
          <option v-for="subOutcome in getSubOutcomes" :key="subOutcome">
            {{ subOutcome }}
          </option>
        </select>
      </div>

      <div class="ic24-flex-row" v-if="areOtherCommentsRequired">
        <label class="outcomes--label">Comments:</label>
        <input-debounce v-model="state.otherOutcome" @input="onUserChange" />
      </div>
    </div>

    <!--    {{ state }}-->
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext
} from "@vue/composition-api";
import {
  convertServerResponse,
  SubOutcomeName
} from "@/calls/details/complete/components/outcomes/outcomes-service";
import { CompleteApi } from "@/calls/details/complete/components/api/complete-api";
import {
  IOutcomes,
  IStep,
  UserOptionsOutcomesOverrideKey
} from "@/calls/details/complete/complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import { IService } from "@/common/services/services-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import InputDebounce from "@/common/ui/fields/input-debounce.vue";
import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { getInformationalOutcomes } from "@/calls/details/complete/components/outcomes/api/outcomes-api";

export default defineComponent({
  name: "Outcomes",
  components: { InputDebounce, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"OUTCOMES">>,
      required: true
    },
    value: {
      type: Object as PropType<IOutcomes>,
      required: true
    },
    service: {
      type: Object as PropType<IService>,
      required: true
    },
    cleoClientService: {
      type: String as PropType<CLEO_CLIENT_SERVICE>,
      required: true
    },
    classification: {
      type: String as PropType<CALL_CLASSIFICATION>,
      required: true
    },
    useCallInformationalOutcomes: {
      type: Boolean,
      default: false
    },
    userOptionsOutcomesOverrideKey: {
      type: String as PropType<UserOptionsOutcomesOverrideKey>,
      default: ""
    }
  },
  setup(
    props: {
      step: IStep<"OUTCOMES">;
      value: IOutcomes;
      service: IService;
      cleoClientService: CLEO_CLIENT_SERVICE;
      classification: CALL_CLASSIFICATION;
      useCallInformationalOutcomes: boolean;
      userOptionsOutcomesOverrideKey: UserOptionsOutcomesOverrideKey;
    },
    context: SetupContext
  ) {
    const state = reactive<IOutcomes>({
      outcome: "",
      subOutcome: "",
      otherOutcome: "",
      outcomeOptions: {}
    });

    const isLoading = ref(false);

    if (
      props.value.outcomeOptions &&
      Object.keys(props.value.outcomeOptions).length > 0
    ) {
      state.outcomeOptions = props.value.outcomeOptions;
    } else {
      loadOptions();
    }

    function loadOptions() {
      isLoading.value = true;
      let prom: Promise<Record<string, string[]> | Record<string, string>>;
      if (props.useCallInformationalOutcomes) {
        prom = getInformationalOutcomes({
          cleoClientService: props.cleoClientService,
          classification: props.classification,
          serviceName: props.service.name,
          userOptionsOutcomesOverrideKey: props.userOptionsOutcomesOverrideKey
        }).then(response => {
          console.log(
            "Outcomes.loadOptions() >>>>> A typeof" + typeof response,
            response
          );
          if (Array.isArray(response)) {
            console.log("Outcomes.loadOptions() >>>>> A isArray", response);
            const simpleMap = response.reduce<Record<string, string[]>>(
              (acc, cur) => {
                acc[cur] = [];
                return acc;
              },
              {}
            );
            return simpleMap;
          } else {
            return response;
          }
        });
      } else {
        prom = new CompleteApi().getOutcomes(props.service.name);
      }

      // new CompleteApi()
      //   .getOutcomes(props.service.name)

      prom
        .then(response => {
          let outcomes;
          if (props.useCallInformationalOutcomes) {
            console.log("Outcomes.loadOptions() >>>>> B", response);
            outcomes = response as Record<string, string[]>;

            // loop across the keys, if "" or null, remove it.  Trim any leading or trailing spaces.
            for (const key in outcomes) {
              if (key === "" || key === null) {
                delete outcomes[key];
              } else {
                const trimmedKey = key.trim();
                if (trimmedKey !== key) {
                  outcomes[trimmedKey] = outcomes[key];
                  delete outcomes[key];
                }
              }
            }
            console.log("Outcomes.loadOptions() >>>>> B2", outcomes);
          } else {
            console.log("Outcomes.loadOptions() >>>>> C", outcomes);
            outcomes = convertServerResponse(
              (response as any) as Record<string, string>
            );
          }
          state.outcomeOptions = outcomes;
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    const getSubOutcomes = computed<SubOutcomeName[]>(() => {
      // if (props.useCallInformationalOutcomes) {
      //   return [];
      // }
      if (state.outcomeOptions && state.outcomeOptions[state.outcome]) {
        return state.outcomeOptions[state.outcome];
      }

      return [];
    });

    function onUserChange() {
      console.log(">>>>> Outcomes.onUserChange", state);
      if (typeof state.subOutcome === "undefined") {
        state.subOutcome = "";
      }
      context.emit("input", simpleObjectClone(state));
    }

    function onUserChangeOutcome() {
      state.subOutcome = "";
      onUserChange();
    }

    const hasOptions = computed(() => {
      if (!state.outcomeOptions) {
        return false;
      }
      return Object.keys(state.outcomeOptions).length > 0;
    });

    const getTitleEnd = computed(() => {
      const messages = [];

      if (props.service.serviceType.length > 0) {
        messages.push(props.service.serviceType);
      }

      if (props.cleoClientService.length > 0) {
        messages.push(props.cleoClientService);
      }

      if (props.classification.length > 0) {
        messages.push(props.classification);
      }

      return messages.join(" | ");
    });

    const areOtherCommentsRequired = computed(() => {
      const outcome = !!(
        state.outcome && state.outcome.toUpperCase() === "OTHER"
      );

      const subOutcome = !!(
        state.subOutcome && state.subOutcome.toUpperCase() === "OTHER"
      );

      return outcome || subOutcome;
    });

    return {
      state,

      getSubOutcomes,
      getTitleEnd,
      hasOptions,
      isLoading,
      areOtherCommentsRequired,

      onUserChangeOutcome,
      onUserChange
    };
  }
});
</script>
<style>
.outcomes--label {
  width: 150px;
}
</style>
