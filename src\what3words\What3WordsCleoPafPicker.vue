<template>
  <div>
    <div class="what-3-words-cleo-paf-picker--input-wrapper">
      <div
        class="what-3-words-cleo-paf-picker--input-div cleo-force-inline-block"
      >
        <PafUserFinder
          class="what-3-words-cleo-auto-suggest--input"
          :class="
            isUserInputPafPostCodeValid
              ? ''
              : 'what-3-words-cleo--input-invalid'
          "
          :post-code="postCode"
          v-on:onSearchFinished="setSearchResults"
        />
      </div>

      <div
        class="what-3-words-cleo-paf-picker--input-clear-suggestions cleo-force-inline-block"
      >
        <a href="#" v-on:click.prevent="clearUi">X</a>
      </div>
    </div>

    <!--    <PafUserFinder-->
    <!--      :class="-->
    <!--        isUserInputPafPostCodeValid ? '' : 'what-3-words-cleo&#45;&#45;input-invalid'-->
    <!--      "-->
    <!--      :post-code="postCode"-->
    <!--      v-on:onSearchFinished="setSearchResults"-->
    <!--    />-->
    <PafUserPicker
      v-if="displayPafPicker"
      :paf-addresses="pafAddresses"
      v-on:addressSelected="pafAddressSelected"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  computed
} from "@vue/composition-api";
import { PafService } from "@/paf/paf-service";
import PafUserFinder from "@/paf/paf-user-finder.vue";
import { IPafAddress } from "@/paf/paf-models";
import { CommonService } from "@/common/common-service";
import PafUserPicker from "@/paf/paf-user-picker.vue";
export default defineComponent({
  name: "what-3-words-cleo-paf-picker",
  components: {
    PafUserPicker,
    PafUserFinder
  },
  props: {
    postCode: {
      required: true
    }
  },
  setup(props: { postCode: string }, context: SetupContext) {
    const postCodeInternal = ref("");
    // const pafService = new PafService();
    const pafAddresses = ref<IPafAddress[]>([]);
    const displayPafPicker = ref<boolean>(false);
    const commonService = new CommonService();

    function doSearch() {
      context.emit("doSearch", postCodeInternal.value);
    }

    const isUserInputPafPostCodeValid = computed<boolean>(() => {
      return true;
      // return pafService.isValidPostCode(postCodeInternal.value);
    });

    function setSearchResults(pafAddressesData: IPafAddress[]) {
      pafAddresses.value = pafAddressesData;
      displayPafPicker.value = true;
    }

    function pafAddressSelected(pafAddress: IPafAddress) {
      displayPafPicker.value = false;
      context.emit(
        "addressSelected",
        commonService.simpleObjectClone(pafAddress)
      );
    }

    function clearUi() {
      displayPafPicker.value = false;
    }

    return {
      postCodeInternal,
      pafAddresses,
      displayPafPicker,
      doSearch,
      isUserInputPafPostCodeValid,
      setSearchResults,
      pafAddressSelected,
      clearUi
    };
  }
});
</script>

<style>
.what-3-words-cleo-paf-picker--input-wrapper {
  width: 150px;
  /*border: 1px solid black;*/
}

.what-3-words-cleo-paf-picker--input-div {
  width: 98%;
}

.what-3-words-cleo-paf-picker--input {
  width: 100%;
  line-height: 2;
  font-weight: 600;
}

.what-3-words-cleo-paf-picker--input-clear-suggestions {
  width: 0%;
  /*padding-left: 5px;*/
  position: relative;
  left: -20px;
}
</style>
