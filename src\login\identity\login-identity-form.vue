<template>
  <div>
    <LoginForm v-on:submit="doLogin"></LoginForm>
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { CLEO_CONFIG } from "@/common/config/config-";
import { loggerInstance } from "@/common/Logger";
import LoginForm from "@/login/login-form.vue";

@Component({
  name: "login-identity-form",
  components: { LoginForm }
})
export default class LoginIdentityForm extends Vue {
  // public userName =
  //   process.env.NODE_ENV === "development"
  //     ? process.env.VUE_APP_CLEO_USER_NAME
  //       ? process.env.VUE_APP_CLEO_USER_NAME
  //       : ""
  //     : "";
  // public password =
  //   process.env.NODE_ENV === "development"
  //     ? process.env.VUE_APP_CLEO_USER_PASSWORD
  //       ? process.env.VUE_APP_CLEO_USER_PASSWORD
  //       : ""
  //     : "";

  public userName = "";
  public password = "USERCREDS";

  public doLogin(): void {
    const formData = new FormData();
    formData.append("UserName", this.userName);
    formData.append("Password", this.password);

    const xhr = new XMLHttpRequest();

    // xhr.open("POST", CLEO_CONFIG.CLEO.BASE_URL + "/names.nsf?login");
    xhr.open("POST", "https://identityserver.sta.apps.ic24.nhs.uk:778/connect/token");

    // const params = "UserName=" + this.userName + "&Password=" + this.password;

    const paramData = {
      grantType: "password",
      clientId: "client",
      clientSecret: "secret",
      userName: "",
      password: "USERCREDS", //  or SSO TOKEN,
      scope: "api1"
    };

    const params: string =
      "grant_type=" +
      paramData.grantType +
      "&client_id=" +
      paramData.clientId +
      "&client_secret=" +
      paramData.clientSecret +
      "&username=" +
      paramData.userName +
      ("&password=" + paramData.password) +
      ("&scope=" + paramData.scope);

    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send(params);
    xhr.onload = () => {
      loggerInstance.log("Login.onload(): " + xhr.responseText);
    };

    xhr.onerror = () => {
      loggerInstance.log("Login.onerror(): " + xhr.responseText);
    };

    xhr.onreadystatechange = () => {
      loggerInstance.log("Login.onreadystatechange(): " + xhr.readyState);

      if (xhr.readyState === 1) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + " Request started."
        );
      }

      if (xhr.readyState === 2) {
        loggerInstance.log(
          "Login.onreadystatechange(): " +
            xhr.readyState +
            " Headers received.............!"
        );
      }

      if (xhr.readyState === 3) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + "  Data loading..!"
        );
      }
      if (xhr.readyState === 4) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + " Request ended."
        );
      }
    };
  }
}
</script>
