<template>
  <CallSummaryForm
    v-if="cleoCallSummary.CallNo > 0"
    :cleo-call-summary="cleoCallSummary"
  ></CallSummaryForm>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";

import { CallSummaryService } from "@/calls/summary/call-summary-service";
import CallSummaryForm from "@/calls/summary/call-summary-form.vue";

const callSummaryService: CallSummaryService = new CallSummaryService();

@Component({
  name: "call-summary-display",
  components: { CallSummaryForm }
})
export default class CallSummaryDisplay extends Vue {
  @Prop({
    default: () => {
      return callSummaryService.factoryCleoCallSummary();
    }
  })
  public readonly cleoCallSummary!: ICleoCallSummary;

  public callSummaryService: CallSummaryService = callSummaryService;
}
</script>
