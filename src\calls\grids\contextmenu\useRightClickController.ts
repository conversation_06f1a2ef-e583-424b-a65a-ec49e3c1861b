import { computed, ref } from "@vue/composition-api";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CommonService } from "@/common/common-service";

const commonService = new CommonService();

export interface IUseRightClickControllerShow {
  data: ICleoCallSummary;
  coords: { x: number; y: number };
  targetGridContainerId: string;
}

const callSummaryService = new CallSummaryService();

export function useRightClickController() {
  const showRightClickMenu = ref(false);
  const isLoading = ref(false);
  const permsForSelectedCall = ref({});
  const rightClickTargetDiv = ref("");
  const rightClickCoords = ref({
    x: 0,
    y: 0
  });
  const cleoCallSummary = ref<ICleoCallSummary>(
    callSummaryService.factoryCleoCallSummary()
  );

  function startMenu(useRightClickControllerShow: IUseRightClickControllerShow) {
    rightClickTargetDiv.value =
      useRightClickControllerShow.targetGridContainerId;
    showRightClickMenu.value = true;
    cleoCallSummary.value = commonService.simpleObjectClone(useRightClickControllerShow.data);
    rightClickCoords.value = commonService.simpleObjectClone(useRightClickControllerShow.coords);
    permsForSelectedCall.value = {};
  }

  return {
    startMenu,
    showRightClickMenu,
    rightClickTargetDiv,
    rightClickCoords,
    isLoading,
    permsForSelectedCall,
    cleoCallSummary
  };
}
