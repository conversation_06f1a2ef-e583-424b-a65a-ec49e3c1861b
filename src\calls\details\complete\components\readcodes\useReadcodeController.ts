import { reactive } from "@vue/composition-api";
import { ReadCodes<PERSON>pi } from "@/calls/details/complete/components/readcodes/read-codes-api";
import {
  IApiReadCode,
  ReadCode
} from "@/calls/details/complete/components/readcodes/readcodes-models";
import {
  convertReadCodeDataForTree,
  IReadCodeTree
} from "@/calls/details/complete/components/readcodes/read-codes-service";
import { simpleObjectClone } from "@/common/common-utils";
// E.g.

export interface IUseReadCodeControllerState {
  isLoading: boolean;
  searchKey: string;
  readCodesSelected: Record<ReadCode, IApiReadCode>;
  searchResults: IApiReadCode[];
  searchResultMessage: string;
  readCodesTree: IReadCodeTree[];
  ui: {
    treeDivId: string;
    treeObject: any | null;
    nodeCount: number;
  };
}

export function useReadCodeController() {
  const state: IUseReadCodeControllerState = reactive({
    isLoading: false,
    searchKey: "",
    readCodesSelected: {},
    searchResults: [],
    searchResultMessage: "",
    readCodesTree: [],
    ui: {
      treeDivId: "readCodesTree",
      treeObject: null,
      nodeCount: 0
    }
  });

  const readCodesApi = new ReadCodesApi();

  function init() {
    readCodeSearch().then(() => {
      createTree();
    });
  }

  function search() {
    console.log("useReadCodeController.search");
    readCodeSearch();
  }

  function searchTermChanged(searchKey: string) {
    state.searchKey = searchKey;
    readCodeSearch().then(() => {
      state.ui.treeObject.tree("loadData", state.readCodesTree);
    });
  }

  function readCodeSearch(): Promise<void> {
    console.log("useReadCodeController.readCodeSearch");
    state.isLoading = true;
    state.searchResultMessage = "Loading data...";
    return readCodesApi
      .getReadCodes(state.searchKey, state.searchKey.length > 0)
      .then(response => {
        console.log(response);
        state.searchResults = response.DATA.readCodes;
        const result = convertReadCodeDataForTree(
          response.DATA.readCodes,
          state.ui.nodeCount
        );
        state.readCodesTree = result.readCodesTree;

        state.ui.nodeCount = result.nodeCount;
        if (state.searchKey.length > 0) {
          state.searchResultMessage =
            state.searchResults.length +
            " search results found for '" +
            state.searchKey +
            "'.";
        } else {
          state.searchResultMessage = "";
        }
        state.isLoading = false;
      });
  }

  function createTree() {
    console.log("useReadCodeController.createTree");

    if (typeof $ === "undefined") {
      console.log("useReadCodeController.createTree jQuery is not loaded");
      return;
    }

    state.ui.treeObject = $("#" + state.ui.treeDivId).tree({
      data: state.readCodesTree,
      closedIcon: $("<div class='open-folder-icon'>+</div>"),
      openedIcon: $("<div class='close-folder-icon'>-</div>"),
      onCreateLi: function(node: any, $li: any) {
        if (node.name !== "Loading data...") {
          const ele = $li.find(".jqtree-title");
          let html = "";

          if (node.children.length === 0) {
            html += "<div class='read-code-spacer'></div>";
          }
          html += "<span class='read-code-current'></span>" + ele.html();
          ele.html(html);
        }
      }
    });

    setUpTreeBindings();
  }

  function setUpTreeBindings() {
    state.ui.treeObject.bind(
      "tree.open", //  'tree.open','tree.click'
      function(e: any) {
        // let childTreeData;
        // let selectedNode;
        // let selectedNodeReloaded;
        // let selectedNodeId;

        // Disable single selection
        e.preventDefault();

        const selectedNode: IReadCodeTree = (e.node as any) as IReadCodeTree;
        // const selectedNodeId = selectedNode.id;

        if (selectedNode.id == undefined) {
          console.log(
            "The multiple selection functions require that nodes have an id"
          );
        }
        //   	TODO...prevent multiple clicks of node....can't find obvious node property.
        // $(".readCodeTreeLoading").show();
        state.isLoading = true;

        const readCodeSelected: IApiReadCode = selectedNode.sourceData!;

        // selectedNode.sourceData.ReadCode

        console.log(
          "useReadCodeController...tree.open readCode: " +
            readCodeSelected.ReadCode,
          readCodeSelected
        );

        readCodesApi
          .getReadCodes(readCodeSelected.ReadCode)
          .then(function(childData) {
            // $(".readCodeTreeLoading").hide();
            state.isLoading = false;
            const childTreeData = convertReadCodeDataForTree(
              childData.DATA.readCodes,
              state.ui.nodeCount
            );

            state.ui.treeObject.tree(
              "loadData",
              childTreeData.readCodesTree,
              selectedNode
            );
          });
      }
    ); //  bind(tree.click)

    state.ui.treeObject.bind("tree.select", function(e: any) {
      console.log("...tree.select");
    });

    state.ui.treeObject.bind("tree.dblclick", function(e: any) {
      const selectedNode = e.node;
      addSelected(selectedNode);
    });
  }

  function reset() {
    state.readCodesSelected = {};
    state.searchResults = [];
    state.searchKey = "";
    state.searchResultMessage = "";

    search();
  }

  function addSelected(node: IReadCodeTree | IApiReadCode) {
    console.log("useReadCodeController.addSelected");
    //  no need to check for dups...

    let apiReadCode: IApiReadCode;

    if ((node as IApiReadCode).ReadCode) {
      apiReadCode = node as IApiReadCode;
    } else {
      apiReadCode = (node as IReadCodeTree).sourceData!;
    }

    const readCodesSelected = simpleObjectClone(state.readCodesSelected);
    readCodesSelected[apiReadCode.ReadCode] = apiReadCode;
    state.readCodesSelected = readCodesSelected;
  }

  function removeSelected(node: IReadCodeTree | IApiReadCode) {
    console.log("useReadCodeController.removeSelected");

    let apiReadCode: IApiReadCode;

    if ((node as IApiReadCode).ReadCode) {
      apiReadCode = node as IApiReadCode;
    } else {
      apiReadCode = (node as IReadCodeTree).sourceData!;
    }

    const newSelected: Record<ReadCode, IApiReadCode> = {};

    for (const key in state.readCodesSelected) {
      if (key !== apiReadCode.ReadCode) {
        newSelected[key] = state.readCodesSelected[key];
      }
    }

    state.readCodesSelected = newSelected;
  }

  return {
    state,
    init,
    search,
    searchTermChanged,
    reset,
    addSelected,
    removeSelected
  };
}
