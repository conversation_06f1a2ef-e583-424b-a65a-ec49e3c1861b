import {
  IService,
  SERVICE_NAME,
  SERVICE_TYPE
} from "@/common/services/services-models";
import { ILegacyKeywordFullService } from "@/common/cleo-legacy-models";

export function list(): Promise<IService[]> {
  return window.CallControllerClient.getFullKeywordsPromise("Service").then(
    (resp: Record<string, ILegacyKeywordFullService>) => {
      const services: IService[] = [];
      return Object.keys(resp).reduce((accum, key) => {
        const legacyService: ILegacyKeywordFullService = resp[key];
        const service: IService = {
          id: Number(legacyService.ServiceId),
          name: legacyService.description as SERVICE_NAME,
          serviceType: legacyService.codeID1 as SERVICE_TYPE
        };
        accum.push(service);
        return accum;
      }, services);
    }
  );
}
