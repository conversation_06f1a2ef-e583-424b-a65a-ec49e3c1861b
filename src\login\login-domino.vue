<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" for="username">User name</label>
        <input id="username" name="username" type="text" v-model="userName" />
      </div>
    </div>
    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" for="password">Password</label>
        <input id="password" name="password" type="text" v-model="password" />
      </div>
    </div>
    <div class="row">
      <div class="col s12 m12 l12">
        <button v-on:click.prevent="doLogin">Submit</button>
        <button v-on:click.prevent="testAdapter">Test Adapter</button>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { CLEO_CONFIG } from "@/common/config/config-";
import { loggerInstance } from "@/common/Logger";

@Component({
  name: "login-domino"
})
export default class LoginDomino extends Vue {
  public userName =
    process.env.NODE_ENV === "development"
      ? process.env.VUE_APP_CLEO_USER_NAME
        ? process.env.VUE_APP_CLEO_USER_NAME
        : ""
      : "";
  public password =
    process.env.NODE_ENV === "development"
      ? process.env.VUE_APP_CLEO_USER_PASSWORD
        ? process.env.VUE_APP_CLEO_USER_PASSWORD
        : ""
      : "";

  public doLogin(): void {
    const formData = new FormData();
    formData.append("UserName", this.userName);
    formData.append("Password", this.password);

    const xhr = new XMLHttpRequest();

    xhr.open("POST", CLEO_CONFIG.CLEO.BASE_URL + "/names.nsf?login");

    const params = "UserName=" + this.userName + "&Password=" + this.password;
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send(params);
    xhr.onload = () => {
      loggerInstance.log("Login.onload(): " + xhr.responseText);
    };

    xhr.onerror = () => {
      loggerInstance.log("Login.onerror(): " + xhr.responseText);
    };

    xhr.onreadystatechange = () => {
      loggerInstance.log("Login.onreadystatechange(): " + xhr.readyState);

      if (xhr.readyState === 1) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + " Request started."
        );
      }

      if (xhr.readyState === 2) {
        loggerInstance.log(
          "Login.onreadystatechange(): " +
            xhr.readyState +
            " Headers received.............!"
        );
      }

      if (xhr.readyState === 3) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + "  Data loading..!"
        );
      }
      if (xhr.readyState === 4) {
        loggerInstance.log(
          "Login.onreadystatechange(): " + xhr.readyState + " Request ended."
        );
      }
    };
  }
}
</script>
