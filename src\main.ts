import Vue from "vue";
import App from "./App.vue";
import router from "./router/router";
import { appStore } from "./store/store";
import "./assets/custom.css";
import "./assets/custom-v2.css";
import "./assets/components.css";
import "./assets/css/dropdown.css";

import ToastedPlugin from "vue-toasted";
import PortalVue from "portal-vue";
// import { defaultErrorToastOptions } from "@/common/config/config-";
import VueCompositionAPI from "@vue/composition-api";

Vue.config.productionTip = false;
Vue.use(ToastedPlugin);

Vue.use(PortalVue);

Vue.use(VueCompositionAPI);

import { ErrorData } from "@/error/error-data";
import { debounce } from "@/common/debounce";
import { useUserMessageHandler } from "@/common/user-messages/UserMessage";

/**
 * Every now and then you get an error that goes into a loop, prevent
 * the submission DOS-ing the server.
 */
export const debounceError = debounce(
  (err: Error, moreInfo: Record<string, string>, info: string) => {
    if (process.env.NODE_ENV === "development") {
      return;
    }

    new ErrorData().submitError(err, moreInfo, info, "");
  },
  150
);

const userMessageHandler = useUserMessageHandler();

/**
 *
 * @param err
 * @param vm
 * @param info    Vue specific life cycle hook
 */
Vue.config.errorHandler = function(err, vm, info) {
  // Vue.toasted.show("Vue app Error: " + err.message, defaultErrorToastOptions);
  userMessageHandler.send("Vue app Error: " + err.message);

  console.error("x x x x x Vue.config.errorHandler xz ", {
    err,
    vm,
    info
  });

  // errorData.submitError(err);
  const moreInfo: Record<string, string> = {};

  //  TODO work out how to get component info in a production build.
  /*
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //  @ts-ignore
  if (vm && (vm.$options._componentTag)) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    data.push(escape("component-tag") + "=" + escape(vm.$options._componentTag));
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //  @ts-ignore
  if ($options && $options.$options && $options.$options._componentTag) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    data.push(escape("component-tag") + "=" + escape($options.$options._componentTag));
  }
  */

  /**
   * If client goes into some error loop, don't DOS the server sending in error logs.
   */
  debounceError(err, moreInfo, info);
};

new Vue({
  router,
  store: appStore,
  render: h => h(App)
}).$mount("#app");
