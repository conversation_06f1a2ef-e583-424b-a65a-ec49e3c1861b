import { CLeoPermissionServerResponse } from "@/permissions/permission-models";

export const userPermissionServerResponseMockData: CLeoPermissionServerResponse = {
  JobRoles: {
    "[Role Disp Cont]":
      "[Despatch Controller, NHS 111, OOH / Warwickshire Healthline, R8008, R8010, Roving GP, Standard]",
    "[Role Nurse 111]":
      "[<PERSON> 111, <PERSON><PERSON>, <PERSON> 111, Nurse Triage, R1070, R8001, R8003, Standard]"
  },
  Permissions: {
    "[Role Disp Cont]": {
      "Call.Print": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Print"
      },
      "Call.NO_SUPPORT_XXX": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "NO_SUPPORT"
      },
      "WEBUI_DOCVIEW.Print": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "Print"
      },
      "Call.First Contact": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "First Contact"
      },
      "WEBUI_DOCVIEW.Make Appointment": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "Make Appointment"
      },
      "Call.Dispatch Select": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Dispatch Select"
      },
      "WEBUI_DOCVIEW.Dispatch Retrieve": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "Dispatch Retrieve"
      },
      "Call.Make Appointment": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Make Appointment"
      },
      "Call.Arrived": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Arrived"
      },
      "Call.PACCS_USER": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "PACCS_USER"
      },
      "Call.Downgrade": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Downgrade"
      },
      ["Call.Priority"]: {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Downgrade"
      },
      ["Call.ADD_CONSULTATION"]: {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "ADD_CONSULTATION"
      },
      "Call.Acknowledge Call On Open": {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "Acknowledge Call On Open"
      },
      "WEBUI_DOCVIEW.New Call - East Kent 111": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - East Kent 111"
      },
      "WEBUI_DOCVIEW.New Call - Devon 111": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Devon 111"
      },
      "WEBUI_DOCVIEW.New Call - POSL": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - POSL"
      },
      "WEBUI_DOCVIEW.New Call - FCMS": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - FCMS"
      },
      "WEBUI_DOCVIEW.GRID_FILTER_CLASSIFICATION": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "GRID_FILTER_CLASSIFICATION"
      },
      "WEBUI_DOCVIEW.EDIT CLASSIFICATION": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "EDIT CLASSIFICATION"
      },
      "GLOBAL.SOCKET__INT_TEST": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "SOCKET__INT_TEST"
      },
      "GLOBAL.CAREHOME_SELECT": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "CAREHOME_SELECT"
      },
      COMPLETE_USE_ROTA_CLINICIAN: {
        PermissionAccess: "A",
        PermissionForm: "CALL",
        PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
      },
      "GLOBAL.SESUI_TELEPHPONE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "SESUI_TELEPHPONE"
      },
      "GLOBAL.CASE_MOVE_TO_OVERSIGHT": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "CASE_MOVE_TO_OVERSIGHT"
      },
      "GLOBAL.COMPLETE_USE_BRISDOC": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "COMPLETE_USE_BRISDOC"
      },
      "GLOBAL.COMPLETE_USE_BRISDOC_NON_CLINI_XXX": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "COMPLETE_USE_BRISDOC_NON_CLINI_XXX"
      },
      "CALL.START CONSULTATION": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "START CONSULTATION"
      },
      "New Call - BrisDoc": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - BrisDoc"
      },
      "New Call - Frailty": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Frailty"
      },
      "New Call - Mental Health": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Mental Health"
      },
      "New Call - Out Of Hours Professional Line": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Out Of Hours Professional Line"
      },
      "New Call - Paediatrics": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Paediatrics"
      },
      "New Call - Patientline": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Patientline"
      },
      "New Call - Weekday Professional Line": {
        PermissionAccess: "A",
        PermissionForm: "WEBUI_DOCVIEW",
        PermissionAction: "New Call - Weekday Professional Line"
      },
      MOVE_CASE: {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "MOVE_CASE"
      },
      MOVE_CASE_OUT_URGENT_FOLLOW_UP: {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "MOVE_CASE_OUT_URGENT_FOLLOW_UP"
      },
      "UNLOCK CALL": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "UNLOCK CALL"
      },
      "GLOBAL.DISPATCH_VEHICLE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "DISPATCH_VEHICLE"
      },
      "GLOBAL.RETRIEVE_VEHICLE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "RETRIEVE_VEHICLE"
      },
      "GLOBAL.APPTS_EXTERNAL": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "APPTS_EXTERNAL"
      },
      "GLOBAL.SHOW_GS_PHOTO": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "SHOW_GS_PHOTO"
      },
      "GLOBAL.REQUEST_GS_PHOTO": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "REQUEST_GS_PHOTO"
      },
      "GLOBAL.PLS_REMOVE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "PLS_REMOVE"
      }
    }
  }
};

// "GLOBAL.SESUI_TELEPHPONE": {
//   PermissionAccess: "A",
//   PermissionForm: "GLOBAL",
//   PermissionAction: "SESUI_TELEPHPONE"
// }

// "GLOBAL.GRID__INT_TEST": {
//   PermissionAccess: "A",
//     PermissionForm: "GLOBAL",
//     PermissionAction: "GRID__INT_TEST"
// }

export const mockLiveOutput = {
  "WebUI_DocView.Resend DTS": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Resend DTS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Assign Base": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Assign Base",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - Sheppey Surgery": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Sheppey Surgery",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.PDS Trace": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "PDS Trace",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "GLOBAL.INSTANT_CHAT": {
    PermissionAccess: "A",
    PermissionForm: "GLOBAL",
    PermissionAction: "INSTANT_CHAT",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Resend Individual": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Resend Individual",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.URGENCY_CHANGED": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "URGENCY_CHANGED",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.UnComplete": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "UnComplete",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "WebUI_DocView.Unlock Call": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Unlock Call",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.ALLOW_MULTI_FORM_PROCESS": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "ALLOW_MULTI_FORM_PROCESS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "GLOBAL.APPTS_DISP_CONT": {
    PermissionAccess: "A",
    PermissionForm: "GLOBAL",
    PermissionAction: "APPTS_DISP_CONT",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Elmley": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Elmley",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Tunbridge Wells UCS": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Tunbridge Wells UCS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Roving GP": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Roving GP",
    AuthorityProfile: "Roving GP",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Make Appointment": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Make Appointment",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.Dispatch Retrieve": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Dispatch Retrieve",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Patient.PDS Trace": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "PDS Trace",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: ""
  },
  "Admin.Assign Smart Card": {
    PermissionAccess: "A",
    PermissionForm: "Admin",
    PermissionAction: "Assign Smart Card",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Secondary Assign": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Secondary Assign",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - East Surrey Hospital AE": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - East Surrey Hospital AE",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Dispatch Select": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Dispatch Select",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Assign Base": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Assign Base",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.ALLOW_MULTI_FORM_PROCESS": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "ALLOW_MULTI_FORM_PROCESS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.CLEO_HEALTH": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "CLEO_HEALTH",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.DX_SET_MANUAL": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "DX_SET_MANUAL",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "WebUI_DocView.New Call - Paediatric Referral": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Paediatric Referral",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Maidstone UCS": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Maidstone UCS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Start GAP Protocol": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Start GAP Protocol",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "READ_MODE",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - QEQM AE PCS": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - QEQM AE PCS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.EDIT REFERRAL": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "EDIT REFERRAL",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.Arrived": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Arrived",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.First Contact": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "First Contact",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.Edit Classification": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Edit Classification",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.Resend Resolved": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Resend Resolved",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "GLOBAL.EDIT_ROTA": {
    PermissionAccess: "A",
    PermissionForm: "GLOBAL",
    PermissionAction: "EDIT_ROTA",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Assign": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Assign",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Save And Close": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Save And Close",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.VIEW_DCR_AAA": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "VIEW_DCR_AAA",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.COMFORT_COURTESY_CALL": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "COMFORT_COURTESY_CALL",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Resend Summary": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Resend Summary",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.COMFORT_COURTESY_CALL": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "COMFORT_COURTESY_CALL",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Edit Symptoms": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Edit Symptoms",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.UTC_CLEO_DAB": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "UTC_CLEO_DAB",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - Ashford Primary Care Stream": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Ashford Primary Care Stream",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Reset Service": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Reset Service",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Clinicial Hub": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Clinicial Hub",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Triage Platform": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Triage Platform",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - LRU": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - LRU",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Make Appointment": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Make Appointment",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.PDS Sync": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "PDS Sync",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.Dispatch Retrieve": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Dispatch Retrieve",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Patient.Merge": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "Merge",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: "1"
  },
  "Call.View Contact Info": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "View Contact Info",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.": {
    PermissionAccess: "R",
    PermissionForm: "Call",
    PermissionAction: "",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.EDIT CALL COMMENTS": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "EDIT CALL COMMENTS",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Patient.Save And Close": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "Save And Close",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: ""
  },
  "Call.EDIT CLASSIFICATION": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "EDIT CLASSIFICATION",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Edit Contact Info": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Edit Contact Info",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.DOS Options": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "DOS Options",
    AuthorityProfile: "NHS 111",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "WebUI_DocView.New Call - Brighton CCG Review Clinic": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Brighton CCG Review Clinic",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.UTC_CLEO_CLICK_PANEL1": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "UTC_CLEO_CLICK_PANEL1",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - CareLine": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - CareLine",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.DAB_CANCEL": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "DAB_CANCEL",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Patient.PDS Sync": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "PDS Sync",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Ashford CCG Review Clinic": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Ashford CCG Review Clinic",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - WKUC Home Treatment Service": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - WKUC Home Treatment Service",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - East Kent 111": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - East Kent 111",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Arrived Change": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Arrived Change",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "WebUI_DocView.Secondary Assign": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Secondary Assign",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Start ILTC Protocol": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Start ILTC Protocol",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "READ_MODE",
    PermissionFormulaResult: "1"
  },
  "Call.Arrived": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Arrived",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Skillset15": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Skillset15",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Patient.Edit Patient": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "Edit Patient",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "READ_MODE",
    PermissionFormulaResult: ""
  },
  "Call.GP CONNECT": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "GP CONNECT",
    AuthorityProfile: "Triage Platform",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.GRID_FILTER_CLASSIFICATION": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "GRID_FILTER_CLASSIFICATION",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Dispatch Select": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Dispatch Select",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.Assign": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Assign",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - Brighton District Nurse": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Brighton District Nurse",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Start NHS Pathways": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Start NHS Pathways",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "WebUI_DocView.New Call - National Paediatric": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - National Paediatric",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.BaseInOut": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "BaseInOut",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Complete": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Complete",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.Arrived Remove": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Arrived Remove",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Patient.UnMerge": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "UnMerge",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: "0"
  },
  "GLOBAL.SELECT_TRIAGE_PLATFORM": {
    PermissionAccess: "A",
    PermissionForm: "GLOBAL",
    PermissionAction: "SELECT_TRIAGE_PLATFORM",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Acknowledge Recipt Base": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Acknowledge Recipt Base",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.Unlock Call": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Unlock Call",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.SESUI_TELEPHPONE": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "SESUI_TELEPHPONE",
    AuthorityProfile: "Despatch Controller",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - GYW111---REMOVED": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - GYW111---REMOVED",
    AuthorityProfile: "NHS 111",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - POSL": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - POSL",
    AuthorityProfile: "OOH / Warwickshire Healthline",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - South Essex 111": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - South Essex 111",
    AuthorityProfile: "NHS 111",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.SEARCH DOS": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "SEARCH DOS",
    AuthorityProfile: "NHS 111",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - Norfolk and Wisbech 111": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - Norfolk and Wisbech 111",
    AuthorityProfile: "NHS 111",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - OOH": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - OOH",
    AuthorityProfile: "OOH / Warwickshire Healthline",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.New Call - COV19": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - COV19",
    AuthorityProfile: "OOH / Warwickshire Healthline",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.SAFEGUARD_ADULT": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "SAFEGUARD_ADULT",
    AuthorityProfile: "Roving GP",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: "1"
  },
  "WebUI_DocView.New Call - West Kent Roving GP": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "New Call - West Kent Roving GP",
    AuthorityProfile: "Roving GP",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.SAFEGUARD_CHILD": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "SAFEGUARD_CHILD",
    AuthorityProfile: "Roving GP",
    PermissionActionMode: "EDIT_MODE",
    PermissionFormulaResult: "0"
  },
  "Patient.Close": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "Close",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Print": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Print",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.View Pathways Report": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "View Pathways Report",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.ADD CALL COMMENTS": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "ADD CALL COMMENTS",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.Patient History": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Patient History",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "WebUI_DocView.Print": {
    PermissionAccess: "A",
    PermissionForm: "WebUI_DocView",
    PermissionAction: "Print",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Call.Close": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "Close",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: ""
  },
  "Patient.Print": {
    PermissionAccess: "A",
    PermissionForm: "Patient",
    PermissionAction: "Print",
    AuthorityProfile: "Standard",
    PermissionActionMode: "READ_MODE",
    PermissionFormulaResult: ""
  },
  "Call.SELECT_SURGERY": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "SELECT_SURGERY",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  },
  "Call.PRINT PATHWAYS": {
    PermissionAccess: "A",
    PermissionForm: "Call",
    PermissionAction: "PRINT PATHWAYS",
    AuthorityProfile: "Standard",
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.LINK_CALL_ACTIONS_NEW_CALL": {
    PermissionActionMode: "",
    PermissionFormulaResult: "0"
  },
  "Call.RESET_SERVICE": {
    PermissionActionMode: "",
    PermissionFormulaResult: "1"
  }
};
