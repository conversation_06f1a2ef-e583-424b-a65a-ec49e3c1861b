import { IFailedContactCode } from "./../../keywords/keywords-models";
import { IConsult } from "@/consults/consult-models";
import { IPaccsForm } from "@/paccs/paccs-models";
import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE,
  GENDER,
  GUID,
  IsoDateTimeLocal,
  IsoDateTimeUTC
} from "@/common/common-models";
import { IObjectBase } from "@/calls/summary/call-summarry-models";
import { IService } from "@/common/services/services-models";
import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";
import {
  CompleteProcess,
  IFailedContactComplete
} from "@/calls/details/complete/complete-models";
import { FollowUpInputState } from "@/calls/details/complete/components/followup/models/follow-up-models";

export interface ICaseConfig {
  dx: {
    validation: string[]; // Dx codes that require validation
  };
  failedContactConfig: IFailedContactComplete;
}

export interface ICallDetailState {
  callDetail: ICallDetail;
  paccsFormData: IPaccsForm;
  startedConsult: boolean;
  showPaccs: boolean;
  isPaccsReady: boolean;
  address: {
    isWhat3WordsReady: boolean;
    showModal: boolean;
  };
  completeCase: {
    show: boolean;
    process: CompleteProcess;
  };
  followUpInputState: FollowUpInputState;
  showFollowUp: boolean;
}

export interface ICleoAddress {
  line1: string;
  line2: string;
  line3: string;
  line4: string;
  town: string;
  postCode: string;
}

export interface ICallDetail {
  Id: number;
  CallNo: number;
  Classification: {
    Id: number;
    Description: CALL_CLASSIFICATION | "";
  }; //  IObjectBase;
  UrgentYn: boolean;
  PathwaysCaseId: GUID; //  Latest Pathways
  PathwaysCaseId_ITK_IN: GUID; //  CallSPA nonsense, PathwaysCaseID can egt moved into here...and can subsequently be removed!!!!!
  PathwaysCaseId_FROM_ITK: GUID; // Guaranteed to have been sent in by ITK
  ContainsRehydratedCase: "1" | "0" | "" | null;
  Consults: IConsult[];
  linkedCallNumbers: number[];
  // startedConsult: boolean;
  PACCS_EVER?: "1" | "0" | "";
  CallDOB: string;
  CallMF: GENDER;
  callAddress: ICleoAddress;
  homeAddress: ICleoAddress;
  DispatchVehicle: string;

  Forename: string;
  Surname: string;

  Age: number;
  AgeClass: string;

  Symptoms: string;

  CareHomeId: number;
  CareHome: IObjectBase;

  Service: IService;
  Contract: IObjectBase;
  cleoClientService: CLEO_CLIENT_SERVICE;

  CallTelNo_R: string;

  failedContacts: IFailedContact[];
  dx: {
    ch: {
      dxCode: string;
      dxDescription: string;
    };
    clini: {
      dxCode: string;
      dxDescription: string;
    };
  };
}

export type UiLegacyDomId = "some-weird-span_id";

export type legacyDomId = keyof Pick<
  ICallDetailLegacy,
  "CallForename" | "CallTelNo_R" | "CallSurname"
>;
export type targetCallDetailProp = keyof Pick<
  ICallDetail,
  "Forename" | "CallTelNo_R" | "Surname"
>;

/**
 * Map from legacy ui <span>, <input> to ICallDetail
 */
export const UiDomMapFromLegacyToNew: Record<
  legacyDomId,
  targetCallDetailProp
> = {
  CallTelNo_R: "CallTelNo_R",
  CallForename: "Forename",
  CallSurname: "Surname"
};

// export type UiDomMapFromLegacyToNewTypes = typeof UiDomMapFromLegacyToNew;
// export type UiDomMapFromLegacyToNewType = keyof UiDomMapFromLegacyToNewTypes;
// export type UiDomMapFromLegacyToNewValue = UiDomMapFromLegacyToNewTypes[keyof UiDomMapFromLegacyToNewTypes];

//  Legacy looks like this.
//CN=Nick Test2/O=staging~Answerphone - Message left~2024-06-26T13:44:11

export type FailedContactType = string;

export interface IFailedContact {
  userName: string;
  type: FailedContactType;
  time: IsoDateTimeLocal;
}
