import { ToastOptions } from "vue-toasted";
import Vue from "vue";
import { defaultErrorToastOptions } from "@/common/config/config-";
import { ErrorData } from "@/error/error-data";

export function useBroadcastUserMessage(
  message: string,
  toastOptions?: ToastOptions
): void {
  Vue.toasted.show(
    message,
    toastOptions ? toastOptions : defaultErrorToastOptions
  );
}

export function useLogServerMessage(
  error: Error,
  moreInfo: Record<string, string>,
  vueInfo: string,
  userMessage?: string
): void {
  if (process.env.NODE_ENV === "development") {
    return;
  }
  new ErrorData().submitError(error, moreInfo, vueInfo);
}
