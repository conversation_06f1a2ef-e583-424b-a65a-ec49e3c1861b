import {
  ICallDetail,
  ICallDetailState,
  ICaseConfig,
  ICleoAddress,
  IFailedContact
} from "@/calls/details/call-details-models";
import {
  CallDetailLegacyFieldName,
  ICallDetailLegacy
} from "@/calls/details/call-details-legacy-models";
import { SERVICE_NAME, SERVICE_TYPE } from "@/common/services/services-models";
import { PaccsService } from "@/paccs/paccs-service";
import { factoryFollowUpInputState } from "@/calls/details/complete/components/followup/models/follow-up-service";
import { CALL_CLASSIFICATION } from "@/common/common-models";

const paccsService = new PaccsService();

export function factoryCaseConfig(): ICaseConfig {
  return {
    dx: {
      validation: []
    },
    failedContactConfig: {
      config: {
        attemptsRequired: 2,
        minsInterval: 20
      }
    }
  };
}

export function factoryCallDetail(): ICallDetail {
  return {
    Id: 0,
    CallNo: 0,
    Classification: {
      Id: 0,
      Description: ""
    },
    UrgentYn: false,
    Consults: [],
    linkedCallNumbers: [],
    // startedConsult: false,
    ContainsRehydratedCase: "",
    PathwaysCaseId: "",
    PathwaysCaseId_FROM_ITK: "",
    PathwaysCaseId_ITK_IN: "",
    CallDOB: "",
    CallMF: "",
    callAddress: factoryCleoAddress(),
    homeAddress: factoryCleoAddress(),
    DispatchVehicle: "",
    Forename: "",
    Surname: "",
    Age: 0,
    AgeClass: "",
    Symptoms: "",
    CareHomeId: 0,
    CareHome: {
      Id: 0,
      Description: ""
    },
    Service: {
      id: 0,
      name: "OOH",
      serviceType: "OOH"
    },
    Contract: {
      Id: 0,
      Description: ""
    },
    cleoClientService: "",
    CallTelNo_R: "",
    failedContacts: [],
    dx: {
      ch: {
        dxCode: "",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    }
  };
}

export function factoryICallDetailState(): ICallDetailState {
  return {
    address: { isWhat3WordsReady: false, showModal: false },
    callDetail: factoryCallDetail(),
    isPaccsReady: false,
    paccsFormData: paccsService.factoryPaccsForm(),
    showPaccs: false,
    startedConsult: false,
    completeCase: {
      show: false,
      process: "SAVE_AND_RETURN_PROCESS"
    },
    followUpInputState: factoryFollowUpInputState(),
    showFollowUp: false
  };
}

export function factoryCleoAddress(): ICleoAddress {
  return {
    line1: "",
    line2: "",
    line3: "",
    line4: "",
    postCode: "",
    town: ""
  };
}

export function mapLegacyCall(legacyCallData: ICallDetailLegacy): ICallDetail {
  const callDetail = factoryCallDetail();

  callDetail.Id = Number(legacyCallData.CallNo.VALUE[0]);
  callDetail.CallNo = callDetail.Id;

  callDetail.Surname = legacyCallData.CallSurname.VALUE[0];
  callDetail.Forename = legacyCallData.CallForename.VALUE[0];

  callDetail.callAddress.line1 = legacyCallData.CallAddress1.VALUE[0];
  callDetail.callAddress.line2 = legacyCallData.CallAddress2.VALUE[0];
  callDetail.callAddress.line3 = legacyCallData.CallAddress3.VALUE[0];
  callDetail.callAddress.line4 = legacyCallData.CallAddress4.VALUE[0];
  callDetail.callAddress.town = legacyCallData.CallTown.VALUE[0];
  callDetail.callAddress.postCode = legacyCallData.CallPostCode.VALUE[0];

  callDetail.Classification.Id = 1;
  callDetail.Classification.Description = legacyCallData.CallClassification
    .VALUE[0] as CALL_CLASSIFICATION;

  callDetail.Age = Number(legacyCallData.CallAge.VALUE[0]);
  callDetail.AgeClass = legacyCallData.CallAgeClass.VALUE[0];

  callDetail.Symptoms = legacyCallData.CallSymptoms.VALUE[0];
  callDetail.DispatchVehicle = legacyCallData.Dispatch_Vehicle
    ? legacyCallData.Dispatch_Vehicle.VALUE[0]
    : "";

  callDetail.CareHomeId = Number(
    getLegacyValueFromJson(legacyCallData, "CareHomeId", "0")
  );

  callDetail.CareHome = {
    Id: Number(getLegacyValueFromJson(legacyCallData, "CareHomeId", "0")),
    Description: getLegacyValueFromJson(legacyCallData, "CareHomeName", "")
  };

  callDetail.Service = {
    id: Number(getLegacyValueFromJson(legacyCallData, "CallServiceId", "0")),
    name: getLegacyValueFromJson(
      legacyCallData,
      "CallService",
      ""
    ) as SERVICE_NAME,
    serviceType: getLegacyValueFromJson(
      legacyCallData,
      "CallServiceType",
      "OOH"
    ) as SERVICE_TYPE
  };

  callDetail.Contract = {
    Id: Number(getLegacyValueFromJson(legacyCallData, "IUC_ContractID", "0")),
    Description: getLegacyValueFromJson(legacyCallData, "IUC_Contract", "")
  };

  callDetail.cleoClientService = getLegacyValueFromJson(
    legacyCallData,
    "cleoClientService",
    ""
  );

  callDetail.CallTelNo_R = getLegacyValueFromJson(
    legacyCallData,
    "CallTelNo_R",
    ""
  );

  callDetail.failedContacts = convertFailedContacts(legacyCallData);

  callDetail.dx = {
    ch: {
      dxCode: getLegacyValueFromJson(
        legacyCallData,
        "CHFinalDispositionCode",
        ""
      ),
      dxDescription: getLegacyValueFromJson(
        legacyCallData,
        "CHFinalDispositionDescription",
        ""
      )
    },
    clini: {
      dxCode: getLegacyValueFromJson(
        legacyCallData,
        "FinalDispositionCode",
        ""
      ),
      dxDescription: getLegacyValueFromJson(
        legacyCallData,
        "FinalDispositionDescription",
        ""
      )
    }
  };

  return callDetail;
}

export function getLegacyValueFromJson(
  legacyCallData: ICallDetailLegacy,
  fieldName: CallDetailLegacyFieldName,
  defaultValue: string
): string {
  if (legacyCallData[fieldName]) {
    return legacyCallData[fieldName]!.VALUE[0] as string;
  }
  return defaultValue;
}

export function convertFailedContacts(
  legacyCallData: ICallDetailLegacy
): IFailedContact[] {
  if (!legacyCallData.PatientContactCode_Events) {
    return [];
  }

  return (legacyCallData.PatientContactCode_Events.VALUE as string[]).reduce<
    IFailedContact[]
  >((acc: IFailedContact[], failedContactString: string) => {
    const failedContactParts: string[] = failedContactString.split("~");

    const failedContact: IFailedContact = {
      userName: failedContactParts[0],
      type: failedContactParts[1],
      time: failedContactParts[2]
    };
    acc.push(failedContact);
    return acc;
  }, []);
}

// export function getLegacyObjectFromJson(
//   legacyCallData: ICallDetailLegacy,
//   fieldName: CallDetailLegacyFieldName,
//   defaultValue: unknown
// ): string {
//   if (legacyCallData[fieldName]) {
//     return legacyCallData[fieldName]!.VALUE;
//   }
//   return defaultValue;
// }

// export function isCareHomeSelectionAllowed(callDetail: ICallDetail, cleoPermissionsForRole: CleoPermissionsForRole ): boolean {
//
// }
