// import { IGridStoreState } from "@/calls/grids/grid-store";
import { IAdapterPagedResponse } from "@/common/common-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { IGridDefinition } from "@/calls/grids/grid-models";

export class GridService {
  // public factoryGridStoreState(): IGridStoreState {
  //   return {
  //     isLoading: false,
  //     gridDefinition: this.factoryGridDefinition()
  //   };
  // }

  public factoryGridDefinition(): IGridDefinition {
    return {
      identifier: "CatchAllCalls", //  TODO  create an "undefined" state
      title: "",
      description: "",
      params: {},
      legacy: false,
      legacyOptions: {
        enabled: false,
        url: ""
      },
      colDefintions: {
        onlyIncludeTheseFields: [],
        excludeTheseFields: []
      }
    };
  }

  public filterAdapterPagedResponse(
    adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary>
  ): ICleoCallSummary[] {
    //  commonService.simpleObjectClone(adapterPagedResponse);
    return adapterPagedResponse.Records.filter(cleoCallSummary => {
      return cleoCallSummary.CallNo > 0;
    });
  }
}
