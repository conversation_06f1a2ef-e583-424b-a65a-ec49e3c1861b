<template>
  <div>
    <CleoModalLoading
      body-message="Loading App..."
      v-if="false"
    ></CleoModalLoading>

    <AppLayoutDefault v-if="getIsAppLoaded"></AppLayoutDefault>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { IAdapterAction, IAdapterMenuMapper } from "@/app-models";
import AppLayoutDefault from "@/layouts/app-layout-default.vue";
import { CONFIG_STORE_CONST } from "@/common/config/config-store";
import { loggerInstance } from "@/common/Logger";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "@/router/router";
import { LoginService } from "@/login/login-service";
import { LOGIN_STORE_STORE_CONST } from "@/login/login-store";
import { KEYWORD_STORE_STORE_CONST } from "@/keywords/keywords-store";
import LoadingSpinnerLarge from "@/common/ui/loading-spinner-large.vue";
import CleoModalLoading from "@/common/ui/modal/cleo-modal-loading.vue";

@Component({
  name: "app",
  components: { CleoModalLoading, LoadingSpinnerLarge, AppLayoutDefault }
})
export default class App extends Vue {
  /**
   * In legacy CLEO, server rendered page creates a variable ADAPTER_MENU
   * This app will watch that variable and is a way to send "actions" from
   * CLEO legacy into new app.  Any changes to this variable will be pushed
   * to the app VUEX store and anything bound to that can react.
   * N.B.  Make sure to not replace ADAPTER_MENU in legacy CLEO, just mutate it.
   */
  public cleoGlobalMenuName: IAdapterMenuMapper = { id: "" };

  public adapterCLEOAction: IAdapterAction = {
    payload: {
      actionType: "",
      data: null
    }
  };

  public hasJwt = false;
  public jwtMessage = "";

  public isKeywordRefDataLoaded = false;

  public created(): void {
    loggerInstance.log("Launch created...");
  }

  public mounted(): void {
    loggerInstance.log("Launch mounted...");
    //  TODO at some point there will probably be an "app" socket.
    // startSocket();

    /**
     * Set up watcher for ADAPTER_MENU.  This global variable exists in legacy CLEO app.
     */
    if (window.ADAPTER_MENU) {
      this.cleoGlobalMenuName = Vue.observable(window.ADAPTER_MENU);
    }

    if (window.ADAPTER_CLEO_ACTION) {
      this.adapterCLEOAction = Vue.observable(window.ADAPTER_CLEO_ACTION);
    }

    if (
      "-ms-scroll-limit" in document.documentElement.style &&
      "-ms-ime-align" in document.documentElement.style
    ) {
      // detect it's IE11
      window.addEventListener(
        "hashchange",
        () => {
          const currentPath = window.location.hash.slice(1);
          if (this.$route.path !== currentPath) {
            this.$router.push(currentPath);
          }
        },
        false
      );
    }

    this.jwtMessage = "Loading App.  Retrieving JWT...";
    const loginService: LoginService = new LoginService();
    loginService.requestJwt().then(response => {
      if (response) {
        this.$store.commit(
          LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME +
            "/" +
            LOGIN_STORE_STORE_CONST.LOGIN_STORE__MUTATION_SET_TOKEN,
          response
        );
        loginService.setJwtAccessToken(response);
        this.hasJwt = true;

        this.$store
          .dispatch(
            KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME +
              "/" +
              KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__ACTIONS_GET_REF_DATA
          )
          .finally(() => {
            this.isKeywordRefDataLoaded = true;
          });
      } else {
        this.jwtMessage = "Loading App.  Retrieving JWT...could not be found.";
      }
    });
  }

  @Watch("cleoGlobalMenuName", { deep: true })
  public onCleoGlobalMenuNameChanged(newValue: IAdapterMenuMapper): void {
    loggerInstance.log("Launch.onCleoGlobalMenuNameChanged @Watch", newValue);
    if (newValue && newValue.id && newValue.id.length > 0) {
      this.handleCleoMenuChange(newValue.id);
    }
  }

  public get getIsAppLoaded(): boolean {
    return this.hasJwt && this.isKeywordRefDataLoaded;
  }

  /**
   *  This handles route switching when triggered by legacy CLEO app.
   * @param viewName
   */
  public handleCleoMenuChange(viewName: string): void {
    loggerInstance.log("Launch.handleCleoMenuChange() viewName: " + viewName);

    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG__MUTATION_SET_LEGACY_GRID_RESIZE,
      { timeIso: new Date().toISOString() }
    );

    /**
     * Allowable list of routes CLEO "classic" can navigate to
     */
    // const cleoViewMap: Record<string, RawLocation> = {
    //   [LAUNCH_ROUTES_PATHS.GRID_111]: {
    //     path: "/" + LAUNCH_ROUTES_PATHS.GRID_111
    //   },
    //   [LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE]: {
    //     path: "/" + LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE
    //   }
    // };

    // const cleoViewMapName: string = LAUNCH_ROUTES_PATHS[viewName];
    const cleoViewMapName = viewName;

    if (viewName.length > 0) {
      const location: RawLocation = {
        path: cleoViewMapName
      };
      if (location) {
        this.$router.push(location).catch(error => {
          //  https://stackoverflow.com/questions/57837758/navigationduplicated-navigating-to-current-location-search-is-not-allowed
          if (error.name !== "NavigationDuplicated") {
            throw error;
          }
        });
        return;
      } else {
        console.error(
          "Launch.handleCleoMenuChange() viewName: " +
            viewName +
            " is not MAPPED!"
        );
      }
    }
    this.$router.push({ path: "/" + LAUNCH_ROUTES_PATHS.WELCOME });
  }

  @Watch("adapterCLEOAction", { deep: true })
  public onAdapterCLEOActionChanged(newValue: IAdapterAction): void {
    loggerInstance.log("Launch.onAdapterCLEOActionChanged:", newValue);
    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG__MUTATION_SET_ADAPTER_CLEO_ACTION,
      newValue
    );
  }
}
</script>

<style>
.app--is-loading {
  display: inline;
  height: 100px;
  opacity: 1;
}
.app--is-loaded {
  visibility: hidden;
  opacity: 0;
  height: 0;
  transition: visibility 0s, opacity 2.5s linear;
}
.trans-show {
  visibility: visible;
  opacity: 1;
  transition-delay: 0.51s;
}

.trans-hide {
  visibility: hidden;
  opacity: 0;
  height: 0;
  transition: height 1.5s, visibility 0.5s, opacity 0.5s linear;
}
</style>
