<template>
  <div>
    <div>
      <div>
        <div class="what-3-words-cleo-confirmed--label">What3Words</div>
        <div class="what-3-words-cleo-confirmed--data">
          <span
            v-text="
              what3wordsControllerState.results.suggestion
                ? what3wordsControllerState.results.suggestion.words
                : ''
            "
          ></span>
        </div>
      </div>
      <div>
        <!--        <div class="what-3-words-cleo-confirmed&#45;&#45;label">Confirmed</div>-->
        <div class="what-3-words-cleo-confirmed--data">
          <input
            type="checkbox"
            id="what-3-words-confirmed"
            v-on:change="toggleWhat3WordsConfirmed"
          />
          <label
            for="what-3-words-confirmed"
            :class="
              'what-3-words-cleo-confirmed--' +
                (what3wordsControllerState.userInput.what3WordsConfirmed
                  ? 'confirmed'
                  : 'not-confirmed')
            "
          >
            Confirmed
            {{ what3wordsControllerState.userInput.what3WordsConfirmed }}
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";

import { IWhat3wordsControllerState } from "@/what3words/what3words-cleo-models";
export default defineComponent({
  name: "what-3-words-cleo-confirmed",
  props: {
    what3wordsControllerState: {
      type: Object as PropType<IWhat3wordsControllerState>,
      required: true
    }
  },
  setup(
    props: { what3wordsControllerState: IWhat3wordsControllerState },
    context: SetupContext
  ) {
    function toggleWhat3WordsConfirmed() {
      context.emit("toggleWhat3WordsConfirmed");
    }

    function toggleAddressConfirmed() {
      context.emit("toggleAddressConfirmed");
    }

    return {
      toggleWhat3WordsConfirmed,
      toggleAddressConfirmed
    };
  }
});
</script>

<style>
.what-3-words-cleo-confirmed--label {
  /*display: inline-block;*/
  font-weight: 600;
  color: #243c5f;
}

.what-3-words-cleo-confirmed--data {
  /*display: inline-block;*/
  font-weight: 600;
}

.what-3-words-cleo-confirmed--confirmed {
  color: green;
}

.what-3-words-cleo-confirmed--not-confirmed {
  color: red;
}
</style>
