import {
  CompleteStepName,
  CompleteStepNameCas,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IStep
} from "@/calls/details/complete/complete-models";
import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import { BrisDocCompleteStepNameCas } from "@/calls/details/complete/brisdoc/models/brisdoc-complete-models";
import * as CompleteService from "@/calls/details/complete/complete-service";

export function factoryBrisDocStepFactoryBaseEndAssessment(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<BrisDocCompleteStepNameCas> {
  // A list of all steps we can build from.
  const stepsDefault: Record<
    CompleteStepName,
    IStep<CompleteStepName>
  > = CompleteService.factorySteps();

  /**
   *   END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
   *     HOW_WAS_CASE_MANAGED: stepsDefault.HOW_WAS_CASE_MANAGED,
   *     CONTACT_MADE: stepsDefault.CONTACT_MADE,
   *     PATIENT_REFERRED_TO: stepsDefault.PATIENT_REFERRED_TO,
   *     OUTCOMES: stepsDefault.OUTCOMES,
   *     READ_CODES: stepsDefault.READ_CODES,
   *     FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
   *     FAILED_CONTACT_SAFEGUARDING: stepsDefault.FAILED_CONTACT_SAFEGUARDING,
   *     FAILED_CONTACT_RISK_ASSESSMENT: stepsDefault.FAILED_CONTACT_RISK_ASSESSMENT,
   *     PATIENT_RISK_ASSESSMENT: stepsDefault.PATIENT_RISK_ASSESSMENT,
   *     INSUFFICIENT_CONTACT_ATTEMPTS: stepsDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
   *     TAXI: stepsDefault.TAXI,
   *     VULNERABILITY: stepsDefault.VULNERABILITY,
   *     UNKNOWN: stepsDefault.UNKNOWN
   */

  // HOW_WAS_CASE_MANAGED: stepsDefault.HOW_WAS_CASE_MANAGED,
  // The actual steps we will use for this process.
  const steps: Record<BrisDocCompleteStepNameCas, IStep<CompleteStepName>> = {
    END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
    CONTACT_MADE: stepsDefault.CONTACT_MADE,
    READ_CODES: stepsDefault.READ_CODES,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    FAILED_CONTACT_RISK_ASSESSMENT: stepsDefault.FAILED_CONTACT_RISK_ASSESSMENT,
    INSUFFICIENT_CONTACT_ATTEMPTS: stepsDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING:
      stepsDefault.BRISDOC_NON_CLINICAL_AND_PRESCRIBING,
    BRISDOC_AUDIT_QUESTIONS: stepsDefault.BRISDOC_AUDIT_QUESTIONS,
    OUTCOMES: stepsDefault.OUTCOMES,
    FURTHER_ACTION: stepsDefault.FURTHER_ACTION,
    UNKNOWN: stepsDefault.UNKNOWN
  };

  // How user moves backwards and forwards thru steps
  const gotoNextMap: Record<BrisDocCompleteStepNameCas, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "CONTACT_MADE";
    },
    // HOW_WAS_CASE_MANAGED: () => {
    //   state.currentStep = "CONTACT_MADE";
    // },
    CONTACT_MADE: () => {
      if (state.userResponse.contactMade.id === "CONTACT_MADE") {
        state.currentStep = "BRISDOC_NON_CLINICAL_AND_PRESCRIBING";
        return;
      }
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    FAILED_CONTACT_REASON: () => {
      // state.currentStep = "PATIENT_RISK_ASSESSMENT";
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
      state.ui.disableNext = true;
      state.ui.disableComplete = true;
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      //  TODO remove this step, left here in case of rollback.
      //  @deprecated
      state.isProcessComplete = state.steps[state.currentStep].isValid;
      state.finalAction =
        state.userResponse.insufficientContactAttempts.id ===
        "OVERRIDE_COMPLETE"
          ? "COMPLETE"
          : "RETURN_TO_OPEN_CASE";
    },
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: () => {
      state.currentStep = "BRISDOC_AUDIT_QUESTIONS";
    },
    BRISDOC_AUDIT_QUESTIONS: () => {
      state.currentStep = "OUTCOMES";
    },
    OUTCOMES: () => {
      state.currentStep = "FURTHER_ACTION";
    },
    FURTHER_ACTION: () => {
      state.currentStep = "READ_CODES";
    },
    READ_CODES: () => {
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  const gotoBackMap: Record<BrisDocCompleteStepNameCas, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    // HOW_WAS_CASE_MANAGED: () => {
    //   state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    // },
    CONTACT_MADE: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "CONTACT_MADE";
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      //  user can not move off this step.
      state.currentStep = "PATIENT_RISK_ASSESSMENT";
    },
    BRISDOC_NON_CLINICAL_AND_PRESCRIBING: () => {
      state.currentStep = "CONTACT_MADE";
    },
    BRISDOC_AUDIT_QUESTIONS: () => {
      state.currentStep = "BRISDOC_NON_CLINICAL_AND_PRESCRIBING";
    },
    OUTCOMES: () => {
      state.currentStep = "BRISDOC_AUDIT_QUESTIONS";
    },
    FURTHER_ACTION: () => {
      state.currentStep = "OUTCOMES";
    },
    READ_CODES: () => {
      state.currentStep = "FURTHER_ACTION";
    },
    UNKNOWN: () => {
      console.error("useCompleteController.cancel under construction!");
    }
  };

  return {
    steps: steps,
    validateMap: CompleteService.validationMapDefault(
      state,
      completeControllerInput
    ),
    gotoNextMap: gotoNextMap,
    gotoBackMap: gotoBackMap
  } as ICompleteUserStepsConfig<BrisDocCompleteStepNameCas>;
}
