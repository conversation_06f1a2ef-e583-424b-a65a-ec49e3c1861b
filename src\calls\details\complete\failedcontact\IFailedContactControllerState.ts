import { IBaseControllerState, IStep } from "../complete-models";
import { ISimpleButtonInputValue } from "../simple-button-selector-models";
import { HowManaged, HowManagedValue } from "./how-managed/how-managed-models";
import { FailedContactReason } from "./failed-contact-reasons/failed-contact-models";
import { FaildContactStepName } from "./useFailedContactController";

export interface IFailedContactControllerState
  extends IBaseControllerState<FaildContactStepName> {
  count: number;
  steps: Record<FaildContactStepName, IStep<FaildContactStepName>>;
  userResponse: {
    howManaged:
      | ISimpleButtonInputValue<HowManaged, HowManagedValue>
      | ISimpleButtonInputValue<"", "">;
    contactMade: boolean | null;
    failedContactReason:
      | ISimpleButtonInputValue<FailedContactReason, FailedContactReason>
      | ISimpleButtonInputValue<"", "">;
    safeguarding: {
      risk: "" | "LOW" | "HIGH";
      furtherAction: null | boolean;
      welfareAction: "";
    };
  };
}
