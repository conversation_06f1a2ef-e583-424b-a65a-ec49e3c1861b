<template>
  <div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  ref,
  watch,
  SetupContext,
  ComputedRef,
  computed
} from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IConsult } from "@/consults/consult-models";
import { ConsultsService } from "@/consults/consults-service";

const commonService: CommonService = new CommonService();
const consultsService: ConsultsService = new ConsultsService();

export default defineComponent({
  // type inference enabled
  name: "test-composition",
  components: {},
  props: {
    consult: {
      default: () => {
        return consultsService.factoryConsult();
      }
    }
  },
  setup(props: { consult: IConsult }, context: SetupContext) {
    let consultInternal: IConsult = reactive(
      commonService.simpleObjectClone(props.consult)
    );

    let showConsultSection = ref(false);

    onBeforeMount(() => {
      console.log("consult>>>>>>>>>>>>>>>>>>mounted!");
    });

    // const consultsService = new ConsultsService();

    watch(
      () => props.consult,
      (newValue: IConsult, oldValue: IConsult) => {
        console.log("consult>>>>>>>>>>>>>>>>>>watch!", {
          newValue,
          oldValue
        });
        Object.assign(consultInternal, newValue);
      }
    );

    const getHeaderTitle: ComputedRef<string> = computed(() => {
      return consultsService.getHeaderTitle(consultInternal);
    });

    const toggleShowConsultSection = () => {
      showConsultSection.value = !showConsultSection.value;
    };

    return {
      consultInternal,
      consultsService,
      getHeaderTitle,
      showConsultSection,
      toggleShowConsultSection
    };
  }
});
</script>

<style scoped>
</style>
