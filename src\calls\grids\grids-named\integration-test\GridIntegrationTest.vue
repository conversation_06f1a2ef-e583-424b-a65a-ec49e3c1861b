<template>
  <div>
    <LoadingSpinner v-if="state.legacy.isLoading"></LoadingSpinner>
    <table style="width: 100%">
      <tr>
        <th></th>
        <th style="text-align: center">CLEO</th>
        <th style="text-align: center">Adapter</th>
      </tr>
      <tr>
        <td><span v-text="gridDefinition.description"></span>Queue Size</td>
        <td style="text-align: center" v-text="state.legacy.data.length"></td>
        <td style="text-align: center" v-text="adapterCalls.length"></td>
      </tr>
    </table>

    <div style="margin-top: 1em">Call difference table (ONLY differences!)</div>
    <table style="width: 100%">
      <tr>
        <th style="text-align: left">Call No</th>
        <th style="text-align: left">CLEO</th>
        <th style="text-align: left">Adapter</th>
        <th style="text-align: left">Diffs</th>
      </tr>

      <tr v-for="callWithDiff in callsWithDiffs" :key="callWithDiff.callNumber">
        <td v-text="callWithDiff.callNumber"></td>
        <td
          v-text="callWithDiff.existsInLegacy"
          :style="callWithDiff.existsInLegacy ? '' : 'color: red'"
        ></td>
        <td
          v-text="callWithDiff.existsInAdapter"
          :style="callWithDiff.existsInAdapter ? '' : 'color: red'"
        ></td>
        <td>{{ getCallDiffs(callWithDiff) }}</td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType } from "@vue/composition-api";
import { reactive } from "@vue/composition-api";
import { IGridDefinition, SocketGroup } from "@/calls/grids/grid-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import https from "@/common/https";
import {
  gridNamedIntegrationService,
  ICompareCallResult
} from "@/calls/grids/grids-named/integration-test/grid-named-integration-service";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import {
  ILegacyCleoCallSummary,
  ILegacyServerResponse
} from "@/common/cleo-legacy-models";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";

export default defineComponent({
  name: "GridIntegrationTest",
  components: { LoadingSpinner },
  props: {
    gridDefinition: {
      type: Object as PropType<IGridDefinition>,
      required: true
    },
    adapterCalls: {
      type: Array as PropType<ICleoCallSummary[]>,
      required: true
    }
  },
  setup(props: {
    gridDefinition: IGridDefinition;
    adapterCalls: ICleoCallSummary[];
  }) {
    const state = reactive({
      showModal: true,
      legacy: {
        data: [],
        isLoading: false
      },
      results: {
        queueSameLength: false,
        compareResults: {}
      }
    });

    const integrationService = gridNamedIntegrationService();
    startTest();

    function startTest() {
      state.showModal = true;
      getLegacyData().finally(() => {
        console.log("startTest");
        const oldCalls = state.legacy.data as ILegacyCleoCallSummary[];
        const compareResults = integrationService.compareQueues(
          oldCalls,
          props.adapterCalls
        );

        state.results.queueSameLength =
          oldCalls.length === props.adapterCalls.length;
        state.results.compareResults = compareResults;
      });
    }

    function getLegacyData(): Promise<any> {
      //  The urls to get data from CLEO back end
      const mapUrl: Partial<Record<SocketGroup, string>> = {
        ["CatchAllCalls"]: "vn=UNKNOWN",
        ["111calls"]:
          "vn=NAV_111_SORT_PRIORITY_BREACH&PAGE_ON=1&PAGE_NUM=1&COV19_ALL=ALL",
        ["PcasCalls"]: "vn=PCAS",
        ["FcmsCalls"]: "vn=FCMS",
        ["NavBase"]: "vn=UNKNOWN"
      };

      if (!mapUrl[props.gridDefinition.identifier]) {
        return Promise.resolve();
      }
      state.legacy.isLoading = true;

      // const legacyJson = require("./cleo-legacy-data.json");
      // const legacyData = (legacyJson as any) as ILegacyServerResponse<
      //   ILegacyCleoCallSummary
      // >;
      // return Promise.resolve(legacyData)
      return https
        .get(
          window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
            "/" +
            window.MyGlobalSession.Global_DB_Paths.PATH_CALL +
            "/getViewData?openagent&" +
            mapUrl[props.gridDefinition.identifier],
          {
            responseType: "json"
          }
        )
        .then((resp: any) => {
          if (resp && resp.items) {
            state.legacy.data = resp.items;
          }
        })
        .finally(() => {
          state.legacy.isLoading = false;
        });
    }

    const callsWithDiffs = computed<ICompareCallResult[]>(() => {
      return integrationService.callsWithDiffs(state.results.compareResults);
    });

    function getCallDiffs(compareCallResult: ICompareCallResult) {
      if (compareCallResult.diffs === false) {
        return "";
      }
      return compareCallResult.diffs;
    }

    return {
      state,
      startTest,
      callsWithDiffs,
      getCallDiffs
    };
  }
});
</script>
