import { IApiReadCode } from "@/calls/details/complete/components/readcodes/readcodes-models";
import { simpleObjectClone, sortArray } from "@/common/common-utils";

// export interface ITreeObject {
//   nodeCount: number;
// }

// export interface ITreeObject extends unknown {
//   nodeCount: number;
// }

export interface IReadCodeTree {
  id: number;
  label: string;
  children: IReadCodeTree[];
  load_on_demand: boolean;
  sourceData: IApiReadCode | null;
  labelLength: number;
}

export interface IConvertReadCodeDataForTree {
  readCodesTree: IReadCodeTree[];
  nodeCount: number;
}

export function convertReadCodeDataForTree(
  readCodes: IApiReadCode[],
  nodeCount: number,
  isSearchInputBoxBeingDone = false
): IConvertReadCodeDataForTree {
  const result: IConvertReadCodeDataForTree = {
    readCodesTree: [],
    nodeCount: nodeCount
  };

  // let converted: IReadCodeTree[] = [];
  let readCode: IApiReadCode;
  let readCodeTree: IReadCodeTree;

  for (let i = 0; i < readCodes.length; i++) {
    readCode = readCodes[i];
    readCodeTree = convertReadCodeForTree(readCode, nodeCount);
    readCodeTree.id = result.nodeCount;
    result.nodeCount++;
    result.readCodesTree.push(readCodeTree);
  }

  if (isSearchInputBoxBeingDone) {
    result.readCodesTree = sortArray("labelLength", result.readCodesTree);
  } else {
    result.readCodesTree = sortArray("label", result.readCodesTree);
  }

  return result;
}

export function convertReadCodeForTree(
  readC: IApiReadCode,
  nodeCount: number
): IReadCodeTree {
  const rct: IReadCodeTree = {
    id: 0,
    label: "",
    children: [],
    load_on_demand: false,
    sourceData: null,
    labelLength: 0
  };
  rct.label = readC.ReadCodeDescription + " : " + readC.ReadCode;
  rct.sourceData = simpleObjectClone(readC);
  rct.labelLength = rct.label.length;

  //  rct.label = rct.label + " = " + rct.label.length;

  if (readC.HasChildren) {
    //	we're not using the built in dataUrl, we need a child else the arrow does not work correctly.
    //  rct.load_on_demand = true;
    //	...just give it some high id value.
    const newCodeCount: number = nodeCount + 100000;
    rct.children.push({
      id: newCodeCount,
      label: "Loading data...",
      children: [],
      load_on_demand: false,
      sourceData: null,
      labelLength: 0
    });
  }
  return rct;
}
