import { LegacyKeywordServerResponse } from "@/common/cleo-legacy-models";

export const mockOutcomes: LegacyKeywordServerResponse = {
  OTHER: {
    unid: "8572DBDE7CA157E580258C53003AF2F6",
    codeID3: "",
    description: "OTHER",
    KeywordService: "CAS",
    codeID2: "",
    codeID1:
      "Self-care advice – call back as necessary; Treatment given- call back as necessary; Severnside Home Visit; Severnside Face-to-Face; Simple medication request; Ambulance called/advised to call; BRI ED; BRI admission; BRI Hot clinic; Southmead ED; Southmead admission; Southmead hot clinic; Weston hospital ED; Weston hospital admission; Bristol Children's hospital ED; Eye hospital ED; St Peter’s Hospice admission; Patient deceased (expected); Patient deceased (unexpected); Referred to UTC/MIU; Referred to community pharmacy; Referred to community services e.g. DNs RRT; Referred to dental service; Referred to DVT service @ GP Care; Refer to social services; Referred to TIA clinic; Referred to early pregnancy clinic; Referred to WDPL; Mental health signposting/referred to VCS Services; Mental Health new referral to AWP assessment Services; Mental Health new referral to AWP treatment Services; Mental health referral/signposting to IAPT; Mental health referral to 999 response; Referred to other services; Patient already in MIU/UTC/ED/called 999; Admin only- referred to GP; Multiple services following F-ACE; Passed to F-ACE; Refer to social care; Severnside Follow Up",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  },
  NON_CLINICAL_REASON__OTHER: {
    unid: "4A417B364096210980258C7400761A10",
    codeID3: "",
    description: "NON_CLINICAL_REASON__OTHER",
    KeywordService: "BrisDoc;CAS;OOH",
    codeID2: "",
    codeID1: "Test case;Other",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  },
  WEEKDAY_PROFESSIONAL_LINE: {
    unid: "ACD953F2185D151780258C53003B330B",
    codeID3: "",
    description: "WEEKDAY_PROFESSIONAL_LINE",
    KeywordService: "CAS",
    codeID2: "",
    codeID1:
      "Passed to Out of Hours (Severnside); Advised to seek specialist advice; Clinical advice from Weekday IUC PL; Clinical advice from Weekday IUC PL with specialist; NBT SDEC; NBT medical admission; NBT directed to ED; NHS@Home; BRI SDEC – for medical assessment; BRI SDEC- admission likely; BRI medical admission; BRI directed to ED; WGH SDEC – for medical assessment; WGH medical admission; WGH directed to ED; Advised community pathway; Advised hot clinic/urgent clinic; Directed to WGH GEMS unit; Referred to frailty virtual ward round; Re-directed to non-medical specialty; Admission advised by hospital clinician; Passed to F-ACE; Administration; Referred to medical day unit",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  },
  NON_CLINICAL_REASON__CANCEL_CASE: {
    unid: "D84C87A50F1C200380258C7400719216",
    codeID3: "",
    description: "NON_CLINICAL_REASON__CANCEL_CASE",
    KeywordService: "BrisDoc;CAS;OOH",
    codeID2: "",
    codeID1:
      "Patient feeling better;Patient attended Emergency Department;Patient attended Urgent Treatment Centre;Patient dialled 999;Patient advised they will contact Own GP;Duplicate case;Patient is Out of area;Test case;Other",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  },
  NON_CLINICAL_REASON__NON_CLINICAL_INPUT_REQUIRED: {
    unid: "89EA57272F60F45E80258C740073553D",
    codeID3: "",
    description: "NON_CLINICAL_REASON__NON_CLINICAL_INPUT_REQUIRED",
    KeywordService: "BrisDoc;CAS;OOH",
    codeID2:
      '{\r\n    "Pharmacy First": [],\r\n    "Passed to Own GP - Service Pressure": [],\r\n    "Passed to Own GP - Service Closure": [],\r\n    "Escalation Navigation": [],\r\n    "Take List": ["Added to Take List", "Other"],\r\n    "ED Referral": ["Added to Children’s Careflow Connect", "Other"]\r\n}',
    codeID1:
      "Pharmacy First;Passed to Own GP - Service Pressure;Passed to Own GP - Service Closure;Escalation Navigation;Take List;ED Referral;Other",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  },
  FRAILTY: {
    unid: "BAF5EDD7D90DFBE580258C53003B32C8",
    codeID3: "",
    description: "FRAILTY",
    KeywordService: "CAS",
    codeID2: "",
    codeID1:
      "Completion to F-ACE team; UCR; Frailty@Home (Virtual ward); Social care referral; Other primary/community care; Multiple community based services; Hot clinic e.g. TIA, DVT; SDEC; Frailty SDEC; F-ACE input not possible/not required; Medical admission; Other specialty; Emergency department; Hospice admission; Death (expected); Death (unexpected); Patient already in ED/hospital/999;",
    keyType: "CALL_INFORMATIONAL_OUTCOMES"
  }
};
