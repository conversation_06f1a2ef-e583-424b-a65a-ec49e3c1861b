<template>
  <div id="cleo-context-menu2" class="cleo-context-menu" v-show="showMenu">
    <div
      class="cleo-context-menu--header"
      :class="
        getHasAnyPerms
          ? 'cleo-context-menu--header-got-perms'
          : 'cleo-context-menu--header-loading-perms'
      "
    >
      <div class="cleo-context-menu--item-icon">
        <LoadingSpinner
          class="cleo-context-menu--icon"
          :class="
            permsLoading
              ? 'cleo-context-menu--perms-icon-loading'
              : 'cleo-context-menu--perms-icon-loaded'
          "
        ></LoadingSpinner>
      </div>
      <div class="cleo-context-menu--item-action">
        Call: <span v-text="cleoCallSummary.CallNo"></span>
      </div>
    </div>

    <!--    <div v-show="getHasAnyPerms">-->
    <!--      <template v-for="action in getActions">-->
    <!--        <div-->
    <!--          :key="action.id"-->
    <!--          class="cleo-context-menu&#45;&#45;item"-->
    <!--          v-on:click.prevent="processAction(action.id)"-->
    <!--        >-->
    <!--          <div class="cleo-context-menu&#45;&#45;item-icon"></div>-->
    <!--          <div class="cleo-context-menu&#45;&#45;item-action">-->
    <!--            &lt;!&ndash;            <a href="#" v-text="action.title"></a>&ndash;&gt;-->
    <!--            <span v-text="action.title"></span>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </template>-->
    <!--    </div>-->

    <div v-show="getHasAnyPerms">
      <div
        v-if="callPermsShort.CAS_APPOINTMENT"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.CAS_APPOINTMENT)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <!--            <a href="#" v-text="action.title"></a>-->
          <span v-text="CLEO_CALL_ACTIONS.CAS_APPOINTMENT.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.MOVE_CASE"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.MOVE_CASE)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <!--            <a href="#" v-text="action.title"></a>-->
          <span v-text="CLEO_CALL_ACTIONS.MOVE_CASE.title"></span>
        </div>
      </div>

      <div
        v-if="
          callPermsShort.MOVE_CASE_OUT_URGENT_FOLLOW_UP &&
            cleoCallSummary.FOLLOW_UP_URGENT === '1'
        "
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.MOVE_CASE_OUT_URGENT_FOLLOW_UP)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <!--            <a href="#" v-text="action.title"></a>-->
          <span
            v-text="CLEO_CALL_ACTIONS.MOVE_CASE_OUT_URGENT_FOLLOW_UP.title"
          ></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.ASSIGN_DX"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.ASSIGN_DX)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.ASSIGN_DX.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.VALIDATE_CAS"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.VALIDATE_CAS)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.VALIDATE_CAS.title"></span>
        </div>
      </div>

      <!--      canOverSightValidation-->
      <div
        v-if="callPermsShort.OVERSIGHT_VALIDATION"
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.OVERSIGHT_VALIDATION)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.OVERSIGHT_VALIDATION.title"></span>
        </div>
      </div>

      <!--CAS Appointment Cancel-->
      <!--Arrived-->
      <!--Modify Arrival Time-->
      <!--Modify Completed Time-->
      <!--Print-->

      <div
        v-if="canAssignToClinicianNoRota"
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.ASSIGN_TO_CLINICIAN_NO_ROTA)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span
            v-text="CLEO_CALL_ACTIONS.ASSIGN_TO_CLINICIAN_NO_ROTA.title"
          ></span>
        </div>
      </div>

      <div
        v-if="canAssignToClinicianNoRotaClear"
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span
            v-text="CLEO_CALL_ACTIONS.ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR.title"
          ></span>
        </div>
      </div>

      <div
        v-if="
          callPermsShort.ASSIGN_TO_BASE_NO_ROTA &&
            !cleoCallSummary.CallClassification.Description !== 'Advice'
        "
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.ASSIGN_TO_BASE_NO_ROTA)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.ASSIGN_TO_BASE_NO_ROTA.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.ADDCOMMENTS"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.ADDCOMMENTS)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.ADDCOMMENTS.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.COMFORT_COURTESY_CALL"
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL)
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.COMFORT_COURTESY_CALL.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort['EDIT CLASSIFICATION']"
        class="cleo-context-menu--item"
        v-on:click.prevent="
          processAction(CLEO_CALL_ACTIONS['EDIT CLASSIFICATION'])
        "
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS['EDIT CLASSIFICATION'].title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.PRIORITY"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.PRIORITY)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.PRIORITY.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort['UNLOCK CALL']"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS['UNLOCK CALL'])"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS['UNLOCK CALL'].title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.PRINT"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.PRINT)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.PRINT.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.DISPATCH_VEHICLE"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.DISPATCH_VEHICLE)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.DISPATCH_VEHICLE.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.RETRIEVE_VEHICLE"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.RETRIEVE_VEHICLE.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.SMS_MANUAL"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.SMS_MANUAL)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.SMS_MANUAL.title"></span>
        </div>
      </div>

      <div
        v-if="callPermsShort.APPTS_EXTERNAL"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.APPTS_EXTERNAL)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span v-text="CLEO_CALL_ACTIONS.APPTS_EXTERNAL.title"></span>
        </div>
      </div>
      <div
        v-if="callPermsShort.REQUEST_GS_PHOTO"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.REQUEST_GS_PHOTO)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span>Request Photo</span>
        </div>
      </div>
      <div
        v-if="callPermsShort.REFRESH_GS_PHOTO"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.REFRESH_GS_PHOTO)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span>Refresh Photo</span>
        </div>
      </div>

      <div
        v-if="callPermsShort.PLS_REMOVE"
        class="cleo-context-menu--item"
        v-on:click.prevent="processAction(CLEO_CALL_ACTIONS.PLS_REMOVE)"
      >
        <div class="cleo-context-menu--item-icon"></div>
        <div class="cleo-context-menu--item-action">
          <span>PLS Remove</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { loggerInstance } from "@/common/Logger";
import { Prop, Watch } from "vue-property-decorator";
import { mapState } from "vuex";
import {
  CONTEXT_MENU_STORE_CONST,
  IContextMenuStoreState
} from "@/calls/grids/contextmenu/context-menu-store";
import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { getElementPosition } from "@/common/common-service";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import {
  CLEO_ACTION_NAME,
  CLEO_CALL_ACTIONS,
  ICleoAction
} from "@/common/cleo-common-models";

import { CleoContextMenuService } from "@/calls/grids/contextmenu/cleo-context-menu-service";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import { PermissionService } from "@/permissions/permission-service";
import * as CallPermissions from "@/calls/permissions/call-permissions";

const callSummaryService = new CallSummaryService();

@Component({
  name: "cleo-context-menu2",
  components: { LoadingSpinner },
  computed: {
    ...mapState<IContextMenuStoreState>(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME,
      {
        // showMenu: (state: IContextMenuStoreState) => state.showMenu,
        // permsForCall: (state: IContextMenuStoreState) => state.permsForCall,
        // clickedElementCoords: (state: IContextMenuStoreState) =>
        //   state.clickedElementCoords,
        // permsLoading: (state: IContextMenuStoreState) => state.permsLoading
        // cleoCallSummary: (state: IContextMenuStoreState) =>
        //   state.cleoCallSummary
      }
    ),
    ...mapState<IPermissionStoreState>(
      PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME,
      {
        permissionStoreState: (state: IPermissionStoreState) => state,
        permsShort: (state: IPermissionStoreState) => state.userAppPermsShort
      }
    ),
    CLEO_CALL_ACTIONS(): Record<CLEO_ACTION_NAME, ICleoAction> {
      return CLEO_CALL_ACTIONS;
    }
  }
})
export default class CleoContextMenu2 extends Vue {
  // public readonly showMenu!: boolean;
  // public readonly permsLoading!: boolean;
  // public readonly permsForCall!: Record<string, ICleoPermission>;

  @Prop({
    default: () => {
      return {
        x: 0,
        y: 0
      };
    }
  })
  public readonly clickedElementCoords!: {
    x: number;
    y: number;
  };
  // public readonly cleoCallSummary!: ICleoCallSummary;

  @Prop({
    required: true
  })
  public readonly containWithDomId!: string;

  @Prop({
    default: false
  })
  public readonly permsLoading!: boolean;

  @Prop({
    default: () => {
      return {};
    }
  })
  public readonly permsForCall!: Record<string, ICleoPermission>;

  @Prop({
    default: false
  })
  public readonly showMenu!: boolean;

  @Prop({
    default: () => {
      return callSummaryService.factoryCleoCallSummary();
    }
  })
  public readonly cleoCallSummary!: ICleoCallSummary;

  public cleoContextMenuService: CleoContextMenuService = new CleoContextMenuService();
  public permissionService: PermissionService = new PermissionService();
  public callSummaryController: CallSummaryController = new CallSummaryController();
  public actions: ICleoAction[] = [];
  public callPermsShort: Record<CleoPermissionName, ICleoPermission> = {};

  public created(): void {
    // this.actions = this.cleoContextMenuService.getActionsForRightClick();
    this.actions = Object.values(CLEO_CALL_ACTIONS);
  }

  public mounted(): void {
    document.addEventListener("click", this.attachClickHandler);
  }

  @Watch("clickedElementCoords")
  public onClickedElementCoordsChanged(newValue: { x: number; y: number }) {
    if (newValue) {
      this.rePositionMenu();
    }
  }

  @Watch("permsLoading")
  public onPermsLoadingChanged(newValue: boolean) {
    if (!newValue && this.getHasAnyPerms) {
      this.rePositionMenu();
    }
  }

  @Watch("permsForCall")
  public onPermsForCallChanged(newValue: Record<string, ICleoPermission>) {
    if (newValue) {
      this.callPermsShort = this.permissionService.simpleKeyPerms(newValue);
    }
  }

  public get getActions(): ICleoAction[] {
    return this.cleoContextMenuService.getActionsWithPermissionsApplied(
      this.actions,
      this.permsForCall
    );
  }

  /**
   *  If user right clicks a row at bottom of grid, need the menu to adjust so
   *  not off bottom of screen.
   */
  public rePositionMenu(): void {
    const gridTopPane = document.getElementById(this.containWithDomId);
    const menuElement = document.getElementById("cleo-context-menu2");
    if (gridTopPane && menuElement) {
      let menuContentHeight = 50;
      if (menuElement.offsetHeight === 0 || !this.getHasAnyPerms) {
        menuContentHeight = 50;
      } else {
        menuContentHeight = menuElement.offsetHeight;
      }
      let menuContentWidth = 50;
      if (menuElement.offsetWidth === 0 || !this.getHasAnyPerms) {
        menuContentWidth = 200;
      } else {
        menuContentWidth = menuElement.offsetWidth;
      }

      const menuRight = this.clickedElementCoords.x + menuContentWidth;
      const menuBottom = this.clickedElementCoords.y + menuContentHeight;

      const gridTopPaneCoords = getElementPosition(gridTopPane);
      const gridTopPaneWidth = gridTopPane.offsetWidth;
      const gridTopPaneHeight = gridTopPane.offsetHeight;
      const gridTopPaneRight = gridTopPaneCoords.left + gridTopPaneWidth;
      const gridTopPaneBottom = gridTopPaneCoords.top + gridTopPaneHeight;

      let newLeft = this.clickedElementCoords.x;
      let newTop = this.clickedElementCoords.y;
      if (menuBottom > gridTopPaneBottom) {
        const diffBottom = menuBottom - gridTopPaneBottom;
        newTop = this.clickedElementCoords.y - diffBottom;
      }
      if (menuRight > gridTopPaneRight) {
        const diffRight = menuRight - gridTopPaneRight;
        newLeft = this.clickedElementCoords.x - diffRight;
      }

      loggerInstance.log(
        "set menu to newLeft: " + newLeft + ", newTop: " + newTop
      );

      menuElement.style.left = newLeft + "px";
      menuElement.style.top = newTop + "px";
    }
  }

  /**
   * Handler so when user clicks outside of menu, it hides it.
   * @param evt
   */
  public attachClickHandler(evt: any): void {
    const flyoutElement = document.getElementById("cleo-context-menu2");
    let targetElement = evt.target; // clicked element

    do {
      if (targetElement == flyoutElement) {
        // This is a click inside. Do nothing, just return.
        return;
      }
      // Go up the DOM
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      targetElement = targetElement.parentNode;
    } while (targetElement);

    // This is a click outside.
    loggerInstance.log("CleoContextMenu2.click OUTSIDE");
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_SHOW_MENU,
      false
    );
    this.$emit("hideMenu");
  }

  public processAction(cleoAction: CLEO_ACTION_NAME | ICleoAction): void {
    this.$emit("hideMenu");
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_SHOW_MENU,
      false
    );

    const cleoActionName =
      typeof cleoAction === "string" ? cleoAction : cleoAction.id;

    this.callSummaryController.processAction(cleoActionName, [
      this.cleoCallSummary
    ]);
  }

  public get canOverSightValidation(): boolean {
    return CallPermissions.canOverSightValidation(
      this.cleoCallSummary,
      this.callPermsShort
    );
  }

  public get canAssignToClinicianNoRota(): boolean {
    return CallPermissions.canAssignToClinicianNoRota(
      this.cleoCallSummary,
      this.callPermsShort
    );
  }

  public get canAssignToClinicianNoRotaClear(): boolean {
    return CallPermissions.canAssignToClinicianNoRotaClear(
      this.cleoCallSummary,
      this.callPermsShort
    );
  }

  public get getHasAnyPerms(): boolean {
    return Object.keys(this.permsForCall).length > 0;
  }

  public beforeDestroy(): void {
    loggerInstance.log("CleoContextMenu2.beforeDestroy();");
    document.removeEventListener("click", this.attachClickHandler);
  }
}
</script>

<style scoped>
.cleo-context-menu {
  position: fixed;
  background-color: white;
  min-height: 50px;
  min-width: 200px;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3);
  border: 1px solid #bbbbbb;
}

.cleo-context-menu--header {
  height: 25px;
  vertical-align: middle;
  padding: 12px 0 0 0;
  border-bottom: 1px solid #a2a0a0;
  background-color: #dfe1e2;
  font-weight: 600;
}

.cleo-context-menu--header-loading-perms {
  background-color: #f3d5cd;
}

.cleo-context-menu--header-got-perms {
  background-color: #c5eac8;
}

.cleo-context-menu--item {
  height: 25px;
  vertical-align: middle;
  padding: 12px 0 0 0;
  border-bottom: 1px solid #f7f7f7;
  cursor: pointer;
}

.cleo-context-menu--item:hover {
  background-color: #eeeeee;
}

.cleo-context-menu--item-icon {
  display: inline-block;
  width: 25px;
  text-align: center;
}

.cleo-context-menu--item-action {
  display: inline-block;
}

.cleo-context-menu--item-action a {
  text-decoration: none;
}

.cleo-context-menu--item-action a:hover {
  color: #3a66dd;
  /*font-weight: 600;*/
}

.cleo-context-menu--icon {
  height: 16px;
  width: 16px;
}

.cleo-context-menu--perms-icon-loading {
  visibility: visible;
}

.cleo-context-menu--perms-icon-loaded {
  visibility: hidden;
}
</style>
