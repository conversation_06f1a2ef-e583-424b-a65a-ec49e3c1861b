import { useSesuiClient } from "@/sesui/useSesui";
import { reactive } from "@vue/composition-api";
import {
  ISesuiTelephoneConfig,
  ISesuiTelephoneState
} from "@/sesui/use-telephone-models";
import {
  broadcastChannelNameForController,
  IBroadcastAction,
  IBroadcastActionEndCall,
  IBroadcastActionMakeCall
} from "@/sesui/use-sesui-models";

export function useTelephoneCall(config: ISesuiTelephoneConfig) {
  const sesuiConnection = useSesuiClient({
    configId: 12729, //  TODO  get into config.
    operatorPhoneNumber: "",
    password: config.pw,
    url: config.url,
    user: config.user
  });

  const broadcastChannelForController = new BroadcastChannel(
    broadcastChannelNameForController
  );

  broadcastChannelForController.onmessage = function(event) {
    console.log("broadcastChannelForController onmessage", event);

    const broadcastAction: IBroadcastAction = event.data as IBroadcastAction;
    if (broadcastAction.action === "MAKE_CALL") {
      makeCall(
        (broadcastAction as IBroadcastActionMakeCall).payload.telephoneNumber
      );
    }

    if (broadcastAction.action === "END_CALL") {
      endCall(
        (broadcastAction as IBroadcastActionEndCall).payload.telephoneNumber
      );
    }
  };

  const state: ISesuiTelephoneState = reactive({
    showLogin: false,
    socketUrl: config && config.url ? config.url : "",
    user: "",
    password: "",
    operatorPhoneNumber: "",
    masterUser: config && config.user ? config.user : "",
    masterPassword: config && config.pw ? config.pw : "",
    session_start_result: null
  });

  // process.env.NODE_ENV === "development"
  //   ? process.env.VUE_APP_SESUI_USER_ID + ""
  //   : ""

  function init() {
    if (!config.enabled) {
      console.log("telephoneCall() NOT enabled");
      return;
    }

    sesuiIdCache().then(resp => {
      console.log("useTelephoneCall.sesuiIdCache: " + resp, resp);
      state.user = resp.sesui_id;
      state.showLogin = true;
    });
  }

  function auditEvent(
    action: string,
    data?: Record<string, string | string[]>[]
  ) {
    ApplicationControllerClient.createActionEventLog(
      "MakeCall",
      action,
      data ? data : undefined
    );
  }

  function isLoggedIn(): boolean {
    return sesuiConnection ? sesuiConnection.isLoggedIn() : false;
  }

  function getUrl() {
    //		return "wss://sesuissl.call-vision.com/api/v1/cc4web/socket";
    return state.socketUrl;
  }

  function makeCall(telNo: string) {
    console.log("useTelephone.makeCall() telNo: " + telNo);
    console.log(
      "useTelephone.makeCall() b state.user: " + state.user + ", state: ",
      JSON.stringify(state)
    );
    sesuiConnection!.makeCall(telNo, state.user);
    const data = [
      { DataType: "TEXT", DataKey: "TEL_NUMBER", DataValues: [telNo] }
    ];
    auditEvent("MakeCall", data);
  }

  function endCall(phoneNumber: string) {
    console.log("telephoneCall.endCall() to: " + phoneNumber);
    if (sesuiConnection) {
      sesuiConnection.terminateCall();
      const data = [
        { DataType: "TEXT", DataKey: "TEL_NUMBER", DataValues: [phoneNumber] }
      ];
      auditEvent("EndCall", data);
    }
  }

  function sesuiIdCache(
    sesuiIdToSet?: string
  ): Promise<{ user: string; sesui_id: string }> {
    //https://dr-sta-dom01.sehnp.nhs.uk/stage/calld.nsf/(sesui)?Openagent&action=GET_ID

    let url =
      window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
      "/" +
      window.MyGlobalSession.Global_DB_Paths.PATH_GLOBAL +
      "/(sesui)?Openagent&action=";
    if (sesuiIdToSet && sesuiIdToSet.length > 0) {
      url += "SET_ID&SESUI_ID=" + sesuiIdToSet;
    } else {
      url += "GET_ID";
    }

    if (process.env.NODE_ENV === "development") {
      console.log("useTelephoneCall.sesuiIdCache() DEV mode");

      return Promise.resolve({
        user: sesuiIdToSet && sesuiIdToSet.length > 0 ? "" : "Local Dev User",
        sesui_id: process.env.VUE_APP_SESUI_USER_ID as string
      });
    }

    console.log("useTelephoneCall.sesuiIdCache() PROD mode");

    return localCache.getUrlDataWithCache(url, false).then(function(resp) {
      return resp as { user: string; sesui_id: string };
    });
  }

  function cancelLogin() {
    state.showLogin = false;
  }

  function processLogin(userId: string) {
    state.user = userId;
    sesuiConnection!.initSocket(userId);

    if (sesuiConnection) {
      sesuiConnection.state.user = userId; // Used to be Number(userId)
    }
    console.log(
      "useTelephone.processLogin() a userId: " + userId + ", state: ",
      JSON.stringify(state)
    );
    state.showLogin = false;
    sesuiIdCache(userId);
    // startMasterConnection();
    const data = [
      { DataType: "TEXT", DataKey: "USER_ID", DataValues: [userId] }
    ];
    auditEvent("Login", data);
  }

  return {
    state,
    sesuiConnection,
    init,
    cancelLogin,
    processLogin,
    isLoggedIn,
    makeCall,
    endCall
  };
}
