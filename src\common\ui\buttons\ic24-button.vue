<template>
  <button
    type="submit"
    class="ic24-button"
    :class="'ic24-button--' + buttonStyle"
    v-on:click="doClick"
  >
    <slot name="button-text"><span v-text="title"></span></slot>
  </button>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "ic24-button",
  props: {
    title: {
      type: String,
      default: () => {
        return "OK";
      }
    },
    buttonStyle: {
      type: String as PropType<"primary" | "tertiary" | "destructive">,
      default: () => {
        return "primary";
      }
    }
  },
  setup(
    props: {
      title: string;
      buttonStyle: "primary" | "tertiary" | "destructive";
    },
    context: SetupContext
  ) {
    function doClick() {
      context.emit("click");
    }

    return { doClick };
  }
});
</script>
