import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
import {
  IGeocoder<PERSON><PERSON><PERSON>,
  IWhat3WordsMapCleoAddress
} from "@/what3words/what3words-cleo-models";

declare const google: any;

const what3wordsCleoService = new What3wordsCleoService();

describe("What3wordsCleoService", () => {
  it("isMobileNumber", () => {
    expect(what3wordsCleoService.isMobileNumber("SW19 2BP")).toBe(false);
    expect(what3wordsCleoService.isMobileNumber("00000000000")).toBe(false);
    expect(what3wordsCleoService.isMobileNumber("0791 262 888")).toBe(false);
    expect(what3wordsCleoService.isMobileNumber("07912x28888")).toBe(false);
    expect(what3wordsCleoService.isMobileNumber("77912628888")).toBe(false);

    expect(what3wordsCleoService.isMobileNumber("0791 262 8888")).toBe(true);
    expect(what3wordsCleoService.isMobileNumber("0791 2628888")).toBe(true);
    expect(what3wordsCleoService.isMobileNumber("07912628888")).toBe(true);
  });

  it("mapGoogleNearestAddressToCleoAddress 1", () => {
    const goog = ({
      address_components: [
        { long_name: "CGMX+6X", short_name: "CGMX+6X", types: ["plus_code"] },
        {
          long_name: "Rochester",
          short_name: "Rochester",
          types: ["postal_town"]
        },
        {
          long_name: "England",
          short_name: "England",
          types: ["administrative_area_level_1", "political"]
        },
        {
          long_name: "United Kingdom",
          short_name: "GB",
          types: ["country", "political"]
        }
      ],
      formatted_address: "CGMX+6X Rochester, UK",
      geometry: {
        bounds: {
          south: 51.433,
          west: 0.549875,
          north: 51.433125,
          east: 0.5499999999999999
        },
        location: { lat: 51.433093, lng: 0.5499959999999999 },
        location_type: "GEOMETRIC_CENTER",
        viewport: {
          south: 51.4317135197085,
          west: 0.5485885197084979,
          north: 51.43441148029149,
          east: 0.551286480291502
        }
      },
      place_id: "GhIJMpBnl2-3SUARPskdNpGZ4T8",
      plus_code: {
        compound_code: "CGMX+6X Rochester, UK",
        global_code: "9F32CGMX+6X"
      },
      types: ["plus_code"]
    } as any) as IGeocoderResult;

    expect(
      what3wordsCleoService.mapGoogleNearestAddressToCleoAddress(goog).town
    ).toBe("Rochester");
  });

  it("mapGoogleNearestAddressToCleoAddress 2", () => {
    const goog = ({
      address_components: [
        { long_name: "13", short_name: "13", types: ["street_number"] },
        {
          long_name: "Kingshill Drive",
          short_name: "Kingshill Dr",
          types: ["route"]
        },
        {
          long_name: "Hoo",
          short_name: "Hoo",
          types: ["locality", "political"]
        },
        {
          long_name: "Rochester",
          short_name: "Rochester",
          types: ["postal_town"]
        },
        {
          long_name: "Medway",
          short_name: "Medway",
          types: ["administrative_area_level_2", "political"]
        },
        {
          long_name: "England",
          short_name: "England",
          types: ["administrative_area_level_1", "political"]
        },
        {
          long_name: "United Kingdom",
          short_name: "GB",
          types: ["country", "political"]
        },
        { long_name: "ME3 9JP", short_name: "ME3 9JP", types: ["postal_code"] }
      ],
      formatted_address: "13 Kingshill Dr, Hoo, Rochester ME3 9JP, UK",
      geometry: {
        bounds: {
          south: 51.4259102,
          west: 0.5592583,
          north: 51.4259888,
          east: 0.5593836999999999
        },
        location: { lat: 51.42594649999999, lng: 0.5593136 },
        location_type: "ROOFTOP",
        viewport: {
          south: 51.42460051970851,
          west: 0.557972019708498,
          north: 51.4272984802915,
          east: 0.5606699802915021
        }
      },
      place_id: "ChIJhZHomf7N2EcRhuv_zP00L9Q",
      types: ["premise"]
    } as any) as IGeocoderResult;

    const res = what3wordsCleoService.mapGoogleNearestAddressToCleoAddress(
      goog
    );

    expect(res.town).toBe("Rochester");
    expect(res.postCode).toBe("ME3 9JP");
    expect(res.address1).toBe("13 Kingshill Dr");
    expect(res.GPS).toBe("51.42594649999999,0.5593136");
  });

  it("getQueryStringFromAddress", () => {
    const what3WordsMapCleoAddress: IWhat3WordsMapCleoAddress = {
      address1: "St Clements",
      address2: "King St",
      address3: "Colyton",
      address4: "",
      town: "Colyton",
      postCode: "EX24 6LF",
      GPS: ""
    };
    expect(
      what3wordsCleoService.getQueryStringFromAddress(what3WordsMapCleoAddress)
    ).toBe("St Clements, King St, Colyton, EX24 6LF");
  });
});
