import { GUID } from "@/common/common-models";

export type PathwaysEntryType = "" | "JUMPTO" | "EARLYEXIT";

export interface IPathwaysPayloadBase {
  entryType: PathwaysEntryType;
  skillset: 14;
}

export type PathwaysJumpToButtonType = "AD" | "ET" | "SS" | "HC";

export interface IPathwaysJumpToPayload extends IPathwaysPayloadBase {
  pwId: string; //  Jump to ID
  caseId: string; //  Pathways Case ID
  sg: null | number;
  entryType: "JUMPTO";
  quId: string; //  "PathwaysJumpToButtonType|PaccsSymptomModelTemplateId|PaccsConditionId"
  presentingCondition: string;
}

export interface IPathwaysEarlyExitPayload extends IPathwaysPayloadBase {
  entryType: "EARLYEXIT";
  caseId: GUID;
}

//  As defined by Pathways
export type PathwaysExitType =
  | "RESTART"
  | "RETURN"
  | "EARLYEXIT"
  | "END"
  | "CHANGE"
  | "POSTNOTES"
  | "FINISHED";

export interface IPathwaysReturnData {
  ExitType: PathwaysExitType;
  CaseId: GUID;
  ReportTextHtml: string;
  LastItkMessage?: {
    Status: "Success" | "Fail" | "N/A";
    Type: "Itk" | "None";
  };
  DOSServiceOds?: string;
  DOSServiceType?: string;
}
