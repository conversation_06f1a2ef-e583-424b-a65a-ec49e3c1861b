<template>
  <button
    v-on:click="handleClick"
    v-text="title"
    class="adapter-button"
    :class="buttonCss"
  ></button>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext
} from "@vue/composition-api";
import { appStore } from "@/store/store";
import { IPaccsStoreState, PACCS_STORE_CONST } from "@/paccs/paccs-store";
import {
  IPathwaysJumpToPayload,
  PathwaysJumpToButtonType
} from "@/paccs/pathways/pathways-models";
import { PaccsConditionId } from "@/paccs/paccs-condition-tab-body/paccs-condition-models";
import { PaccsSymptomModelTemplateId } from "@/paccs/paccs-symptom-search/paccs-symptom-models";
import { PaccsService } from "@/paccs/paccs-service";
import { loggerInstance } from "@/common/Logger";

export default defineComponent({
  name: "paccs-button-pw-jump",
  props: {
    title: {
      required: true,
      type: String
    },
    id: {
      required: true,
      type: String
    },
    symptomGroup: {
      type: Number || null || undefined
    },
    buttonType: {
      required: true,
      type: String as PropType<PathwaysJumpToButtonType>
    },
    paccsSymptomModelTemplateId: {
      default: () => {
        return "";
      },
      type: String as PropType<PaccsSymptomModelTemplateId>
    },
    paccsConditionId: {
      default: () => {
        return "";
      },
      type: String as PropType<PaccsConditionId>
    }
  },
  components: {},
  setup(
    props: {
      title: string;
      id: string;
      symptomGroup: number | null | undefined;
      buttonType: PathwaysJumpToButtonType;
      paccsSymptomModelTemplateId: PaccsSymptomModelTemplateId;
      paccsConditionId: PaccsConditionId;
    },
    context: SetupContext
  ) {
    const store = appStore;
    const paccsStoreState = computed<IPaccsStoreState>(() => {
      return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
    });
    const paccsService = new PaccsService();

    const buttonCss = computed<Record<string, boolean | "">>(() => {
      let buttonType: PathwaysJumpToButtonType = props.buttonType;
      const cssMap: Record<PathwaysJumpToButtonType, string> = {
        AD: "red",
        ET: "blue",
        SS: "orange",
        HC: "green"
      };
      const cssColour =
        "adapter-button--" + (cssMap[buttonType] ? cssMap[buttonType] : "grey");

      return {
        [cssColour]: true,
        "paccs-button-pw-jump--hide": !props.id || props.id.length === 0
      };
    });

    function handleClick() {
      const hackButtonMap: Record<string, PathwaysJumpToButtonType> = {
        A: "AD",
        E: "ET",
        S: "SS",
        H: "HC"
      };

      let buttonType: PathwaysJumpToButtonType = props.buttonType;
      if (buttonType.length === 0) {
        const firstLetterOfLabel = props.title.slice(0, 1);
        buttonType = hackButtonMap[firstLetterOfLabel];
        if (!buttonType) {
          loggerInstance.error("Button has no button type!");
          return;
        }
      }

      const messageHostSystem = paccsService.getOutputForHostSystem(
        paccsStoreState.value.triageRecords
      );

      //  TODO Ouch, direct to legacy DOM.  THis should be sent to PACCS store.
      //  "Presenting complaint"
      const chubPresenting = window.document.getElementById(
        "CHUB_Presenting"
      ) as HTMLTextAreaElement;
      if (chubPresenting) {
        chubPresenting.value =
          chubPresenting.value +
          (messageHostSystem.length > 0 ? " PACCS: " + messageHostSystem : "");
      }

      const pathwaysJumpToPayload: IPathwaysJumpToPayload = paccsService.factoryPathwaysJumpToPayload();
      pathwaysJumpToPayload.caseId =
        paccsStoreState.value.addCaseRecordResponse.caseId;
      pathwaysJumpToPayload.pwId = props.id;
      pathwaysJumpToPayload.sg = props.symptomGroup ? props.symptomGroup : null;
      pathwaysJumpToPayload.quId =
        buttonType +
        "|" +
        props.paccsSymptomModelTemplateId +
        "|" +
        props.paccsConditionId;
      pathwaysJumpToPayload.presentingCondition = window.CallControllerClient.createPaccsConsultDataForPathways();

      store.dispatch(
        PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
          "/" +
          PACCS_STORE_CONST.PACCS__ACTION_JUMP_TO_ID_PATHWAYS,
        pathwaysJumpToPayload
      );
    }

    return {
      handleClick,
      buttonCss
    };
  }
});
</script>

<style>
.paccs-button-pw-jump--hide {
  visibility: hidden;
}
</style>
