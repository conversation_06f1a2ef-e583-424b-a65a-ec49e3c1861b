import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import {
  doesPassFailedContactValidation,
  getNewestFailedContact,
  isFailedContactOlderThanInterval
} from "@/calls/details/complete/complete-service";

const callDetail: ICallDetail = factoryCallDetail();

describe("CAS Clini useCompleteController failure", () => {
  it("CAS Clini contact made flow failure: RETURN_TO_OPEN_CASE", () => {
    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    callDetail.failedContacts = [
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "Answerphone - Message left",
        time: "2024-06-28T01:49:00"
      },
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T03:29:46"
      },
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T02:29:46"
      }
    ];

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.processName).toBe("COMPLETE_CAS_CLINICAL");
    expect(controller.state.autoProgress).toBe(true);

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(14);

    expect(stepNames[0]).toBe("END_ASSESSMENT_CONFIRMATION");
    expect(stepNames[1]).toBe("HOW_WAS_CASE_MANAGED");
    expect(stepNames[2]).toBe("CONTACT_MADE");
    expect(stepNames[3]).toBe("PATIENT_REFERRED_TO");
    expect(stepNames[4]).toBe("OUTCOMES");
    expect(stepNames[5]).toBe("READ_CODES");
    expect(stepNames[6]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[7]).toBe("FAILED_CONTACT_SAFEGUARDING");
    expect(stepNames[8]).toBe("FAILED_CONTACT_RISK_ASSESSMENT");
    expect(stepNames[9]).toBe("PATIENT_RISK_ASSESSMENT");
    expect(stepNames[10]).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(stepNames[11]).toBe("TAXI");
    expect(stepNames[12]).toBe("VULNERABILITY");
    expect(stepNames[13]).toBe("UNKNOWN");

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  will fail as not valid and stay on same step
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    //  can move forward from HOW_WAS_CASE_MANAGED to CONTACT_MADE as already answered
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("CONTACT_MADE");

    //  but not any further as not answered CONTACT_MADE
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onContactMade({
      id: "CONTACT_FAILURE",
      description: "Contact Failure",
      value: "CONTACT_FAILURE"
    });

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("FAILED_CONTACT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "No Answer"
    });

    expect(
      controller.completeControllerInput.callDetail.failedContacts.length
    ).toBe(3);

    // {
    //   userName: "CN=Hannah Spicer/O=sehnp",
    //     type: "No Answer",
    //   time: "2024-06-28T03:29:46"
    // },
    const newestFailedContact = getNewestFailedContact(
      callDetail.failedContacts
    );
    expect(newestFailedContact.time).toBe("2024-06-28T03:29:46");

    controller.state.failedContactConfig.config.attemptsRequired = 4;
    expect(
      controller.completeControllerInput.callDetail.failedContacts.length
    ).toBe(3);
    const doesPass = doesPassFailedContactValidation(
      controller.state,
      callDetail.failedContacts
    );
    expect(doesPass).toBe(false);

    callDetail.failedContacts.push(
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T04:29:46"
      },
      {
        userName: "CN=Hannah Spicer/O=sehnp",
        type: "No Answer",
        time: "2024-06-28T05:29:46"
      }
    );

    expect(
      isFailedContactOlderThanInterval(
        {
          userName: "CN=Hannah Spicer/O=sehnp",
          type: "No Answer",
          time: "2024-06-28T03:29:46"
        },
        20,
        "2024-06-28T03:49:46"
      )
    ).toBe(false);

    expect(
      isFailedContactOlderThanInterval(
        {
          userName: "CN=Hannah Spicer/O=sehnp",
          type: "No Answer",
          time: "2024-06-28T03:29:46"
        },
        20,
        "2024-06-28T03:59:46"
      )
    ).toBe(true);

    expect(
      controller.completeControllerInput.callDetail.failedContacts.length
    ).toBe(5);

    controller.state.failedContactConfig.config.seedTime =
      "2024-06-28T04:01:46";

    const seedDateTime = new Date("2024-06-28T04:01:46");
    expect(seedDateTime.getFullYear()).toBe(2024);
    expect(seedDateTime.getMonth()).toBe(5);
    expect(seedDateTime.getDate()).toBe(28);
    expect(seedDateTime.getMinutes()).toBe(1);

    const doesPassWithFiveFailed = doesPassFailedContactValidation(
      controller.state,
      callDetail.failedContacts
    );
    expect(doesPassWithFiveFailed).toBe(true);

    expect(controller.state.currentStep).toBe("FAILED_CONTACT_RISK_ASSESSMENT");

    /*
    expect(controller.state.currentStep).toBe("PATIENT_RISK_ASSESSMENT");
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe("NOT_SELECTED");

    controller.onPatientRiskAssessmentSelected({
      risk: {
        id: "NO",
        description: "No",
        value: "NO"
      },
      actionTaken: ""
    });
    expect(controller.state.currentStep).toBe("PATIENT_RISK_ASSESSMENT");
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");

    controller.onInsufficientContactAttemptTypeSelected({
      id: "SAVE_AND_RETURN",
      description: "Save and Return to Queue",
      value: "SAVE_AND_RETURN"
    });

    expect(controller.state.finalAction).toBe("RETURN_TO_OPEN_CASE");
    */
  });
});
