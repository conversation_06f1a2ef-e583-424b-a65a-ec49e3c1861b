export const completeOutcomesMockOptions: Record<string, string> = {
  "999": "20~999",
  "A&E": "30~A&E",
  "Admission Requested-Bexhill": "50~Admission Requested-Bexhill",
  "Admission Requested-Conquest": "50~Admission Requested-Conquest",
  "Admission Requested-Crowborough": "50~Admission Requested-Crowborough",
  "Admission Requested-Darent Valley": "50~Admission Requested-Darent Valley",
  "Admission Requested-EDGH": "50~Admission Requested-EDGH",
  "Admission Requested-Faversham": "50~Admission Requested-Faversham",
  "Admission Requested-Folkestone": "50~Admission Requested-Folkestone",
  "Admission Requested-K&C": "50~Admission Requested-K&C",
  "Admission Requested-Maidstone": "50~Admission Requested-Maidstone",
  "Admission Requested-Minster": "50~Admission Requested-Minster",
  "Admission Requested-Other": "50~Admission Requested-Other",
  "Admission Requested-QEQM": "50~Admission Requested-QEQM",
  "Admission Requested-RSCH": "50~Admission Requested-RSCH",
  "Admission Requested-Rye": "50~Admission Requested-Rye",
  "Admission Requested-Sevenoaks": "50~Admission Requested-Sevenoaks",
  "Admission Requested-Sittingbourne": "50~Admission Requested-Sittingbourne",
  "Admission Requested-Tonbridge": "50~Admission Requested-Tonbridge",
  "Admission Requested-Uckfield": "50~Admission Requested-Uckfield",
  "Admission Requested-WHH": "50~Admission Requested-WHH",
  "Call Not Triaged-Caller Given Health Information":
    "15~Call Not Triaged-Caller Given Health Information",
  "Call Not Triaged-Caller Referred, No Triage":
    "15~Call Not Triaged-Caller Referred, No Triage",
  "Call Not Triaged-Caller Terminated Call":
    "15~Call Not Triaged-Caller Terminated Call",
  "Call Not Triaged-Other": "15~Call Not Triaged-Other",
  "Carer will contact Surgery": "110~Carer will contact Surgery",
  "Contact GP / OOH If Needed": "010~Contact GP / OOH If Needed",
  "Deceased-Expected": "60~Deceased-Expected",
  "Deceased-Unexpected": "60~Deceased-Unexpected",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient Asleep":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient Asleep",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient or Caller dialled 999":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient or Caller dialled 999",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient Uncontactable":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient Uncontactable",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient went to A&E":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Patient went to A&E",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Reason Unknown":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Reason Unknown",
  "Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Symptoms Improved":
    "70~Did Not Arrive/Did Not Wait/No Answer/Patient Cancelled Call-Symptoms Improved",
  "District Nurse-Daytime Team": "80~District Nurse-Daytime Team",
  "District Nurse-OOHs Team": "80~District Nurse-OOHs Team",
  Other: "90~Other",
  "Own GP Follow Up-Please assess for Visit":
    "100~Own GP Follow Up-Please assess for Visit",
  "Own GP Follow Up-Please ring Patient":
    "100~Own GP Follow Up-Please ring Patient",
  "Patient Refuses Treatment-999": "40~Patient Refuses Treatment-999",
  "Patient Refuses Treatment-A&E": "40~Patient Refuses Treatment-A&E",
  "Patient Refuses Treatment-Other": "40~Patient Refuses Treatment-Other",
  "Referral-Dentist/Dentaline": "120~Referral-Dentist/Dentaline",
  "Referral-Hermes": "120~Referral-Hermes",
  "Referral-Hospice": "120~Referral-Hospice",
  "Referral-Other": "120~Referral-Other",
  "Referral-Wellbeing Referral": "120~Referral-Wellbeing Referral",
  "Referred to other provider": "~Referred to other provider"
};
