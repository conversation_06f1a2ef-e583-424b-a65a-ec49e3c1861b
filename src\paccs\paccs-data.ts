import { CLEO_CONFIG } from "@/common/config/config-";
import https from "@/common/https";
import {
  IPathwaysDataset,
  IPathwaysDatasetContinue,
  ITriageRecord,
  ITriageRecordResponse
} from "@/paccs/paccs-models";
import { GUID } from "@/common/common-models";
import { PathwaysJumpToButtonType } from "@/paccs/pathways/pathways-models";

export interface IPaccsJump {
  label: string;
  orderNo: number;
  pathwayJump: string;
  buttonType: PathwaysJumpToButtonType;
}

export interface IAddCaseRecordResponse {
  caseId: GUID;
  jumps: IPaccsJump[];
}

export class PaccsData {
  private endPoint = CLEO_CONFIG.PACCS_URL ? CLEO_CONFIG.PACCS_URL : "";

  public addCaseRecord(
    pathwaysDataset: IPathwaysDataset
  ): Promise<IAddCaseRecordResponse> {
    return https
      .post(this.endPoint + "/api/case/addcaserecord", pathwaysDataset)
      .then(resp => {
        return typeof resp === "string" ? JSON.parse(resp) : resp;
      });
  }

  public restartCaseRecord(
    pathwaysDatasetContinue: IPathwaysDatasetContinue
  ): Promise<IAddCaseRecordResponse> {
    return https
      .post(
        this.endPoint + "/api/case/continuecaserecord",
        pathwaysDatasetContinue
      )
      .then(resp => {
        return typeof resp === "string" ? JSON.parse(resp) : resp;
      });
  }

  public submitTriageRecord(
    triageRecord: ITriageRecord
  ): Promise<ITriageRecordResponse> {
    return https
      .post(this.endPoint + "/api/triage/addtriagerecord", triageRecord)
      .then(resp => {
        return typeof resp === "string" ? JSON.parse(resp) : resp;
      });
  }

  public restartTriage(caseId: GUID): Promise<ITriageRecordResponse> {
    return https.post(this.endPoint + "/api/triage/restarttriage", {
      caseId
    });
  }

  public restartPathwaysQuestions(caseId: GUID): Promise<unknown> {
    return https.post(this.endPoint + "/api/triage/changetriage", {
      caseId
    });
  }

  public postNotes(caseId: GUID, notes: string): Promise<unknown> {
    return https.post(this.endPoint + "/api/case/addcasenote", {
      caseId,
      presentingCondition: notes
    });
  }
}
