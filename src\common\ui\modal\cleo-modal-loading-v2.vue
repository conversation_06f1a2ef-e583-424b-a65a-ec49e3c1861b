<template>
  <CleoModalV2 :title="title" header-type="info">
    <div slot="content">
      <div style="width: 200px;height: 100px">{{ simpleContent }}</div>
    </div>
    <div slot="buttons"></div>
  </CleoModalV2>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";
import CleoModalV2 from "@/common/ui/modal/cleo-model-v2.vue";

export default defineComponent({
  name: "cleo-modal-loading-v2",
  components: { CleoModalV2 },
  props: {
    title: {
      type: String,
      default: () => {
        return "Loading";
      }
    },
    simpleContent: {
      type: String,
      default: () => {
        return "Please wait...";
      }
    }
  }
});
</script>
