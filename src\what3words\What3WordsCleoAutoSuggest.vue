<template>
  <div>
    <div class="what-3-words-cleo-auto-suggest--input-wrapper">
      <div
        class="what-3-words-cleo-auto-suggest--input-div cleo-force-inline-block"
      >
        <input
          class="what-3-words-cleo-auto-suggest--input"
          :class="
            isWordLongEnough
              ? ''
              : 'what-3-words-cleo-auto-suggest--input-invalid'
          "
          v-model="autoSuggestInternal"
          v-on:keyup="debounceSearch"
          placeholder="Search what3words..."
        />
      </div>

      <div
        class="what-3-words-cleo-auto-suggest--input-clear-suggestions cleo-force-inline-block"
      >
        <a href="#" v-on:click.prevent="clearUi">X</a>
      </div>
    </div>
    <div v-if="showResults" class="what-3-words-cleo-auto-suggest--suggestions">
      <div
        v-for="suggestion in what3WordsSuggestions"
        :key="suggestion.rank"
        class="what-3-words-cleo-auto-suggest--suggestion-content"
        v-on:click="suggestionSelected(suggestion)"
      >
        <div>
          <span class="what-3-words-cleo-auto-suggest--suggestion-logo"
            >///</span
          >
          <span
            class="what-3-words-cleo-auto-suggest--location-context"
            v-text="suggestion.words"
          ></span>
        </div>
        <div class="what-3-words-cleo-auto-suggest--nearest-place">
          <span class="what-3-words-cleo-auto-suggest--near">near</span
          ><span v-text="suggestion.nearestPlace"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { What3WordsSuggestion } from "../typings/unknown-lib";
import { debounce } from "@/common/debounce";
import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
export default defineComponent({
  name: "what-3-words-cleo-auto-suggest",
  props: {
    autoSuggest: {
      type: String,
      default: () => {
        return "";
      }
    },
    what3WordsSuggestions: {
      type: Array as PropType<What3WordsSuggestion[]>,
      default: () => {
        return [];
      }
    }
  },
  setup(
    props: {
      autoSuggest: string;
      what3WordsSuggestions: What3WordsSuggestion[];
    },
    context: SetupContext
  ) {
    const autoSuggestInternal = ref(props.autoSuggest);
    const showResults = ref(false);
    const what3wordsCleoService = new What3wordsCleoService();

    const debounceSearch = debounce(() => {
      findSuggestions();
    }, 250);

    watch(
      () => props.autoSuggest,
      (newValue: string) => {
        if (autoSuggestInternal.value !== newValue) {
          autoSuggestInternal.value = newValue;
        }
      }
    );

    function suggestionSelected(what3WordsSuggestion: What3WordsSuggestion) {
      showResults.value = false;
      context.emit("suggestionSelected", what3WordsSuggestion);
    }

    function findSuggestions() {
      showResults.value = true;
      context.emit("findSuggestions", autoSuggestInternal.value);
    }

    const isWordLongEnough = computed<boolean>(() => {
      return what3wordsCleoService.isWordLongEnoughForSearch(
        autoSuggestInternal.value
      );
    });

    function clearUi() {
      showResults.value = false;
      context.emit("clearSuggestions");
    }

    return {
      autoSuggestInternal,
      showResults,
      debounceSearch,
      suggestionSelected,
      isWordLongEnough,
      clearUi
    };
  }
});
</script>

<style>
.what-3-words-cleo-auto-suggest--input-invalid {
  background-color: #f3b5b5;
}

.what-3-words-cleo-auto-suggest--input-wrapper {
  width: 100%;
  /*border: 1px solid black;*/
}

.what-3-words-cleo-auto-suggest--input-div {
  width: 98%;
}

.what-3-words-cleo-auto-suggest--input {
  width: 100%;
  line-height: 2;
  font-weight: 600;
}

.what-3-words-cleo-auto-suggest--input-clear-suggestions {
  width: 0%;
  /*padding-left: 5px;*/
  position: relative;
  left: -20px;
}

.what-3-words-cleo-auto-suggest--drop {
}

.what-3-words-cleo-auto-suggest--suggestions {
  border-left: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}

.what-3-words-cleo-auto-suggest--suggestion-content {
  font-size: 17px;
  font-family: Source Sans Pro, sans-serif !important;
  height: 68px;
  padding-left: 16px;
  padding-right: 16px;
  border-top: 1px solid #e1e1e1;
}

.what-3-words-cleo-auto-suggest--suggestion-content:hover {
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}

.what-3-words-cleo-auto-suggest--suggestion-logo {
  color: #e11f26;
  font-weight: 600;
}

.what-3-words-cleo-auto-suggest--location-context {
  /*transform: translateY(-4%);*/
  font-weight: 600;
  line-height: 2;
}

.what-3-words-cleo-auto-suggest--nearest-place {
  font-size: 13px;
  color: #525252;
  font-weight: 400;
  line-height: 17px;
}

.what-3-words-cleo-auto-suggest--near {
  margin-right: 5px;
}
</style>
