<template>
  <div style="height: 95%">
    <div class="grid--header">
      <GridRouteToolbar
        :view-title="gridController1.config.viewTitle"
        :call-count="gridController1.state.gridData.length"
        :call-filter-count="gridController1.state.callFilterCount"
        :is-loading="gridController1.state.isLoading"
        :socket-loading-trigger="socketLoadingTrigger"
        :socket-status="cleoSocketWrapperController.status"
        :last-reloaded="gridController1.state.lastUpdatedHumanReadTime"
        v-on:getData="joinSocketGroup"
        v-on:showSocketMessages="showSocketMessages"
      >
        <div slot="toolbar-content">
          <ToolbarWidgetNewCall
            style="display: inline"
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
          />

          <ToolbarWidgetActions
            style="display: inline"
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
          />

          <ButtonSupport
            v-if="
              !permissionStoreState.userAppPermsShort[
                permissionNames.NO_SUPPORT
              ]
            "
            :user-app-perms-short="permissionStoreState.userAppPermsShort"
            :permission-names="permissionNames"
          />

          <div
            style="display: inline"
            :class="
              gridController1.state.filters.length > 0
                ? 'grid-route-toolbar--filters-warn'
                : ''
            "
          >
            <input
              placeholder="Enter search term..."
              class="grid-route-toolbar--filters-sep"
              :class="
                quickFilterText1.length > 0
                  ? 'grid-route-toolbar--filters-warn-value'
                  : ''
              "
              v-model="quickFilterText1"
              v-on:keyup="quickFilterTextChanged"
            />
            <!--            <CleoContextMenu></CleoContextMenu>-->
            <GridFilterCov19
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              v-on:change="onCov19Filter"
            />
            <GridFilterClassn
              v-show="
                permissionStoreState.userAppPermsShort
                  .GRID_FILTER_CLASSIFICATION
              "
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              :classifications="getClassificationFilterOptions"
              v-on:change="onClassificationFilter"
            />

            <GridIntegrationTest
              v-if="
                permissionStoreState.userAppPermsShort[
                  permissionNames.SOCKET__INT_TEST
                ]
              "
              class="cleo-force-inline-block grid-route-toolbar--filters-sep"
              :grid-definition="gridDefinition"
              :adapter-calls="gridController1.state.gridData"
            />

            <!--            <button v-on:click="showStackMessages = true">Stack</button>-->
          </div>
        </div>
      </GridRouteToolbar>
    </div>

    <GridDefault
      oncontextmenu="javascript:return false;"
      :grid-data="gridController1.state.gridData"
      :new-grid-data="gridController1.state.newCalls"
      :updated-grid-data="gridController1.state.updatedCalls"
      :removed-grid-data="gridController1.state.removedCalls"
      :recalc-breach-trigger="gridController1.state.simpleTriggerBreach"
      :show-multi-select="gridController1.config.allowMultiSelect"
      :select-all-trigger="selectAllTrigger"
      :quick-filter-trigger="quickFilterTrigger1"
      :external-filters="gridController1.state.filters"
      v-on:onRowClicked="onRowClicked"
      v-on:onRowDoubleClicked="onRowDoubleClicked"
      v-on:onCellContextMenu="onCellContextMenu"
      v-on:onCurrentSelectedDocs="onCurrentSelectedDocs"
      v-on:onFilterChangedRowCount="onFilterChangedRowCount"
    />
    <CleoModal
      v-if="showConfirmOpeningCall"
      header-message="Open Call"
      :body-message="getOpenCallMessage"
      v-on:closeSecondary="showConfirmOpeningCall = false"
      v-on:closePrimary="proceedToOpeningLockedCall"
    />
    <!--    <CleoContextMenu2 contain-with-dom-id="grid&#45;&#45;top-pane" x="xtz" />-->
    <CleoModal
      v-if="showStackMessages"
      :header-message="
        'Last ' +
          gridController1.config.stackCollectionMessagesCount +
          ' Socket Messages'
      "
      v-on:closePrimary="showStackMessages = false"
    >
      <div slot="button-close-secondary"></div>
      <GridControllerMessages
        slot="body"
        :grid-controller-stack-messages="gridController1.state.stackMessages"
      />
    </CleoModal>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { GridCallsData } from "./grid-calls-data";
import {
  ICleoCallSummary,
  ISimpleTrigger
} from "../summary/call-summarry-models";
import { CallSummaryService } from "../summary/call-summary-service";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import GridDefault from "@/calls/grids/grid-default.vue";

import { CommonService } from "@/common/common-service";
import { loggerInstance } from "@/common/Logger";

import { mapState } from "vuex";
import SocketLoadingSpinner from "@/socket/socket-loading-spinner.vue";
import CallSummaryDisplay from "@/calls/summary/call-summary-display.vue";
import { GridController } from "@/calls/grids/grid-controller/grid-controller";
import { CallSummaryController } from "@/calls/summary/call-summary-controller";
import CallSummaryToolbar from "@/calls/summary/call-summary-toolbar/call-summary-toolbar.vue";
import {
  ICleoPermission,
  PERMISSION_NAMES
} from "@/permissions/permission-models";
import { PermissionData } from "@/permissions/permission-data";

import Splitpanes from "../../../node_modules/splitpanes/src/components/splitpanes/splitpanes.vue";
import Pane from "../../../node_modules/splitpanes/src/components/splitpanes/pane.vue";
import GridRouteToolbar from "@/calls/grids/grid-route-toolbar.vue";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import ToolbarWidgetNewCall from "@/calls/grids/toolbar-widgets/toolbar-widget-newcall.vue";
import GridFilterCov19 from "@/calls/grids/grid-filter/grid-filter-cov19.vue";
import {
  factoryGridFilters,
  IGridFilter
} from "@/calls/grids/grid-filter/grid-filter-models";
import GridFilterClassn from "@/calls/grids/grid-filter/grid-filter-classn.vue";
import {
  CALL_CLASSIFICATION,
  IAdapterPagedResponse
} from "@/common/common-models";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import GridFilterQuick from "@/calls/grids/grid-filter/grid-filter-quick.vue";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";

import {
  CleoSocketWrapperController,
  SOCKET_ACTIONS
} from "@/socket/socket-controller";
import ToolbarWidgetActions from "@/calls/grids/toolbar-widgets/toolbar-widget-actions.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState
} from "@/common/config/config-store";
import CleoContextMenu2 from "@/calls/grids/contextmenu/cleo-context-menu2.vue";
import { CONTEXT_MENU_STORE_CONST } from "@/calls/grids/contextmenu/context-menu-store";
import LoginIdentityForm from "@/login/identity/login-identity-form.vue";
import { ILoginStoreState, LOGIN_STORE_STORE_CONST } from "@/login/login-store";
import ButtonSupport from "@/calls/grids/toolbar-widgets/buttons/button-support.vue";
import { Prop, Watch } from "vue-property-decorator";
import { IGridDefinition, SocketGroup } from "@/calls/grids/grid-models";
import GridControllerMessages from "@/calls/grids/grid-controller/grid-controller-messages.vue";

@Component({
  name: "grid-standard",
  components: {
    GridIntegrationTest: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "grid-integration-test" */
        "@/calls/grids/grids-named/integration-test/GridIntegrationTest.vue"
      );
    },
    GridControllerMessages,
    ButtonSupport,
    LoginIdentityForm,
    CleoContextMenu2,
    ToolbarWidgetActions,
    GridFilterQuick,
    CleoModal,
    GridFilterClassn,
    GridFilterCov19,
    ToolbarWidgetNewCall,
    GridRouteToolbar,
    CallSummaryToolbar,
    CallSummaryDisplay,
    SocketLoadingSpinner,
    GridDefault,
    LoadingSpinner,
    Splitpanes,
    Pane
  },
  computed: {
    ...mapState<IUserStoreState>(USER_STORE_CONST.USER__CONST_MODULE_NAME, {
      userStoreState: (state: IUserStoreState) => state
    }),
    ...mapState<IPermissionStoreState>(
      PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME,
      {
        permissionStoreState: (state: IPermissionStoreState) => state
      }
    ),
    ...mapState<IConfigStoreState>(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME,
      {
        configStoreState: (state: IConfigStoreState) => state
      }
    ),
    ...mapState<ILoginStoreState>(
      LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME,
      {
        loginStoreState: (state: ILoginStoreState) => state
      }
    )
  }
})
export default class GridStandard extends Vue {
  public readonly permissionStoreState!: IPermissionStoreState;
  public readonly userStoreState!: IUserStoreState;
  public readonly configStoreState!: IConfigStoreState;
  public readonly loginStoreState!: ILoginStoreState;

  @Prop({
    required: true
  })
  public gridDefinition!: IGridDefinition;

  public cleoCommonService: CleoCommonService = new CleoCommonService();
  public commonService: CommonService = new CommonService();
  public callSummaryController: CallSummaryController = new CallSummaryController();

  public gridCallsData: GridCallsData = new GridCallsData();
  public callSummaryService: CallSummaryService = new CallSummaryService();

  public cleoCallSummarySelected: ICleoCallSummary = this.callSummaryService.factoryCleoCallSummary();
  public cleoCallSummarySelectedPerms: Record<string, ICleoPermission> = {};
  public isLoadingPerms = false;

  public permissionData: PermissionData = new PermissionData();
  public permissionNames = PERMISSION_NAMES;

  public isLoading = false;

  public socketLoading = false;
  public socketLoadingTrigger: ISimpleTrigger<unknown> = { timeIso: "" };
  public breachRecalcTimer: any;
  public selectAllTrigger: ISimpleTrigger<boolean> = {
    timeIso: "",
    data: false
  };
  public selectAll1 = false;
  public quickFilterText1 = "";
  public quickFilterTrigger1: ISimpleTrigger<string> = {
    timeIso: "",
    data: ""
  };

  public filterQuick = "";
  public filterValueClassification = "";
  public filterValueCov19 = "";

  public showConfirmOpeningCall = false;

  public cleoSocketWrapperController: CleoSocketWrapperController | null = null;
  public gridController1 = new GridController();
  public socketGroupNameSelected: SocketGroup = "111calls";
  public showStackMessages = false;

  public PREFIX = Math.random()
    .toString(36)
    .substring(2);

  public created(): void {
    loggerInstance.log("GridStandard.created()...a...");

    this.gridController1.config = {
      viewTitle: this.gridDefinition.title,
      divTimer: this.PREFIX,
      adapterQueueParams: {
        PageNumber: 1,
        RecordsPerPage: 1_000,
        QueryName: "",
        Criteria: {}
      },
      /**
       * breachRefreshRateMs and longPollingRefreshRateMs should actually match, else
       * odd ui as breach will read "5m" but the long polling hasn't happened, so in case
       * off Dx333, the row color should also have changed colour.
       */
      breachRefreshRateMs: 15_000,
      longPollingRefreshRateMs: 3_000_000,
      allowMultiSelect: false,
      stackCollectionMessagesCount: 100
    };

    const headers = {
      Authorization:
        "Bearer " + this.loginStoreState.tokenResponse.access_token,
      HeaderAuthorization:
        "Bearer " + this.loginStoreState.tokenResponse.access_token
    };

    this.cleoSocketWrapperController = new CleoSocketWrapperController(headers);

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_NEW,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_NEW +
            ", Grid: " +
            this.gridDefinition.identifier +
            ", Call: " +
            cleoCallSummary.CallNo,
          cleoCallSummary
        );
        this.handleNew(cleoCallSummary);
      }
    );

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_UPDATED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_UPDATED +
            ", Grid: " +
            this.gridDefinition.identifier +
            ", Call: " +
            cleoCallSummary.CallNo,
          cleoCallSummary
        );
        this.handleUpdated(cleoCallSummary);
      }
    );

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_LOCKED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_LOCKED +
            ", Grid: " +
            this.gridDefinition.identifier +
            ", Call: " +
            cleoCallSummary.CallNo,
          cleoCallSummary
        );
        this.handleUpdated(cleoCallSummary);
      }
    );

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_UNLOCKED,
      (cleoCallSummary: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_UNLOCKED +
            ", Grid: " +
            this.gridDefinition.identifier +
            ", Call: " +
            cleoCallSummary.CallNo,
          cleoCallSummary
        );
        this.handleUpdated(cleoCallSummary);
      }
    );

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_REMOVED,
      (removedCallNumber: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_REMOVED +
            ", Grid: " +
            this.gridDefinition.identifier,
          removedCallNumber
        );
        this.handleRemoved(removedCallNumber);
      }
    );

    this.cleoSocketWrapperController.socket.on(
      SOCKET_ACTIONS.ON_CASE_CLOSED,
      (removedCallNumber: ICleoCallSummary) => {
        loggerInstance.log(
          "GridStandard socket.on: " +
            SOCKET_ACTIONS.ON_CASE_CLOSED +
            ", Grid: " +
            this.gridDefinition.identifier,
          removedCallNumber
        );
        this.handleRemoved(removedCallNumber);
      }
    );

    this.cleoSocketWrapperController.socket.onreconnecting((response: any) => {
      loggerInstance.log(
        "GridStandard socket.on: " +
          "onreconnecting" +
          ", Grid: " +
          this.gridDefinition.identifier
      );
    });

    this.cleoSocketWrapperController.socket.onreconnected((response: any) => {
      loggerInstance.log(
        "GridStandard socket.on: " +
          "onreconnected" +
          ", Grid: " +
          this.gridDefinition.identifier
      );
    });

    this.cleoSocketWrapperController.socket.onclose((response: any) => {
      loggerInstance.log(
        "GridStandard socket.on: " +
          "onclose" +
          ", Grid: " +
          this.gridDefinition.identifier
      );
    });

    this.cleoSocketWrapperController.startSocket();
  }

  public mounted(): void {
    this.gridController1.init();
    this.configStoreState.splitPanes.panes;
  }

  public toggleSocket(): void {
    if (this.cleoSocketWrapperController?.isConnected) {
      this.cleoSocketWrapperController.stopSocket();
    } else {
      this.cleoSocketWrapperController?.startSocket(
        "registerForEpisodeOfCareQueue",
        this.gridDefinition.identifier
      );
    }
  }

  @Watch("cleoSocketWrapperController.status")
  public onSocketStatusChanged(newValue: string, oldValue: string): void {
    loggerInstance.log(
      "onSocketStatusChanged>>>>>> newValue: " +
        newValue +
        ", oldValue: " +
        oldValue
    );
    this.joinSocketGroup();
  }

  @Watch("gridController1.state.simpleTriggerLongPoll")
  public onSimpleTriggerLongPollChanged(
    newValue: ISimpleTrigger<unknown>,
    oldValue: ISimpleTrigger<unknown>
  ): void {
    if (newValue.timeIso !== oldValue.timeIso) {
      loggerInstance.log("gridController1.onSimpleTriggerLongPollChanged()");
      this.joinSocketGroup();
    }
  }

  public joinSocketGroup(): void {
    if (
      !this.cleoSocketWrapperController ||
      this.cleoSocketWrapperController.status !== "Connected"
    ) {
      return;
    }

    let params = "";
    const paramKeys = Object.keys(this.gridDefinition.params);
    if (paramKeys.length > 0) {
      params = paramKeys.reduce((accum, key) => {
        accum +=
          (accum.length > 0 ? "&" : "") +
          key +
          "=" +
          this.gridDefinition.params[key];
        return accum;
      }, "");
    }

    const groupName =
      this.gridDefinition.identifier + (params.length > 0 ? "?" + params : "");

    this.cleoSocketWrapperController.socket
      .invoke("RegisterForEpisodeOfCareQueue", groupName)
      .then((adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary>) => {
        loggerInstance.log(
          "onSocketStatusChanged>>>>>> invoke() invokeResponse",
          adapterPagedResponse
        );
        // this.gridController1.state.gridData = invokeResponse;
        this.gridController1.setData(adapterPagedResponse);
      })
      .catch((err: Error) => {
        console.error(err);
      });
  }

  public handleNew(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("GridStandard.handleNew()...");
    this.socketLoading = true;
    this.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    this.gridController1.newCalls([cleoCallSummary]);
  }

  public handleUpdated(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("GridStandard.handleUpdated()...");
    this.socketLoading = true;
    this.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    const updatedCalls = [cleoCallSummary];

    this.gridController1.updateCalls(updatedCalls);

    // TODO update summary display if required or remove if not in view any more.
    this.checkIfSummaryDisplayNeedUpdating(updatedCalls);
  }

  public handleRemoved(callNo: ICleoCallSummary): void {
    loggerInstance.log("GridStandard.handleRemoved()...");
    this.socketLoading = true;
    this.socketLoadingTrigger = { timeIso: new Date().toISOString() };

    this.gridController1.removeCalls([callNo]);

    // TODO update summary display if required or remove if not in view any more.
    //  this.checkIfSummaryDisplayNeedUpdating(updatedCalls);
  }

  public checkIfSummaryDisplayNeedUpdating(
    updatedCalls: ICleoCallSummary[]
  ): void {
    const cleoCallSummarySelected = this.cleoCallSummarySelected;

    const pred = (callSummary: ICleoCallSummary) => {
      return callSummary.CallNo === cleoCallSummarySelected.CallNo;
    };

    const callMatchUpdate: ICleoCallSummary | null = this.commonService.findFirst(
      pred,
      updatedCalls
    );
    if (callMatchUpdate) {
      this.cleoCallSummarySelected = this.commonService.simpleObjectClone(
        callMatchUpdate
      );
      this.$emit("onRowClicked", this.cleoCallSummarySelected);
    }
  }

  public onCellContextMenu(payload: {
    data: ICleoCallSummary;
    coords: { x: number; y: number };
  }): void {
    loggerInstance.log("GridStandard.onCellContextMenu();", payload.data);
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_SHOW_MENU,
      true
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_COORDS,
      payload.coords
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_CLEO_CALL_SUMMARY,
      payload.data
    );
    this.$store.commit(
      CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME +
        "/" +
        CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__MUTATIONS_PERMS,
      {}
    );

    this.onRowClicked(payload.data);
  }

  public onRowClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("GridStandard.onRowClicked();", cleoCallSummary);
    this.cleoCallSummarySelected = this.commonService.simpleObjectClone(
      cleoCallSummary
    );

    this.$emit("onRowClicked", cleoCallSummary);
  }

  public onRowDoubleClicked(cleoCallSummary: ICleoCallSummary): void {
    loggerInstance.log("GridStandard.onRowDoubleClicked();", cleoCallSummary);

    if (this.cleoCallSummarySelected.CallNo !== cleoCallSummary.CallNo) {
      this.onRowClicked(cleoCallSummary);
    }

    if (this.isCallLockedByAnotherUser()) {
      this.showConfirmOpeningCall = true;
      return;
    }
    this.proceedToOpeningCall(cleoCallSummary);
  }

  public isCallLockedByAnotherUser(): boolean {
    if (!this.cleoCallSummarySelected) {
      return false;
    }
    if (!this.userStoreState.user.userName) {
      return false;
    }

    return this.callSummaryService.isCallLockedByAnotherUser(
      this.cleoCallSummarySelected,
      this.userStoreState.user.userName
    );
  }

  public proceedToOpeningLockedCall(): void {
    this.showConfirmOpeningCall = false;
    this.proceedToOpeningCall(this.cleoCallSummarySelected);
  }

  public get getOpenCallMessage(): string {
    if (
      this.cleoCallSummarySelected &&
      this.cleoCallSummarySelected.CallNo > 0
    ) {
      return (
        "Call " +
        this.cleoCallSummarySelected.CallNo +
        (this.isCallLockedByAnotherUser()
          ? " is locked by: " +
            this.cleoCommonService.formatUserDominoName(
              this.cleoCallSummarySelected.IsLocked
            ) +
            ", continue to open?"
          : "")
      );
    }
    return "";
  }

  public proceedToOpeningCall(cleoCallSummary: ICleoCallSummary): void {
    this.callSummaryController.openCall(
      cleoCallSummary,
      this.userStoreState.user
    );
  }

  public onCurrentSelectedDocs(cleoCallSummaries: ICleoCallSummary[]): void {
    this.gridController1.state.selectedCalls = this.commonService.simpleObjectClone(
      cleoCallSummaries
    );
  }

  public selectAll(doSelectAll: boolean): void {
    this.selectAllTrigger = {
      timeIso: new Date().toISOString(),
      data: doSelectAll
    };
  }

  public onSelectAllChanged(): void {
    this.selectAll(this.selectAll1);
  }

  public quickFilterTextChanged(): void {
    this.gridController1.state.quickFilterText = {
      timeIso: new Date().toISOString(),
      data: this.quickFilterText1
    };
    this.quickFilterTrigger1 = {
      timeIso: new Date().toISOString(),
      data: this.quickFilterText1
    };
  }

  public get getClassificationFilterOptions(): CALL_CLASSIFICATION[] {
    return ["CH Advice", "Message"];
  }

  public onQuickFilter(filterValue: string): void {
    this.filterQuick = filterValue;
    this.setFilters();
  }

  public onClassificationFilter(filterValue: string): void {
    this.filterValueClassification = filterValue;
    this.setFilters();
  }

  public onCov19Filter(filterName: string): void {
    this.filterValueCov19 = filterName;
    this.setFilters();
  }

  public setFilters(): void {
    const filters: IGridFilter[] = [];

    if (this.filterQuick.length > 0) {
      const quickFilter = factoryGridFilters.QUICK;
      quickFilter.filterValue = this.filterQuick;
      filters.push(quickFilter);
    }

    if (this.filterValueClassification.length > 0) {
      const cleoFilter = factoryGridFilters.CLASSIFICATION;
      cleoFilter.filterValue = this.filterValueClassification;
      filters.push(cleoFilter);
    }

    if (this.filterValueCov19 === "COV19") {
      filters.push(factoryGridFilters.COV19);
    }
    if (this.filterValueCov19 === "NOT_COV19") {
      filters.push(factoryGridFilters.NOT_COV19);
    }

    this.gridController1.state.filters = filters;
  }

  public onFilterChangedRowCount(callFilterCount: number): void {
    this.gridController1.state.callFilterCount = callFilterCount;
  }

  public showSocketMessages(): void {
    this.showStackMessages = true;
  }

  public beforeDestroy(): void {
    loggerInstance.log("GridStandard.beforeDestroy();");
    this.gridController1.destroy();
    if (this.cleoSocketWrapperController) {
      this.cleoSocketWrapperController.stopSocket();
    }
  }

  public destroyed(): void {
    loggerInstance.log("GridStandard.destroyed();");
  }
}
</script>
