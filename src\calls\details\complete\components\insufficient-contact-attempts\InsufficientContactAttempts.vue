<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">Warning - please note:</div>

    <div class="ic24-vertical-spacer-large"></div>

    <div style="color: red;font-weight: bold">
      Due to insufficient call attempts this case can not be closed and will be
      returned to the queue.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <SimpleButtonSelecter
      class="ic24-flex-row ic24-justify-flex-space-between"
      :options="[
        insufficientMap.SAVE_AND_RETURN,
        insufficientMap.OVERRIDE_COMPLETE
      ]"
      :value="insufficientContactAttempts"
      @input="onSelected"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IStep, IUserResponseGeneric } from "../../complete-models";
import {
  insufficientContactAttemptMap,
  InsufficientContactAttemptType
} from "@/calls/details/complete/components/insufficient-contact-attempts/insufficient-contact-attempts";
import SimpleButtonSelecter from "@/calls/details/complete/SimpleButtonSelecter.vue";
import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "InsufficientContactAttempts",
  components: { CompleteStepHeader, SimpleButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"INSUFFICIENT_CONTACT_ATTEMPTS">>,
      required: true
    },
    insufficientContactAttempts: {
      type: Object as PropType<
        IUserResponseGeneric<
          InsufficientContactAttemptType,
          InsufficientContactAttemptType
        >
      >,
      required: true
    }
  },
  setup(
    props: {
      step: IStep<"INSUFFICIENT_CONTACT_ATTEMPTS">;
      insufficientContactAttempts: IUserResponseGeneric<
        InsufficientContactAttemptType,
        InsufficientContactAttemptType
      >;
    },
    context: SetupContext
  ) {
    const insufficientMap = insufficientContactAttemptMap;

    function onSelected(
      option: IUserResponseGeneric<
        InsufficientContactAttemptType,
        InsufficientContactAttemptType
      >
    ) {
      console.log("InsufficientContactAttempts.onSelected: ", option);
      context.emit("input", simpleObjectClone(option));

      if (option.id === "SAVE_AND_RETURN") {
        context.emit("saveAndReturnToQueue", option);
      }
      if (option.id === "OVERRIDE_COMPLETE") {
        context.emit("complete", option);
      }
    }

    return { insufficientMap, onSelected };
  }
});
</script>

<style scoped></style>
