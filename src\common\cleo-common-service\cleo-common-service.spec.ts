import {CleoCommonService} from "@/common/cleo-common-service/cleo-common-service";

const cleoCommonService: CleoCommonService = new CleoCommonService();

describe("CleoCommonService", () => {
  it("applyFilters", () => {
    expect(cleoCommonService.formatUserDominoName("Joe Bloggs", "CN")).toBe("Joe Bloggs");
    expect(cleoCommonService.formatUserDominoName("CN=Joe Bloggs", "CN")).toBe("Joe Bloggs");
    expect(cleoCommonService.formatUserDominoName("Joe Bloggs/sehnp", "CN")).toBe("Joe Bloggs");
    expect(cleoCommonService.formatUserDominoName("CN=Joe Bloggs/O=sehnp", "CN")).toBe("Joe Bloggs");


    expect(cleoCommonService.formatUserDominoName("<PERSON> Bloggs", "ABBREV")).toBe("<PERSON> Bloggs");
    expect(cleoCommonService.formatUserDominoName("CN=Joe Bloggs", "ABBREV")).toBe("Joe Bloggs");
    expect(cleoCommonService.formatUserDominoName("Joe Bloggs/sehnp", "ABBREV")).toBe("Joe Bloggs/sehnp");
    expect(cleoCommonService.formatUserDominoName("CN=Joe Bloggs/O=sehnp", "ABBREV")).toBe("Joe Bloggs/sehnp");

  })
})
