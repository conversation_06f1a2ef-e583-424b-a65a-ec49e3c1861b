import {
  ILegacyCleoCallSummary,
  ILegacyServerResponse
} from "@/common/cleo-legacy-models";
import { IAdapterPagedResponse } from "@/common/common-models";
import { ICleoCallSummary, ICleoService, IObjectBase } from "@/calls/summary/call-summarry-models";
import { CommonService } from "@/common/common-service";

export interface IIntegrationCallSummary {
  CallNo: number;
  CallService: ICleoService;
  CallClassification: IObjectBase;
  CallUrgentYn: boolean;
  CallStatusValue: number;
  CallSurname: string;
  CallForename: string;
  CallAddress1: string;
  CallAddress2: string;
  CallAddress3: string;
  CallAddress4: string;
  CallTown: string;
  CallPostCode: string;
}


const commonService = new CommonService();

export interface ICompareCallResult {
  callNumber: number;
  diffs: any;
  existsInLegacy: boolean;
  existsInAdapter: boolean;
}

export function gridNamedIntegrationService() {
  function areDataSetsSameSize(
    oldData: ILegacyServerResponse<ILegacyCleoCallSummary>,
    newData: IAdapterPagedResponse<ICleoCallSummary>
  ) {
    return oldData.items.length === newData.Records.length;
  }

  function mapLegacyCallModel(
    legacyCleoCallSummary: ILegacyCleoCallSummary
  ): IIntegrationCallSummary {
    return {
      CallClassification: {
        Id: 0,
        Description: legacyCleoCallSummary.CallClassification
      },
      CallNo: Number(legacyCleoCallSummary.CallNo),
      CallService: {
        Id: 0,
        Description: legacyCleoCallSummary.CallService,
        Type: "OOH"
      },
      CallUrgentYn: legacyCleoCallSummary.CallUrgentYN === "Yes",
      CallStatusValue: Number(legacyCleoCallSummary.CallStatusValue),
      CallSurname: legacyCleoCallSummary.CallSurname,
      CallForename: legacyCleoCallSummary.CallForename,
      CallAddress1: legacyCleoCallSummary.CallAddress1,
      CallAddress2: legacyCleoCallSummary.CallAddress2,
      CallAddress3: legacyCleoCallSummary.CallAddress3,
      CallAddress4: legacyCleoCallSummary.CallAddress4,
      CallTown: legacyCleoCallSummary.CallTown,
      CallPostCode: legacyCleoCallSummary.CallPostCode
    };
  }

  function mapNewCallModel(
    adapterCall: ICleoCallSummary
  ): IIntegrationCallSummary {
    return {
      CallClassification: adapterCall.CallClassification,
      CallNo: adapterCall.CallNo,
      CallService: {
        ...adapterCall.CallService,
        Type: "OOH"
      },
      CallUrgentYn: adapterCall.CallUrgentYn,
      CallStatusValue: adapterCall.CallStatusValue,
      CallSurname: adapterCall.CallSurname,
      CallForename: adapterCall.CallForename,
      CallAddress1: adapterCall.CallAddress1,
      CallAddress2: adapterCall.CallAddress2,
      CallAddress3: adapterCall.CallAddress3,
      CallAddress4: adapterCall.CallAddress4,
      CallTown: adapterCall.CallTown,
      CallPostCode: adapterCall.CallPostCode
    };
  }

  function callDifferences(
    cleoCall: ILegacyCleoCallSummary,
    adapterCall: ICleoCallSummary
  ) {
    const oldCall = mapLegacyCallModel(cleoCall);
    const newCall = mapNewCallModel(adapterCall);

    return commonService.differenceBetweenTwoObjects(oldCall, newCall);
  }

  function compareQueues(
    oldQueue: ILegacyCleoCallSummary[],
    newQueue: ICleoCallSummary[]
  ): Record<string, ICompareCallResult> {
    const oldInts = commonService.sortArray(
      "CallNo",
      oldQueue.map(oldCall => {
        return mapLegacyCallModel(oldCall);
      })
    );
    const mapOldInts = commonService.convertArrayToObject("CallNo", oldInts);

    const newInts = commonService.sortArray(
      "CallNo",
      newQueue.map(newCall => {
        return mapNewCallModel(newCall);
      })
    );
    const mapNewInts = commonService.convertArrayToObject("CallNo", newInts);

    const allCallNumbers: number[] = commonService.unique([...oldInts, ...newInts].map(intCall => {
      return intCall.CallNo;
    }));

    const result: Record<string, ICompareCallResult> = allCallNumbers.reduce<
      Record<string, ICompareCallResult>
    >((accum, callNumber) => {
      const key = callNumber.toString();

      if (!accum[key]) {
        accum[key] = {
          callNumber: callNumber,
          diffs: false,
          existsInLegacy: false,
          existsInAdapter: false
        };
      }

      const compareCallResult: ICompareCallResult = accum[key];

      if (mapOldInts[key]) {
        compareCallResult.existsInLegacy = true;
      }

      if (mapNewInts[key]) {
        compareCallResult.existsInAdapter = true;
      }

      if (
        compareCallResult.existsInLegacy &&
        compareCallResult.existsInAdapter
      ) {
        compareCallResult.diffs = commonService.differenceBetweenTwoObjects(
          mapOldInts[key],
          mapNewInts[key]
        );
      }

      return accum;
    }, {});

    return result;
  }

  function callsWithIssues(compareResult: Record<string, ICompareCallResult>): ICompareCallResult[] {
    return Object.values(compareResult).filter( (result) => {
      const hasDiffs = result.diffs && Object.keys(result).length > 0;
      const inBothQueues = result.existsInLegacy && result.existsInAdapter;

      return hasDiffs || !inBothQueues;
    });
  }

  return {
    areDataSetsSameSize,
    callDifferences,
    mapLegacyCallModel,
    mapNewCallModel,
    compareQueues,
    callsWithDiffs: callsWithIssues
  };
}
