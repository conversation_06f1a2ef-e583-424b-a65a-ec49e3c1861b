export class CleoCommonService {
  public serviceNameMask(serviceName: string): string {
    serviceName = serviceName.replace("South Essex 111", "South Mid Essex 111");
    serviceName = serviceName.replace(
      "Norfolk and Wisbech 111",
      "Norfolk and Waveney 111"
    );
    return serviceName;
  }

  /**
   * Legacy BS
   * @param charsIn
   */
  public escapeFormattedText(charsIn: string): string {
    let sOut = typeof charsIn === "undefined" ? "" : charsIn;
    sOut = sOut.replace(/\\/gi, "[BACKSLASH]");
    sOut = sOut.replace(/\//gi, "[FWDSLASH]");
    sOut = sOut.replace(/"/gi, "[QUOTE]");
    sOut = sOut.replace(/'/gi, "[APOS]");
    sOut = sOut.replace(/\r\n/gi, "[BR]");
    sOut = sOut.replace(/\n/gi, "[BR]");
    sOut = sOut.replace(/<BR>/gi, "[BR]");
    sOut = sOut.replace(/;/gi, "[SEMICOLON]");
    sOut = sOut.replace(/=/gi, "[EQUALS]");
    sOut = sOut.replace(/&/gi, "[AMP]");
    sOut = sOut.replace(/%/gi, "[PERCENT]");
    sOut = sOut.replace(/~/gi, "[TILDA]");
    sOut = sOut.replace(/Ã‚Â£/gi, "[POUND]");
    return sOut;
  }

  /**
   * Legacy BS.
   * @param charsIn
   * @param charNewLine
   */
  public unescapeFormattedText(charsIn: string, charNewLine = "<BR>"): string {
    let sOut: string = typeof charsIn === "undefined" ? "" : charsIn;
    sOut = sOut.replace(/\[BACKSLASH]/gi, "\\");
    sOut = sOut.replace(/\[FWDSLASH]/gi, "/");
    sOut = sOut.replace(/\[QUOTE]/gi, '"');
    sOut = sOut.replace(/\[APOS]/gi, "'");
    sOut = sOut.replace(/\[BR]/gi, charNewLine);
    sOut = sOut.replace(/\[SEMICOLON]/gi, ";");
    sOut = sOut.replace(/\[EQUALS]/gi, "=");
    sOut = sOut.replace(/\[AMP]/gi, "&");
    sOut = sOut.replace(/\[PERCENT]/gi, "%");
    sOut = sOut.replace(/\[TILDA]/gi, "~");
    sOut = sOut.replace(/\[POUND]/gi, "Ã‚Â£");
    return sOut;
  }

  public formatUserDominoName(
    userName: string,
    nameFormat: "CN" | "ABBREV" = "CN"
  ): string {

    if (!userName) {
      return "";
    }

    const hasSeparator = (un: string) => {
      return un.indexOf("/");
    };

    const formatter: Record<typeof nameFormat, (un: string) => string> = {
      CN: (un: string) => {
        //  E.g. CN=Joe Bloggs/O=sehnp
        let tmp = un;
        if (hasSeparator(un)) {
          tmp = un.split("/")[0];
        }
        return tmp.replace("CN=", "");
      },
      ABBREV: (un: string) => {
        return un.replace("CN=", "").replace("O=", "");
      }
    };
    return formatter[nameFormat] ? formatter[nameFormat](userName) : userName;
  }
}
