// export const PACCS_GENDER = {
//   MALE: "Male",
//   FEMALE: "Female"
// };
//
// export const PACCS_AGE_GROUPS = {
//   ADULT: "Adult",
//   CHILD: "Child",
//   TODDLER: "Toddler",
//   INFANT: "Infant",
//   NEONATE: "Neonate"
// };


export type PaccsSymptomModelTemplateId = string;

export interface IPaccsSymptomModel {
  templateId: string; //  E.g. "Cs000001"
  version: number; //  E.g. 1
  templateName: string; //  E.g. "Drug, Solvent or Alcohol misuse"
  synonyms: string; //  E.g. "drug|alcohol|solvent abuse|addiction problem|withdrawl|detox"
  symptomGroup: number; //  E.g. 1185
  servicesJump: string; //  E.g. "PW1846.0"
  etcJump: string; //  E.g. "PW1847.0"
}
