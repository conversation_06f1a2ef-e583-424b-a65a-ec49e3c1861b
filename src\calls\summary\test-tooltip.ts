import { ITooltipParams } from "ag-grid-community";

export default function CustomTooltip() {
  console.log("CustomTooltip");
}

CustomTooltip.prototype.init = function(params: ITooltipParams) {
  const eGui = (this.eGui = document.createElement("div"));
  const color = "red";
  const data = params.api.getDisplayedRowAtIndex(params.rowIndex).data;

  const cleoCallSummary = data;

  eGui.classList.add("custom-tooltip");
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  eGui.style["background-color"] = color;
  eGui.innerHTML =
    "<p><span class'name'>" +
    cleoCallSummary.CallNo +
    "</span></p>" +
    "<p><span>Country: </span>" +
    cleoCallSummary.PDSTracedAndVerified +
    "</p>" +
    "<p><span>Total: </span>" +
    cleoCallSummary.CallAge +
    "</p>";
};

CustomTooltip.prototype.getGui = function() {
  return this.eGui;
};
