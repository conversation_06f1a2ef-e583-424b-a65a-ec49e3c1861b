<template>
  <div class="ic24-flex-row ic24-flex-gap">
    <RadioButton
      label="No"
      :option-value="false"
      v-model="valueInternal"
      @onChanged="onValueChanged"
    />
    <RadioButton
      label="Yes"
      :option-value="true"
      v-model="valueInternal"
      @onChanged="onValueChanged"
    />
  </div>
</template>

<script lang="ts">
import {
  ref,
  defineComponent,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { simpleObjectClone } from "@/common/common-utils";
import RadioButton from "@/common/ui/fields/RadioButton.vue";

export default defineComponent({
  name: "RadioBooleanNull",

  components: { RadioButton },
  props: {
    value: {
      type: [String, Number, Boolean, null] as PropType<
        string | number | boolean | null
      >,
      required: false,
      default: null
    },
    isDisabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  setup(
    props: {
      value: unknown;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(simpleObjectClone(props.value));

    watch(
      () => props.value,
      (newValue: unknown) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = simpleObjectClone(newValue);
        }
      },
      {
        immediate: true
      }
    );

    function onValueChanged() {
      console.log("RadioButton.onValueChanged", valueInternal.value);
      context.emit("input", valueInternal.value);
      context.emit("onChanged", valueInternal.value);
    }

    return { onValueChanged, valueInternal };
  }
});
</script>
