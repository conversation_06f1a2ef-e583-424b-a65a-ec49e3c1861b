import { computed, reactive } from "@vue/composition-api";
import * as FollowUpService from "@/calls/details/complete/components/followup/models/follow-up-service";
import * as FollowUpApi from "@/calls/details/complete/components/followup/api/follow-up-api";
import {
  FollowUpClassification,
  FollowUpInput,
  FollowUpState
} from "@/calls/details/complete/components/followup/models/follow-up-models";
import { LegacyKeywordServerResponse } from "@/common/cleo-legacy-models";

export function useFollowUpController(followUpInput: FollowUpInput) {
  const state = reactive<FollowUpState>(FollowUpService.factoryFollowUpState());

  /**
   * Initialise the follow up controller
   */
  function init() {
    state.data.isReady = false;

    // TODO
    // <Should get this from config>
    // set the dateTimePickerOptions
    const minDate = new Date();
    // add 6 hours
    minDate.setHours(minDate.getHours() + 1);
    state.data.dateTimePickerOptions.minUtc = minDate
      .toISOString()
      .slice(0, 16);

    // max is 4 days from now...
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 4);
    // YYYY-MM-DDTHH:MM
    state.data.dateTimePickerOptions.maxUtc = maxDate
      .toISOString()
      .slice(0, 16);
    // </Should get this from config>

    // set min human display as format of minDate as example 21 Apr 2025, 13:25
    state.data.dateTimePickerOptions.minHumanDisplay = minDate.toLocaleString(
      "en-GB",
      {
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "numeric",
        minute: "numeric"
      }
    );

    // set max human display as format of maxDate as example 21 Apr 2025, 13:25
    state.data.dateTimePickerOptions.maxHumanDisplay = maxDate.toLocaleString(
      "en-GB",
      {
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "numeric",
        minute: "numeric"
      }
    );

    const promGetDxData = FollowUpApi.getDxData(
      followUpInput.callDetail.Service.name
    ).then(data => {
      console.log(data);
      state.data.dxCodes = data;
    });

    const promGetCleoClientServices = FollowUpApi.getCleoClientServices(
      followUpInput.callDetail.Service.name
    ).then(data => {
      console.log(data);

      // sort by key
      const sortedData = Object.keys(data)
        .sort()
        .reduce((acc, key: string) => {
          const localKey: string = (key as any) as string;
          acc[localKey] = data[localKey];
          return acc;
        }, {} as LegacyKeywordServerResponse);

      state.data.cleoClientServices = sortedData;
    });

    const promGetBaseQuestion = FollowUpApi.getBaseQuestion(
      followUpInput.callDetail.Service.name
    ).then(data => {
      console.log(data);

      // use reduce and Object.keys to get the codeID1 values and split them by ;
      const questions: string[] = Object.keys(data).reduce<string[]>(
        (acc, key: string) => {
          const localKey: string = (key as any) as string;
          const question = data[localKey].codeID1;
          const questionArray = question.split(";");
          acc.push(...questionArray);
          return acc;
        },
        []
      );

      state.data.baseQuestionTriageTypes = questions;
    });

    const promises = [
      promGetDxData,
      promGetCleoClientServices,
      promGetBaseQuestion
    ];

    state.data.isLoading = true;
    Promise.all(promises).finally(() => {
      state.data.isLoading = false;
      state.data.isReady = true;
    });
  }

  /**
   *
   */
  function resetUserInput() {
    state.input.userConfirmed = false;
    state.input.followUpType = null;
    state.input.cleoClientService = null;
    state.input.dxCode = null;
    state.input.dateTimePicker = "";
    state.input.baseQuestionTriageType = "";
  }

  /**
   * Set the follow up type
   * @param followUpType
   */
  function setFollowUpType(followUpType: FollowUpClassification | null) {
    if (followUpType === null) {
      resetUserInput();
      return;
    }
    state.input.followUpType = state.data.followUpRecord[followUpType];

    if (followUpType === "Advice") {
      // followUpController.state.input.cleoClientService = "";

      // loop over followUpController.state.data
      //               .cleoClientServices  and if FOLLOW_UP found, set to that.
      const cleoClientServices = Object.values(state.data.cleoClientServices);
      const foundService = cleoClientServices.find(
        service => service.description === "FOLLOW_UP"
      );
      if (foundService) {
        state.input.cleoClientService = foundService;
      }
    }

    setDxCodesForClassification();
  }

  function onClassificationChanged() {
    state.input.cleoClientService = null;
    state.input.dxCode = null;
    state.input.dateTimePicker = "";

    setDxCodesForClassification();
    validate();
  }

  function setDxCodesForClassification() {
    if (state.input.followUpType === null) {
      state.data.dxCodesForClassification = [];
      return;
    }

    state.data.dxCodesForClassification = FollowUpService.getDxCodesForClassification(
      state.data.dxCodes,
      state.input.followUpType.classification
    );
  }

  function validate() {
    state.data.errors = FollowUpService.validate(state);
  }

  function userConfirmed() {
    state.input.userConfirmed = true;
  }

  const getClassification = computed(() => {
    return state.input.followUpType
      ? state.input.followUpType.classification
      : "";
  });

  const getConfirmationMessage = computed(() => {
    return FollowUpService.getConfirmedMessage(state);
  });

  const hasErrors = computed(() => {
    return Object.keys(state.data.errors).length > 0;
  });

  return {
    state,

    getClassification,
    getConfirmationMessage,
    hasErrors,

    init,
    setFollowUpType,
    onClassificationChanged,
    validate,
    userConfirmed,
    resetUserInput
  };
}
