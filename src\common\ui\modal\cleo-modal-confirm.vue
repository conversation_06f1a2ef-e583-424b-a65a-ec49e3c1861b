<template>
  <CleoModal
    :header-message="headerMessage"
             :body-message="bodyMessage"
    v-on:clickPrimary="clickPrimary"
    v-on:clickSecondary="clickSecondary"
  >
    <slot name="footer">
      <div class="e4s-modal-footer">
        <slot name="buttons">
          <slot name="button-close-secondary">
            <button
              class="adapter-button adapter-width-5 adapter-button--red"
              v-on:click.stop="clickSecondary"
            >
              <span v-text="buttonSecondaryText"></span>
            </button>
          </slot>

          <slot name="button-close-primary">
            <button
              class="adapter-button adapter-width-5 adapter-button--green"
              v-on:click.stop="$emit('clickPrimary')"
            >
              <span v-text="buttonPrimaryText"></span>
            </button>
          </slot>
        </slot>
      </div>
    </slot>
  </CleoModal>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";

import CleoModal from "@/common/ui/modal/cleo-modal.vue";
export default defineComponent({
  name: "cleo-modal-confirm",
  components: { CleoModal },
  props: {
    buttonSecondaryText: {
      default: "Cancel!"
    },
    buttonPrimaryText: {
      default: "OK!"
    },
    headerMessage: {
      default: "Confirm"
    },
    bodyMessage: {
      default: ""
    }
  },
  setup(
    props: {
      buttonSecondaryText: string;
      buttonPrimaryText: string;
      headerMessage: string;
      bodyMessage: string;
    },
    context: SetupContext
  ) {
    function clickSecondary() {
      alert("clickSecondary");
      context.emit("clickSecondary");
    }

    function clickPrimary() {
      alert("clickPrimary");
      context.emit("clickPrimary");
    }

    return {
      clickSecondary,
      clickPrimary
    };
  }
});
</script>
