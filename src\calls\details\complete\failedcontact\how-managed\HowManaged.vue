<template>
  <div>
    <CompleteStepHeader :step="step" />
    <div class="complete-step--subheader">
      Confirm how this case was managed.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <div>
      Have you contacted/attempted to contact the patient.
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <SimpleButtonSelecter
      class="ic24-flex-column ic24-flex-gap-large complete-step--simple-buttons"
      :options="options"
      :value="value"
      @input="onInput"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, PropType } from "@vue/composition-api";
import SimpleButtonSelecter, {
  ISimpleButtonInputValue
} from "../../SimpleButtonSelecter.vue";
import { HowManaged, HowManagedValue } from "./how-managed-models";
import { IStep } from "@/calls/details/complete/complete-models";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "HowManaged",
  components: { CompleteStepHeader, SimpleButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"HOW_WAS_CASE_MANAGED">>,
      required: true
    },
    value: {
      type: Object as PropType<
        ISimpleButtonInputValue<HowManaged, HowManagedValue>
      >,
      required: true
    },
    options: {
      type: Array as PropType<
        ISimpleButtonInputValue<HowManaged, HowManagedValue>[]
      >,
      default: () => {
        const options: ISimpleButtonInputValue<
          HowManaged,
          HowManagedValue
        >[] = [
          {
            id: "VIRTUAL_CONSULT_TEL_ONLY",
            description: "Virtual consultation - Telephone Only",
            value: "1-VirtualConsultTelOnly"
          },
          {
            id: "VIRTUAL_CONSULT_TEL_AND_VIDEO",
            description: "Virtual consultation - Telephone and Video",
            value: "2-VirtualConsultTelVideo"
          },
          {
            id: "BASE",
            description: "Base - Face to Face",
            value: "3-BaseF2F"
          },
          {
            id: "HOME_VISIT",
            description: "Visit - Face to Face",
            value: "4-HomeVisitF2F"
          }
        ];
        return options;
      }
    }
  },
  setup(
    props: {
      step: IStep<"HOW_WAS_CASE_MANAGED">;
      value: ISimpleButtonInputValue<HowManaged, HowManagedValue>;
      options: ISimpleButtonInputValue<HowManaged, HowManagedValue>[];
    },
    context: SetupContext
  ) {
    function onInput(
      simpleButtonInputValue: ISimpleButtonInputValue<
        HowManaged,
        HowManagedValue
      >
    ) {
      console.log("HowManaged.onInput()", simpleButtonInputValue);
      context.emit("input", simpleButtonInputValue);
    }

    return { onInput };
  }
});
</script>

<style scoped></style>
