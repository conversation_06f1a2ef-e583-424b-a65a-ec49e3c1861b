import { GridLegacyServerResponse } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

export const queueCasMockYellow: GridLegacyServerResponse = ({
  Count: 38,
  Returned: 38,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: [
    {
      AFT_appt_id: "",
      AFT_CANCELLED_REASON: "",
      AFT_datetime_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_time_start: "",
      AFT_UNABLE_REASON: "",
      ALL_VIEW_INCLUDE: "",
      ApplyBreach: "1",
      BreachActualTime: "2025-05-13T13:14:31+01:00",
      Brea<PERSON><PERSON><PERSON>: "NurseAdviceNo",
      BreachLevel1Mins: "15",
      BreachPreActualTime: "2025-05-13T12:59:31+01:00",
      BreachPriority: "5",
      BreachPriorityGroup: "",
      BreachPriorityLabel: "Urgent 15 mins",
      BreachWarnActualTime: "2025-05-13T13:19:31+01:00",
      Call1stContact: "13/05/2025 16:16:36",
      Call1stContactPathways: "13/05/2025 12:53:27",
      CallAdastraLock: "0",
      CallAdastraSent: "",
      CallAddress1: "313 Meadgate Avenue",
      CallAddress2: "CHELMSFORD",
      CallAddress3: "",
      CallAddress4: "",
      CallAge: "21 yrs",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      CallCallback: "0",
      CallCCMS: "",
      CallClassification: "Nurse Advice",
      CallCName: "rian - partner",
      CallCompleted: "",
      CallCompletedYN: "No",
      CallCreatedBy: "Boarding Dzinotizei",
      CallCRel: "Relative or Friend",
      CallDobIso: "2004-02-03",
      CallDoctorName: "CN=Michelle Jarrett/O=sehnp",
      CallDoctorNameCN: "Michelle Jarrett",
      CallForename: "Liberty",
      CallID: "250855616",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      CallMF: "Female",
      CallNHSNo: "**********",
      CallNo: "250855616",
      CallPatientTitle: "",
      CallPostCode: "CM2 7NL",
      CallPracticeOCS: "F81117",
      CallPtcl: "",
      CallReceivedISO: "2025-05-13T12:59:31+01:00",
      CallReceivedTime: "12:51",
      CallReceivedTimeISO: "2025-05-13T12:51:18+01:00",
      CallSecondOpenCall: "",
      CallService: "South Essex 111",
      CallServiceAlt: "",
      CallServiceOriginal: "South Essex 111",
      CallServiceSub: "",
      CallStatus: "New",
      CallStatusValue: "1",
      CallSurname: "WOODS",
      CallSymptoms:
        "medication ran out, severe cramps, stomach acid, bone pain, pt diagnosed with neuroendocrino cancer, Red surge[FWDSLASH]No WT attempted",
      CallTelNo: "07498 589081",
      CallTelNo_R: "07498 589081",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallTown: "",
      CallTriaged: "No",
      CallUrgentYN: "No",
      CallWarmTransferred: "No",
      CallWithBaseAckTime: "",
      CareConnectAppointmentStart: "",
      CareHomeName: "",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CAS_TRANSFER_ERROR: "",
      CC: "Nurse Advice",
      CHFinalDispositionCode: "Dx3318",
      CHFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Early exit, declared medical history",
      ClinicalHub_111ToHubReason: "",
      CliniHighPriority: "",
      Comfort_Cancelled_REASON: "",
      Comfort_Cancelled_TIME: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS2: "",
      Courtesy_Contact: "",
      Courtesy_Count: "",
      Courtesy_Time: "",
      Courtesy_User: "",
      Cov19Priority: "",
      CSC: "",
      DAB_EndDateTime: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      dateAppointmentStart: "",
      Dispatch_Vehicle: "",
      dtArrivedTime: "",
      DutyBase: "",
      EConsult_ArrivalMethod: "",
      EConsult_AttendanceSource: "",
      EConsult_code: "",
      EConsult_ITK_IN: "",
      EConsult_LiveOverseas: "",
      EConsult_LivingArrangement: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_SameProblem: "",
      EConsult_Source: "",
      EConsult_Status: "",
      EConsult_UTC: "",
      EConsult_XRay: "",
      ED_arrived: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "1",
      Info: "",
      IsLocked: "Michelle Jarrett/sehnp",
      ITK_111_Online: "",
      IUC_CAS_AT_ONE_TIME: "",
      IUC_Contract: "",
      KMS_SC_MESSAGE: "",
      Linked_Call_ID: "",
      name: "250855616",
      Pathways_ITK_Send: "",
      PathwaysCaseId: "c805e249-636e-4491-b9d9-5e4f04e277db",
      patient_alerts_count: "",
      PatientContactCode: "Answerphone - Message left",
      PatientContactCode_count: "1",
      PatientContactCode_Current_ForView: "2025-05-13T16:18:51",
      PatientContactCode_Initial: "13/05/2025 16:18:51",
      PatientName: "WOODS, Liberty",
      PDSTraced: "true",
      PDSTracedAndVerified: "Yes",
      SDec_Service: "0",
      SetBaseInfo_Base: "",
      Source: "",
      StartConsultationPerformed: "1",
      TomTomOrderID: "",
      txtAppointmentBase: "",
      unid: "B1C74CEF879105E780258C8900411F20",
      UTC_Assigned: "",
      UTC_CaseComments: "",
      WalkIn: "0"
    },
    {
      unid: "DD3556EFD37C054780258C840024F07B",
      name: "250658881",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250658881",
      CallID: "250658881",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-08T07:43:28+01:00",
      CallReceivedTimeISO: "2025-05-08T07:43:28+01:00",
      BreachWarnActualTime: "2025-05-08T11:23:28+01:00",
      BreachPreActualTime: "2025-05-08T07:43:28+01:00",
      BreachActualTime: "2025-05-08T11:43:28+01:00",
      BreachPriority: "8",
      BreachLevel1Mins: "240",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "07:43",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "64 yrs",
      CallDoctorNameCN: "",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "No",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "07912626865",
      CallTelNo_R: "07912626865",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx03",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx03",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "1",
      SMS_SENT: "0",
      SMS_LATEST_AT: "08/05/2025 07:43:42",
      SMS_LATEST_USER: "ITK",
      SMS_LATEST_MESSAGE:
        "Your details have been passed to us by NHS111.  We are working hard to contact you as soon as possible, but please be aware this can be longer than the timeframe given by NHS111. If this is the case, one of our call handlers will phone you to check how you are.  Severnside Integrated Urgent Care. "
    }
  ],
  Page: {
    Enabled: 1,
    PageNumber: 1,
    PageSize: 1000,
    getRowCount: 38,
    getStartRowNumber: 1,
    TotalSearchRowCount: 38,
    getTotalNumberOfPages: 1
  }
} as any) as GridLegacyServerResponse;
