<template>
  <div class="ic24-flex-row ic24-flex-gap">
    <div v-for="message in validationMessages" :key="message.id">
      <div>{{ message.message }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import { IValidationMessage } from "@/calls/details/complete/complete-models";
export default defineComponent({
  name: "CompleteValidationMessages",
  props: {
    validationMessages: {
      type: Array as PropType<IValidationMessage[]>,
      default: () => {
        return [];
      }
    }
  }
});
</script>
