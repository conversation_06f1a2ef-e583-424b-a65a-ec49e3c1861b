/* The dropdown container */
.dropdown {
    /*float: left;*/
    overflow: hidden;
}

/* Dropdown button */
.dropdown .drop-btn {
    /*font-size: 16px;*/
    border: none;
    outline: none;
    color: white;
    padding: 14px 16px;
    background-color: inherit;
    font-family: inherit; /* Important for vertical align on mobile phones */
    margin: 0; /* Important for vertical align on mobile phones */
}

/* Add a red background color to navbar links on hover */
.navbar a:hover,
.dropdown:hover .drop-btn {
    background-color: red;
}

/* Dropdown content (hidden by default) */
.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
    z-index: 1;
}

/* Links inside the dropdown */
.dropdown-content a {
    float: none;
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
}

/* Add a grey background color to dropdown links on hover */
.dropdown-content a:hover {
    background-color: #ddd;
}

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {
    display: block;
}

/* Dropup Button...badly named...see legacy*/
/*.dropbtn {*/
/*    padding: 5px 15px;*/
/*    border: none;*/
/*    background: transparent linear-gradient(180deg, #E9EFFD 0%, #CBDFF6 64%, #BCD5F0 65%, #BCD5F0 100%) 0% 0% no-repeat padding-box;*/
/*    height: calc(var(--grid--header-height) - 1px);*/
/*}*/

/*Looks like legacy CLEO toolbar dijit button*/
.toolbar--legacy-button {
    border: 1px solid transparent;
    padding: 5px 15px;
    /*border: none;*/
    background: transparent linear-gradient(180deg, #E9EFFD 0%, #CBDFF6 64%, #BCD5F0 65%, #BCD5F0 100%) 0% 0% no-repeat padding-box;
    height: calc(var(--grid--header-height) - 1px);
}

.toolbar--legacy-button:hover {
    border: 1px solid #316ac5;
    background: transparent linear-gradient(180deg, #9abbea 0%, #9abbea 64%, #9abbea 65%, #9abbea 100%) 0% 0% no-repeat padding-box;
}

/* The container <div> - needed to position the dropup content */

.navbar-up {
    overflow: hidden;
    background-color: #333;
    font-family: Arial;
}

.standard-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-position-y: center;
    background-repeat: no-repeat;
    vertical-align: middle;
}


.dijit-editor-icon {
    background-image: url(../editorIconsEnabled.png);
}

.dijit-editor-icon--copy {
    background-position-x: -72px;
}

.dijit-editor-icon--paste {
    background-position-x: -90px;
}

.dijit-arrow-icon {
    background-image: url(../spriteRoundedIconsSmall.png);
}

.dijit-arrow-icon--open {
    background-position-x: -15px;
}

.dijit-arrow-icon--closed {
    background-position-x: -30px;
}

.standard-icon--socket-connected {
    background-image: url(../ball-green.png);
}

.standard-icon--socket-not-connected {
    background-image: url(../ball-red.png);
}

/***


DROP UP


 */
.dropup {
    position: relative;
    display: inline-block;
}

/* Dropup content (Hidden by Default) */
.dropup-content {
    display: none;
    position: absolute;
    /*bottom: 50px;*/
    /*bottom: 35px; <--this makes go up*/
    background-color: #f1f1f1;
    min-width: 160px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

/* Links inside the dropup */
.dropup-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    /*min-width: 160px;*/
    display: block;
    width: 200px;
    /*z-index: 9999;      Me*/
}

/* Change color of dropup links on hover */
.dropup-content a:hover {
    background-color: #ddd;
}

/* Show the dropup menu on hover */
.dropup:hover .dropup-content {
    display: block;
}

/* Change the background color of the dropup button when the dropup content is shown */
.dropup:hover .dropbtn {
    background-color: #2980b9;
}

.not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: grey !important;
}
