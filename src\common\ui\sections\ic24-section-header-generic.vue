<template>
  <section class="ic24-section-wrapper">
    <h5 class="ic24-header--400" v-text="title"></h5>
    <div class="ic24-flex-column">
      <slot name="content"></slot>
    </div>
  </section>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "ic24-section-header-generic",
  components: {},
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      }
    }
  }
});
</script>
