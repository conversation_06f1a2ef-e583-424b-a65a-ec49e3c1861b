<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <div class="ic24-flex-row ic24-flex-gap">
      <div class="ic24-flex-column ic24-flex-gap" v-if="false">
        <div class="ic24-flex-row ic24-flex-gap">
          <span>Service</span>
          <span
            >{{ callDetail.Service.id }}: {{ callDetail.Service.name }} -
            {{ callDetail.Service.serviceType }}</span
          >
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <span>Classification</span>
          <span
            >{{ callDetail.Classification.Id }}:
            {{ callDetail.Classification.Description }}</span
          >
        </div>
        <div class="ic24-flex-row ic24-flex-gap">
          <span>isCasAdvice</span>
          <span>{{ isCasAdvice }}</span>
        </div>
      </div>

      <Ic24Button
        title="Further Urgent Care Follow up required?"
        :disabled="followUpController.state.input.userConfirmed"
        @click="setFollowUpType('Advice')"
      ></Ic24Button>
      <Ic24Button
        v-if="isCasAdvice"
        title="Face-to-Face Urgent Care Referral"
        :disabled="followUpController.state.input.userConfirmed"
        @click="setFollowUpType('Base')"
      ></Ic24Button>
      <Ic24Button
        v-if="isCasAdvice"
        title="Home Visit Urgent Care Referral"
        :disabled="followUpController.state.input.userConfirmed"
        @click="setFollowUpType('Visit')"
      ></Ic24Button>
    </div>

    <!--    :disabled="followUpController.state.input.userConfirmed"-->
    <div class="ic24-flex-row ic24-flex-gap">
      <div class="ic24-flex-column ic24-flex-gap">
        <label>Follow Up Type (Service)</label>
        <select
          v-model="followUpController.state.input.followUpType"
          @change="onClassificationChanged()"
          disabled="true"
        >
          <option :value="null">Select</option>

          <option
            v-for="followUpType in getFollowUpTypes"
            :key="followUpType.label"
            v-text="followUpType.label"
            :value="followUpType"
          >
          </option>
        </select>
        <!--        v-for="followUpType in followUpController.state.data.followUpRecord"-->
      </div>

      <div class="ic24-flex-column ic24-flex-gap">
        <label>Priority</label>
        <select
          v-model="followUpController.state.input.dxCode"
          @change="validate"
          :disabled="followUpController.state.input.userConfirmed"
        >
          <option :value="null">Select</option>
          <option
            v-for="dxCode in followUpController.state.data
              .dxCodesForClassification"
            :key="dxCode.description"
            v-text="dxCode.codeID1"
            :value="dxCode"
          ></option>
        </select>
      </div>

      <div
        class="ic24-flex-column ic24-flex-gap"
        v-if="
          followUpController.getClassification.value.toUpperCase() === 'ADVICE'
        "
      >
        <!--N.B. time min/max restrictions are not adhered to by html 5 datetime-->
        <label>Make Case Active When</label>
        <div class="ic24-flex-row ic24-flex-gap">
          <input
            style="width: fit-content"
            type="datetime-local"
            :min="followUpController.state.data.dateTimePickerOptions.minUtc"
            :max="followUpController.state.data.dateTimePickerOptions.maxUtc"
            v-model="followUpController.state.input.dateTimePicker"
            @change="validate"
            :disabled="followUpController.state.input.userConfirmed"
          />

          <span
            v-if="followUpController.state.data.errors.dateTimePicker"
            v-text="followUpController.state.data.errors.dateTimePicker"
          >
          </span>
        </div>

        <div class="ic24-flex-row ic24-flex-gap">
          Time Range:
          <span
            v-text="
              followUpController.state.data.dateTimePickerOptions
                .minHumanDisplay
            "
          ></span>
          to
          <span
            v-text="
              followUpController.state.data.dateTimePickerOptions
                .maxHumanDisplay
            "
          ></span>
        </div>
      </div>

      <div
        class="ic24-flex-column ic24-flex-gap"
        v-if="
          ['BASE', 'VISIT'].indexOf(
            followUpController.getClassification.value.toUpperCase()
          ) > -1
        "
      >
        <label>Triage Type</label>
        <select
          v-model="followUpController.state.input.baseQuestionTriageType"
          @change="validate"
          :disabled="followUpController.state.input.userConfirmed"
        >
          <option :value="''">Select</option>
          <option
            v-for="baseTriageType in followUpController.state.data
              .baseQuestionTriageTypes"
            :key="baseTriageType"
            v-text="baseTriageType"
          ></option>
        </select>
      </div>
    </div>

    <div v-if="followUpController.hasErrors.value">
      Validation Errors:
      <ul>
        <li
          v-for="error in followUpController.state.data.errors"
          :key="error"
          v-text="error"
        ></li>
      </ul>
    </div>

    <div class="ic24-flex-row ic24-flex-gap">
      <Ic24Button
        title="Cancel"
        button-style="destructive"
        @click="cancel"
      ></Ic24Button>

      <Ic24Button
        title="Confirm"
        @click="confirm"
        :disabled="followUpController.state.input.userConfirmed"
      ></Ic24Button>
    </div>

    <div
      class="ic24-flex-row"
      v-text="followUpController.getConfirmationMessage.value"
    ></div>

    <!--    min{{ followUpController.state.data.dateTimePickerOptions.minUtc }}-->
    <!--    <br />-->
    <!--    max{{ followUpController.state.data.dateTimePickerOptions.maxUtc }}-->
    <!--    <br />-->
    <!--    selected{{ followUpController.state.input.dateTimePicker }}-->
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext
} from "@vue/composition-api";
import { useFollowUpController } from "@/calls/details/complete/components/followup/models/useFollowUpController";
import * as CallDetailService from "@/calls/details/call-detail-service";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { ICallDetail } from "@/calls/details/call-details-models";
import { simpleObjectClone } from "@/common/common-utils";
import {
  FollowUpClassification,
  FollowUpType
} from "@/calls/details/complete/components/followup/models/follow-up-models";
export default defineComponent({
  name: "FollowUp",
  components: { Ic24Button },
  props: {
    callDetail: {
      type: Object as PropType<ICallDetail>,
      default: () => {
        return CallDetailService.factoryCallDetail();
      }
    }
  },
  setup(props: { callDetail: ICallDetail }, context: SetupContext) {
    const followUpController = useFollowUpController({
      callDetail: props.callDetail
    });
    followUpController.init();

    function cancel() {
      followUpController.setFollowUpType(null);
      onInput();
    }

    function setFollowUpType(followUpType: FollowUpClassification | null) {
      followUpController.resetUserInput();
      followUpController.setFollowUpType(followUpType);
      confirm();
    }

    function validate() {
      followUpController.validate();
    }

    function confirm() {
      followUpController.validate();

      if (Object.keys(followUpController.state.data.errors).length > 0) {
        return;
      }

      followUpController.userConfirmed();

      console.log(
        "FollowUp.confirm",
        simpleObjectClone(followUpController.state.input)
      );
      onInput();
    }

    function onInput() {
      console.log(
        "FollowUp.onInput",
        simpleObjectClone(followUpController.state.input)
      );
      context.emit("input", simpleObjectClone(followUpController.state.input));
    }

    function onClassificationChanged() {
      followUpController.onClassificationChanged();
      validate();
    }

    const isCasAdvice = computed(() => {
      return (
        props.callDetail.Classification.Description?.toUpperCase() ===
          "ADVICE" && props.callDetail.Service.name.toUpperCase() === "CAS"
      );
    });

    const getFollowUpTypes = computed<
      Partial<Record<FollowUpClassification, FollowUpType>>
    >(() => {
      // Return a partial Record of the followUpRecord. if isCasAdvice is true, return only the "Advice" type
      return Object.values(followUpController.state.data.followUpRecord).reduce<
        Partial<Record<FollowUpClassification, FollowUpType>>
      >((accum, followUpType) => {
        if (isCasAdvice.value) {
          accum[followUpType.classification] = followUpType;
        } else {
          if (followUpType.classification.toUpperCase() === "ADVICE") {
            accum[followUpType.classification] = followUpType;
          }
        }
        return accum;
      }, {});
    });

    return {
      followUpController,
      isCasAdvice,
      getFollowUpTypes,

      setFollowUpType,
      onClassificationChanged,
      confirm,
      validate,
      cancel
    };
  }
});
</script>
