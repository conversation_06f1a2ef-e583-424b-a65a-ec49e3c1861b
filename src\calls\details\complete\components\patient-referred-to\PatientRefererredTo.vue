<template>
  <div class="ic24-flex-column ic24-flex-gap-x patient-referred-to--step">
    <CompleteStepHeader :step="step" />

    <div
      style="color: var(--red-500);margin: var(--ic24-flex-gap-small) 0;"
      class="ic24-align-self-flex-center ic24-subheader--200"
    >
      **Post Assessment Questionnaire - internal data capture only, not an
      appointment booking**
    </div>

    <div class="ic24-flex-column ic24-flex-gap">
      <div class="ic24-flex-row ic24-flex-gap">
        <div class="ic24-flex-column ic24-flex-gap">
          <div class="patient-referred-to--header">
            Close Case With No Further Action
          </div>
          <div
            class="ic24-flex-column ic24-flex-gap patient-referred-to--sub-col"
          >
            <div class="patient-referred-to--sub-header">No Further Action</div>

            <div class="ic24-flex-column ic24-flex-gap-small ic24-flex-wrap">
              <RadioButtonObj
                v-for="opt in noFurtherActionOptions"
                :key="opt.value"
                v-model="patientReferredToLocal.referredTo"
                :option-value="opt"
                :label="opt.description"
                @onChanged="onUserChange"
              />
            </div>
          </div>
        </div>

        <div class="ic24-flex-row ic24-flex-gap ic24-flex-grow">
          <div class="ic24-flex-column ic24-flex-gap ic24-flex-grow">
            <div class="patient-referred-to--header">Onward Referral Made</div>

            <div class="ic24-flex-row">
              <div
                class="ic24-flex-column ic24-flex-gap ic24-flex-wrap patient-referred-to--sub-col"
              >
                <div class="patient-referred-to--sub-header">999</div>
                <RadioButtonObj
                  v-model="patientReferredToLocal.referredTo"
                  :option-value="buttonOptions['6-Called 999']"
                  :label="buttonOptions['6-Called 999'].description"
                  @onChanged="onUserChange"
                />
              </div>

              <div
                class="ic24-flex-column ic24-flex-gap patient-referred-to--sub-col "
              >
                <div class="patient-referred-to--sub-header">
                  OOH Primary Care
                </div>
                <div
                  class="ic24-flex-column ic24-flex-gap-small ic24-flex-wrap"
                >
                  <RadioButtonObj
                    v-for="opt in primaryCareOptions"
                    :key="opt.value"
                    v-model="patientReferredToLocal.referredTo"
                    :option-value="opt"
                    :label="opt.description"
                    @onChanged="onUserChange"
                  />
                </div>
              </div>

              <div
                class="ic24-flex-column ic24-flex-gap patient-referred-to--sub-col"
              >
                <div class="patient-referred-to--sub-header">
                  Secondary Care
                </div>
                <div class="ic24-flex-column  ic24-flex-wrap">
                  <div
                    class="ic24-flex-column ic24-flex-gap-small ic24-flex-wrap"
                  >
                    <RadioButtonObj
                      v-for="opt in secondaryCareOptions"
                      :key="opt.value"
                      v-model="patientReferredToLocal.referredTo"
                      :option-value="opt"
                      :label="opt.description"
                      @onChanged="onUserChange"
                    />
                  </div>
                  <!--                  <div-->
                  <!--                    class="ic24-flex-column"-->
                  <!--                    v-if="-->
                  <!--                      patientReferredToLocal.referredTo.id ===-->
                  <!--                        '17-Other Secondary Care'-->
                  <!--                    "-->
                  <!--                  >-->
                  <!--                    Secondary Care Referral: Other - please provide more-->
                  <!--                    details.-->
                  <!--                    <textarea-->
                  <!--                      v-model="patientReferredToLocal.referredText"-->
                  <!--                      class="patient-referred-to&#45;&#45;textarea ic24-full-width-x"-->
                  <!--                      @change="onUserChange"-->
                  <!--                    ></textarea>-->
                  <!--                  </div>-->
                </div>
              </div>

              <div
                class="ic24-flex-column ic24-flex-gap patient-referred-to--sub-col"
              >
                <div class="patient-referred-to--sub-header">Primary Care</div>
                <div
                  class="ic24-flex-column ic24-flex-gap-small ic24-flex-wrap"
                >
                  <RadioButtonObj
                    style="width: fit-content"
                    v-model="patientReferredToLocal.referredTo"
                    :option-value="
                      buttonOptions['18-PCN contracted bookable 111 slot']
                    "
                    :label="
                      buttonOptions['18-PCN contracted bookable 111 slot']
                        .description
                    "
                    @onChanged="onUserChange"
                  />
                </div>
              </div>

              <div
                class="ic24-flex-column ic24-flex-gap patient-referred-to--sub-col"
              >
                <div class="patient-referred-to--sub-header">
                  Community Services
                </div>
                <div
                  class="ic24-flex-column ic24-flex-gap-small ic24-flex-wrap"
                >
                  <RadioButtonObj
                    v-for="opt in communityServiceOptions"
                    :key="opt.value"
                    v-model="patientReferredToLocal.referredTo"
                    :option-value="opt"
                    :label="opt.description"
                    @onChanged="onUserChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="ic24-flex-column"
        v-if="
          patientReferredToLocal.referredTo.id === '17-Other Secondary Care'
        "
      >
        Secondary Care Referral: Other - please provide more details.
        <textarea
          v-model="patientReferredToLocal.referredText"
          class="patient-referred-to--textarea ic24-full-width-x"
          @change="onUserChange"
        ></textarea>
      </div>

      <!--GP section-->
      <div class="ic24-flex-column ic24-flex-gap">
        <div class="patient-referred-to--header">
          Important - further actions/information which will be sent to the GP
        </div>

        <!--      {{ buttonFurtherActionOptions }}-->

        <div class="ic24-flex-row ic24-flex-gap">
          <div class="patient-referred-to--sub-header">GP Further Action</div>

          <div
            class="ic24-flex-row ic24-flex-gap-large ic24-flex-wrap"
            style="flex-grow: 1"
          >
            <RadioButtonObj
              v-model="patientReferredToLocal.furtherActionGP"
              :option-value="
                buttonFurtherActionOptions['1-Patient Advised Contact Own GP']
              "
              :label="
                buttonFurtherActionOptions['1-Patient Advised Contact Own GP']
                  .description
              "
              @onChanged="onUserChange"
            />

            <RadioButtonObj
              v-model="patientReferredToLocal.furtherActionGP"
              :option-value="
                buttonFurtherActionOptions['2-GP to contact Patient']
              "
              :label="
                buttonFurtherActionOptions['2-GP to contact Patient']
                  .description
              "
              @onChanged="onUserChange"
            />

            <RadioButtonObj
              v-model="patientReferredToLocal.furtherActionGP"
              :option-value="{
                id: '',
                value: '',
                description: ''
              }"
              label="None"
              @onChanged="onUserChange"
            />
            <!--            <textarea-->
            <!--              v-if="patientReferredToLocal.furtherActionGP.id !== ''"-->
            <!--              v-model="patientReferredToLocal.furtherActionGPText"-->
            <!--              class="patient-referred-to&#45;&#45;textarea ic24-full-width"-->
            <!--              placeholder="E.g. recommend to book patient for an ultra sound."-->
            <!--              @change="onUserChange"-->
            <!--            ></textarea>-->
          </div>
        </div>

        <textarea
          v-if="patientReferredToLocal.furtherActionGP.id !== ''"
          v-model="patientReferredToLocal.furtherActionGPText"
          class="patient-referred-to--textarea ic24-full-width"
          placeholder="E.g. recommend to book patient for an ultra sound."
          @change="onUserChange"
        ></textarea>
      </div>
      <!--/GP section-->
    </div>

    <!--    <div-->
    <!--      v-if="patientReferredToLocal.referredTo.id === '17-Other Secondary Care'"-->
    <!--    >-->
    <!--      Secondary Care Referral: Other - please provide more details.-->
    <!--      <textarea-->
    <!--        v-model="patientReferredToLocal.referredText"-->
    <!--        class="patient-referred-to&#45;&#45;textarea ic24-full-width"-->
    <!--        @change="onUserChange"-->
    <!--      ></textarea>-->
    <!--    </div>-->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import {
  patientReferredToButtonOption,
  patientReferredToFurtherActionButtonOption,
  PatientReferredToType
} from "@/calls/details/complete/components/patient-referred-to/patient-referred-to-models";
import { ISimpleButtonInputValue } from "@/calls/details/complete/simple-button-selector-models";
import {
  IPatientReferredTo,
  IStep
} from "@/calls/details/complete/complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import RadioButtonObj from "@/common/ui/fields/RadioButtonObj.vue";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "PatientReferredTo",
  components: { CompleteStepHeader, RadioButtonObj },
  props: {
    step: {
      type: Object as PropType<IStep<"PATIENT_REFERRED_TO">>,
      required: true
    },
    value: {
      type: Object as PropType<IPatientReferredTo>,
      required: true
    }
  },
  setup(
    props: { step: IStep<"PATIENT_REFERRED_TO">; value: IPatientReferredTo },
    context: SetupContext
  ) {
    const patientReferredToLocal = ref<IPatientReferredTo>(
      simpleObjectClone(props.value)
    );

    const buttonOptions = patientReferredToButtonOption;
    const buttonFurtherActionOptions = patientReferredToFurtherActionButtonOption;

    const noFurtherActionOptions: ISimpleButtonInputValue<
      PatientReferredToType,
      PatientReferredToType
    >[] = [
      buttonOptions["1-Advice Only"],
      buttonOptions["2-Advice Prescription"],
      buttonOptions["3-Appointment Declined"],
      buttonOptions["4-Duplicate Case"],
      buttonOptions["5-Patient Deceased"]
    ];

    const primaryCareOptions: ISimpleButtonInputValue<
      PatientReferredToType,
      PatientReferredToType
    >[] = [
      buttonOptions["7-Base Appointment"],
      buttonOptions["8-UTC"],
      buttonOptions["9-OOH Home Visit"]
    ];

    const secondaryCareOptions: ISimpleButtonInputValue<
      PatientReferredToType,
      PatientReferredToType
    >[] = [
      buttonOptions["10-Emergency Department"],
      buttonOptions["11-SDEC"],
      buttonOptions["12-Surgical"],
      buttonOptions["13-Medical"],
      buttonOptions["14-Paediatric"],
      buttonOptions["15-OG Ward"],
      buttonOptions["16-Early Pregnancy Unit"],
      buttonOptions["17-Other Secondary Care"]
    ];

    const communityServiceOptions: ISimpleButtonInputValue<
      PatientReferredToType,
      PatientReferredToType
    >[] = [
      buttonOptions["19-Community Nurses"],
      buttonOptions["20-Palliative Care"],
      buttonOptions["21-Mental Health Team"],
      buttonOptions["22-Dental Service"],
      buttonOptions["23-Other Community Services"]
    ];

    function onUserChange() {
      console.log(
        "PatientReferredTo.onUserChange",
        patientReferredToLocal.value
      );
      context.emit("input", simpleObjectClone(patientReferredToLocal.value));
    }

    return {
      patientReferredToLocal,
      buttonOptions,
      noFurtherActionOptions,
      primaryCareOptions,
      secondaryCareOptions,
      communityServiceOptions,
      buttonFurtherActionOptions,
      onUserChange
    };
  }
});
</script>

<style scoped>
.patient-referred-to--header {
  font-weight: bold;
  background: rgb(240, 248, 255);
  padding: var(--ic24-flex-gap-small);
  height: 30px;
  border: solid 1px rgb(211, 211, 211);
  align-content: center;
  text-align: center;
}

.patient-referred-to--sub-col {
  width: 150px;
}

.patient-referred-to--sub-header {
  font-weight: bold;
  color: var(--slate-500);
  flex-shrink: 0;
  //width: 150px;
}

.patient-referred-to--sub-header {
  font-weight: bold;
  flex-shrink: 0;
  width: 135px;
}

.patient-referred-to--textarea {
  padding: 4px;
  height: 40px;
  min-height: 40px;
  resize: vertical;
  width: 990px;
}

.patient-referred-to--step label {
  //width: 180px;
  flex-shrink: 0;
}
</style>
