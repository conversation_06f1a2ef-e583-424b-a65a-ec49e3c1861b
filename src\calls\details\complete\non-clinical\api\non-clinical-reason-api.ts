import https from "@/common/https";
import { <PERSON><PERSON><PERSON>_CONFIG } from "@/common/config/config-";
import { LegacyKeywordServerResponse } from "@/common/cleo-legacy-models";

export function getNonClinicalReasons(
  service?: string
): Promise<LegacyKeywordServerResponse> {
  if (process.env.NODE_ENV === "development") {
    return Promise.resolve({
      "Cancel Case": {
        unid: "8B0730CD3A33826380258C4A0057CE40",
        codeID3: "",
        description: "Cancel Case",
        KeywordService: "",
        codeID2: "",
        codeID1: "",
        keyType: "NON_CLINICAL_REASON"
      },
      "Non Clinical Input required": {
        unid: "9EF8DCC67F188ACB80258C59003AD217",
        codeID3: "",
        description: "Non Clinical Input required",
        KeywordService: "",
        codeID2: "",
        codeID1: "",
        keyType: "NON_CLINICAL_REASON"
      },
      Other: {
        unid: "9EF8DCC67F188ACB80258C59003AD217",
        codeID3: "",
        description: "Other",
        KeywordService: "",
        codeID2: "",
        codeID1: "",
        keyType: "NON_CLINICAL_REASON"
      }
    });
  }

  return https.get(
    CLEO_CONFIG.CLEO.XCLEO_PATH +
      "/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=NON_CLINICAL_REASON" +
      (service ? "&service=" + service : ""),
    {
      responseType: "json"
    }
  );
}
