import {
  DynamicQuestionsOutput,
  DynamicQuestionsState,
  Question
} from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { UnwrapRef } from "@vue/composition-api";
import { deepCloneObject, simpleObjectClone } from "@/common/common-utils";

export function useDynamicQuestions(
  stateInject?: UnwrapRef<DynamicQuestionsState>
) {
  const state:
    | DynamicQuestionsState
    | UnwrapRef<DynamicQuestionsState> = stateInject
    ? stateInject
    : {
        questions: [],
        answers: {}
      };

  /**
   *
   * @param questions
   */
  function init(questions: Question[]) {
    const parsedQuestions: Question[] = deepCloneObject(questions).map(
      question => {
        //  Question condition may have come from server as a string, turn to function
        if (question.condition && typeof question.condition === "string") {
          question.condition = new Function(
            "answers",
            `return ${question.condition};`
          ) as any;
        }
        return question;
      }
    );

    state.questions = deepCloneObject(parsedQuestions);
  }

  /**
   *
   * @param question
   */
  function onQuestionAnswered(question: Question) {
    state.answers[question.id] = question.value;
    setVisibility();
  }

  /**
   *
   */
  function setVisibility() {
    state.questions.forEach(question => {
      // state.questionsVisibility[question.id] = question.visible;

      if (typeof question.value === "undefined") {
        if (question.type === "checkbox") {
          question.value = [];
        } else {
          question.value = "";
        }
      }

      if (question.condition) {
        if (question.condition && typeof question.condition === "string") {
          question.condition = new Function(
            "answers",
            `return ${question.condition};`
          ) as any;
        }

        const condition = question.condition as (
          answers: Record<string, unknown>
        ) => boolean;

        const isVisible = condition(state.answers);

        question.visible = isVisible;
        if (!isVisible) {
          // remove property from answers
          delete state.answers[question.id];
        }
        // state.questionsVisibility[question.id] = question.visible;
      }
    });
  }

  /**
   * Returns visible questions that have not been answered.
   */
  function validateAnswers(): Question[] {
    const answers = state.answers;
    const questions = state.questions;
    const visibleQuestions = questions.filter(question => question.visible);
    const mandatoryQuestions = visibleQuestions.filter(
      question => question.mandatory
    );

    return mandatoryQuestions.filter(question => !answers[question.id]);
  }

  /**
   * Returns the questions that are visible
   */
  function getOutput(): DynamicQuestionsOutput {
    const stateClone = simpleObjectClone(state);
    return {
      ...stateClone,
      isValid: validateAnswers().length === 0,
      questionsThatNeedAnswer: validateAnswers()
    };
  }

  return {
    state,

    init,
    onQuestionAnswered,
    validateAnswers,
    getOutput
  };
}
