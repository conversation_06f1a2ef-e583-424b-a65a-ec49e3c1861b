import { GUID } from "@/common/common-models";
import { CommonService } from "@/common/common-service";
const commonService = new CommonService();

export class PaccsLegacyFormController {
  public readonly domConsultFields = ["CallObjective"];
  public currentClinicianNotes = "";

  /**
   *
   * @param shouldHide
   */
  public pathwaysHideLegacyDomElements(shouldHide: boolean): void {
    const displayText = shouldHide ? "none" : "";
    const editUserAddressButton = document.getElementById("id_ECD");
    if (editUserAddressButton) {
      editUserAddressButton.style.display = displayText;
    }
    if (window.dijit && displayText === "none") {
      const startPathwaysMenuButton = window.dijit.byId("btnNHSPathways");
      if (startPathwaysMenuButton) {
        startPathwaysMenuButton.domNode.style.display = displayText;
      }
    }
  }

  public setPaccsIsReady(isReady: boolean): void {
    window.CallControllerClient.paccs.isReady = isReady;
  }

  public setPaccsHasStarted(hasStarted: boolean, pathwaysCaseId: string): void {
    window.CallControllerClient.paccs.hasStarted = hasStarted;

    //  Set on back end a PACCS has ever taken place.
    window.CallControllerClient.submitSimpleFields({
      PACCS_EVER: "1",
      PathwaysCaseId: pathwaysCaseId
    });
  }

  /**
   *
   */
  public getPaccsPathwaysCaseIdFromCall(): GUID | "" {
    const pathwaysCaseId = commonService.valueAsString(
      window.CallControllerClient.getFieldValue("PathwaysCaseId")
    );
    // const pathwaysCaseIdItkIn = commonService.valueAsString(CallControllerClient.getValueFromCallJson(
    //   "PathwaysCaseId_ITK_IN"
    // ));
    const pathwaysCaseIdFromItk = commonService.valueAsString(
      window.CallControllerClient.getValueFromCallJson(
        "PathwaysCaseId_FROM_ITK"
      )
    );
    const containsRehydratedCase = commonService.valueAsString(
      window.CallControllerClient.getValueFromCallJson("ContainsRehydratedCase")
    );
    const paccsEver = commonService.valueAsString(
      window.CallControllerClient.getValueFromCallJson("PACCS_EVER")
    );

    if (
      pathwaysCaseIdFromItk.length > 0 &&
      containsRehydratedCase === "1" &&
      paccsEver !== "1"
    ) {
      //  We have a PW come inbound from external provider, says rehydrate it
      //  but we can't, as we don't have the PW.  So, move current to PathwaysCaseId_ITK_IN
      //  clear out pathwaysCaseId and get a new one.
      return "";
    }
    return pathwaysCaseId;
  }

  public setPathwaysCaseIdOnCallForm(pathwaysCaseId: GUID): void {
    window.CallControllerClient.setFieldValue("PathwaysCaseId", pathwaysCaseId);
  }
}
