export interface IVehicleLegacy {
  Status: "Active" | "Inactive";
  CarName: string;
  unid: string;

  Status_TomTom: string;
  CarSuffix: string;
  CarID: string;
  Permalink: string;
  CARNAME_DISP: string;
  BaseCode: string;
  Base: string;
}

export interface IVehicle {
  id: number;
  name: string;
  isActive: boolean;
  isTomTomActive: boolean;
  Long: number;
  Lat: number;
  unid: string;
}

export interface ITomTomVehicle {
  objectno: string; //  E.g. Delta 50
  objectuid: string; //  some sort of unid
  objectstate: number; // all set to 3...?!
  latitude: number;
  longitude: number;
  lineardistance: number; //  E.g.5109378  not really sure what this is, they are all pretty much the same
}
