<template>
  <div class="ic24-flex-column button-filters">
    <span class="text-generic">
      Select one or more filters to apply to the case queue.
    </span>

    <hr class="ic24-hr" />

    <div class="ic24-flex-row ic24-flex-gap-xxlarge">
      <div class="ic24-flex-column ic24-flex-gap">
        <span class="text-generic">Select all case types to apply:</span>
        <div class="ic24-flex-row ic24-flex-gap-large">
          <div class="ic24-flex-column ic24-flex-gap">
            <Ic24Button
              title="All"
              @click="reset"
              class="button-filters--standard"
              :class="
                buttonFiltersController.state.gridFilterUserInput
                  .CLEO_CLIENT_SERVICE.length === 0
                  ? 'button-filters--selected'
                  : ''
              "
            />

            <Ic24Button
              v-for="cleoClientService in Object.keys(
                cleoClientServiceMapWithoutAmbulanceAndEDValidation
              )"
              :title="cleoClientServiceMap[cleoClientService]"
              :key="cleoClientService"
              @click="toggleCleoClientService(cleoClientService)"
              class="button-filters--standard"
              :class="
                isCleoClientServiceSelected(cleoClientService)
                  ? 'button-filters--selected'
                  : ''
              "
            />
          </div>

          <div class="ic24-flex-column ic24-flex-gap">
            <Ic24Button
              :title="cleoClientServiceMap['ED Validation']"
              @click="toggleCleoClientService('ED Validation')"
              class="button-filters--standard"
              :class="
                isCleoClientServiceSelected('ED Validation')
                  ? 'button-filters--selected'
                  : ''
              "
            />

            <Ic24Button
              :title="cleoClientServiceMap['C3/C4 Validation']"
              @click="toggleCleoClientService('C3/C4 Validation')"
              class="button-filters--standard"
              :class="
                isCleoClientServiceSelected('C3/C4 Validation')
                  ? 'button-filters--selected'
                  : ''
              "
            />

            <Ic24Button
              :title="cleoClientServiceMap['Follow Up']"
              @click="toggleCleoClientService('Follow Up')"
              class="button-filters--standard"
              :class="
                isCleoClientServiceSelected('Follow Up')
                  ? 'button-filters--selected'
                  : ''
              "
            />

            <!--            <Ic24Button-->
            <!--              title="Ambulance !!"-->
            <!--              @click="buttonFiltersController.toggleAmbulance()"-->
            <!--              class="button-filters&#45;&#45;standard"-->
            <!--              :class="-->
            <!--                buttonFiltersController.state.gridFilterUserInput-->
            <!--                  .AMBULANCE_CLEO_CLIENT_SERVICES-->
            <!--                  ? 'button-filters&#45;&#45;selected'-->
            <!--                  : ''-->
            <!--              "-->
            <!--            />-->

            <!--            <Ic24Button-->
            <!--              title="ED Validation !!"-->
            <!--              @click="buttonFiltersController.toggleEDValidation()"-->
            <!--              class="button-filters&#45;&#45;standard"-->
            <!--              :class="-->
            <!--                buttonFiltersController.state.gridFilterUserInput-->
            <!--                  .ED_VALIDATION_CLEO_CLIENT_SERVICES-->
            <!--                  ? 'button-filters&#45;&#45;selected'-->
            <!--                  : ''-->
            <!--              "-->
            <!--            />-->

            <!--            <Ic24Button-->
            <!--              title="(o) Toxic Ingestion and All Other Dx Codes"-->
            <!--              @click="buttonFiltersController.toggleToxicIngestionAndEmpty()"-->
            <!--              class="button-filters&#45;&#45;standard"-->
            <!--              :class="-->
            <!--                buttonFiltersController.state.gridFilterUserInput-->
            <!--                  .TOXIC_INGESTION_AND_EMPTY-->
            <!--                  ? 'button-filters&#45;&#45;selected'-->
            <!--                  : ''-->
            <!--              "-->
            <!--            />-->

            <Ic24Button
              title="Toxic Ingestion and All Other Dx Codes"
              @click="toggleCleoClientService('TOXIC INGESTION')"
              class="button-filters--standard"
              :class="
                isCleoClientServiceSelected('TOXIC INGESTION')
                  ? 'button-filters--selected'
                  : ''
              "
            />

            <!--            <Ic24Button-->
            <!--              title="CAS (all other DX)"-->
            <!--              @click="toggleDxCodeTypes('Other')"-->
            <!--              class="button-filters&#45;&#45;standard"-->
            <!--              :class="isAllOtherDxSelected ? 'button-filters&#45;&#45;selected' : ''"-->
            <!--            />-->
          </div>
        </div>
      </div>

      <div class="ic24-flex-column ic24-flex-gap">
        <span class="text-generic">Now select all filters that apply:</span>
        <Ic24Button
          title="My Assigned Cases and Paramedics"
          @click="buttonFiltersController.toggleMyCasesParamedicOnScene()"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput
              .MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled === true
              ? 'button-filters--selected'
              : ''
          "
        />

        <Ic24Button
          title="Not Traced and Verified"
          @click="toggleTracedAndVerified(false)"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput
              .PDS_TRACED_AND_VERIFIED === false
              ? 'button-filters--selected'
              : ''
          "
        />

        <Ic24Button
          title="Unassigned"
          @click="setAssignedDoctor(false)"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput.ASSIGNED_TO ===
            false
              ? 'button-filters--selected'
              : ''
          "
        />

        <Ic24Button
          title="Assigned"
          @click="setAssignedDoctor(true)"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput.ASSIGNED_TO ===
            true
              ? 'button-filters--selected'
              : ''
          "
        />

        <Ic24Button
          title="Breached"
          @click="toggleBreached(true)"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput.BREACHED === true
              ? 'button-filters--selected'
              : ''
          "
        />

        <Ic24Button
          title="Requires Validation"
          @click="buttonFiltersController.toggleRequiresValidation()"
          class="button-filters--standard"
          :class="
            buttonFiltersController.state.gridFilterUserInput
              .REQUIRES_VALIDATION === true
              ? 'button-filters--selected'
              : ''
          "
        />

        <!--        <Ic24Button-->
        <!--          title="Follow Up"-->
        <!--          @click="buttonFiltersController.toggleFollowUp()"-->
        <!--          class="button-filters&#45;&#45;standard"-->
        <!--          :class="-->
        <!--            buttonFiltersController.state.gridFilterUserInput.FOLLOW_UP === true-->
        <!--              ? 'button-filters&#45;&#45;selected'-->
        <!--              : ''-->
        <!--          "-->
        <!--        />-->
      </div>
    </div>

    <hr class="ic24-hr" style="margin-top: var(--ic24-flex-gap-large)" />

    <div class="ic24-flex-row" style="margin-top: var(--ic24-flex-gap)">
      <Ic24Button
        title="Cancel"
        @click="cancel"
        class="button-filters--standard button-bottom"
        style="width: 125px"
      />

      <div class="ic24-flex-row ic24-flex-gap ic24-flex-row--end">
        <Ic24Button
          title="Reset Filters"
          @click="reset"
          class="button-filters--standard button-bottom"
          style="width: 125px"
        />
        <Ic24Button
          title="Apply"
          @click="input"
          class="button-filters--selected button-bottom"
          style="width: 125px"
        />
      </div>
    </div>

    <!--    {{ buttonFiltersController.state }}-->
    <!--        {{ buttonFiltersController.state.gridFilterUserInput.CLEO_CLIENT_SERVICE }}-->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
  computed,
  reactive
} from "@vue/composition-api";
import {
  DxCodeGroupType,
  IGridFilterUserInput
} from "@/calls/grids/grid-filter/grid-filter-models";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";
import {
  CLEO_CLIENT_SERVICE,
  CLEO_CLIENT_SERVICE_MAP
} from "@/common/common-models";
import { useButtonFiltersController } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";

export default defineComponent({
  // type inference enabled
  name: "ButtonFilters",
  computed: {
    CLEO_CLIENT_SERVICE_MAP() {
      return CLEO_CLIENT_SERVICE_MAP;
    }
  },
  components: { Ic24Button },
  props: {
    gridFilterUserInput: {
      type: Object as PropType<IGridFilterUserInput>,
      required: true
    },
    applyFiltersImmediately: {
      type: Boolean,
      default: false
    }
  },
  setup(
    props: {
      gridFilterUserInput: IGridFilterUserInput;
      applyFiltersImmediately: boolean;
    },
    context: SetupContext
  ) {
    const gridFilterService = new GridFilterService();

    const buttonFiltersController = useButtonFiltersController();

    // use CLEO_CLIENT_SERVICE_MAP but create a local instance and add ED Validation to it.
    const cleoClientServiceMap = reactive({
      ...CLEO_CLIENT_SERVICE_MAP,
      ["ED Validation"]: "ED Validation",
      ["C3/C4 Validation"]: "Ambulance",
      ["Follow Up"]: "Follow Up"
    });

    // create computed property of cleoClientServiceMap without ED Validation or C3/C4 Validation
    const cleoClientServiceMapWithoutAmbulanceAndEDValidation = computed(() => {
      const excludedServices = [
        "ED Validation",
        "C3/C4 Validation",
        "Follow Up",
        "FOLLOW_UP",
        "TOXIC INGESTION"
      ];
      return Object.fromEntries(
        Object.entries(cleoClientServiceMap).filter(
          ([key]) => !excludedServices.includes(key)
        )
      );
    });

    buttonFiltersController.init({
      gridFilterUserInput: gridFilterService.factoryGridFilterUserInput()
    });

    // watch for changes to the gridFilterUserInput prop and update the internal state
    watch(
      () => props.gridFilterUserInput,
      (newValue: IGridFilterUserInput) => {
        // gridFilterUserInputInternal.value = simpleObjectClone(newValue);

        buttonFiltersController.init({
          gridFilterUserInput: newValue
        });
      }
    );

    function reset() {
      // gridFilterUserInputInternal.value = gridFilterService.factoryGridFilterUserInput();
      buttonFiltersController.reset();
      // input();
    }

    function toggleCleoClientService(cleoClientService: CLEO_CLIENT_SERVICE) {
      buttonFiltersController.toggleCleoClientService(cleoClientService);
      inputChanged();
    }

    function toggleDxCodeTypes(dxType: DxCodeGroupType) {
      buttonFiltersController.toggleDxCodeTypes(dxType);
      inputChanged();
    }

    function toggleToxicIngestionAndEmpty() {
      buttonFiltersController.toggleToxicIngestionAndEmpty();
      inputChanged();
    }

    // function toggleDxCodes(dxCodesToToggle: Uppercase<string>[]) {
    //   buttonFiltersController.toggleDxCodes(dxCodesToToggle);
    //   inputChanged();
    // }

    /**
     * Toggles the PDS_TRACED_AND_VERIFIED filter.  null is the default value: ALL
     * @param show
     */
    function toggleTracedAndVerified(show: boolean) {
      buttonFiltersController.toggleTracedAndVerified(show);
      inputChanged();
    }

    function setAssignedDoctor(isAssignedDoctor: boolean) {
      buttonFiltersController.setAssignedDoctor(isAssignedDoctor);
      inputChanged();
    }

    function toggleBreached(isBreached: boolean) {
      buttonFiltersController.toggleBreached(isBreached);
      inputChanged();
    }

    function inputChanged() {
      if (!props.applyFiltersImmediately) {
        return;
      }
      input();
    }

    function cancel() {
      context.emit("cancel");
    }

    function input() {
      console.log(
        "ButtonFilters.input()",
        buttonFiltersController.state.gridFilterUserInput
      );
      context.emit("input", buttonFiltersController.state.gridFilterUserInput);
    }

    // const isDxSelected = computed(() => {
    //   return (dxCode: string) => {
    //     return buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.includes(
    //       dxCode
    //     );
    //   };
    // });

    const isDxSelectedAmbulanceValidation = computed(() => {
      const ambDxs =
        buttonFiltersController.state.dxGroupTypes["Ambulance Validation"];

      // check is buttonFiltersController.state.gridFilterUserInput.DX.dxCodes includes any of the ambulance validation dx codes
      return (
        buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.length >
          0 &&
        ambDxs.some(dxCode =>
          buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.includes(
            dxCode
          )
        ) &&
        buttonFiltersController.state.gridFilterUserInput.DX.include === true
      );
    });

    const isDxSelectedToxicIngestion = computed(() => {
      const toxDxs =
        buttonFiltersController.state.dxGroupTypes["Toxic Ingestion"];
      // check is buttonFiltersController.state.gridFilterUserInput.DX.dxCodes includes any of the toxic ingestion dx codes
      return (
        buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.length >
          0 &&
        toxDxs.some(dxCode =>
          buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.includes(
            dxCode
          )
        ) &&
        buttonFiltersController.state.gridFilterUserInput.DX.include === true
      );
    });

    const isAllOtherDxSelected = computed(() => {
      return (
        buttonFiltersController.state.gridFilterUserInput.DX.dxCodes.length >
          0 &&
        buttonFiltersController.state.gridFilterUserInput.DX.include === false
      );
    });

    const isCleoClientServiceSelected = computed(() => {
      return (cleoClientService: CLEO_CLIENT_SERVICE) => {
        return buttonFiltersController.state.gridFilterUserInput.CLEO_CLIENT_SERVICE.includes(
          cleoClientService.toUpperCase()
        );
      };
    });

    return {
      buttonFiltersController,

      isAllOtherDxSelected,
      isDxSelectedAmbulanceValidation,
      isDxSelectedToxicIngestion,
      isCleoClientServiceSelected,
      cleoClientServiceMap,
      cleoClientServiceMapWithoutAmbulanceAndEDValidation,

      cancel,
      input,
      reset,
      toggleCleoClientService,
      toggleDxCodeTypes,
      toggleTracedAndVerified,
      setAssignedDoctor,
      toggleBreached
    };
  }
});
</script>

<style>
.button-filters .text-generic {
  font-family: "Arial";
  font-weight: 700;
  font-size: 14px;
  line-height: 40px;
  vertical-align: middle;
  color: var(--cleo-dark-blue);
}

.button-filters hr {
  margin: 0;
  border-top: 1px solid var(--cleo-dark-blue);
}

.button-filters . {
  padding: 10px;
}

.button-filters button > span {
  font-family: "Arial";
  font-weight: 700;
  font-size: 14px;
  color: var(--cleo-dark-blue);
}

.button-filters .button-filters--standard {
  background: linear-gradient(
    to bottom,
    #e0e8f0,
    #a2c6e8
  ); /* Light blue gradient */
  color: #003366 !important;
  border: 1px solid #706f6f;
  width: 280px; /* Adjust width as needed */
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0px 4px 4px 0px #00000040;
  justify-content: flex-start;
}

.button-filters .button-bottom {
  justify-content: center;
}

.button-filters .button-filters--selected {
  //background: red;
  background: var(--cleo-dark-blue);
  box-shadow: none;
}

.button-filters .button-filters--selected > span {
  color: white;
}
</style>
