import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import {
  IOutcomes,
  IStep,
  IUserResponseGeneric
} from "@/calls/details/complete/complete-models";
import { ExitReason } from "@/calls/details/complete/failedcontact/exitreasons/exit-reasons-models";

describe("CAS Clini useCompleteController Save-Return", () => {
  it("CAS Clini Save and Return -- No further action", () => {
    const callDetail: ICallDetail = factoryCallDetail();

    callDetail.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "SAVE_AND_RETURN_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "SAVE_AND_RETURN_PROCESS"
    );
    expect(controller.state.processName).toBe("SAVE_AND_RETURN_CAS_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(false);
    expect(controller.state.autoProgress).toBe(true);
    expect(controller.state.userOptions.exitReason.forUser.length).toBe(3);

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(5);

    expect(stepNames[0]).toBe("EXIT_REASON");
    expect(stepNames[1]).toBe("HOW_WAS_CASE_MANAGED");
    expect(stepNames[2]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[3]).toBe("FAILED_CONTACT_WARNING");
    expect(stepNames[4]).toBe("UNKNOWN");

    expect(controller.state.currentStep).toBe("EXIT_REASON");

    // controller.onExitReasonSelected();
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("EXIT_REASON");
    expect(controller.state.validationMessages.length).toBe(1);
    expect(controller.state.validationMessages[0].id).toBe(
      "EXIT_REASON__NOT_SELECTED"
    );

    controller.goto("BACK");
    expect(controller.state.currentStep).toBe("EXIT_REASON");

    controller.onExitReasonSelected({
      id: "FURTHER_ACTION_REQUIRED",
      description: "Further action required",
      value: "FURTHER_ACTION_REQUIRED"
    });

    expect(controller.state.userResponse.exitReason.id).toBe(
      "FURTHER_ACTION_REQUIRED"
    );

    expect((controller.state.steps.EXIT_REASON as IStep<any>).isValid).toBe(
      true
    );

    expect(controller.state.isProcessComplete).toBe(true);
    expect(controller.state.finalAction).toBe("FURTHER_ACTION_REQUIRED");
  });
});
