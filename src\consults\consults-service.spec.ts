import { ConsultsService } from "@/consults/consults-service";
import { IConsultServerOutbound } from "@/consults/consult-models";
import { IKeywordsRefData, IKeywordsRefDataKey } from "@/keywords/keywords-models";
import { KeywordService } from "@/keywords/keyword-service";

const consultsService: ConsultsService = new ConsultsService();
const keywordService: KeywordService = new KeywordService();

describe("ConsultService", () => {
  it("mapConsultFromServer", () => {
    const consultServer: IConsultServerOutbound = {
      Id: 138,
      Callno: 2000245755,
      BaseId: 64,
      ClientId: "00000000-0000-0000-0000-000000000000",
      GPAssignedId: "BSLK-AVXE2E",
      EpsScriptGuid: "00000000-0000-0000-0000-000000000000",
      UniqueUserId: "CN=<PERSON>/O=staging",
      Details: "",
      Objective:
        " [Observations] BP: 90 / 90, Temp: 90.90 C, Pulse: 90, Sats1: 90, Resp: 90, [object HTMLInputElement]",
      Plan: "",
      Assessment: "Test NEWS2",
      UrgentAtStart: false,
      UrgentAtEnd: false,
      IsUserUsingSmartCard: true,
      TeleAssessment: false,
      PatientDobChecked: false,
      Presenting: "",
      PastHistory: "",
      Representative: "",
      RepresentativeTypeId: 0,
      RedFlags: "",
      Diagnosis: "",
      Management: "",
      Empowering: "",
      SafetyNet: "",
      ClassAtStartId: 4,
      ClassAtEndId: 4,
      TimeStart: "2020-02-28T16:10:51+00:00",
      TimeEnd: "2020-02-28T16:11:41+00:00",
      RoleId: 9,
      ClientDevice: {
        Id: 144,
        DeviceName: "PC",
        MacAddress: "",
        MachineSid: "",
        Active: true
      },
      LegacyData: {
        Id: 144,
        GpAssignedType: "DOCTOR",
        CasPriority: "",
        CasPriorityClass: ""
      },
      FailedContactCodeId: 0,
      Observation: null,
      Prescriptions: []
    };

    const keywordsRefData: IKeywordsRefData = {
      bases: [{
        Id: 64,
        Name: "Ashford"
      }]
    };

    const keywordsRefDataKey: IKeywordsRefDataKey = keywordService.mapKeywordsRefData(keywordsRefData);

    expect(consultsService.mapConsultFromServer(consultServer, keywordsRefDataKey).Base.name).toBe("Ashford");
  });
});
