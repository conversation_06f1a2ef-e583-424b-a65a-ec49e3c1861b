import {
  ITomTomVehicle,
  IVehicle,
  IVehicleLegacy
} from "@/vehicles/vehicles-models";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";

const cleoCommonService = new CleoCommonService();

export class VehicleService {
  public factoryVehicle(): IVehicle {
    return {
      Lat: 0,
      Long: 0,
      id: 0,
      isActive: false,
      isTomTomActive: false,
      name: "",
      unid: ""
    };
  }

  public mapVehicleLegacyToVehicle(vehicleLegacy: IVehicleLegacy): IVehicle {
    return {
      Lat: 0,
      Long: 0,
      id: -1,
      isActive: false,
      isTomTomActive: vehicleLegacy.Status === "Active",
      name: vehicleLegacy.CarName,
      unid: vehicleLegacy.unid
    };
  }

  public mapVehicleTomTomToVehicle(vehicleTomTom: ITomTomVehicle): IVehicle {
    //  turn Tomtom 52468100 / 1743276 => 52.4681 / 1.743276
    return {
      Lat: vehicleTomTom.latitude / 1000000,
      Long: vehicleTomTom.longitude / 1000000,
      id: -1,
      isActive: false,
      isTomTomActive: true,
      name: vehicleTomTom.objectno,
      unid: vehicleTomTom.objectuid
    };
  }

  public getVehicleNameDisplay(vehicle: IVehicle): string {
    return cleoCommonService.formatUserDominoName(vehicle.name, "CN");
  }
}
