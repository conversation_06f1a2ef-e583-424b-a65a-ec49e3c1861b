import * as signalR from "@microsoft/signalr";
import { CLEO_CONFIG } from "../common/config/config-";
import { HubConnection } from "@microsoft/signalr";
import { loggerInstance } from "@/common/Logger";
import { MessageHeaders } from "@microsoft/signalr/src/IHubProtocol";
import { LoginService } from "@/login/login-service";
import { SocketGroup } from "@/calls/grids/grid-models";
import {
  useBroadcastUserMessage,
  useLogServerMessage
} from "@/common/common-app";

loggerInstance.log("..............process.env:", process.env);

const cleoConfig = CLEO_CONFIG;

export const SOCKET_ACTIONS = {
  ON_CASE_NEW: "OnNewCase",
  ON_CASE_UPDATED: "OnCaseUpdated",
  ON_CASE_LOCKED: "OnCaseLocked",
  ON_CASE_UNLOCKED: "OnCaseUnlocked",
  ON_CASE_CLOSED: "OnCaseClosed",
  ON_CASE_REMOVED: "OnCaseRemoved"
};
const socketKeys = Object.values(SOCKET_ACTIONS);
export type SocketActionType = typeof socketKeys[number];

const socketUrl: string = cleoConfig.SOCKET_URL ? cleoConfig.SOCKET_URL : "";

export type SOCKET_NAME = "registerForEpisodeOfCareQueue";

export type SOCKET_STATUS =
  | "Not Connected"
  | "Re-connecting"
  | "Connecting"
  | "Connected"
  | "Error";

export class CleoSocketWrapperController {
  public socket: HubConnection;
  public status: SOCKET_STATUS;
  public isTransitioning = false;
  private SOCKET_ALIVE_POLL_EVERY_MS =
    process.env.NODE_ENV === "development" ? 60000 : 5000;
  private socketAlivePollingTimer: any;

  public isTryingReconnect = false;

  public constructor(headers: MessageHeaders) {
    this.socket = new signalR.HubConnectionBuilder()
      .withUrl(socketUrl, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
        headers: headers,
        accessTokenFactory: () => {
          return new LoginService().getJwtAccessToken();
        }
      })
      .configureLogging(signalR.LogLevel.Trace)
      // .withAutomaticReconnect()
      .build();



    this.status = "Not Connected";

    this.socket.onreconnecting((response: any) => {
      this.status = "Re-connecting";
      loggerInstance.log(
        ">>>>>>>>>> factoryCleoSocket() connection.onreconnecting():",
        response
      );
    });

    this.socket.onreconnected((response: any) => {
      this.status = "Connected";
      loggerInstance.log(
        ">>>>>>>>>> factoryCleoSocket() connection.onreconnected():",
        response
      );
    });

    this.socket.onclose((response: any) => {
      this.status = "Not Connected";
      loggerInstance.log(
        ">>>>>>>>>> factoryCleoSocket() connection.onclose():",
        response
      );
    });
  }

  public startSocket(
    socketName?: SOCKET_NAME,
    socketGroup?: SocketGroup
  ): Promise<any> {
    this.isTransitioning = true;
    clearTimeout(this.socketAlivePollingTimer);
    return this.socket
      .start()
      .then(() => {
        this.status = "Connected";

        this.checkSocketConnected();

        // Connect to whatever pools we want to get "data burst"
        // registerForEpisodeOfCareQueue
        //  IUC_Cas_ALL
        if (socketName && socketGroup) {
          // this.socket
          //   .invoke(socketName, socketGroup)
          //   .then((invokeResponse: any) => {
          //     console.log("invoke() invokeResponse", invokeResponse);
          //   })
          //   .catch((err: Error) => {
          //     console.error(err);
          //   });
        }
      })
      .catch((err: Error) => {
        loggerInstance.log(
          "CleoSocketWrapperController.startSocket error: ",
          err
        );
        useBroadcastUserMessage("startSocket error: " + err.message);
        useLogServerMessage(err, { socketUrl }, "");
      })
      .finally(() => {
        this.isTransitioning = false;
      });
  }

  public stopSocket(): Promise<any> {
    this.isTransitioning = true;
    clearTimeout(this.socketAlivePollingTimer);
    return this.socket
      .stop()
      .then(() => {
        this.status = "Not Connected";
      })
      .catch((err: Error) => {
        this.status = "Error";
        console.warn("CleoSocketWrapperController.stopSocket error: ", err);
      })
      .finally(() => {
        this.isTransitioning = false;
      });
  }

  public get isConnected(): boolean {
    return this.status === "Connected";
  }

  public checkSocketConnected(): void {
    this.socketAlivePollingTimer = window.setTimeout(() => {
      const currentStatus = this.status;
      let signalRStatus = "";

      if (this.socket) {
        signalRStatus = this.socket.state;

        loggerInstance.log(
          "socketAlivePollingTimer: " +
            this.socketAlivePollingTimer +
            new Date() +
            " CleoSocketWrapperController.checkSocketConnected()...currentStatus: " +
            currentStatus +
            ", signalRStatus: " +
            signalRStatus +
            ", isTryingReconnect: " +
            this.isTryingReconnect
        );

        if (this.socket.state === "Disconnected") {
          if (!this.isTryingReconnect) {
            this.isTryingReconnect = true;
            this.startSocket()
              .catch(() => {
                this.checkSocketConnected();
              })
              .finally(() => {
                this.isTryingReconnect = false;
                loggerInstance.log(
                  "CleoSocketWrapperController.checkSocketConnected()...finally() state: " +
                    this.socket.state
                );
                if (this.socket.state === "Disconnected") {
                  this.checkSocketConnected();
                }
              });
            //  Do not get out of this "if" else will trigger another checkSocketConnected()
            return;
          }
        }
      }

      this.checkSocketConnected();
    }, this.SOCKET_ALIVE_POLL_EVERY_MS);
  }
}
