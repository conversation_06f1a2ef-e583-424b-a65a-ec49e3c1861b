export const mockCasOtherQuestions = {
  AUDIT_QUESTION_DATA_CLASS_VISIT: {
    unid: "41684667151DBE1580258C520056FC20",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_CLASS_VISIT",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "verificationOfDeath",\r\n    "type": "radio",\r\n    "label": "Q1 - Was this case for verification of death?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "text",\r\n    "label": "Please enter your GMC# to support the onward Practice process",\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027verificationOfDeath\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q2 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  },
  AUDIT_QUESTION_DATA_OTHER: {
    unid: "3D1FF631F6DFB97680258C520050330E",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_OTHER",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "verificationOfDeath",\r\n    "type": "radio",\r\n    "label": "Q1 - Was this case for verification of death?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "supportGmc",\r\n    "type": "text",\r\n    "label": "Please enter your GMC# to support the onward Practice process",\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027verificationOfDeath\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  },
  AUDIT_QUESTION_DATA_MENTAL_HEALTH: {
    unid: "784134FD649E06AD80258C50005CD416",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_MENTAL_HEALTH",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "primaryPresentingNeed",\r\n    "type": "select",\r\n    "label": "Q2 - What was the primary presenting need of the patient? ",\r\n    "options": [\r\n      "suicidal [thoughts]",\r\n      "self-harm [thoughts]",\r\n      "psychosis",\r\n      "alcohol",\r\n      "anxiety",\r\n      "dementia",\r\n      "depression",\r\n      "illicit drug",\r\n      "low mood",\r\n      "social factors",\r\n      "self-injury [requires medical support]",\r\n      "self-injury [no medical support required]",\r\n      "suicide attempt [requires medical support]",\r\n      "suicide attempt [no medical support required]",\r\n      "emotional dysregulation",\r\n      "medication requested",\r\n      "medication advice",\r\n      "eating disorder",\r\n      "physical health issue",\r\n      "PTSD",\r\n      "overdose"\r\n    ],\r\n    "mandatory": true,\r\n    "value": "",\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "levelOfIntervention",\r\n    "type": "select",\r\n    "label": "Q3 - What level of intervention was provided (as per SOP)? ",\r\n    "options": [\r\n      "guided to another service for assessment",\r\n      "Assessment of known presentation",\r\n      "Information sharing only",\r\n      "Assessment of new presentation"\r\n    ],\r\n    "mandatory": true,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "onwardPathway",\r\n    "type": "select",\r\n    "label": "Q4 - What was the onward pathway? ",\r\n    "options": [\r\n      "Referred for non-blue light pool car response",\r\n      "Referred for SWASFT MH RV (blue light)",\r\n      "Referred to rapid engagement workers (REW)",\r\n      "Signposted back to NHS treatment team CCo",\r\n      "Signposted to own MH treatment team [CMHT, Crisis]",\r\n      "Signposted to VCSE or third sector [Safe Haven/San]",\r\n      "Agreement for self-care [no signposting]",\r\n      "Signposted or referred to GP/New referral MH assessment team [PCLS/Bris Triage]",\r\n      "New referral to crisis team",\r\n      "New referral to MH treatment team [CMHT, Recovery]",\r\n      "New referral to drug and alcohol services",\r\n      "Referred to emergency services [999]",\r\n      "Referred for physical health clinician assessment",\r\n      "Referred for meds by physical health clinician",\r\n      "Referred to UAC",\r\n      "Information requested but not shared",\r\n      "Signposted to self-convey to ED",\r\n      "Professional Liaison"\r\n    ],\r\n    "mandatory": true,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "supportAndLiaisonRequired",\r\n    "type": "radio",\r\n    "label": "Did the call require support and liaison from both physical and mental health expertise in the CAS",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "visible": true\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  },
  AUDIT_QUESTION_DATA_PAEDIATRICS: {
    unid: "A90641849EF52B5B80258C52004F0185",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_PAEDIATRICS",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "hospitalAssessment",\r\n    "type": "radio",\r\n    "label": "Q2 - Did the child require hospital assessment/care?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "couldAssessedNonUrgentCare",\r\n    "type": "radio",\r\n    "label": "Q3 - Could the child have been safely assessed/managed via a non-ED urgent care (SDEC) pathway?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "couldAssessedNonUrgentCareTimeFrame",\r\n    "type": "select",\r\n    "label": "What are the longest timeframes which would have been appropriate for this SDEC assessment?",\r\n    "options": [\r\n      "Same Day",\r\n      "Next Day",\r\n      "2-5 Days",\r\n      "6 or more days"\r\n    ],\r\n    "value": [],\r\n    "mandatory": true,\r\n    "visible": false,\r\n    "condition": "answers[\u0027couldAssessedNonUrgentCare\u0027] \u003d\u003d\u003d \u0027Yes\u0027",\r\n    "clearOnHidden": true\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  },
  AUDIT_QUESTION_DATA_FRAILTY: {
    unid: "182A59441CF19F1A80258C52003C5962",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_FRAILTY",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  },\r\n  {\r\n    "id": "frailtyScore",\r\n    "type": "radio",\r\n    "label": "Q2 – Clinical Frailty score Dropdown options of:",\r\n    "options": [\r\n      "1: Very Fit - People who are robust, active, energetic and motivated. These people commonly exercise regularly. They are among the fittest for their age.",\r\n      "2: Well - People who have no active disease symptoms but are less fit than category 1. Often, they exercise or are very active occasionally, e.g. seasonally.",\r\n      "3: Managing Well - people whose medical problems are well controlled but are not regularly active beyond routine walking.",\r\n      "4: Vulnerable - While not dependant on others for daily help, often symptoms limit activities. A common complaint is being slowed up, and/or being tired during the day.",\r\n      "5: Mildly Frail: These people often have more evident slowing, and need help in high order IADL\u0027s (finances, transportation, heavy housework, medications). Typically, mild frailty progressively impairs shopping and walking outside alone, meal preparation and housework.",\r\n      "6: Moderately Frail: People need help with all outside activities and with keeping house inside, they often have problems with stairs and need help with bathing and might need minimal assistance (cuing standby) with dressing.",\r\n      "7: Severely Frail - Completely dependent for personal care, from whatever cause (physical or cognitive). Even so, they seem stable and not at risk of dying (within 6 months).",\r\n      "8: Very Severely Frail - Completely dependent, approaching the end of life. Typically, they could not recover even from a minor illness",\r\n      "9: Terminally Ill - Approaching the end of life. This category applies to people with a life expectancy \u003c6 months, who are not otherwise evidently frail."\r\n    ],\r\n    "mandatory": true,\r\n    "value": "",\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "admittedToHospital",\r\n    "type": "radio",\r\n    "label": "Q3 - Was the patient admitted to hospital?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true,\r\n    "helpText": "Assessed and managed by Frailty-ACE (Assessment and Co-ordination for Emergency and Urgent Care) seeking alternatives to admission/conveyance for frail patients with urgent/emergency needs"\r\n  },\r\n  {\r\n    "id": "admittedToHospitalReason",\r\n    "type": "select",\r\n    "label": "Why was the patient admitted to hospital?",\r\n    "options": [\r\n      "Patient/Family wish",\r\n      "Clinically Required",\r\n      "No capacity to Support management at home"\r\n    ],\r\n    "mandatory": true,\r\n    "visible": false,\r\n    "condition": "answers[\u0027admittedToHospital\u0027] \u003d\u003d\u003d \u0027Yes\u0027",\r\n    "clearOnHidden": true\r\n  },\r\n  {\r\n    "id": "admittedToHospitalServicesNoCapacity",\r\n    "type": "checkbox",\r\n    "label": "Services you tried but there was no capacity to support",\r\n    "options": [\r\n      "SDEC",\r\n      "UCR",\r\n      "Other Sirona Community service",\r\n      "NHS@Home",\r\n      "Social Care",\r\n      "Mental Health",\r\n      "Geriatrician",\r\n      "Community Care",\r\n      "Other"\r\n    ],\r\n    "value": [],\r\n    "mandatory": true,\r\n    "visible": false,\r\n    "condition": "answers[\u0027admittedToHospital\u0027] \u003d\u003d\u003d \u0027Yes\u0027 \u0026\u0026 answers[\u0027admittedToHospitalReason\u0027] \u003d\u003d\u003d \u0027No capacity to Support management at home\u0027",\r\n    "clearOnHidden": true\r\n  },\r\n  {\r\n    "id": "admittedToHospitalReasonOther",\r\n    "type": "text",\r\n    "label": "Other (Please Specify)",\r\n    "value": "",\r\n    "mandatory": true,\r\n    "visible": false,\r\n    "condition": "answers[\u0027admittedToHospitalServicesNoCapacity\u0027]  \u0026\u0026 answers[\u0027admittedToHospitalServicesNoCapacity\u0027].indexOf(\u0027Other\u0027)!\u003d\u003d-1",\r\n    "clearOnHidden": true\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  },
  AUDIT_QUESTION_DATA_CLASS_BASE: {
    unid: "6D5D4467177DF66980258C520056FC47",
    codeID3: "",
    description: "AUDIT_QUESTION_DATA_CLASS_BASE",
    KeywordService: "BrisDoc;CAS",
    codeID2: "",
    codeID1:
      '[\r\n  {\r\n    "id": "safeguardingConcerns",\r\n    "type": "radio",\r\n    "label": "Q1 - Do you have any safeguarding concerns relating to the current consultation?",\r\n    "options": [\r\n      "Yes",\r\n      "No"\r\n    ],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "radio",\r\n    "label": "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027safeguardingConcerns\u0027] \u003d\u003d\u003d \u0027Yes\u0027"\r\n  }\r\n]',
    keyType: "AUDIT_QUESTION_DATA"
  }
};

export const mockCasOtherQuestionsParsed = [
  {
    id: "verificationOfDeath",
    type: "radio",
    label: "Q1 - Was this case for verification of death?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: true
  },
  {
    id: "supportGMC",
    type: "text",
    label: "Please enter your GMC# to support the onward Practice process",
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['verificationOfDeath'] === 'Yes'"
  },
  {
    id: "safeguardingConcerns",
    type: "radio",
    label:
      "Q1 - Do you have any safeguarding concerns relating to the current consultation?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: true
  },
  {
    id: "safeguardingReferral",
    type: "radio",
    label:
      "Have you made a safeguarding referral for this patient (should be made if immediate concerns) ?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['safeguardingConcerns'] === 'Yes'"
  }
];
