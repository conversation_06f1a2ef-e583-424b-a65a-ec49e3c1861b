<template>
  <div
    style="display: flex;flex-direction: row;width: 100%;justify-content: space-between;position: absolute;"
  >
    <slot name="header-left">
      <div class="cleo-float-left-a">
        <!--        <span class="cleo-force-inline-block">-->
        <slot name="view-title">
          <div class="grid-route-toolbar--view-title-wrapper">
            <span
              v-text="viewTitle"
              class="grid-route-toolbar--view-title"
            ></span>
          </div>
        </slot>
        <!--        </span>-->
        <!--        <div class="cleo-float-left-b">-->
        <slot name="toolbar-content"></slot>
        <!--        </div>-->
      </div>
    </slot>

    <slot name="header-right">
      <div class="grid-route-toolbar--header-right">
        <SocketLoadingSpinner
          :socket-loading-trigger="socketLoadingTrigger"
          class="grid-route-toolbar--reload-spinner"
        />
        <LoadingSpinner
          v-if="isLoading"
          class="grid-route-toolbar--reload-spinner"
        />
        <span
          class="grid-route-toolbar--call-count"
          v-text="getCallCountMessage"
        ></span>
        <a
          href="#"
          v-on:click.prevent="debounceRefresh"
          class="grid-route-toolbar--refresh-link"
          :class="isLoading ? 'disabled-link' : ''"
          >Refresh</a
        >
        <span title="Time data last refreshed" v-text="lastReloaded"></span>
        <SocketUiStatus
          class="grid-route-toolbar--socket-local"
          :socket-status="socketStatus"
          v-on:showSocketMessages="showSocketMessages"
        ></SocketUiStatus>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import SocketLoadingSpinner from "@/socket/socket-loading-spinner.vue";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import { ISimpleTrigger } from "@/calls/summary/call-summarry-models";
import { mapState } from "vuex";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import { debounce } from "@/common/debounce";
import { loggerInstance } from "@/common/Logger";
import SocketUiStatus from "@/socket/socket-ui-status.vue";

@Component({
  name: "grid-route-toolbar",
  components: { SocketUiStatus, LoadingSpinner, SocketLoadingSpinner },
  computed: {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    ...mapState(PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME, {
      permissionStoreState: (state: IPermissionStoreState) => state
    })
  }
})
export default class GridRouteToolbar extends Vue {
  public readonly permissionStoreState!: IPermissionStoreState;

  @Prop({ default: "" })
  public readonly viewTitle!: string;

  @Prop({ default: false })
  public readonly isLoading!: boolean;

  @Prop({ default: false })
  public readonly socketStatus!: boolean;

  @Prop({
    default: () => {
      return {
        timeIso: ""
      } as ISimpleTrigger<unknown>;
    }
  })
  public readonly socketLoadingTrigger!: ISimpleTrigger<unknown>;

  @Prop({ default: "" })
  public readonly lastReloaded!: string;

  @Prop({ default: 0 })
  public readonly callCount!: number;

  @Prop({ default: 0 })
  public readonly callFilterCount!: number;

  @Prop({ default: false })
  public readonly areAnyFiltersActive!: boolean;

  public debounceRefresh: any;

  public created(): void {
    this.debounceRefresh = debounce(() => {
      loggerInstance.log("GridRouteToolbar.debounceNewCalls()...");
      this.getDataRefresh();
    }, 1000);
  }

  public getDataRefresh(): void {
    if (this.isLoading) {
      return;
    }
    this.$emit("getData");
  }

  public get getCallCountMessage(): string {
    if (!this.areAnyFiltersActive) {
      return "Count: " + this.callCount;
    }
    // if (this.callCount === this.callFilterCount || this.callFilterCount === 0) {
    //   return "Count: " + this.callCount;
    // }
    return "Count: " + this.callCount + " Filter: " + this.callFilterCount;
  }

  public showSocketMessages(): void {
    this.$emit("showSocketMessages");
  }
}
</script>

<style scoped>
.cleo-float-left-a {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

.cleo-float-left-b {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  position: relative;
  padding: 0 16px 0 0;
  align-items: center;
  margin: 0;
}

.grid-route-toolbar--socket-local {
  margin-left: 5px;
}

.grid-route-toolbar--call-count {
  margin-right: 5px;
}
</style>
