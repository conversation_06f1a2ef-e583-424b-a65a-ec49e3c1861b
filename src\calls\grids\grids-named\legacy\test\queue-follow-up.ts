import { GridLegacyServerResponse } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

export const queueFollowUpMock: GridLegacyServerResponse = ({
  Count: 7,
  Returned: 7,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: [
    {
      unid: "85104F132959F1F380258C8C00522B82",
      name: "250659047",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250659047",
      CallID: "250659047",
      CallNHSNo: "**********",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1975-03-16",
      CallPatientTitle: "",
      CallAddress1: "260 VICTORIA ROAD",
      CallAddress2: "SHIPLEY",
      CallAddress3: "W YORKSHIRE",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "BD18 3JZ",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "13/05/2025 18:45:41",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Lucy Grinnell/O=cleouat",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-09T16:08:13+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-09T17:48:13+01:00",
      BreachPreActualTime: "2025-05-09T16:05:26+01:00",
      BreachActualTime: "2025-05-09T18:08:13+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "18:45",
      dtArrivedTime: "13/05/2025 18:45:56",
      CallAge: "50 yrs",
      CallDoctorNameCN: "Chelsea Stevens",
      PatientName: "ELANGO, Chann",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Marksbury Road",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "",
      CallTelNo_R: "",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "ELANGO",
      CallForename: "Chann",
      CallDoctorName: "CN=Chelsea Stevens/O=cleout",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "B83013",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Toxic Ingestion/Inhalation",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "09/05/2025 16:08:13",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE:
        "No specific PPE requirements/infectious concerns",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "TAKE_LIST",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "9F1DA39ED4BC973380258C8C0051FF97",
      name: "250659020",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250659020",
      CallID: "250659020",
      CallNHSNo: "",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Female",
      Call_HCP: "",
      CallDobIso: "2015-07-16",
      CallPatientTitle: "",
      CallAddress1: "BRISDOC HEALTHCARE SERVICES LTD",
      CallAddress2: "21 OSPREY COURT",
      CallAddress3: "HAWKFIELD WAY",
      CallAddress4: "HAWKFIELD BUSINESS PARK",
      CallTown: "BRISTOL",
      CallPostCode: "BS14 0BB",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Lucy Grinnell/O=cleouat",
      CallCName: "",
      CallCRel: "Carer",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-09T12:57:37+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-09T18:37:37+01:00",
      BreachPreActualTime: "2025-05-09T12:23:19+01:00",
      BreachActualTime: "2025-05-09T18:57:37+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "12:15",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "9 yrs",
      CallDoctorNameCN: "",
      PatientName: "RYAN, Dave",
      CallTriaged: "",
      CallSymptoms: "Nat test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Marksbury Road",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "RYAN",
      CallForename: "Dave",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "A20047",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "17/05/2025 12:15:00",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "09/05/2025 12:57:37",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "Isolation Room/PPE Required",
      OversightValidationType: "Approve",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "03EB8C9DCFCB416080258C8C00522B7E",
      name: "250659021",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250659021",
      CallID: "250659021",
      CallNHSNo: "",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "2018-07-16",
      CallPatientTitle: "",
      CallAddress1: "BRISDOC HEALTHCARE SERVICES LTD",
      CallAddress2: "21 OSPREY COURT",
      CallAddress3: "HAWKFIELD WAY",
      CallAddress4: "HAWKFIELD BUSINESS PARK",
      CallTown: "BRISTOL",
      CallPostCode: "BS14 0BB",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Natalie Ryan/O=cleout",
      CallCName: "",
      CallCRel: "Crisis Team - HCP",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-09T12:59:16+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-09T18:39:16+01:00",
      BreachPreActualTime: "2025-05-09T12:42:35+01:00",
      BreachActualTime: "2025-05-09T18:59:16+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "12:15",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "6 yrs",
      CallDoctorNameCN: "",
      PatientName: "TEST, Penny",
      CallTriaged: "",
      CallSymptoms: "test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Clevedon",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST",
      CallForename: "Penny",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "30 mins",
      FinalDispositionCode: "P6h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "17/05/2025 12:15:00",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "09/05/2025 12:59:16",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "High Consequence Infectious Disease",
      OversightValidationType: "Approve",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "22D8C8971DA785BB80258C8F0048307F",
      name: "250659733",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250659733",
      CallID: "250659733",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "RECEIVED",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "Non Clinical Complete",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "22/05/2025 16:35:24",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "Ben Smythson",
      CallCName: "",
      CallCRel: "NHS @ Home/Virtual Wards - HCP",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-19T14:10:08+01:00",
      CallReceivedTimeISO: "2025-05-19T14:08:30+01:00",
      BreachWarnActualTime: "2025-05-19T19:50:08+01:00",
      BreachPreActualTime: "2025-05-19T14:10:08+01:00",
      BreachActualTime: "2025-05-19T20:10:08+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "360",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 6 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "14:08",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "29 yrs",
      CallDoctorNameCN: "Nick Wall2",
      PatientName: "TEST, Test",
      CallTriaged: "No",
      CallSymptoms: "TEST",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Social care referral",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "27/05/2025 11:39:33",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST",
      CallForename: "Test",
      CallDoctorName: "CN=Nick Wall2/O=cleouat",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "G00234",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P6h",
      CHFinalDispositionDescription: "6 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "FRAILTY",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "TAKE_LIST",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "B7D2E6E20FA27E2180258C97003F6144",
      name: "250670287",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250670287",
      CallID: "250670287",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "2024-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "Non Clinical Complete",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "27/05/2025 12:32:38",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-27T12:32:16+01:00",
      CallReceivedTimeISO: "2025-05-27T12:32:16+01:00",
      BreachWarnActualTime: "2025-05-27T13:12:16+01:00",
      BreachPreActualTime: "2025-05-27T12:32:16+01:00",
      BreachActualTime: "2025-05-27T13:32:16+01:00",
      BreachPriority: "11",
      BreachLevel1Mins: "60",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Urgent 60 mins",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:32",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "1 yrs",
      CallDoctorNameCN: "Nick Wall2",
      PatientName: "TEST_DO_NOT_CALL_NOR_DX17, TEST",
      CallTriaged: "No",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Severnside Home Visit",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "27/05/2025 12:33:07",
      CallCallback: "0",
      CallTelNo: "07791 230000",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "TEST_DO_NOT_CALL_NOR_DX17",
      CallForename: "TEST",
      CallDoctorName: "CN=Nick Wall2/O=cleouat",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx17",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx17",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "OTHER",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "1",
      SMS_SENT: "1",
      SMS_LATEST_AT: "27/05/2025 12:32:28",
      SMS_LATEST_USER: "ITK",
      SMS_LATEST_MESSAGE:
        "Your details have been passed to us by NHS111.  We are working hard to contact you as soon as possible, but please be aware this can be longer than the timeframe given by NHS111. If this is the case, one of our call handlers will phone you to check how you are.  Severnside Integrated Urgent Care. ",
      SMS_COUNT: "1"
    },
    {
      unid: "5E214FEF0D22A1A480258C97003A7CF2",
      name: "250670285",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250670285",
      CallID: "250670285",
      CallNHSNo: "",
      CallService: "OOH",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Female",
      Call_HCP: "",
      CallDobIso: "1990-08-07",
      CallPatientTitle: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "unk",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Base",
      CC: "Base",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall2/O=cleouat",
      CallCName: "",
      CallCRel: "Doctor of Patient - HCP",
      BreachKey: "BaseNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-27T11:38:50+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-27T13:18:50+01:00",
      BreachPreActualTime: "2025-05-19T10:48:04+01:00",
      BreachActualTime: "2025-05-27T13:38:50+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "120",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 2 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "34 yrs",
      CallDoctorNameCN: "",
      PatientName: "NBVXM, Fgfg",
      CallTriaged: "",
      CallSymptoms: "",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "01234 567890",
      CallTelNo_R: "01234 567890",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "NBVXM",
      CallForename: "Fgfg",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "Z10000",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P2h",
      CHFinalDispositionDescription: "30 mins",
      FinalDispositionCode: "P2h",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "false",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "Face to Face",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "27/05/2025 11:38:50",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "High Consequence Infectious Disease",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: ""
    },
    {
      unid: "555217AFC58B068880258C97003F4ABE",
      name: "250670286",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250670286",
      CallID: "250670286",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "",
      CallDobIso: "1961-04-09",
      CallPatientTitle: "",
      CallAddress1: "test",
      CallAddress2: "MUNDESLEY",
      CallAddress3: "NORWICH",
      CallAddress4: "NORFOLK",
      CallTown: "",
      CallPostCode: "NR11 8DH",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "Non Clinical Complete",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "27/05/2025 12:37:12",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "111",
      CallCName: "",
      CallCRel: "Relative Or Friend",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-05-27T12:31:19+01:00",
      CallReceivedTimeISO: "2025-05-27T12:31:19+01:00",
      BreachWarnActualTime: "2025-05-27T16:11:19+01:00",
      BreachPreActualTime: "2025-05-27T12:31:19+01:00",
      BreachActualTime: "2025-05-27T16:31:19+01:00",
      BreachPriority: "8",
      BreachLevel1Mins: "240",
      Source: "NHS111Interop",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "12:31",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "64 yrs",
      CallDoctorNameCN: "Nick Wall2",
      PatientName: "Nick_DX03, TEST",
      CallTriaged: "No",
      CallSymptoms: "Test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "Severnside Follow Up",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "27/05/2025 12:37:38",
      CallCallback: "0",
      CallTelNo: "07791 230000",
      CallTelNo_R: "07791 230000",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "Nick_DX03",
      CallForename: "TEST",
      CallDoctorName: "CN=Nick Wall2/O=cleouat",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPracticeOCS: "D82053",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx03",
      CHFinalDispositionDescription: "Speak to a local service within 1 hour",
      FinalDispositionCode: "Dx03",
      FinalDispositionDescription: "Speak to a local service within 1 hour",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "Yes",
      PDSTraced: "Yes",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "OVERSIGHT_FOLLOW_UP",
      cleoClientService: "",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "CHILDREN_ED_REFERRAL",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      SMS_HAS: "1",
      SMS_SENT: "1",
      SMS_LATEST_AT: "27/05/2025 12:31:36",
      SMS_LATEST_USER: "ITK",
      SMS_LATEST_MESSAGE:
        "Your details have been passed to us by NHS111.  We are working hard to contact you as soon as possible, but please be aware this can be longer than the timeframe given by NHS111. If this is the case, one of our call handlers will phone you to check how you are.  Severnside Integrated Urgent Care. ",
      SMS_COUNT: "1"
    }
  ],
  Page: {
    Enabled: 1,
    PageNumber: 1,
    PageSize: 1000,
    getRowCount: 7,
    getStartRowNumber: 1,
    TotalSearchRowCount: 7,
    getTotalNumberOfPages: 1
  }
} as any) as GridLegacyServerResponse;
