<template>
  <div>
    <CallSummaryToolbar
      class="grid--call-summary-toolbar"
      :cleo-call-summary="cleoCallSummary"
      :is-loading="isLoadingPerms"
      :perms-for-call="filterPerms"
    ></CallSummaryToolbar>

    <CallSummaryDisplay
      class="grid--call-summary-display"
      :cleo-call-summary="cleoCallSummary"
    ></CallSummaryDisplay>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from "@vue/composition-api";

import CallSummaryToolbar from "@/calls/summary/call-summary-toolbar/call-summary-toolbar.vue";
import CallSummaryDisplay from "@/calls/summary/call-summary-display.vue";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { ICleoPermission } from "@/permissions/permission-models";

const callSummaryService: CallSummaryService = new CallSummaryService();

export default defineComponent({
  name: "call-summary-section",
  props: {
    cleoCallSummary: {
      type: Object as PropType<ICleoCallSummary>,
      default: () => {
        return callSummaryService.factoryCleoCallSummary();
      }
    },
    isLoadingPerms: {
      default: false
    },
    cleoCallSummaryPerms: {
      type: Object as PropType<Record<string, ICleoPermission>>,
      default: () => {
        return {};
      }
    }
  },
  components: {
    CallSummaryDisplay,
    CallSummaryToolbar
  },
  setup(props: {
    cleoCallSummary: ICleoCallSummary;
    isLoadingPerms: boolean;
    cleoCallSummaryPerms: Record<string, ICleoPermission>;
  }) {
    const filterPerms = computed<Record<string, ICleoPermission>>(() => {
      return props.cleoCallSummary.CallStatusValue !== 1
        ? {}
        : props.cleoCallSummaryPerms;
    });

    return {
      filterPerms
    };
  }
});
</script>
