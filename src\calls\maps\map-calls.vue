<template>
  <div style="height: 80vh;width: 100vw;">
    <div style="height: 100%;width: 100%;">
      <div
        class="map--full"
        id="map-cleo"
        style="height: 100%;width: 80%;"
      ></div>
      <MapBasesTable
        :bases="mapController.cleoMapControllerState.bases.objs"
        v-on:baseClicked="baseClicked"
      ></MapBasesTable>
      <VehiclesSmallTable
        :vehicles="mapController.cleoMapControllerState.cars.objs"
      ></VehiclesSmallTable>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="loadCalls"
      >
        <span>Load Calls</span>
      </button>

      <div class="adapter-button--separator"></div>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="addBasesToMap"
      >
        <span>Add Bases</span>
      </button>

      <div class="adapter-button--separator"></div>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="clearBasesFromMap"
      >
        <span>Clear Bases</span>
      </button>

      <div class="adapter-button--separator"></div>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="stopAnimation"
      >
        <span>Stop Animation</span>
      </button>

      <div class="adapter-button--separator"></div>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="clearRoutes"
      >
        <span>Clear Routes</span>
      </button>

      <button
        class="adapter-button adapter-width-auto adapter-button--green"
        v-on:click.stop="mapController.getMarkersInBounds('CAR')"
      >
        <span>Near Cars</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  SetupContext
} from "@vue/composition-api";

import { Loader } from "@googlemaps/js-api-loader";
import { IBaseSummary } from "@/bases/base-models";
import {
  getMapControllerInstance,
  IMapController
} from "@/calls/maps/map-controller-factory";
import MapBasesTable from "@/calls/maps/bases/map-bases-table.vue";
import { loggerInstance } from "@/common/Logger";
import { appStore } from "@/store/store";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import VehiclesSmallTable from "@/calls/maps/vehicles/vehicles-small-table.vue";

declare const google: any;

export default defineComponent({
  name: "map-calls",
  components: { VehiclesSmallTable, MapBasesTable },
  props: {},
  setup(props: Record<string, any>, context: SetupContext) {
    const mapApiKey = "AIzaSyCA9S5winwBlVSpUA0CQQdyOzOUIzA1jAw";
    // const mapApiKey = "AIzaSyD0zdCViFaubv3fu3TxFNgv_5zoxWSo37M";

    const store = appStore;
    const userStoreState = computed<IUserStoreState>(() => {
      return store.state[USER_STORE_CONST.USER__CONST_MODULE_NAME];
    });

    let mapController: IMapController = getMapControllerInstance();

    onMounted(() => {
      let loader = new Loader({
        apiKey: mapApiKey,
        libraries: ["geometry"]
      });
      loader.load().then(() => {
        const mapDiv = document.getElementById("map-cleo");
        if (mapDiv) {
          mapController.setGoogleMap(
            new google.maps.Map(mapDiv, {
              center: {
                lat: 51.43333,
                lng: 0.55
              },
              zoom: 9,
              zoomControl: true,
              scaleControl: true
            })
          );
          mapController.loadBases().then(mapController.addBasesToMap);
          mapController.loadCalls();
          mapController.loadVehicles();
        }
      });
    });

    function baseClicked(base: IBaseSummary) {
      loggerInstance.log("map-calls......baseClicked", base);
      mapController.baseClicked(base);
    }

    function loadCalls() {
      mapController.loadCalls();
    }

    function addBasesToMap() {
      mapController.addBasesToMap();
    }

    function clearBasesFromMap() {
      mapController.removeMarkersFromMap(
        mapController.cleoMapControllerState.bases.markers
      );
      mapController.cleoMapControllerState.bases.markers = [];
    }

    function stopAnimation() {
      [
        mapController.cleoMapControllerState.calls.markers,
        mapController.cleoMapControllerState.cars.markers,
        mapController.cleoMapControllerState.bases.markers
      ].forEach(markers => {
        mapController.bounceMarkers(markers, false);
      });
    }

    function clearRoutes() {
      mapController.clearRoutes();
    }

    function getNearestCars() {
      mapController.getMarkersInBounds("CAR");
    }

    return {
      mapController,
      baseClicked,
      loadCalls,
      addBasesToMap,
      clearBasesFromMap,
      stopAnimation,
      clearRoutes,
      getNearestCars
    };
  }
});
</script>

<style>
.map--full {
  width: 100vw;
  height: 100vw;
}
</style>
