import {
  CLEO_CLIENT_SERVICE,
  DominoNameFull,
  <PERSON><PERSON><PERSON>,
  IsoDateTimeWithOffset,
} from "@/common/common-models";
import { BrisDocSupportType } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";

export type GridLegacyViewName = "NAV_111_SORT_PRIORITY_BREACH";

export interface GridLegacyServerRequestBase {
  vn: GridLegacyViewName;
  PAGE_ON: number;
  PAGE_NUM: number;
  COV19_ALL: "ALL" | "";
}

export interface GridLegacyServerResponse {
  Count: number;
  Returned: number;
  identifier: string;
  label: string;
  Limit: number;
  items: GridLegacyCallSummary[];
}

export type TextSimpleInteger = "0" | "1" | "2" | "3" | "4" | "5";
export type TextSimpleZeroOne = "0" | "1";
export type TextSimpleBooleanYesNo = "Yes" | "No";
export type TextSimpleBooleanTrueFalse = "true" | "false";

export type GoodSamStatus = "REQUESTED" | "RECEIVED" | "REQUESTED_AGAIN" | "";

export interface GridLegacyCallSummary {
  unid: string; // "B62F7D0DCAB89AE480258BAF00479DDE";
  name: string; // "**********";
  CallCCMS: string; // "";
  Info: string; // "";
  IsLocked: string; // "";
  CallNo: string; // "**********";
  CallID: string; // "**********";
  CallNHSNo: string; // "**********";
  CallService: string; // "Norfolk and Wisbech 111";
  CallServiceSub: string; // "";
  CallServiceOriginal: string; // "Norfolk and Wisbech 111";
  CallServiceAlt: string; // "";
  cleoClientService: CLEO_CLIENT_SERVICE; // ;
  CallMF: GENDER; // "Male";
  CallDobIso: string; // "1967-05-04";
  CallPatientTitle: string; // "";
  CallAddress1: string; // "INTEGRATED CARE 24";
  CallAddress2: string; // "KINGSTON HOUSE THE LONG BARROW";
  CallAddress3: string; // "ORBITAL PARK";
  CallAddress4: string; // "";
  CallTown: string; // "ASHFORD";
  CallPostCode: string; // "TN24 0GP";
  UTC_Assigned: string; // "";
  CallClassification: string; // "Nurse Advice (Requires Closing)";
  CC: string; // "Nurse Advice";
  CSC: string; // "Requires Closing";
  WalkIn: TextSimpleZeroOne;
  CallUrgentYN: "" | TextSimpleBooleanYesNo;
  Call1stContact: string; // "07/10/2024 14:07:03";
  Call1stContactPathways: string; // "07/10/2024 14:05:30";
  PathwaysCaseId: string; // "483f7934-78a3-42ab-b06d-9502494c77db";
  CallCreatedBy: string; // "Olubunmi Oderinde";
  CallCName: string; // "";
  CallCRel: string; // "Patient";
  BreachKey: string; // "NurseAdviceYes";
  ApplyBreach: "" | TextSimpleZeroOne;
  CallReceivedISO: string; // "2024-10-07T14:06:49+01:00";
  CallReceivedTimeISO: string; // "2024-10-07T14:02:14+01:00";
  BreachWarnActualTime: string; // "2024-10-07T14:26:49+01:00";
  BreachPreActualTime: string; // "2024-10-07T14:06:49+01:00";
  BreachActualTime: string; // "2024-10-07T14:36:49+01:00";
  BreachPriority: "" | TextSimpleInteger; // ... etc.
  BreachLevel1Mins: string; // "30";
  Source: string; // "";
  BreachPriorityGroup: string; // "";
  BreachPriorityLabel: string; // "Urgent 30 mins";
  CallWithBaseAckTime: string; // "";
  CallReceivedTime: string; // "14:02";
  CallAppointmentTime: string; // "14:45";
  CallArrivedTime: string; // "";
  dtArrivedTime: string; // "";
  CallAge: string; // "57 yrs";
  CallDoctorNameCN: string; // "Olubunmi Oderinde";
  PatientName: string; // "ORION, Cleo Test";
  CallTriaged: string; // "No";
  CallSymptoms: string; // "test";
  CallPtcl: string; // "";
  CallStatus: string; // "New";
  CallStatusValue: string; // "1";
  PatientContactCode: string; // "";
  PatientContactCode_count: string; // "";
  PatientContactCode_Initial: string; // "";
  PatientContactCode_Current_ForView: string; // "";
  CallInformationalOutcomes: string; // "";
  CallInformationalOutcomesComment: string; // "";
  DutyBase: string; // " ";
  Dispatch_Vehicle: string; // "";
  CallCompletedYN: string; // "No";
  CallCompleted: string; // "";
  CallCallback: "" | TextSimpleInteger; // ... etc.
  CallTelNo: string; // "";
  CallTelNo_R: string; // "";
  CallTelNoAlt_1: string; // "";
  CallTelNoAltType_1: string; // "";
  CallSurname: string; // "ORION";
  CallForename: string; // "Cleo Test";
  CallDoctorName: string; // "CN=Olubunmi Oderinde/O=staging";
  CallSecondOpenCall: string; // "";
  TomTomOrderID: string; // "";
  Linked_Call_ID: string; // E.g. ASHT-DFEK52-20250404T153441
  CallPractice: string; // "Lime Street Surgery";
  CallPracticeOCS: string; // "A20047";
  SDec_Service: string; // "0";
  CAS_Booking_Dr: string; // "";
  CAS_Booking_Time: string; // "";
  CallWarmTransferred: "" | TextSimpleBooleanYesNo;
  CHFinalDispositionCode: string; // "";
  CHFinalDispositionDescription: string; // "";
  FinalDispositionCode: string; // "Dx108";
  FinalDispositionDescription: string; // "Call is closed with no further action needed";
  FLAG_REMOVE_FIRST_CONTACT: string; // "";
  PDSTracedAndVerified: TextSimpleBooleanYesNo;
  PDSTraced: string; // "true";
  PDSAdminTrace: "NO_MATCH" | "TOO_MANY_MATCHES" | string;
  CliniHighPriority: string; // "";
  StartConsultationPerformed: string; // "1";
  ClinicalHub_111ToHubReason: string; // "";
  Courtesy_User: string; // "";
  Courtesy_Time: string; // "";
  Courtesy_Count: "" | TextSimpleInteger; // ... etc.
  Courtesy_Contact: "" | TextSimpleZeroOne | TextSimpleBooleanTrueFalse;
  CAS_TRANSFER_ERROR: string; // "";
  Pathways_ITK_Send: string; // "";
  ITK_111_Online: "" | TextSimpleZeroOne;
  AFT_appt_id: string; // "";
  AFT_datetime_start: string; // "";
  AFT_UNABLE_REASON: string; // "";
  DAB_Id: string; // "";
  DAB_StartDateTime: string; // "";
  DAB_EndDateTime: string; // "";
  AFT_time_start: string; // "";
  AFT_datetime_start_ORIG: string; // "";
  AFT_CANCELLED_REASON: string; // "";
  ED_arrived: string; // "";
  txtAppointmentBase: string; // "";
  CareConnectAppointmentStart: string; // "";
  IUC_CAS_AT_ONE_TIME: string; // "";
  IUC_Contract: string; // "";
  SetBaseInfo_Base: string; // "";
  Comfort_SENT_SERVICE: string; // "";
  Comfort_SENT_SERVICE_TIME: string; // "";
  Comfort_SENT_SERVICE2: string; // "";
  Comfort_SENT_SERVICE2_TIME: string; // "";
  Comfort_SMS2: string; // "";
  Comfort_SMS_TIME2: string; // "";
  Comfort_SMS: string; // "";
  Comfort_SMS_TIME: string; // "";
  Comfort_Cancelled_TIME: string; // "";
  Comfort_Cancelled_REASON: string; // "";
  Cov19Priority: string; // "";
  dateAppointmentStart: string; // "03/07/2025 07:30:00";
  EConsult_UTC: string; // "";
  EConsult_code: string; // "";
  EConsult_Priority: string; // "";
  EConsult_PriorityLabel: string; // "";
  EConsult_ITK_IN: string; // "";
  EConsult_Status: string; // "";
  EConsult_Source: string; // "";
  EConsult_LivingArrangement: string; // "";
  EConsult_AttendanceSource: string; // "";
  EConsult_LiveOverseas: string; // "";
  EConsult_ArrivalMethod: string; // "";
  EConsult_SameProblem: string; // "";
  EConsult_XRay: string; // "";
  patient_alerts_count: string; // "";
  UTC_CaseComments: string; // "";
  EMIS_CDB: string; //
  EMIS_PatientId: string; //
  EMIS_UserId: string; //
  KMS_SC_MESSAGE: string; //
  CareHomeName: string; //
  CasValidationCount: string; // "2"
  CasValidationUser: string; // "CN=Joe Bloggs/O=sehnp"
  CasValidationTime: string; // "09/04/2025 14:14:29"
  CasValidationReason: string; // "Reason for validation"
  Call_CaseComments: string; // E.g. "2025-04-15T11:27:51 01:00~:~rewr3ere~~~2025-04-15T11:27:31 01:00~:~ vb gfb gbnghnghng",
  COMPLETE_PREVENT: TextSimpleZeroOne | "";
  FOLLOW_UP_URGENT: TextSimpleZeroOne | "";
  FOLLOW_UP_Active: string; // "09/05/2025 12:57:37"
  Call_HCP: TextSimpleZeroOne | "";
  OversightValidationType:
    | undefined
    | "Approve"
    | "Clinical Co-Ordinator Review in progress"
    | "Further Clinical Input Required";
  OVERSIGHT_BASE_TRIAGE_TYPE: string | "Isolation" | "Does patient need PPE"; // etc. from Keywords, putting her as an example.
  SMS_HAS: TextSimpleZeroOne | undefined;
  SMS_SENT: TextSimpleZeroOne | undefined;
  SMS_LATEST_AT: IsoDateTimeWithOffset;
  SMS_LATEST_USER: DominoNameFull;
  SMS_LATEST_MESSAGE: string;

  Cpl_supportTypeRequired: BrisDocSupportType | "";
  GOODSAM_IMAGE_STATUS?: GoodSamStatus;
  CallFAction: string;
  Cpl_furtherActionGPText: string;

  PLS_REASON: string;
  PLS_TIME: string;
  PLS_USER: string;
  PLS_ACTION: string;
  PLS_ACTIONTEXT: string;
}
