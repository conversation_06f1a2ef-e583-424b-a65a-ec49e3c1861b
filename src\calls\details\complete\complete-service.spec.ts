import {
  getFailedContactMessage,
  isFailedContactOlderThanInterval,
  getNewestFailedContact
} from "./complete-service";
import { IFailedContact } from "@/calls/details/call-details-models";
import { IFailedContactComplete } from "./complete-models";

describe("getFailedContactMessage", () => {
  let failedContactConfig: IFailedContactComplete;
  let failedContacts: IFailedContact[];

  beforeEach(() => {
    // Default configuration
    failedContactConfig = {
      config: {
        attemptsRequired: 2,
        minsInterval: 20
      }
    };

    // Default failed contacts
    failedContacts = [
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00"
      }
    ];
  });

  it("should return message when enough attempts have been made", () => {
    // Arrange
    failedContactConfig.config.attemptsRequired = 2;
    failedContacts = [
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00"
      },
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:30:00"
      }
    ];
    const seedTime = "2024-06-28T11:00:00+01:00";

    // Act
    const message = getFailedContactMessage(
      failedContactConfig,
      failedContacts,
      seedTime
    );

    // Assert
    expect(message).toBe(
      "In accordance with organisation policy, there has been the required 2 attempts for this patient"
    );
  });

  it(
    "should return message when not enough attempts have been made" +
      " and newest attempt is older than interval",
    () => {
      // Arrange
      failedContactConfig.config.attemptsRequired = 3;
      failedContacts = [
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:00:00"
        }
      ];
      const seedTime = "2024-06-28T10:30:00+01:00"; // 30 mins later (> 20 mins interval)

      // Act
      const message = getFailedContactMessage(
        failedContactConfig,
        failedContacts,
        seedTime
      );

      // Assert
      expect(message).toBe(
        "In accordance with organisation policy, there has only been" +
          "  1 of the 3 required attempts for this patient"
      );
    }
  );

  it(
    "should return message when not enough attempts have" +
      " been made and newest attempt is newer than interval",
    () => {
      // Arrange
      failedContactConfig.config.attemptsRequired = 3;
      failedContacts = [
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:00:00"
        }
      ];
      const seedTime = "2024-06-28T10:10:00+01:00"; // 10 mins later (< 20 mins interval)

      // Act
      const message = getFailedContactMessage(
        failedContactConfig,
        failedContacts,
        seedTime
      );

      // Assert
      expect(message).toBe(
        "In accordance with organisation policy, there has only been  1 of" +
          " the 3 required attempts for this patient and the last attempt was less than 20 minutes ago."
      );
    }
  );

  it("should handle multiple failed contacts and find the newest one", () => {
    // Arrange
    failedContactConfig.config.attemptsRequired = 4;
    failedContacts = [
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00"
      },
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:30:00" // This is the newest
      },
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:15:00"
      }
    ];
    const seedTime = "2024-06-28T10:40:00+01:00"; // 10 mins after newest (< 20 mins interval)

    // Act
    const message = getFailedContactMessage(
      failedContactConfig,
      failedContacts,
      seedTime
    );

    // Assert
    expect(message).toBe(
      "In accordance with organisation policy, there has only been" +
        "  3 of the 4 required attempts for this patient and the last attempt was less than 20 minutes ago."
    );
  });

  it("should use current time if seedTime is not provided", () => {
    // This test uses jest's timer mocking
    //jest.useFakeTimers();

    // Set the system time to a known value
    // const fixedDate = new Date("2024-06-28T11:00:00");
    //jest.setSystemTime(fixedDate);

    // Arrange
    failedContactConfig.config.attemptsRequired = 3;
    failedContacts = [
      {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:30:00" // 30 mins before fixed time
      }
    ];

    // Act
    const message = getFailedContactMessage(
      failedContactConfig,
      failedContacts,
      "2024-06-28T11:00:00"
    );

    // Assert
    expect(message).toBe(
      "In accordance with organisation policy, there has only been  1 of the 3 required attempts for this patient"
    );

    // Restore real timers
    //jest.useRealTimers();
  });

  it("should handle empty failed contacts array", () => {
    // Arrange
    failedContactConfig.config.attemptsRequired = 2;
    failedContacts = [];
    const seedTime = "2024-06-28T10:30:00+01:00";

    const message = getFailedContactMessage(
      failedContactConfig,
      failedContacts,
      seedTime
    );
    expect(message).toBe(
      "In accordance with organisation policy, there has only been  0 of " +
        "the 2 required attempts for this patient and the last attempt was less than 20 minutes ago."
    );
  });
});

describe("helper functions", () => {
  describe("getNewestFailedContact", () => {
    it("should return the only contact when there is just one", () => {
      // Arrange
      const failedContacts: IFailedContact[] = [
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:00:00"
        }
      ];

      // Act
      const result = getNewestFailedContact(failedContacts);

      // Assert
      expect(result).toEqual(failedContacts[0]);
    });

    it("should return the newest contact when there are multiple", () => {
      // Arrange
      const failedContacts: IFailedContact[] = [
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:00:00"
        },
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:30:00" // This is the newest
        },
        {
          userName: "Test User",
          type: "No Answer",
          time: "2024-06-28T10:15:00"
        }
      ];

      // Act
      const result = getNewestFailedContact(failedContacts);

      // Assert
      expect(result).toEqual(failedContacts[1]);
    });
  });

  describe("isFailedContactOlderThanInterval", () => {
    it("should return true when contact is older than interval", () => {
      const failedContactConfig = {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      };

      // Arrange
      const failedContact: IFailedContact = {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00"
      };
      const intervalMins = failedContactConfig.config.minsInterval;
      const seedTime = "2024-06-28T10:30:00"; // 30 mins later

      // Act
      const result = isFailedContactOlderThanInterval(
        failedContact,
        intervalMins,
        seedTime
      );

      // Assert
      expect(result).toBe(true);

      const message = getFailedContactMessage(
        failedContactConfig,
        [failedContact],
        seedTime
      );
      expect(message).toBe(
        "In accordance with organisation policy, there has only been  1 of the 2 required attempts for this patient"
      );
    });

    it("should return false when contact is newer than interval", () => {
      // Arrange
      const failedContact: IFailedContact = {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00"
      };
      const intervalMins = 20;
      const seedTime = "2024-06-28T10:10:00"; // 10 mins later

      // Act
      const result = isFailedContactOlderThanInterval(
        failedContact,
        intervalMins,
        seedTime
      );

      // Assert
      expect(result).toBe(false);
    });

    it("should use current time if seedTime is empty", () => {
      const seedTime = "2024-06-28T10:30:00"; // 30 mins later

      const failedContactConfig = {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      };

      // Arrange
      const failedContact: IFailedContact = {
        userName: "Test User",
        type: "No Answer",
        time: "2024-06-28T10:00:00" // 30 mins before seedTime
      };
      const intervalMins = failedContactConfig.config.minsInterval;

      // Act
      const result = isFailedContactOlderThanInterval(
        failedContact,
        intervalMins,
        seedTime
      );

      // Assert
      expect(result).toBe(true);

      const message = getFailedContactMessage(
        failedContactConfig,
        [failedContact],
        seedTime
      );
      expect(message).toBe(
        "In accordance with organisation policy, there has only been  1 of the 2 required attempts for this patient"
      );
    });
  });
});
