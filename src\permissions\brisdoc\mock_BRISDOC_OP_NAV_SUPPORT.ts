import { CLeoPermissionServerResponse } from "@/permissions/permission-models";

export const mock_BRISDOC_OP_NAV_SUPPORT: CLeoPermissionServerResponse = ({
  JobRoles: {},
  AuthProf: {},
  Permissions: {
    "[BRISDOC_OP_NAV_SUPPORT]": {
      "Call.AddComments": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "AddComments",
        AuthorityProfile: "AddCaseComments",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - Mental Health": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - Mental Health",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - Out Of Hours Professional Line": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - Out Of Hours Professional Line",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - PatientLine": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - PatientLine",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - Paediatrics": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - Paediatrics",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - Weekday Professional Line": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - Weekday Professional Line",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - Frailty": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - Frailty",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.New Call - BrisDoc": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "New Call - BrisDoc",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.LINK_CALL_ACTIONS_NEW_CALL": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "LINK_CALL_ACTIONS_NEW_CALL",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.DX_SET_MANUAL": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "DX_SET_MANUAL",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Hide Urgent": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Hide Urgent",
        AuthorityProfile: "BrisDoc",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "GLOBAL.COMPLETE_USE_BRISDOC_NON_CLINI": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "COMPLETE_USE_BRISDOC_NON_CLINI",
        AuthorityProfile: "BrisNonClinician",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.COMPLETE_USE_BRISDOC": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "COMPLETE_USE_BRISDOC",
        AuthorityProfile: "BrisNonClinician",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.CAS_APPOINTMENT": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "CAS_APPOINTMENT",
        AuthorityProfile: "CAS_BOOKING",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.EDIT_ROTA": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "EDIT_ROTA",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.APPTS_BASE_RECEP": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "APPTS_BASE_RECEP",
        AuthorityProfile: "CAS_BOOKING_CONSULT",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.APPTS_DISP_CONT": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "APPTS_DISP_CONT",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Unlock Call": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Unlock Call",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Assign_DELETE": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Assign_DELETE",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Dispatch Retrieve": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Dispatch Retrieve",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Resend Summary": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Resend Summary",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.URGENCY_CHANGED": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "URGENCY_CHANGED",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.EDIT CALL COMMENTS": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "EDIT CALL COMMENTS",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.COMFORT_COURTESY_CALL": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "COMFORT_COURTESY_CALL",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.SELECT_TRIAGE_PLATFORM": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "SELECT_TRIAGE_PLATFORM",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Patient.Merge": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "Merge",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "EDIT_MODE",
        PermissionFormulaResult: "1"
      },
      "Admin.Assign Smart Card": {
        PermissionAccess: "A",
        PermissionForm: "Admin",
        PermissionAction: "Assign Smart Card",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.INSTANT_CHAT": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "INSTANT_CHAT",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.VIEW_TELEPHONE_INFO": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "VIEW_TELEPHONE_INFO",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Skillset15": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Skillset15",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.EDIT CLASSIFICATION": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "EDIT CLASSIFICATION",
        AuthorityProfile: "Edit_Classification",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Patient.Edit Patient": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "Edit Patient",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "READ_MODE",
        PermissionFormulaResult: ""
      },
      "Patient.Save And Close": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "Save And Close",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "EDIT_MODE",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Resend DTS": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Resend DTS",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Resend Resolved": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Resend Resolved",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Start GAP Protocol": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Start GAP Protocol",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "READ_MODE",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Resend Individual": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Resend Individual",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.ALLOW_MULTI_FORM_PROCESS": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "ALLOW_MULTI_FORM_PROCESS",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.CLEO_HEALTH": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "CLEO_HEALTH",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Make Appointment": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Make Appointment",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Arrived": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Arrived",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.": {
        PermissionAccess: "R",
        PermissionForm: "Call",
        PermissionAction: "",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Secondary Assign": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Secondary Assign",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.COMFORT_COURTESY_CALL": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "COMFORT_COURTESY_CALL",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Patient.PDS Sync": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "PDS Sync",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "EDIT_MODE",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.BaseInOut": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "BaseInOut",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Edit Symptoms": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Edit Symptoms",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Save And Close": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Save And Close",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Assign Base_DELETE": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Assign Base_DELETE",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.DAB_CANCEL": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "DAB_CANCEL",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Assign_DELETE": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Assign_DELETE",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Acknowledge Recipt Base": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Acknowledge Recipt Base",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Assign Base_DELETE": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Assign Base_DELETE",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Start ILTC Protocol": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Start ILTC Protocol",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "READ_MODE",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Reset Service": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Reset Service",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Dispatch Retrieve": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Dispatch Retrieve",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Complete": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Complete",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.GP CONNECT": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "GP CONNECT",
        AuthorityProfile: "Triage Platform",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Unlock Call": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Unlock Call",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Edit Contact Info": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Edit Contact Info",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "GLOBAL.USER_TYPE_DISPATCH": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "USER_TYPE_DISPATCH",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.View Contact Info": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "View Contact Info",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Secondary Assign": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Secondary Assign",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.VIEW_DCR_AAA": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "VIEW_DCR_AAA",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Patient.PDS Trace": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "PDS Trace",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "EDIT_MODE",
        PermissionFormulaResult: ""
      },
      "Call.UTC_CLEO_DAB": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "UTC_CLEO_DAB",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.PDS Sync": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "PDS Sync",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.GRID_FILTER_CLASSIFICATION": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "GRID_FILTER_CLASSIFICATION",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.ALLOW_MULTI_FORM_PROCESS": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "ALLOW_MULTI_FORM_PROCESS",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.CLEO_CLICK_PANEL1": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "CLEO_CLICK_PANEL1",
        AuthorityProfile: "EMIS_ACCESS",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.CAREHOME_SELECT": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "CAREHOME_SELECT",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Arrived": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Arrived",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Edit Classification": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Edit Classification",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.PDS Trace": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "PDS Trace",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Make Appointment": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Make Appointment",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.EDIT REFERRAL": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "EDIT REFERRAL",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Dispatch Select": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Dispatch Select",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "GLOBAL.MOVE_CASE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "MOVE_CASE",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "WebUI_DocView.Priority": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Priority",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "GLOBAL.ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "ASSIGN_TO_CLINICIAN_NO_ROTA_CLEAR",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "GLOBAL.VALIDATE_CAS": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "VALIDATE_CAS",
        AuthorityProfile: "Despatch Controller",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.EMIS_CLICK_PANEL1": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "EMIS_CLICK_PANEL1",
        AuthorityProfile: "EMIS_ACCESS",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.EMIS_AVAILABLE": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "EMIS_AVAILABLE",
        AuthorityProfile: "EMIS_ACCESS",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "GLOBAL.EMIS_ACCESS": {
        PermissionAccess: "A",
        PermissionForm: "GLOBAL",
        PermissionAction: "EMIS_ACCESS",
        AuthorityProfile: "EMIS_ACCESS",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.EMIS_ACCESS": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "EMIS_ACCESS",
        AuthorityProfile: "EMIS_ACCESS",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.SEARCH DOS": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "SEARCH DOS",
        AuthorityProfile: "NHS 111",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.SAFEGUARD_ADULT": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "SAFEGUARD_ADULT",
        AuthorityProfile: "Roving GP",
        PermissionActionMode: "EDIT_MODE",
        PermissionFormulaResult: "1"
      },
      "Call.SEPSIS_WARNING": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "SEPSIS_WARNING",
        AuthorityProfile: "Sepsis_Warning",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Patient.Close": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "Close",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.SELECT_SURGERY": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "SELECT_SURGERY",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.PRINT PATHWAYS": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "PRINT PATHWAYS",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "WebUI_DocView.Print": {
        PermissionAccess: "A",
        PermissionForm: "WebUI_DocView",
        PermissionAction: "Print",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Print": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Print",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.Close": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Close",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Patient.Print": {
        PermissionAccess: "A",
        PermissionForm: "Patient",
        PermissionAction: "Print",
        AuthorityProfile: "Standard",
        PermissionActionMode: "READ_MODE",
        PermissionFormulaResult: ""
      },
      "Call.View Pathways Report": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "View Pathways Report",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      },
      "Call.Patient History": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "Patient History",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: ""
      },
      "Call.NO_SUPPORT": {
        PermissionAccess: "A",
        PermissionForm: "Call",
        PermissionAction: "NO_SUPPORT",
        AuthorityProfile: "Standard",
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      }
    }
  }
} as any) as CLeoPermissionServerResponse;
