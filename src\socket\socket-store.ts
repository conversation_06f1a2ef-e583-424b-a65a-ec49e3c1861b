import { SocketService } from "./socket-service";
import { Modu<PERSON> } from "vuex";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { IRootState } from "@/store/store";

const socketService: SocketService = new SocketService();

export type SOCKET_STATUS =
  | "Not Started"
  | "Connecting"
  | "Connected"
  | "Error"
  | "Reconnecting"
  | "Reconnected"
  | "Closed";

export interface ISocketStoreState {
  isConnected: boolean;
  socketStatus: SOCKET_STATUS;
  socketUrl: string;

  bufferNewKey: Record<string, ICleoCallSummary>;
  bufferUpdatedKey: Record<string, ICleoCallSummary>;

  newCases: ICleoCallSummary[];
  updatedCases: ICleoCallSummary[];
}

export enum SOCKET_STORE_CONST {
  SOCKET__CONST_MODULE_NAME = "SOCKET__CONST_MODULE_NAME",

  // <MUTATIONS>
  SOCKET__MUTATIONS_SET_SOCKET_STATUS = "SOCKET__MUTATIONS_SET_SOCKET_STATUS",
  // SOCKET__MUTATIONS_SET_CALLS = "SOCKET__MUTATIONS_SET_CALLS",
  SOCKET__MUTATIONS_SET_NEW_CASE = "SOCKET__MUTATIONS_SET_NEW_CASE",
  SOCKET__MUTATIONS_SET_UPDATE_CASE = "SOCKET__MUTATIONS_SET_UPDATE_CASE",
  SOCKET__MUTATIONS_SET_LOCKED_CASE = "SOCKET__MUTATIONS_SET_LOCKED_CASE",
  SOCKET__MUTATIONS_SET_UNLOCKED_CASE = "SOCKET__MUTATIONS_SET_UNLOCKED_CASE",
  SOCKET__MUTATIONS_SET_CLEAR_BUFFERS = "SOCKET__MUTATIONS_SET_CLEAR_BUFFERS",
  SOCKET__MUTATIONS_SET_CLEAR_NEW_CALLS_BUFFER = "SOCKET__MUTATIONS_SET_CLEAR_NEW_CALLS_BUFFER",
  SOCKET__MUTATIONS_SET_CLEAR_UPDATED_CALLS_BUFFER = "SOCKET__MUTATIONS_SET_CLEAR_UPDATED_CALLS_BUFFER"
  // </MUTATIONS>

  // <ACTIONS>
  // </ACTIONS>
}

//  TODO socket server sending up KMS model, not ICleoCallSummary

const mutations = {
  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_SOCKET_STATUS](
    state: ISocketStoreState,
    status: SOCKET_STATUS
  ): void {
    state.socketStatus = status;
    state.isConnected = state.socketStatus === "Connected";
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_NEW_CASE](
    state: ISocketStoreState,
    cleoCallSummary: ICleoCallSummary
  ): void {
    const buffer = { ...state.bufferNewKey };

    buffer[cleoCallSummary.CallNo] = { ...cleoCallSummary };
    state.bufferNewKey = buffer;
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_UPDATE_CASE](
    state: ISocketStoreState,
    cleoCallSummary: ICleoCallSummary
  ): void {
    const buffer = { ...state.bufferUpdatedKey };
    buffer[cleoCallSummary.CallNo] = { ...cleoCallSummary };
    state.bufferUpdatedKey = buffer;
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_LOCKED_CASE](
    state: ISocketStoreState,
    cleoCallSummary: ICleoCallSummary
  ): void {
    const buffer = { ...state.bufferUpdatedKey };
    buffer[cleoCallSummary.CallNo] = { ...cleoCallSummary };
    state.bufferUpdatedKey = buffer;
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_UNLOCKED_CASE](
    state: ISocketStoreState,
    cleoCallSummary: ICleoCallSummary
  ): void {
    const buffer = { ...state.bufferUpdatedKey };
    buffer[cleoCallSummary.CallNo] = { ...cleoCallSummary };
    state.bufferUpdatedKey = buffer;
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_CLEAR_BUFFERS](
    state: ISocketStoreState
  ): void {
    state.bufferNewKey = {};
    state.bufferUpdatedKey = {};
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_CLEAR_NEW_CALLS_BUFFER](
    state: ISocketStoreState
  ): void {
    state.bufferNewKey = {};
  },

  [SOCKET_STORE_CONST.SOCKET__MUTATIONS_SET_CLEAR_UPDATED_CALLS_BUFFER](
    state: ISocketStoreState
  ): void {
    state.bufferUpdatedKey = {};
  }
};

const getters = {};

const actions = {};

export const socketStore: Module<ISocketStoreState, IRootState> = {
  namespaced: true,
  state: socketService.factorySocketStoreState,
  mutations,
  getters,
  actions
};
