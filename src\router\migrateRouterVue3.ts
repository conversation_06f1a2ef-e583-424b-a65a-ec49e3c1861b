// import { reactive } from "@vue/composition-api";
// import { NavigationGuardNext, Route } from "vue-router";
import router from "@/router/router";

// const currentRoute = reactive({
//   ...router.currentRoute
// });
//
// router.beforeEach((to: Route, from: Route, next: NavigationGuardNext) => {
//   Object.keys(to).forEach(key => {
//     // eslint-disable-next-line @typescript-eslint/ban-ts-comment
//     // @ts-ignore
//     currentRoute[key] = to[key];
//   });
//   next();
// });

export function useRoute() {
  return router.currentRoute;
}

export function useRouter() {
  return router;
}
