import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import {
  IGridFilter,
  IGridFilterUserInput,
} from "@/calls/grids/grid-filter/grid-filter-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { CLEO_CLIENT_SERVICE } from "@/common/common-models";
import { formatUserDominoName } from "@/common/common-utils";

const callSummaryService: CallSummaryService = new CallSummaryService();

export class GridFilterService {
  public applyFilters(
    cleoCallSummaries: ICleoCallSummary[],
    filters: IGridFilter[]
  ): ICleoCallSummary[] {
    return cleoCallSummaries.filter((cleoCallSummary: ICleoCallSummary) => {
      return this.applyFiltersToCall(cleoCallSummary, filters);
    });
  }

  /**
   * As of now, the filters are: filter1 && filter2, etc., i.e. they all need to equate to true.
   * @param cleoCallSummary
   * @param filters
   */
  public applyFiltersToCall(
    cleoCallSummary: ICleoCallSummary,
    filters: IGridFilter[]
  ): boolean {
    return filters.reduce((accum: boolean, filter: IGridFilter) => {
      let doesMatch;
      if (filter.filterName !== "QUICK" && filter.filterFunction) {
        doesMatch = filter.filterFunction(cleoCallSummary, filter.filterValue);
      } else {
        doesMatch = this.matchesQuickFilter(
          cleoCallSummary,
          (filter.filterValue ? filter.filterValue : "") as string
        );
      }

      if (!doesMatch) {
        accum = false;
      }
      return accum;
    }, true as boolean);
  }

  public matchesQuickFilter(
    cleoCallSummary: ICleoCallSummary,
    filterValue: string
  ): boolean {
    filterValue = filterValue.toUpperCase();

    const dx = (fv: string) => {
      const dx =
        cleoCallSummary.FinalDispositionCode &&
        cleoCallSummary.FinalDispositionCode.length
          ? cleoCallSummary.FinalDispositionCode
          : cleoCallSummary.ChFinalDispositionCode &&
            cleoCallSummary.ChFinalDispositionCode.length > 0
          ? cleoCallSummary.ChFinalDispositionCode
          : "";

      return dx.toUpperCase().indexOf(fv) > -1;
    };

    if (dx(filterValue)) {
      return true;
    }

    const callTown = callSummaryService.getTown(cleoCallSummary).toUpperCase();
    if (callTown.indexOf(filterValue) > -1) {
      return true;
    }

    if (
      cleoCallSummary.CallClassification.Description &&
      cleoCallSummary.CallClassification.Description.toUpperCase().indexOf(
        filterValue
      ) > -1
    ) {
      return true;
    }

    if (
      cleoCallSummary.CallService.Description &&
      cleoCallSummary.CallService.Description.toUpperCase().indexOf(
        filterValue
      ) > -1
    ) {
      return true;
    }

    const flatPropNames: (keyof ICleoCallSummary)[] = [
      "CallAge",
      "CallNo",
      "CallDoctorName",
      "CallSurname",
      "CallForename",
    ];

    for (let i = 0; i < flatPropNames.length; i++) {
      const value = cleoCallSummary[flatPropNames[i]];
      if (value) {
        const pos = value
          .toString()
          .toUpperCase()
          .indexOf(filterValue);
        if (pos > -1) {
          //  We got a match, no need to iterate through any more filters.
          return true;
        }
      }
    }
    return false;
  }

  public isCleoClientServiceMatched(
    cleoCallSummary: ICleoCallSummary,
    filterValue: CLEO_CLIENT_SERVICE[]
  ): boolean {
    let cleoClientService = cleoCallSummary.cleoClientService.toUpperCase();
    if (cleoClientService === "FOLLOW_UP") {
      cleoClientService = "FOLLOW UP";
    }

    // If any of the "normal" CLEO_CLIENT_SERVICEs match, then we're good.
    const isMatch = filterValue.indexOf(cleoClientService) > -1;

    // But we have 2 processes that use "FOLLOW UP" or "FOLLOW_UP" as the CLEO_CLIENT_SERVICE.
    //  So if we're filtering on FOLLOW_UP, then we need to check for both.
    // const isFollowUpMatch = ["FOLLOW_UP", "FOLLOW UP"].includes(
    //   cleoClientService
    // );

    // return isMatch || isFollowUpMatch;
    return isMatch;
  }

  /**
   * 1. Are any cases assigned to the current clini user.
   * 2. Is the paramedic on scene (Relationship to patient).
   * 3. If some of the CLEO_CLIENT_SERVICEs are selected, then we need to check for those as well.
   */
  public clientServicesMyAssignedAndParamedicOnScene(
    cleoCallSummary: ICleoCallSummary,
    gridFilterUserInput: IGridFilterUserInput
  ): boolean {
    if (gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled === null) {
      return true;
    }

    // const userName = filterValue.userName.toUpperCase();
    const userName = gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.userName.toUpperCase();
    const doctorName = formatUserDominoName(
      cleoCallSummary.CallDoctorName.toUpperCase(),
      "CN"
    );

    // Will match "Ambulance Paramedic on Scene" AND "Paramedic on Scene"
    const isParaMedicOnScene =
      cleoCallSummary.CallCRel.toUpperCase().indexOf("PARAMEDIC ON SCENE") > -1;

    let isClientServiceMatched = false;
    if (gridFilterUserInput.CLEO_CLIENT_SERVICE.length > 0) {
      isClientServiceMatched = this.isCleoClientServiceMatched(
        cleoCallSummary,
        gridFilterUserInput.CLEO_CLIENT_SERVICE
      );
    }

    if (
      doctorName === userName ||
      isParaMedicOnScene ||
      isClientServiceMatched
    ) {
      console.warn(
        "clientServicesMyAssignedAndParamedicOnScene() cleoCallSummary.CallNo: " +
          cleoCallSummary.CallNo +
          " userName: " +
          userName +
          " doctorName: " +
          doctorName +
          " isParaMedicOnScene: " +
          isParaMedicOnScene +
          " isClientServiceMatched: " +
          isClientServiceMatched
      );

      if (cleoCallSummary.CallNo.toString() === "250658018") {
        console.error(
          "clientServicesMyAssignedAndParamedicOnScene() cleoCallSummary.CallNo: " +
            cleoCallSummary.CallNo +
            " userName: " +
            userName +
            " doctorName: " +
            doctorName +
            " isParaMedicOnScene: " +
            isParaMedicOnScene +
            " isClientServiceMatched: " +
            isClientServiceMatched,
          {
            cleoClientService: cleoCallSummary.cleoClientService,
            cleoClientServices: gridFilterUserInput.CLEO_CLIENT_SERVICE,
          }
        );
      }
    }

    return (
      doctorName === userName || isParaMedicOnScene || isClientServiceMatched
    );
  }

  /**
   * This is the default filter user input object.
   */
  public factoryGridFilterUserInput(): IGridFilterUserInput {
    return {
      QUICK: "",
      COV19: "",
      NOT_COV19: "",
      TOWN: "",
      CLASSIFICATION: "",
      CLEO_CLIENT_SERVICE: [],
      SUB_CLASSIFICATION: "",
      DX: {
        include: true,
        dxCodes: [],
      },
      ASSIGNED_TO: null,
      BREACHED: null,
      PDS_TRACED_AND_VERIFIED: null,
      TOXIC_INGESTION_AND_EMPTY: null,
      AMBULANCE_CLEO_CLIENT_SERVICES: null,
      ED_VALIDATION_CLEO_CLIENT_SERVICES: null,
      MY_CASE_AND_PARAMEDIC_ON_SCENE: {
        userName: "",
        enabled: null,
      },
      REQUIRES_VALIDATION: null,
      FOLLOW_UP: null,
    };
  }
}
