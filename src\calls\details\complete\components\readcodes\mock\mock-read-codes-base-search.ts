import { ICleoServerResponse } from "@/common/cleo-common-models";
import { IApiServerReadCodes } from "@/calls/details/complete/components/readcodes/readcodes-models";

export const mockReadCodesBaseSearch: ICleoServerResponse<IApiServerReadCodes> = {
  RESULT: "SUCCESS",
  DATA: {
    key: "",
    count: 5,
    readCodes: [
      {
        ReadCode: "XaBVJ",
        ReadCodeDescription: "Clinical findings",
        HasChildren: true
      },
      {
        ReadCode: "9....",
        ReadCodeDescription: "Administration",
        HasChildren: false
      },
      {
        ReadCode: "X78tJ",
        ReadCodeDescription: "Additional values",
        HasChildren: true
      },
      {
        ReadCode: "Xa22Y",
        ReadCodeDescription: "Operatns, proceds, interventns",
        HasChildren: true
      },
      {
        ReadCode: "X74VQ",
        ReadCodeDescription: "Context-dependent categories",
        HasChildren: true
      }
    ]
  }
};
