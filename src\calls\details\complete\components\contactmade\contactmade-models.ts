import { ISimpleButtonInputValue } from "../../SimpleButtonSelecter.vue";

export type ContactType = "CONTACT_MADE" | "CONTACT_FAILURE";

export const contactMadeButtonOption: ISimpleButtonInputValue<
  ContactType,
  ContactType
> = {
  id: "CONTACT_MADE",
  description: "Contact Made",
  value: "CONTACT_MADE"
};

export const contactFailureButtonOption: ISimpleButtonInputValue<
  ContactType,
  ContactType
> = {
  id: "CONTACT_FAILURE",
  description: "Contact Failure",
  value: "CONTACT_FAILURE"
};

export const ContactTypeMap: Record<
  ContactType,
  ISimpleButtonInputValue<ContactType, ContactType>
> = {
  CONTACT_MADE: contactMadeButtonOption,
  CONTACT_FAILURE: contactFailureButtonOption
};
