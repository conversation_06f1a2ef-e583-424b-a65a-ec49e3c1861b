import { QueueCollection } from "@/common/collections/QueueCollection";
import { StackCollection } from "@/common/collections/StackCollection";

interface IPersonMock {
  name: string;
  age: number;
}

describe("QueueCollection", () => {
  it("stuff", () => {
    const someStuff: IPersonMock[] = [
      {
        name: "bob",
        age: 33
      },
      {
        name: "<PERSON>",
        age: 34
      },
      {
        name: "<PERSON>",
        age: 34
      },
      {
        name: "<PERSON>",
        age: 29
      }
    ];
    const queue = new QueueCollection<IPersonMock>();

    expect(queue.size()).toBe(0);

    queue.enqueue(someStuff[0]);

    expect(queue.size()).toBe(1);

    queue.enqueue(someStuff[1]);
    queue.enqueue(someStuff[2]);
    queue.enqueue(someStuff[3]);

    const personMock = queue.dequeue();

    expect(personMock?.name).toBe("bob");

    expect(queue.size()).toBe(3);

    queue.clearStorage();
    expect(queue.size()).toBe(0);
  });
});

describe("StackCollection", () => {
  it("stuff", () => {
    const someStuff: IPersonMock[] = [
      {
        name: "bob",
        age: 33
      },
      {
        name: "Sue",
        age: 34
      },
      {
        name: "Jack",
        age: 34
      },
      {
        name: "Nick",
        age: 29
      }
    ];
    const stack = new StackCollection<IPersonMock>();

    expect(stack.size()).toBe(0);

    stack.push(someStuff[0]);

    expect(stack.size()).toBe(1);

    stack.push(someStuff[1]);
    stack.push(someStuff[2]);
    stack.push(someStuff[3]);

    expect(stack.size()).toBe(4);
    expect(stack.peek()?.name).toBe("Nick");

    stack.pop();
    expect(stack.size()).toBe(3);
    expect(stack.peek()?.name).toBe("Jack");

    const data = stack.getStorage();
    expect(data.length).toBe(3);

    data[data.length - 1].name = "xxx";
    expect(stack.peek()?.name).toBe("Jack");

    stack.clearStorage();
    expect(stack.size()).toBe(0);
  });

  it("stuff", () => {
    const someStuff: IPersonMock[] = [
      {
        name: "bob",
        age: 33
      },
      {
        name: "Sue",
        age: 34
      },
      {
        name: "Jack",
        age: 34
      },
      {
        name: "Nick",
        age: 29
      }
    ];
    const stack = new StackCollection<IPersonMock>(2);

    expect(stack.size()).toBe(0);

    stack.push(someStuff[0]);

    expect(stack.size()).toBe(1);

    stack.push(someStuff[1]);
    expect(stack.peek()?.name).toBe("Sue");
    stack.push(someStuff[2]);
    expect(stack.peek()?.name).toBe("Jack");

    expect(stack.size()).toBe(2);
  });

  it("check max size", () => {
    // const person: IPersonMock = [
    //   {
    //     name: "bob",
    //     age: 33
    //   },
    //   {
    //     name: "Sue",
    //     age: 34
    //   },
    //   {
    //     name: "Jack",
    //     age: 34
    //   },
    //   {
    //     name: "Nick",
    //     age: 29
    //   }
    // ];

    const stack = new StackCollection<IPersonMock>(5);

    expect(stack.size()).toBe(0);

    for (let i = 0; i < 10; i++) {
      stack.push({ name: "bob-" + i, age: i });
    }

    expect(stack.size()).toBe(5);

    expect(stack.peek()?.name).toBe("bob-9");

    // stack.push(someStuff[1]);
    // expect(stack.peek()?.name).toBe("Sue");
    // stack.push(someStuff[2]);
    // expect(stack.peek()?.name).toBe("Jack");
    //
    // expect(stack.size()).toBe(2);
  });
});
