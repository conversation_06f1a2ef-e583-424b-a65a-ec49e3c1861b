import { CallSummaryHtml } from "@/calls/summary/call-summary-html";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { format, parseISO } from "date-fns";
import { toLocalISOWithOffset } from "@/common/common-utils";

const callSummaryHtml = new CallSummaryHtml();

describe("CallSummaryHtml", () => {
  it("reachedFailedContactWarn", () => {
    const warnMinsUrgent = 20;
    const warnMinsNotUrgent = 40;

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: false,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6
        } as ICleoCallSummary,
        warnMins<PERSON>rgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: false,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: ""
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: false,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:05:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:05:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: false,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:10:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: false,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:40:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: true,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:05:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(true);

    //  Urgent call, last called over 20mins
    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: true,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:29:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: true,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:30:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: true,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:45:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:50:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedFailedContactWarn(
        {
          CallUrgentYn: true,
          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T23:50:00"
        } as ICleoCallSummary,
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T23:55:00")
      )
    ).toBe(false);
  });

  it("reachedBreachPriority", () => {
    //  Dx35 not in Priority list.
    expect(
      callSummaryHtml.reachedBreachPriority(
        {
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T15:58:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          ChFinalDispositionCode: "Dx35"
        } as ICleoCallSummary,
        "BreachActualTime",
        ["DX333"],
        parseISO("2021-02-04T17:58:29+00:00")
      )
    ).toBe(false);

    expect(
      callSummaryHtml.reachedBreachPriority(
        {
          BreachActualTime: "2021-02-04T17:00:29+00:00",
          ChFinalDispositionCode: "Dx333"
        } as ICleoCallSummary,
        "BreachActualTime",
        ["DX333"],
        parseISO("2021-02-04T17:00:30+00:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.reachedBreachPriority(
        {
          BreachActualTime: "2021-02-04T17:00:30+00:00",
          ChFinalDispositionCode: "Dx333"
        } as ICleoCallSummary,
        "BreachActualTime",
        ["DX333"],
        parseISO("2021-02-04T17:00:30+00:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.reachedBreachPriority(
        {
          BreachActualTime: "2021-02-04T17:00:31+00:00",
          ChFinalDispositionCode: "Dx333"
        } as ICleoCallSummary,
        "BreachActualTime",
        ["DX333"],
        parseISO("2021-02-04T17:00:30+00:00")
      )
    ).toBe(false);
  });

  it("getRowStyle", () => {
    const warnMinsUrgent = 20;
    const warnMinsNotUrgent = 40;

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T15:58:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallCallback: 2,
          CallReceivedTime: "2021-02-04T15:53:55+00:00",
          CallUrgentYn: false,
          Call1StContact: "2021-02-04T16:29:51+00:00",
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx35",
          ChFinalDispositionDescription:
            "Speak to Clinician from our service within 2 hours",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35",
          FinalDispositionDescription: ""
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:58:29+00:00")
      ).message
    ).toBe("DEFAULT");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T15:58:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallCallback: 2,
          CallReceivedTime: "2021-02-04T15:53:55+00:00",
          CallUrgentYn: false,
          Call1StContact: "2021-02-04T16:29:51+00:00",
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx35",
          ChFinalDispositionDescription:
            "Speak to Clinician from our service within 2 hours",
          CliniHighPriority: true,
          FinalDispositionCode: "Dx35",
          FinalDispositionDescription: ""
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:58:29+00:00")
      ).message
    ).toBe("CLINI_HIGH_PRIORITY");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T15:58:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallCallback: 2,
          CallReceivedTime: "2021-02-04T15:53:55+00:00",
          CallUrgentYn: false,
          Call1StContact: "2021-02-04T16:29:51+00:00",
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx35",
          ChFinalDispositionDescription:
            "Speak to Clinician from our service within 2 hours",
          CliniHighPriority: true,
          FinalDispositionCode: "Dx35",
          FinalDispositionDescription: ""
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:58:29+00:00")
      ).message
    ).toBe("CLINI_HIGH_PRIORITY");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T15:58:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallCallback: 2,
          CallReceivedTime: "2021-02-04T15:53:55+00:00",
          CallUrgentYn: false,
          Call1StContact: "2021-02-04T16:29:51+00:00",
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          ChFinalDispositionDescription:
            "Speak to Clinician from our service within 2 hours",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35",
          FinalDispositionDescription: ""
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:58:29+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__BREACH");

    expect(
      callSummaryHtml.reachedBreachPriority(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:00:30+00:00",
          CallUrgentYn: false,
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false
        } as ICleoCallSummary,
        "BreachActualTime",
        ["DX333"],
        parseISO("2021-02-04T17:00:30+00:00")
      )
    ).toBe(true);

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:00:30+00:00",
          CallUrgentYn: false,
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:00:29+00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__STANDARD");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:00:30+00:00",
          CallUrgentYn: false,
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:00:30+00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__BREACH");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:00:30+00:00",
          CallUrgentYn: false,
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:00:33+00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__BREACH");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:38:28+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__WARN");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:42:28+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__WARN");

    expect(
      format(parseISO("2021-04-05T16:42:08+01:00"), "dd/MM/yyyy HH:mm")
    ).toBe("05/04/2021 16:42");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:49:28+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__PRE_BREACH");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:59:28+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__BREACH");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35",

          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T16:10:30"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T16:15:30+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__STANDARD");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "Dx35",

          PatientContactCode: "No Answer",
          PatientContactCodeCount: 6,
          LastFailedContactTime: "2021-02-04T16:10:30"
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T16:55:30")
      ).message
    ).toBe("LEVEL_1_PRIORITY__STANDARD");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T17:58:28+00:00",
          BreachPreActualTime: "2021-02-04T17:48:28+00:00",
          BreachWarnActualTime: "2021-02-04T17:38:28+00:00",
          CallUrgentYn: false,
          Call1StContactPathways: "2021-02-04T15:56:24+00:00",
          ChFinalDispositionCode: "Dx333",
          CliniHighPriority: false,
          FinalDispositionCode: "",

          PatientContactCode: "",
          PatientContactCodeCount: 0,
          LastFailedContactTime: ""
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T17:38:28+00:00")
      ).message
    ).toBe("LEVEL_1_PRIORITY__WARN");

    expect(
      callSummaryHtml.getRowStyle(
        {
          ApplyBreach: true,
          BreachActualTime: "2021-02-04T16:54:44+00:00",
          BreachPreActualTime: "2021-02-04T10:54:44+00:00",
          BreachWarnActualTime: "2021-02-04T12:54:44+00:00",
          CallArrivedTime: "2021-02-04T16:24:02+00:00",
          CallCallback: 0,
          CallNo: **********,
          CallReceivedTime: "2021-02-04T10:54:41+00:00",
          CallUrgentYn: true,
          Call1StContact: "2021-02-04T10:54:41+00:00",
          ChFinalDispositionCode: "Dx02",
          CliniHighPriority: true,
          ComfortSentServiceTime: "2021-02-04T13:00:08+00:00",
          FinalDispositionCode: "Dx02",
          IsLocked: "",
          Itk111Online: false,
          IucContract: {
            Id: null,
            Description: "",
            Type: "111"
          },
          PathwaysCaseId: "8ce13b2b-05b8-4294-9d7c-a249b44ad1f8",
          PatientContactCode: "",
          PatientContactCodeCount: 0,
          PdsTracedAndVerified: true,
          WalkIn: false,
          StartConsultationPerformed: false
        } as ICleoCallSummary,
        ["DX333"],
        warnMinsUrgent,
        warnMinsNotUrgent,
        parseISO("2021-02-04T16:55:30")
      ).message
    ).toBe("CLINI_HIGH_PRIORITY");
  });

  it("comfortCallOutput", () => {
    let res;

    //  Only CLEO -> MessageBird
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-02-04T16:55:30+00:00",
      ComfortSmsTime: "",
      CourtesyUser: "",
      CourtesyTime: "",
      CourtesyCount: 0,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(true);
    expect(res.icon).toBe("CLEO_SENT");

    //  CLEO -> MessageBird -> Response
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-02-04T16:55:30+00:00",
      ComfortSmsTime: "2021-02-04T16:58:30+00:00",
      CourtesyUser: "",
      CourtesyTime: "",
      CourtesyCount: 0,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(true);
    expect(res.cleoReceivedSms).toBe(true);
    expect(res.icon).toBe("CLEO_RECEIVED");

    //  CLEO -> MessageBird -> Response and Manual is before auto
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-02-04T16:20:30+00:00",
      ComfortSmsTime: "2021-02-04T16:20:31+00:00",
      CourtesyUser: "CN=Test Doctor/O=staging",
      CourtesyTime: "2021-02-04T16:20:28+00:00",
      CourtesyCount: 1,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(true);
    expect(res.cleoReceivedSms).toBe(true);
    expect(res.icon).toBe("CLEO_RECEIVED");

    //  CLEO -> MessageBird -> Response and Manual is after auto
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-02-04T16:20:30+00:00",
      ComfortSmsTime: "2021-02-04T16:20:31+00:00",
      CourtesyUser: "CN=Test Doctor/O=staging",
      CourtesyTime: "2021-02-04T16:20:33+00:00",
      CourtesyCount: 1,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(true);
    expect(res.cleoReceivedSms).toBe(true);
    expect(res.icon).toBe("MANUAL_UNSUCCESSFUL");

    //  CLEO -> MessageBird -> Response and Manual is after auto with contact
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-02-04T16:20:30+00:00",
      ComfortSmsTime: "2021-02-04T16:20:31+00:00",
      CourtesyUser: "CN=Test Doctor/O=staging",
      CourtesyTime: "2021-02-04T16:20:33+00:00",
      CourtesyCount: 1,
      CourtesyContact: true
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(true);
    expect(res.cleoReceivedSms).toBe(true);
    expect(res.icon).toBe("MANUAL_SUCCESS");
    expect(res.count).toBe(2);

    //  Only Manual no contact
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "",
      ComfortSmsTime: "",
      CourtesyUser: "CN=Test Doctor/O=staging",
      CourtesyTime: "2021-02-04T16:20:33+00:00",
      CourtesyCount: 1,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(false);
    expect(res.cleoReceivedSms).toBe(false);
    expect(res.icon).toBe("MANUAL_UNSUCCESSFUL");
    expect(res.count).toBe(1);

    //  Only Manual with contact
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "",
      ComfortSmsTime: "",
      CourtesyUser: "CN=Test Doctor/O=staging",
      CourtesyTime: "2021-02-04T16:20:33+00:00",
      CourtesyCount: 1,
      CourtesyContact: true
    } as ICleoCallSummary);
    expect(res.cleoSentSms).toBe(false);
    expect(res.cleoReceivedSms).toBe(false);
    expect(res.icon).toBe("MANUAL_SUCCESS");
    expect(res.count).toBe(1);

    //  Second sent
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-12-13T15:17:01+00:00",
      ComfortSmsTime: "2021-12-13T15:18:01+00:00",
      ComfortSentService2Time: "2021-12-13T15:19:01+00:00",
      ComfortSmsTime2: null,
      CourtesyUser: "",
      CourtesyTime: "",
      CourtesyCount: 0,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.icon).toBe("CLEO_SENT2");

    //  Second sent
    res = callSummaryHtml.comfortCallOutput({
      ComfortSentServiceTime: "2021-12-13T15:17:01+00:00",
      ComfortSmsTime: "2021-12-13T15:18:01+00:00",
      ComfortSentService2Time: "2021-12-13T15:19:01+00:00",
      ComfortSmsTime2: "2021-12-13T15:19:01+00:00",
      CourtesyUser: "",
      CourtesyTime: "",
      CourtesyCount: 0,
      CourtesyContact: false
    } as ICleoCallSummary);
    expect(res.icon).toBe("CLEO_RECEIVED2");
  });

  it("comfortCallOutput one off test", () => {
    const data = ({
      AllViewInclude: "",
      AllViewExclude: "0",
      ApplyBreach: true,
      BreachActualTime: "2021-03-05T13:40:46+00:00",
      BreachPreActualTime: "2021-03-05T13:10:46+00:00",
      BreachPriority: 3,
      BreachWarnActualTime: "2021-03-05T13:30:46+00:00",
      BreachLevel1Mins: 60,
      CallAddress1: "47 Cannell Green",
      CallAddress2: "NORWICH",
      CallAddress3: "Norfolk",
      CallAddress4: "",
      CallAge: 27,
      CallAgeClass: "yrs",
      CallAppointmentTime: null,
      CallArrivedTime: null,
      CallCallback: 0,
      CallClassification: {
        Id: 10,
        Description: "Nurse Advice"
      },
      CallCName: "",
      CallCreatedBy: "Chloe Lipscombe",
      CallCRel: "Patient",
      CallDobIso: null,
      CallDoctorName: "",
      CallForename: "Dovile",
      CallGenderId: 2,
      CallInformationalSubOutcomes: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      CallNhsNo: "",
      CallNo: **********,
      CallPostCode: "NR3 1TT",
      CallReceivedTime: "2021-03-05T12:59:44+00:00",
      CallService: {
        Id: 3,
        Description: "Norfolk and Wisbech 111",
        Type: "111"
      },
      CallServiceOriginal: "",
      cleoClientService: "",
      CallStatusValue: 1,
      CallSubClassification: {
        Id: null,
        Description: ""
      },
      CallSurname: "APANAVICIUTE",
      CallSymptoms:
        "Toothache, temp 38, abdo pain, vomiting, no energy, 15 weeks pregnant - Sandy Clinician Norfolk advised to ask pt if she would like to be assessed for abdo pain and vomiting or will pt go elsewhere for that help, if pt would like to be assessed then give dental dispo advice then reassess. Pt states she would just like help with her dental problem.",
      CallTelNo: "07777777777",
      CallTelNoR: "",
      CallTelNo_R: "",
      CallTelNoAlt1: "",
      CallTelNoAltType1: "",
      CallTown: "",
      CallUrgentYn: true,
      CallWithBaseAckTime: null,
      CallWarmTransferred: false,
      Call1StContact: "2021-03-05T15:30:10+00:00",
      Call1StContactPathways: "2021-03-05T13:02:24+00:00",
      CaseWarmTransferFail: "",
      ChFinalDispositionCode: "Dx324",
      ChFinalDispositionDescription:
        "Speak to a Clinician from our service Immediately - Refused Disposition",
      CliniHighPriority: false,
      ComfortSentServiceTime: null,
      ComfortSmsTime: "",
      ComfortSentService2Time: null,
      ComfortSmsTime2: null,
      ComfortCallResponse: null,
      CourtesyUser: "",
      CourtesyTime: "",
      CourtesyCount: 0,
      CourtesyContact: false,
      DutyBase: "",
      DispatchVehicle: "",
      FinalDispositionCode: "Dx324",
      FinalDispositionDescription: "",
      IsLocked: "",
      Itk111Online: false,
      LastFailedContactTime: "2021-03-05T15:36:17+00:00",
      IucContract: {
        Id: null,
        Description: "",
        Type: ""
      },
      PathwaysCaseId: "91f40fe1-d164-471a-89b1-9e3bf677813f",
      PatientContactCode: "No Answer",
      PatientContactCodeCount: 1,
      PdsTracedAndVerified: true,
      PDSAdminTrace: "",

      StartConsultationPerformed: true,
      WalkIn: false,
      CasValidationCount: 0,
      CasValidationUser: "",
      CasValidationTime: "",
      CasValidationReason: "",
      CaseComments: "",
      COMPLETE_PREVENT: "0",
      LinkedCallID: "",
      FOLLOW_UP_URGENT: "0",
      FOLLOW_UP_Active: "",
      Call_HCP: "0",
      OversightValidationType: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      SMS_COUNT: "1",
      SMS_HAS: true,
      SMS_SENT: true,
      SMS_LATEST_AT: "2021-03-05T15:36:17+00:00",
      SMS_LATEST_USER: "CN=Test Doctor/O=staging",
      SMS_LATEST_MESSAGE: "Hello",

      Cpl_supportTypeRequired: "",
      GOODSAM_IMAGE_STATUS: ""
    } as any) as ICleoCallSummary;

    //  Only CLEO -> MessageBird
    expect(
      callSummaryHtml.reachedFailedContactWarn(
        data,
        40,
        20,
        parseISO("2021-03-05T16:50:00")
      )
    ).toBe(true);
  });

  // getFollowUpActiveTime
  it("getFollowUpActiveTime", () => {
    expect(
      callSummaryHtml.getFollowUpActiveTime({
        FOLLOW_UP_Active: "17/03/2025 08:35:00"
      } as ICleoCallSummary)
    ).toBe("17th Mar 08:35");

    const dateWithNoTime = "17/03/2025";
    const isoFollowUpActive = toLocalISOWithOffset(dateWithNoTime);

    const timePortion = dateWithNoTime.length;

    expect(timePortion).toBe(10);

    expect(isoFollowUpActive.error).toBe(
      "time not in format HH:MM:SS: 17/03/2025"
    );

    expect(
      callSummaryHtml.getFollowUpActiveTime({
        FOLLOW_UP_Active: dateWithNoTime
      } as ICleoCallSummary)
    ).toBe("17th Mar 00:00");
  });
});
