import { ICallDetailLegacy } from "@/calls/details/call-details-legacy-models";
import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";

export function getCallDetail(
  callId: string | number,
  lockCall = true
): Promise<ICallDetailLegacy> {
  if (process.env.NODE_ENV === "development") {
    return Promise.resolve(window.CallControllerClient.JSONFields);
  } else {
    return https.get(
      CLEO_CONFIG.CLEO.XCLEO_PATH +
        "/xpbeaninterface.xsp?processformat=json&action=GETDOCJSONEXTERNAL&sid=" +
        callId +
        "&LOCK=" +
        (lockCall ? "1" : ""),
      {
        responseType: "json"
      }
    );
  }
}

// export class CallDetailData {
//   public getCallDetail(callId: string | number, lockCall = true): Promise<ICallDetailLegacy> {
//     if (process.env.NODE_ENV === "development") {
//       return Promise.resolve(window.CallControllerClient.JSONFields);
//     } else {
//       return https.get(
//         CLEO_CONFIG.CLEO.XCLEO_PATH +
//         "/xpbeaninterface.xsp?processformat=json&action=GETDOCJSONEXTERNAL&sid=" + callId + "&LOCK=" + (lockCall ? "1" : ""),
//         {
//           responseType: "json"
//         }
//       );
//     }
//   }
// }
