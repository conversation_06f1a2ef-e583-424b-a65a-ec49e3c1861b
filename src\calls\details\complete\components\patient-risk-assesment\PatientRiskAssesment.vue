<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <div class="complete-step--subheader">
      Are you closing this case in line with the organisational failed contact
      process?
    </div>

    <div class="ic24-vertical-spacer-large"></div>

    <SimpleButtonSelecter
      class="ic24-flex-column ic24-flex-gap-large complete-step--simple-buttons"
      :options="options"
      :value="patientRiskAssessmentInternal.risk"
      @input="onSelectedOption"
    />

    <div class="ic24-vertical-spacer-large"></div>

    <div class="ic24-flex-column">
      <div class="complete-step--subheader">
        Mitigating actions taken: please give details.
      </div>

      <div class="ic24-vertical-spacer-large"></div>

      <label>Please give details.</label>
      <div class="ic24-vertical-spacer-large"></div>
      <textarea
        v-model="patientRiskAssessmentInternal.actionTaken"
        @change="onChanged"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import {
  IPatientRiskAssessment,
  IStep
} from "@/calls/details/complete/complete-models";
import {
  ISimpleButtonInputValue,
  SimpleButtonTypeBoolean
} from "@/calls/details/complete/simple-button-selector-models";
import { simpleObjectClone } from "@/common/common-utils";
import SimpleButtonSelecter from "@/calls/details/complete/SimpleButtonSelecter.vue";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";

export default defineComponent({
  name: "PatientRiskAssessment",
  components: { CompleteStepHeader, SimpleButtonSelecter },
  props: {
    step: {
      type: Object as PropType<IStep<"PATIENT_RISK_ASSESSMENT">>,
      required: true
    },
    patientRiskAssessment: {
      type: Object as PropType<IPatientRiskAssessment>,
      required: true
    }
  },
  setup(
    props: { patientRiskAssessment: IPatientRiskAssessment },
    context: SetupContext
  ) {
    const patientRiskAssessmentInternal = ref<IPatientRiskAssessment>(
      simpleObjectClone(props.patientRiskAssessment)
    );

    const optionYes: ISimpleButtonInputValue<
      SimpleButtonTypeBoolean,
      SimpleButtonTypeBoolean
    > = {
      id: "YES",
      description: "Yes",
      value: "YES"
    };

    const optionNo: ISimpleButtonInputValue<
      SimpleButtonTypeBoolean,
      SimpleButtonTypeBoolean
    > = {
      id: "NO",
      description: "No",
      value: "NO"
    };

    const options: ISimpleButtonInputValue<
      SimpleButtonTypeBoolean,
      SimpleButtonTypeBoolean
    >[] = [optionYes, optionNo];

    watch(
      () => props.patientRiskAssessment,
      newValue => {
        patientRiskAssessmentInternal.value = simpleObjectClone(newValue);
      }
    );

    function onSelectedOption(
      option: ISimpleButtonInputValue<
        SimpleButtonTypeBoolean,
        SimpleButtonTypeBoolean
      >
    ) {
      console.log("PatientRiskAssessment.onSelectedOption: ", option);

      patientRiskAssessmentInternal.value.risk = option;

      onChanged();
    }

    function onChanged() {
      context.emit(
        "input",
        simpleObjectClone(patientRiskAssessmentInternal.value)
      );
    }

    return {
      patientRiskAssessmentInternal,
      optionYes,
      optionNo,
      options,
      onSelectedOption,
      onChanged
    };
  }
});
</script>

<style scoped>
textarea {
  padding: 4px;
  height: 60px;
  min-height: 60px;
  max-height: 200px;
  resize: vertical;
}
</style>
