<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <CompleteStepHeader :step="step" />

    <div class="ic24-flex-row ic24-flex-gap ic24-justify-fle-row-vert-center">
      <InputDebounce @input="controller.searchTermChanged" />
      <Ic24Button
        @click="controller.searchTermChanged()"
        :disabled="controller.state.isLoading"
      />

      <span v-text="controller.state.searchResultMessage"></span>
      <ic24-button
        @click="testAdd"
        v-if="configHelper.isLocalDevServer.value"
      />

      <a class="ic24-flex-row--end" href="#" @click.prevent="reset()">
        Reset
      </a>
    </div>

    <span>Click on '+' to expand an entry, double click to select.</span>

    <div class="ic24-flex-row ic24-flex-gap-large">
      <div class="ic24-flex-column">
        <div class="ic24-flex-column readCodeTreeContainer">
          <div :id="controller.state.ui.treeDivId"></div>
        </div>
      </div>

      <div class="ic24-flex-column ic24-flex-gap">
        <div
          class="ic24-flex-row ic24-flex-gap"
          v-for="(readCode, prop, index) in controller.state.readCodesSelected"
          :key="readCode.label"
        >
          <div v-text="index + 1" class="read-codes--table-index"></div>
          <div
            v-text="readCode.ReadCode"
            class="read-codes--table-readcode"
          ></div>
          <div v-text="readCode.ReadCodeDescription"></div>

          <a
            class="ic24-flex-row--end"
            href="#"
            @click.prevent="controller.removeSelected(readCode)"
          >
            X
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import {
  IReadCodesUserResponse,
  IStep
} from "@/calls/details/complete/complete-models";
import InputDebounce from "@/common/ui/fields/input-debounce.vue";
import { useReadCodeController } from "@/calls/details/complete/components/readcodes/useReadcodeController";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import { useConfigHelper } from "@/common/config/config-store";

export default defineComponent({
  name: "ReadCodes",
  components: { Ic24Button, InputDebounce, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"READ_CODES">>,
      required: true
    }
  },
  setup(
    props: { step: IStep<"READ_CODES">; value: IReadCodesUserResponse },
    context: SetupContext
  ) {
    const controller = useReadCodeController();

    const configHelper = useConfigHelper();

    onMounted(() => {
      controller.init();
    });

    const tempCounter = ref(0);

    function testAdd() {
      const readCodeToSelect =
        controller.state.readCodesTree[tempCounter.value];
      controller.addSelected(readCodeToSelect);
      tempCounter.value++;
    }

    watch(
      () => controller.state.readCodesSelected,
      (newValue, oldValue) => {
        context.emit("input", Object.values(newValue));
      }
    );

    function reset() {
      controller.reset();
      context.emit("input", []);
    }

    return { controller, testAdd, tempCounter, configHelper, reset };
  }
});
</script>

<style>
.read-codes--table-index {
  width: 20px;
}

.read-codes--table-readcode {
  width: 45px;
}

.readCodeTreeContainer {
  height: 400px;
  overflow-y: auto;
}
</style>
