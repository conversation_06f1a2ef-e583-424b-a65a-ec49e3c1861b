<template>
  <div class="calls-small-table--wrapper">
    <table>
      <tr>
        <td>Name</td>
        <td>Count</td>
        <td></td>
      </tr>
      <tr v-for="vehicle in vehicles" :key="vehicle.id">
        <td><span v-text="vehicle.name"></span></td>
        <td>
          <div v-text="getVehicleCallCount(vehicle)" class="vehicles-small-table--call-count"></div>
        </td>
        <td>
          <a href="#" v-on:click.prevent="locate(vehicle)">Locate</a>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { getMapControllerInstance } from "@/calls/maps/map-controller-factory";
import { IVehicle } from "@/vehicles/vehicles-models";

export default defineComponent({
  name: "vehicles-small-table",
  components: {},
  props: {
    vehicles: {
      type: Array as PropType<IVehicle[]>
    }
  },
  setup(props: { vehicles: IVehicle[] }, context: SetupContext) {
    const mapControllerInstance = getMapControllerInstance();

    function locate(vehicle: IVehicle) {
      const marker = mapControllerInstance.getMarker(vehicle, "CAR");
      if (marker) {
        mapControllerInstance.bounceMarkers([marker], true);
        mapControllerInstance.openMarkerInfoWindow(marker);
      }
    }

    function getVehicleCallCount(vehicle: IVehicle): number {
      const vehicleMap =
        mapControllerInstance.cleoMapControllerState.cars.assignedCalls[
          vehicle.name
        ];
      if (vehicleMap) {
        return vehicleMap.length;
      }
      return 0;
    }

    return {
      locate,
      getVehicleCallCount
    };
  }
});
</script>

<style scoped>
.vehicles-small-table--wrapper {

}

.vehicles-small-table--call-count {
  float: right;
}

</style>
