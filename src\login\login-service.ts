import { ITokenResponse } from "@/login/login-models";
import { ILoginStoreState } from "@/login/login-store";
import { loggerInstance } from "@/common/Logger";

export class LoginService {
  public factoryLoginStoreState(): ILoginStoreState {
    return {
      tokenResponse: this.factoryTokenResponse()
    };
  }

  public factoryTokenResponse(): ITokenResponse {
    return {
      access_token: "",
      expires_in: 0,
      token_type: "Bearer",
      scope: "api1"
    };
  }

  public requestJwt(ssoToken?: string): Promise<ITokenResponse | null> {
    const jwtFromUserSssoToken = ApplicationControllerClient.getSsoJwt();
    if (jwtFromUserSssoToken) {
      loggerInstance.log("LoginService.requestJwt() found SSO JWT");
      return Promise.resolve(jwtFromUserSssoToken);
    }

    loggerInstance.log("LoginService.requestJwt() request generic Jwt");
    return ApplicationControllerClient.requestGenericJwt();
  }

  public setJwtAccessToken(tokenResponse: ITokenResponse): void {
    sessionStorage.setItem("user", JSON.stringify(tokenResponse));
  }

  public getJwtAccessToken(): string {
    const tokenResponseStorage: string | null = sessionStorage.getItem("user");
    if (tokenResponseStorage) {
      const tokenResponse: ITokenResponse = JSON.parse(tokenResponseStorage);
      return tokenResponse.access_token;
    }
    return "";
  }
}
