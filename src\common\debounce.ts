export function debounce(func: any, wait: any, immediate?: any) {
  let timeout: any;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  return function(...args) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const context: any = this;
    const argsInternal: any = args;
    const later = function() {
      timeout = null;
      if (!immediate) {
        func.apply(context, argsInternal);
      }
    };

    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) {
      func.apply(context, argsInternal);
    }
  };
}
