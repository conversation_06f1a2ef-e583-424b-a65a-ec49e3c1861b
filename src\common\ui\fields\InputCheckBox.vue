<template>
  <label class="input-check-box--label">
    <input
      type="checkbox"
      class="browser-default ic24-input-field input-check-box--input"
      :class="checkBoxClass"
      style="accent-color: var(--ic24-button-primary--background)"
      :disabled="isDisabled"
      v-model="valueInternal"
      v-on:change="submit"
    />
    <!--Leave this as div for now, else have a fight with Materialize-->
    <slot>
      <div
        class="input-check-box--label"
        :class="isDisabled ? 'input-check-box--label-disabled' : ''"
        v-text="valueLabel"
        v-if="valueLabel.length > 0"
      ></div>
    </slot>
  </label>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";

export default defineComponent({
  name: "InputCheckBox",
  components: {},
  inheritAttrs: false,
  props: {
    value: {
      type: Boolean,
      required: true
    },
    valueLabel: {
      type: String,
      default: ""
    },
    checkBoxClass: {
      type: String,
      default: () => {
        return "";
      }
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    isDebug: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  setup(
    props: {
      value: boolean;
      valueLabel: string;
      checkBoxClass: string;
      isDisabled: boolean;
      isDebug: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);

    watch(
      () => props.value,
      (newValue: boolean) => {
        if (props.isDebug) {
          console.log(
            "input-check-box.watch() props.value: " +
              props.value +
              ", changing to: " +
              newValue
          );
        }
        valueInternal.value = newValue;
        // if (newValue !== valueInternal.value) {
        //   valueInternal.value = newValue;
        // }
      },
      {
        immediate: true
      }
    );

    function submit() {
      console.log(
        "input-check-box.submit() value: " +
          props.value +
          ", changing to: " +
          !props.value
      );
      context.emit("input", valueInternal.value);
      context.emit("onChange", valueInternal.value);
      // console.log(">>>>>>>>>>", attrs.value);
    }
    // function keypressEnter() {
    //   console.log(
    //     "form-generic-input-text-v2.keypressEnter() value: " +
    //       valueInternal.value
    //   );
    //   context.emit("keypressEnter", valueInternal.value);
    // }
    //
    // function everything(x: unknown) {
    //   console.log("everything");
    // }

    return { valueInternal, submit };
  }
});
</script>

<style>
.input-check-box--label {
  display: flex;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  word-wrap: break-word;
  color: black;
  gap: var(--ic24-flex-gap);
}

.input-check-box--input {
  width: 16px !important;
}

.input-check-box--input:not(:checked),
.input-check-box--input:checked {
  position: unset !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}
</style>
