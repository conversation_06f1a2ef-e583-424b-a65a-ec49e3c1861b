<template>
  <div>
    <iframe
      v-if="!useHtml"
      :src="pathwaysReportUrl"
      id="pathways-report"
      class="pathways-report"
    ></iframe>
    <div v-if="useHtml" v-html="reportHtml"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from "@vue/composition-api";

import { GUID } from "@/common/common-models";
import { loggerInstance } from "@/common/Logger";

export default defineComponent({
  name: "pathways-report",
  props: {
    pathwaysCaseId: {
      required: true,
      type: String
    },
    reportHtml: {
      type: String
    }
  },
  components: {},
  setup(props: { pathwaysCaseId: GUID; reportHtml: string }) {
    const pathwaysReportUrl =
      window.NHSPathwaysSEHReportUrl + props.pathwaysCaseId;

    const useHtml = ref(props.reportHtml && props.reportHtml.length > 0);

    onMounted(() => {
      loggerInstance.log(
        "pathways-report pathwaysCaseId: ",
        props.pathwaysCaseId
      );
    });

    return {
      useHtml,
      pathwaysReportUrl
    };
  }
});
</script>

<style>
.pathways-report {
  width: 100%;
  height: 800px;
}
#TriageReportTitle {
  margin-bottom: 1em;
  font-weight: 600;
}
#TriageReportDetail {
}
</style>
