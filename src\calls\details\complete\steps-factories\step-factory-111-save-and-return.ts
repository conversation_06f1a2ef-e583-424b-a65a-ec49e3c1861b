import {
  CompleteStepName,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IStep,
  IValidationMessage,
  SaveAndReturnStepName111
} from "@/calls/details/complete/complete-models";
import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import {
  factoryStep,
  factorySteps,
  validationMapDefault
} from "@/calls/details/complete/complete-service";

export function factoryUserStepsConfigSaveAndReturn111(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<SaveAndReturnStepName111> {
  const stepsDefault: Record<
    SaveAndReturnStepName111,
    IStep<CompleteStepName>
  > = factorySteps();

  const steps: Record<SaveAndReturnStepName111, IStep<CompleteStepName>> = {
    EXIT_REASON: stepsDefault.EXIT_REASON,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    UNKNOWN: factoryStep("UNKNOWN", "UNKNOWN")
  };

  const validationDefault = validationMapDefault(
    state,
    completeControllerInput
  );

  const validateMap: Record<
    SaveAndReturnStepName111,
    () => IValidationMessage[]
  > = {
    EXIT_REASON: validationDefault.EXIT_REASON,
    FAILED_CONTACT_REASON: validationDefault.FAILED_CONTACT_REASON,
    UNKNOWN: validationDefault.UNKNOWN
  };

  const gotoNextMap: Record<SaveAndReturnStepName111, () => void> = {
    EXIT_REASON: () => {
      if (state.userResponse.exitReason.id === "FAILED_CONTACT") {
        state.currentStep = "FAILED_CONTACT_REASON";
      }
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  const gotoBackMap: Record<SaveAndReturnStepName111, () => void> = {
    EXIT_REASON: () => {
      state.currentStep = "EXIT_REASON";
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "EXIT_REASON";
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  return {
    steps: steps,
    validateMap: validateMap,
    gotoNextMap: gotoNextMap,
    gotoBackMap: gotoBackMap
  } as ICompleteUserStepsConfig<SaveAndReturnStepName111>;
}
