import { Module } from "vuex";
import { IRootState } from "@/store/store";

import { ConfigService } from "@/common/config/config-service";
import { ISimpleTrigger } from "@/calls/summary/call-summarry-models";
import { IAdapterAction } from "@/app-models";

import router, { LAUNCH_ROUTES_PATHS } from "@/router/router";
import { computed } from "@vue/composition-api";

const configService: ConfigService = new ConfigService();

export interface ISplitPane {
  max: number;
  min: number;
  size: number;
}

export interface ISplitPaneConfig {
  gridContentDefault: {
    size: number;
    minSize: number;
  };
  callSummaryDefault: {
    size: number;
    minSize: number;
    maxSize: number;
  };
  panes: ISplitPane[];
}

export interface IConfigStoreState {
  debug: boolean;
  isLocalDevServer: boolean;
  legacyGridResize: ISimpleTrigger<any>;
  priorityDxCodes: string[]; //  Ideally get this from back end.
  failedContactWarnMinutes: {
    notUrgent: 15;
    urgent: 15;
  };
  splitPanes: ISplitPaneConfig;
  adapterCleoAction: IAdapterAction;
}

export enum CONFIG_STORE_CONST {
  CONFIG__CONST_MODULE_NAME = "CONFIG__CONST_MODULE_NAME",

  CONFIG__MUTATION_SET_LEGACY_GRID_RESIZE = "CONFIG__MUTATION_SET_LEGACY_GRID_RESIZE",
  CONFIG__MUTATION_SET_SPLIT_PANES = "CONFIG__MUTATION_SET_SPLIT_PANES",
  CONFIG__MUTATION_SET_ADAPTER_CLEO_ACTION = "CONFIG__MUTATION_SET_ADAPTER_CLEO_ACTION"
}

const mutations = {
  [CONFIG_STORE_CONST.CONFIG__MUTATION_SET_LEGACY_GRID_RESIZE](
    state: IConfigStoreState,
    trigger: ISimpleTrigger<any>
  ): void {
    state.legacyGridResize = { ...trigger };
  },

  [CONFIG_STORE_CONST.CONFIG__MUTATION_SET_SPLIT_PANES](
    state: IConfigStoreState,
    panes: ISplitPane[]
  ): void {
    state.splitPanes.panes = panes;
  },

  [CONFIG_STORE_CONST.CONFIG__MUTATION_SET_ADAPTER_CLEO_ACTION](
    state: IConfigStoreState,
    adapterAction: IAdapterAction
  ): void {
    state.adapterCleoAction = Object.assign({}, adapterAction);

    if (adapterAction.payload.actionType === "ROUTE") {
      router.push(adapterAction.payload.data.path);
      return;
    }

    if (adapterAction.payload.actionType === "OPEN_CALL") {
      router
        .push({
          path:
            "/" +
            LAUNCH_ROUTES_PATHS.CALL_DETAIL_ROUTE +
            "/" +
            adapterAction.payload.data.callNumber
        })
        .catch(error => {
          //  https://stackoverflow.com/questions/57837758/navigationduplicated-navigating-to-current-location-search-is-not-allowed
          if (error.name !== "NavigationDuplicated") {
            throw error;
          }
        });
      return;
    }
  }
};
const getters = {};
const actions = {};

export const configStore: Module<IConfigStoreState, IRootState> = {
  namespaced: true,
  state: configService.factoryConfigStoreState(),
  mutations,
  getters,
  actions
};

// export const useIsLocalDevServer = computed<boolean>(() => {
//   return (configStore.state as IConfigStoreState).isLocalDevServer;
// });

export function useConfigHelper() {
  const isLocalDevServer = computed(() => {
    return process.env.NODE_ENV === "development";
  });

  return {
    isLocalDevServer
  };
}
