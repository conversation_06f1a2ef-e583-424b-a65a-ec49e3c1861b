<template>
  <div
    :title="'Socket Status: ' + socketStatus"
    class="grid-route-toolbar--socket-local standard-icon"
    :class="
      state.status === 'ON'
        ? 'standard-icon--socket-connected'
        : 'standard-icon--socket-not-connected'
    "
  ></div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  onUpdated,
  onUnmounted
} from "@vue/composition-api";
import { reactive } from "@vue/composition-api";
import { SOCKET_STATUS } from "@/socket/socket-controller";

export default defineComponent({
  // type inference enabled
  name: "socket-ui-status-new",
  props: {
    socketStatus: {
      type: String,
      default: "Not Connected",
      validator: function(value: SOCKET_STATUS) {
        // The value must match one of these strings
        return ["Connected", "Not Connected"].indexOf(value) !== -1;
      }
    }
  },
  setup() {
    const { state } = someTest();

    const book = reactive({
      title: "Vue 3 Guide"
    });
    return {
      state,
      book
    };
  }
});

function someTest(): {
  state: {
    count: number;
    status: string;
  };
} {
  let state = {
    count: 34,
    status: "ON"
  };

  return {
    state
  };
}
</script>
