<template>
  <div>
    <Ic24Button
      @click="onSelected(inputValue)"
      :title="inputValue.description"
      v-for="inputValue in options"
      :key="inputValue.id"
      class="complete-step--select-button"
      :class="
        isSelected(inputValue) ? 'complete-step--select-button-selected' : ''
      "
      :disabled="isSelected(inputValue)"
    />
  </div>
</template>

<script lang="ts">
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import {
  defineComponent,
  SetupContext,
  PropType,
  ref
} from "@vue/composition-api";

export interface ISimpleButtonInputValue<KEY_TYPE, VALUE_TYPE> {
  id: KEY_TYPE;
  description: string;
  value: VALUE_TYPE;
}

export default defineComponent({
  name: "SimpleButtonSelector",
  components: { Ic24Button },
  props: {
    value: {
      type: Object as PropType<ISimpleButtonInputValue<unknown, unknown>>,
      required: true
    },
    options: {
      type: Array as PropType<ISimpleButtonInputValue<unknown, unknown>[]>,
      required: true
    }
  },
  setup(
    props: {
      value: ISimpleButtonInputValue<unknown, unknown>;
      options: ISimpleButtonInputValue<unknown, unknown>[];
    },
    context: SetupContext
  ) {
    // const currentlySelected = ref<ISimpleButtonInputValue<unknown> | null>(
    //   null
    // );

    function onSelected(
      simpleButtonInputValue: ISimpleButtonInputValue<unknown, unknown>
    ) {
      // currentlySelected.value = simpleButtonInputValue;
      console.log("onSelected: " + simpleButtonInputValue);
      context.emit("input", simpleButtonInputValue);
    }

    function isSelected(optionValue: unknown) {
      const obj1 = JSON.stringify(optionValue);
      const obj2 = JSON.stringify(props.value);
      console.log("isSelected " + (obj1 == obj2), {
        optionValue,
        prop: props.value
      });
      return obj1 == obj2;
    }

    return { onSelected, isSelected };
  }
});
</script>
