import { ICallDetail } from "@/calls/details/call-details-models";
import { ILegacyKeywordSimple } from "@/common/cleo-legacy-models";
import { IsoDateTimeLocal } from "@/common/common-models";

// TODO this should be an Extract of CALL_CLASSIFICATION
export type FollowUpClassification = "Advice" | "Base" | "Visit";

// export interface DxCode {
//   dxCode: string;
// }

export interface ILegacyKeywordDx extends ILegacyKeywordSimple {
  //  codeId3 could be "" | "ADVICE" | "ADVICE;VISIT" | "ADVICE;BASE;VISIT", etc.
  codeID3: string;
}

export type DxCodeServerResponse = Record<string, ILegacyKeywordDx>;

export interface FollowUpType {
  classification: FollowUpClassification;
  label: string;
}

export const FOLLOW_UP_TYPE_RECORDS: Record<
  FollowUpClassification,
  FollowUpType
> = {
  Advice: {
    classification: "Advice",
    label: "Urgent Care Follow Up"
  },
  Base: {
    classification: "Base",
    label: "Face-to-Face Urgent Care Referral"
  },
  Visit: {
    classification: "Visit",
    label: "Home Visit Urgent Care Referral"
  }
};

export interface FollowUpInput {
  callDetail: ICallDetail;
}

export interface FollowUpInputState {
  followUpType: FollowUpType | null;
  dxCode: ILegacyKeywordDx | null;
  followUpNotes: string;
  cleoClientService: ILegacyKeywordDx | null;
  baseQuestionTriageType: string;
  dateTimePicker: IsoDateTimeLocal;
  userConfirmed: boolean;
}

export interface FollowUpState {
  input: FollowUpInputState;
  data: {
    isReady: boolean;
    isLoading: boolean;
    followUpRecord: Record<FollowUpClassification, FollowUpType>;
    dxCodes: DxCodeServerResponse;
    dxCodesForClassification: ILegacyKeywordDx[];
    cleoClientServices: DxCodeServerResponse;
    baseQuestionTriageTypes: string[];
    errors: Record<string, string>;
    dateTimePickerOptions: {
      minUtc: IsoDateTimeLocal;
      maxUtc: IsoDateTimeLocal;
      minHumanDisplay: string;
      maxHumanDisplay: string;
    };
  };
}

export interface FollowUpServerPayload {
  action: "MOVE_CASE_OVERSIGHT";
  CALLNO: number | string;
  CLASSIFICATION: FollowUpClassification;
  SUB_CLASSIFICATION: "";
  IS_ACTIVE_TIME: "" | IsoDateTimeLocal;
  DX: string;
  CLEO_CLIENT_SERVICE: string;
  BASE_TRIAGE_QUESTION: string;
}
