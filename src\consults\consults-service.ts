import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import { format, parseISO } from "date-fns";
import {
  IConsult,
  IConsultLegacyData,
  IConsultObservation,
  IConsultPrescription,
  IConsultPrescriptionItem,
  IConsultServerOutbound
} from "@/consults/consult-models";
import { KeywordService } from "@/keywords/keyword-service";
import {
  IClassification,
  ICleoBase,
  ICleoRole,
  IKeywordsRefDataKey
} from "@/keywords/keywords-models";

const cleoCommonService: CleoCommonService = new CleoCommonService();
const keywordService: KeywordService = new KeywordService();

export class ConsultsService {
  public factoryConsultLegacyData(): IConsultLegacyData {
    return {
      Id: 0,
      CasPriority: "",
      CasPriorityClass: "",
      GpAssignedType: ""
    };
  }

  public factoryConsultObservation(): IConsultObservation {
    return {
      AcvpuTypeId: 0,
      BloodPressureLower: "",
      BloodPressureUpper: "",
      Cbg: "",
      Gcs1: "",
      Gcs2: "",
      IsPregnant: false,
      OnOxygen: false,
      PeakFlow: "",
      Pulse: "",
      Resp: "",
      Sats1: "",
      Sats2: "",
      Temperature: "",
      TemperatureScaleId: 0
    };
  }

  public factoryConsultPrescriptionItem(): IConsultPrescriptionItem {
    return {
      Id: 0,
      ISeps: false,
      Name: "",
      Quantity: "",
      UniqueDrugId: "",
      Time: ""
    };
  }

  public factoryConsultPrescription(): IConsultPrescription {
    return {
      Id: 0,
      PrescriptionType: "",
      EpsScriptId: [],
      PrescriptionItems: []
    };
  }

  public factoryConsultServer(): IConsultServerOutbound {
    return {
      Assessment: "",
      BaseId: 0,
      Callno: 0,
      ClassAtEndId: 0,
      ClassAtStartId: 0,
      ClientDevice: keywordService.factoryClientDevice(),
      ClientId: "",
      Details: "",
      Diagnosis: "",
      Empowering: "",
      EpsScriptGuid: "",
      FailedContactCodeId: 0,
      GPAssignedId: "",
      Id: 0,
      IsUserUsingSmartCard: false,
      LegacyData: this.factoryConsultLegacyData(),
      Management: "",
      Objective: "",
      Observation: null,
      PastHistory: "",
      PatientDobChecked: false,
      Plan: "",
      Prescriptions: [],
      Presenting: "",
      RedFlags: "",
      RepresentativeTypeId: 0,
      Representative: "",
      RoleId: 0,
      SafetyNet: "",
      TeleAssessment: false,
      TimeEnd: "",
      TimeStart: "",
      UniqueUserId: "",
      UrgentAtEnd: false,
      UrgentAtStart: false
    };
  }

  public factoryConsult(): IConsult {
    return {
      Id: 0,
      Callno: 0,
      ClientId: "",
      UniqueUserId: "",
      GPAssigned: {
        Id: 0,
        name: ""
      },
      EpsScriptGuid: "",
      SafetyNet: "",
      Details: "",
      Objective: "",
      Plan: "",
      Assessment: "",
      UrgentAtStart: false,
      UrgentAtEnd: false,
      IsUserUsingSmartCard: false,
      TeleAssessment: false,
      PatientDobChecked: false,
      Presenting: "",
      PastHistory: "",
      RedFlags: "",
      Diagnosis: "",
      Management: "",
      Empowering: "",
      PatientRepresentative: {
        Id: 0,
        name: ""
      },
      Representative: "",
      ClassAtStart: {
        Id: 0,
        name: ""
      },
      ClassAtEnd: {
        Id: 0,
        name: ""
      },
      TimeStart: "",
      TimeEnd: "",
      Role: {
        Id: 0,
        name: ""
      },
      ClientDevice: keywordService.factoryClientDevice(),
      LegacyData: this.factoryConsultLegacyData(),
      FailedContactCode: {
        Id: 0,
        name: ""
      },
      Base: {
        Id: 0,
        name: ""
      },
      Observation: null,
      Prescriptions: []
    };
  }

  public getHeaderTitle(consult: IConsult): string {
    return (
      format(parseISO(consult.TimeStart), "dd-MMM-yyyy HH:mm") +
      " - " +
      cleoCommonService.formatUserDominoName(consult.UniqueUserId) +
      " | " +
      consult.Role.name +
      " (" +
      (consult.UrgentAtEnd ? "Urgent" : "Not Urgent") +
      ")" +
      " | " +
      "Smart Card: " +
      (consult.IsUserUsingSmartCard ? "Yes" : "NO") +
      (consult.ClassAtStart.Id !== consult.ClassAtEnd.Id
        ? " | " + consult.ClassAtStart.name + " to " + consult.ClassAtEnd.name
        : "")
    );
  }

  public mapConsultFromServer(
    consultServer: IConsultServerOutbound,
    keywordsRefDataKey: IKeywordsRefDataKey
  ): IConsult {
    //  The quick way to map across...
    const consult = Object.assign(this.factoryConsult(), consultServer);

    //  Now move the bits we really want.
    const cleoBase: ICleoBase | null = keywordService.getKeywordsRefObject(
      keywordsRefDataKey,
      "bases",
      consultServer.BaseId
    );
    consult.Base = {
      Id: consultServer.BaseId,
      name: cleoBase ? cleoBase.Name : ""
    };

    let classification: IClassification | null = keywordService.getKeywordsRefObject(
      keywordsRefDataKey,
      "classifications",
      consultServer.ClassAtStartId
    );
    consult.ClassAtStart = {
      Id: consultServer.ClassAtStartId,
      name: classification ? classification.Description : ""
    };

    classification = keywordService.getKeywordsRefObject(
      keywordsRefDataKey,
      "classifications",
      consultServer.ClassAtEndId
    );
    consult.ClassAtEnd = {
      Id: consultServer.ClassAtEndId,
      name: classification ? classification.Description : ""
    };

    const role: ICleoRole | null = keywordService.getKeywordsRefObject(
      keywordsRefDataKey,
      "roles",
      consultServer.RoleId
    );
    consult.Role = {
      Id: consultServer.RoleId,
      name: role ? role.Name : ""
    };

    return consult;
  }
}
