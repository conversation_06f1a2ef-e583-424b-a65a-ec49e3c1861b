<template>
  <GridStandardLayout :grid-definition="gridDefinition" style="height: 100%" />
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";
import { GRID_DEFINITIONS } from "../grid-models";
import GridStandardLayout from "@/calls/grids/grids-named/GridStandardLayout.vue";

export default defineComponent({
  name: "grid-classification-visit",
  components: { GridStandardLayout },
  setup() {
    const gridDefinition = GRID_DEFINITIONS["ClassificationVisit"];

    return {
      gridDefinition
    };
  }
});
</script>