import { IBaseSummary } from "@/bases/base-models";
import { ICleoGoogleMarker, MARKER_TYPE } from "@/calls/maps/map-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { IVehicle } from "@/vehicles/vehicles-models";
import { PafService } from "@/paf/paf-service";
import { ICallDetail, ICleoAddress } from "@/calls/details/call-details-models";

const pafService = new PafService();

export class MapService {
  public createBasePopOverHtml(base: IBaseSummary): string {
    const html: string[] = [];
    html.push("<div>");
    html.push(" <div class='cleo-map--marker-header'>");
    html.push("   <div class='cleo-map--marker-title'>");
    html.push("     " + base.Name);
    html.push("   <div>");
    html.push(" <div>");

    html.push(" <div class='cleo-map--marker-body'>");
    html.push("   <div class='cleo-map--marker-address'>");
    html.push("     " + base.address + "<br>");
    html.push("     " + base.town + "<br>");
    html.push("     " + base.postCode);
    html.push("   <div>");
    html.push(" <div>");

    html.push("</div>");
    return html.join("");
  }

  public getMarkerId(
    cleoObject: IBaseSummary | ICleoCallSummary | IVehicle,
    markerType: MARKER_TYPE
  ): string {
    if (markerType === "BASE") {
      return "BASE-" + (cleoObject as IBaseSummary).Id;
    }
    if (markerType === "CALL") {
      return "CALL-" + (cleoObject as ICleoCallSummary).CallNo;
    }
    if (markerType === "CAR") {
      return "CAR-" + (cleoObject as IVehicle).id;
    }
    return "NA";
  }

  public getBaseMarker(
    base: IBaseSummary,
    markers: ICleoGoogleMarker[]
  ): null | ICleoGoogleMarker {
    const markerId = this.getMarkerId(base, "BASE");
    return this.getMarker(markerId, markers);
  }

  public getCallMarker(
    cleoCallSummary: ICleoCallSummary,
    markers: ICleoGoogleMarker[]
  ): null | ICleoGoogleMarker {
    const markerId = this.getMarkerId(cleoCallSummary, "CALL");
    return this.getMarker(markerId, markers);
  }

  public getMarker(
    markerId: string,
    markers: ICleoGoogleMarker[]
  ): null | ICleoGoogleMarker {
    const marker: null | ICleoGoogleMarker = markers.reduce((accum, marker) => {
      if (marker.id === markerId) {
        accum = marker;
      }
      return accum;
    }, null as null | ICleoGoogleMarker);
    return marker;
  }

  public getCallsForVehicle(
    cleoCallSummaries: ICleoCallSummary[],
    vehicle: IVehicle
  ): ICleoCallSummary[] {
    return cleoCallSummaries.filter(cleoCallSummary => {
      return cleoCallSummary.DispatchVehicle === vehicle.name;
    });
  }

  public filterAddresses(
    results: google.maps.GeocoderResult[]
  ): google.maps.GeocoderResult[] {
    return results.filter(result => {
      // establishment
      // point_of_interest
      // administrative_area_level_4
      // political
      // postal_town
      // postal_code
      // postal_code_prefix
      // ...etc.
      const isEstablishment =
        result.types.join(".").indexOf("establishment") > -1;
      const isPointOfInterest =
        result.types.join(".").indexOf("point_of_interest") > -1;
      const streetAddress =
        result.types.join(".").indexOf("street_address") > -1;
      return isEstablishment || isPointOfInterest || streetAddress;
    });
  }

  public getGeocoderPostCodes(results: google.maps.GeocoderResult[]): string[] {
    const res = results.reduce<string[]>((accum, geo) => {
      const postCodes = geo.address_components.reduce<string[]>(
        (acc, address) => {
          if (address.types.indexOf("postal_code") > -1) {
            acc.push(address.long_name);
          }
          return acc;
        },
        []
      );

      postCodes.forEach(postCode => {
        accum.push(postCode);
      });

      return accum;
    }, []);

    //  Unique results
    return [...new Set(res)].filter(postcode => {
      return pafService.isValidPostCode(postcode);
    });
  }

  public getCallPopOverHtml(callData: ICallDetail | ICleoCallSummary): string {
    if ((callData as ICallDetail).callAddress) {
      return this.createCallDetailPopOverHtml(callData as ICallDetail);
    }
    return this.createCallSummaryPopOverHtml(callData as ICleoCallSummary);
  }

  public createCallDetailPopOverHtml(callDetail: ICallDetail): string {
    const html: string[] = [];
    html.push("<div>");
    html.push(" <div class='cleo-map--marker-header'>");
    html.push("   <div class='cleo-map--marker-title'>");
    html.push("     <b>Call no: " + callDetail.CallNo + "</b>");
    html.push("   <div>");
    html.push(" <div>");

    html.push(" <div class='cleo-map--marker-body'>");
    html.push("   <div class='cleo-map--marker-address'>");
    const addressArray = this.getUniqueAddressComponents(
      this.getCleoAddressFromCallData(callDetail)
    );
    html.push(addressArray.join("<br>"));
    html.push("   <div>");
    html.push(" <div>");

    html.push("</div>");
    return html.join("");
  }

  public createCallSummaryPopOverHtml(
    cleoCallSummary: ICleoCallSummary
  ): string {
    const html: string[] = [];
    html.push("<div>");
    html.push(" <div class='cleo-map--marker-header'>");
    html.push("   <div class='cleo-map--marker-title'>");
    html.push(
      "     " +
        cleoCallSummary.CallNo +
        ": " +
        cleoCallSummary.CallSurname +
        ", " +
        cleoCallSummary.CallForename
    );
    html.push("   <div>");
    html.push(" <div>");

    html.push(" <div class='cleo-map--marker-body'>");
    html.push("   <div class='cleo-map--marker-address'>");

    const addressArray = this.getUniqueAddressComponents(
      this.getCleoAddressFromCallData(cleoCallSummary)
    );
    html.push(addressArray.join("<br>"));
    html.push("   <div>");
    html.push(" <div>");

    html.push("</div>");
    return html.join("");
  }

  public mapCallDetailAddressToCleoAddress(
    callDetail: ICallDetail,
    whichAddress: "CURRENT" | "HOME"
  ): ICleoAddress {
    const cleoAddress: ICleoAddress =
      whichAddress === "CURRENT"
        ? callDetail.callAddress
        : callDetail.homeAddress;

    return {
      line1: cleoAddress.line1,
      line2: cleoAddress.line2,
      line3: cleoAddress.line3,
      line4: cleoAddress.line4,
      postCode: cleoAddress.postCode,
      town: cleoAddress.town
    };
  }

  public mapCallSummaryAddressToCleoAddress(
    cleoCallSummary: ICleoCallSummary
  ): ICleoAddress {
    return {
      line1: cleoCallSummary.CallAddress1,
      line2: cleoCallSummary.CallAddress2,
      line3: cleoCallSummary.CallAddress3,
      line4: cleoCallSummary.CallAddress4,
      postCode: cleoCallSummary.CallPostCode,
      town: cleoCallSummary.CallTown
    };
  }

  public getCleoAddressFromCallData(
    callData: ICallDetail | ICleoCallSummary
  ): ICleoAddress {
    if ((callData as ICallDetail).callAddress) {
      return this.mapCallDetailAddressToCleoAddress(
        callData as ICallDetail,
        "CURRENT"
      );
    }
    return this.mapCallSummaryAddressToCleoAddress(
      callData as ICleoCallSummary
    );
  }

  /**
   * Returns address in format: ["St Clements", "King St", "Colyton", "EX24 6LF"]
   * ...where the input might have "Colyton" in line3 and in town.
   * @param what3WordsMapCleoAddress
   */
  public getQueryStringFromAddress(cleoAddress: ICleoAddress): any {
    return this.getUniqueAddressComponents(cleoAddress)
      .filter(addressValue => {
        return addressValue.length > 0;
      })
      .join(", ");
  }

  /**
   * Returns address in format: St Clements, King St, Colyton, EX24 6LF
   * @param what3WordsMapCleoAddress
   */
  public getUniqueAddressComponents(cleoAddress: ICleoAddress): string[] {
    return [...new Set(Object.values(cleoAddress))].filter(address => {
      return address.length > 0;
    });
  }

  // public isValidPostCode(postCode: string): boolean {
  //   const regex = new RegExp(/^(?:(?:[A-PR-UWYZ][0-9]{1,2}|[A-PR-UWYZ][A-HK-Y][0-9]{1,2}|[A-PR-UWYZ][0-9][A-HJKSTUW]|[A-PR-UWYZ][A-HK-Y][0-9][ABEHMNPRV-Y])[0-9][ABD-HJLNP-UW-Z]{2}|GIR 0AA)$/);
  //   const postCodeTest = postCode.replace(/ /g, "");
  //   const answer = regex.test(postCodeTest);
  //   return answer;
  // }
}
