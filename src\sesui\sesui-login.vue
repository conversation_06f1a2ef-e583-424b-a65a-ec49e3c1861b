<template>
  <div class="dat-content-wrapper" style="width: 400px;">
    <div class="dat-flex-column">
      <h3>Sesui Login</h3>
      <div
        class="dat-card dat-card--generic dat-info--info"
        style="width: auto"
      >
        The details will be cached so you do not need to re-enter on your next
        login.
      </div>

      <div v-if="showCancelConfirm">
        <div class="dat-vertical-spacer"></div>
        <div
          class="dat-card dat-card--generic dat-info--error"
          style="width: auto"
        >
          Please note: if you continue you will be unable to use Sesui within
          CLEO. If you later decide to use Sesui, you will need to logout of
          CLEO and log back in.
        </div>
      </div>
    </div>

    <!--    <hr />-->

    <div class="dat-vertical-spacer"></div>

    <div class="dat-flex-row dat-justify-flex-space-between">
      <div>Sesui User ID</div>
      <input v-model="userId" />
    </div>
    <div style="padding:10px 0px 10px 0px;display:none;">
      <div>
        Enter here your Telephone number or extension. If using Sesui dashboard,
        please leave blank.
      </div>
      <span style="width: 80px;display: inline-block;">Tel # :</span
      ><input
        id="telno-' + divSuffix + '"
        value="' + state.operatorPhoneNumber + '"
      />
    </div>

    <div class="dat-vertical-spacer"></div>

    <div class="dat-flex-row dat-justify-flex-space-between">
      <button
        v-if="!showCancelConfirm"
        class="adapter-button adapter-width-8 adapter-button--grey"
        v-on:click.stop="askCancelConfirm"
      >
        <span>Cancel</span>
      </button>
      <button
        v-if="showCancelConfirm"
        class="adapter-button adapter-width-8 adapter-button--red"
        v-on:click.stop="cancel"
      >
        <span>Confirm Cancel</span>
      </button>
      <span class="adapter-button--separator"></span>
      <button
        :disabled="userId.length === 0"
        class="adapter-button adapter-width-8 adapter-button--green"
        v-on:click.stop="login"
      >
        <span>Login</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "sesui-login",
  props: {
    defaultUserId: {
      type: String,
      default: () => {
        return "";
      }
    }
  },
  setup(props: { defaultUserId: string }, context: SetupContext) {
    const userId = ref(props.defaultUserId);

    const showCancelConfirm = ref(false);

    function askCancelConfirm() {
      showCancelConfirm.value = true;
    }

    function cancel() {
      context.emit("cancel");
    }

    function login() {
      context.emit("loginSesui", userId.value);
    }

    return { userId, cancel, askCancelConfirm, showCancelConfirm, login };
  }
});
</script>

<style>
.sesui--wrapper {
  min-width: 400px;
  padding: 10px;
}
</style>
