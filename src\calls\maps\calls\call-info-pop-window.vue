<template>
  <div class="cleo-map--marker-wrapper">
    <div class="cleo-map--marker-header">
      <div class="cleo-map--marker-label">Call</div>
      <div class="cleo-map--marker-data">
        <span v-text="cleoCallInternal.CallNo"></span>
      </div>
    </div>

    <div class="cleo-map--marker-body">
      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Class</div>
        <div class="cleo-map--marker-data">
          <span v-text="cleoCallInternal.Classification.Description"></span>
          <span
            v-if="cleoCallInternal.CallUrgentYn"
            class="cleo-map--marker-call-urgent"
            >Urgent</span
          >
          <span
            v-if="!cleoCallInternal.CallUrgentYn"
            class="cleo-map--marker-call-non-urgent"
            >Not Urgent</span
          >
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Patient</div>
        <div class="cleo-map--marker-data">
          <span
            v-text="
              cleoCallInternal.Surname +
                ', ' +
                cleoCallInternal.Forename +
                ' (' +
                cleoCallInternal.Age +
                cleoCallInternal.AgeClass +
                ')'
            "
          ></span>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Current Location</div>
        <div class="cleo-map--marker-data">
          <div v-html="getAddress"></div>
        </div>
        <div class="cleo-map--marker-line">
          <div class="cleo-map--marker-label">W3W Lat/Long</div>
          <div class="cleo-map--marker-data">
            <span v-text="currentW3wLatLong"></span>
          </div>
        </div>
        <div class="cleo-map--marker-line">
          <div class="cleo-map--marker-label">W3W Words</div>
          <div class="cleo-map--marker-data">
            <span v-text="currentW3wWords"></span>
          </div>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Symptoms</div>
        <div class="cleo-map--marker-data cleo-map--marker-data-large">
          <div v-text="cleoCallInternal.Symptoms"></div>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Vehicle</div>
        <div class="cleo-map--marker-data cleo-map--marker-data-large">
          <span v-text="currentVehicleName"></span>

          <span v-if="userPermissions['DISPATCH SELECT']">
            <select v-model="vehicleSelected" v-if="!isDispatched">
              <!--            <option :value="'PLEASE_SELECT'">Please select...</option>-->
              <option
                v-for="vehicle in getVehicles"
                :key="vehicle.name"
                :value="vehicle"
              >
                <span
                  v-text="vehicleService.getVehicleNameDisplay(vehicle)"
                ></span>
              </option>
            </select>

            <LoadingSpinner
              v-if="
                dispatchHttpResponseController.state.isLoading ||
                  retrieveHttpResponseController.state.isLoading
              "
            />

            <button
              v-if="!isDispatched && !showDispatchConfirm"
              :disabled="isDispatchedButtonDisabled"
              class="adapter-button adapter-width-5 adapter-button--green cleo-map--marker-button-dispatch"
              v-on:click.stop="askUserForDispatchConfirm"
            >
              Dispatch
            </button>

            <button
              v-if="isDispatched"
              :disabled="retrieveHttpResponseController.state.isLoading"
              class="adapter-button adapter-width-5 adapter-button--red cleo-map--marker-button-dispatch"
              v-on:click.stop="retrieveFromVehicle"
            >
              Retrieve
            </button>
          </span>

          <!--          showDispatchConfirm {{showDispatchConfirm}}-->

          <div v-if="showDispatchConfirm">

            <div class="cleo-map--marker-line"></div>

            <div v-html="getDispatchConfirmMessage"></div>

            <div class="cleo-map--marker-line"></div>

            <button
              :disabled="isDispatchedButtonDisabled"
              class="adapter-button adapter-width-5 adapter-button--red"
              v-on:click.stop="showDispatchConfirm = false"
            >
              Cancel
            </button>

            <div class="adapter-button--separator"></div>

            <button
              :disabled="isDispatchedButtonDisabled"
              class="adapter-button adapter-width-5 adapter-button--green"
              v-on:click.stop="dispatchToVehicle"
            >
              Dispatch
            </button>
          </div>

          <!--          isDispatched {{isDispatched}}-->
          <!--          dis{{isDispatchedButtonDisabled}}-->
          <!--          disp {{ dispatchResponseController }}-->
          <!--          perms{{ userPermissions['DISPATCH RETRIEVE'] }}-->
          <!--          dispatchHttpResponseController-->
          <!--          {{ dispatchHttpResponseController.state }}-->
        </div>
      </div>

      <!--      <div class="cleo-map&#45;&#45;marker-section" v-if="false">-->
      <!--        <button-->
      <!--          class="adapter-button adapter-width-5 adapter-button&#45;&#45;green adapter-button&#45;&#45;separator"-->
      <!--          v-on:click.stop="$emit('openCall', cleoCall)"-->
      <!--        >-->
      <!--          <span>Open Call</span>-->
      <!--        </button>-->

      <!--        <div class="adapter-button&#45;&#45;separator"></div>-->

      <!--        <select v-model="nearestCars">-->
      <!--          <option value="1">1</option>-->
      <!--          <option value="2">2</option>-->
      <!--          <option value="3">3</option>-->
      <!--        </select>-->
      <!--        <button-->
      <!--          class="adapter-button adapter-width-5 adapter-button&#45;&#45;green"-->
      <!--          v-on:click.stop="findNearestVehicles"-->
      <!--        >-->
      <!--          <span>Nearest</span>-->
      <!--        </button>-->

      <!--        <button-->
      <!--          class="adapter-button adapter-width-5 adapter-button&#45;&#45;red cleo-map&#45;&#45;marker-button-dispatch"-->
      <!--          v-on:click.stop="$emit('dispatchCall', cleoCall)"-->
      <!--        >-->
      <!--          <span v-text="isDispatched ? 'Retrieve' : 'Dispatch'"></span>-->
      <!--        </button>-->
      <!--      </div>-->
    </div>
  </div>
  <!--  <a href="#" v-on:click.prevent="handleClick" class="map-base&#45;&#45;title-link">-->
  <!--    <span v-text="cleoCallSummary.CallNo"></span>-->

  <!--  </a>-->
</template>

<script lang="ts">
import {
  toRaw,
  computed,
  ComputedRef,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext
} from "@vue/composition-api";
import { ICleoCallSummary } from "../../summary/call-summarry-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import { MapService } from "@/calls/maps/map-service";
import { IVehicle } from "@/vehicles/vehicles-models";
import { VehicleService } from "@/vehicles/vehicle-service";
import { What3WordsResponse } from "@/typings/unknown-lib";
import { ICleoPermission } from "@/permissions/permission-models";
import { useHttpResponseControllerReactive } from "@/common/useHttpController";

import { VehiclesData } from "@/vehicles/vehicles-data";
import { ILegacyCleoServerResponse } from "@/common/cleo-legacy-models";
import { CommonService } from "@/common/common-service";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";

export default defineComponent({
  name: "call-info-pop-window",
  components: { LoadingSpinner },
  props: {
    cleoCall: {
      required: true,
      type: Object as PropType<ICleoCallSummary | ICallDetail>
    },
    vehicles: {
      type: Array as PropType<IVehicle[]>,
      default: () => {
        return [];
      }
    },
    what3WordsResponse: {
      type: Object as PropType<ComputedRef<What3WordsResponse>>,
      default: () => {
        return {
          coordinates: {
            lat: 0,
            lng: 0
          },
          words: ""
        };
      }
    },
    userPermissions: {
      type: Object as PropType<Record<string, ICleoPermission>>,
      default: () => {
        return {};
      }
    }
  },
  setup(
    props: {
      cleoCall: ICleoCallSummary | ICallDetail;
      message: string;
      vehicles: IVehicle[];
      what3WordsResponse: ComputedRef<What3WordsResponse>;
      userPermissions: Record<string, ICleoPermission>;
    },
    context: SetupContext
  ) {
    const commonService = new CommonService();
    const cleoCommonService = new CleoCommonService();
    const mapService = new MapService();
    const vehicleService = new VehicleService();
    const nearestCars = ref(2);
    const showDispatchConfirm = ref<boolean>(false);

    console.log(
      "call-info-pop-window.setUp() userPermissions:",
      commonService.simpleObjectClone(props.userPermissions)
    );
    console.log(
      "call-info-pop-window.setUp() vehicles:",
      commonService.simpleObjectClone(props.vehicles)
    );

    const cleoCallInternal = reactive<ICleoCallSummary | ICallDetail>(
      commonService.simpleObjectClone(props.cleoCall)
    );

    const defaultVehicle: IVehicle = vehicleService.factoryVehicle();
    defaultVehicle.name = "Please select";
    defaultVehicle.id = 0;

    const vehicleSelected = ref(defaultVehicle);
    const getVehicles = computed(() => {
      return [defaultVehicle, ...props.vehicles];
    });

    const dispatchHttpResponseController = useHttpResponseControllerReactive<
      ILegacyCleoServerResponse<unknown>
    >(null, "Successfully Dispatched");

    const retrieveHttpResponseController = useHttpResponseControllerReactive<
      ILegacyCleoServerResponse<unknown>
    >(null, "Successfully Retrieved");

    function handleClick() {
      context.emit("cleoCallClicked", props.cleoCall);
    }

    const getAddress = computed<string>(() => {
      return mapService
        .getUniqueAddressComponents(
          mapService.getCleoAddressFromCallData(props.cleoCall)
        )
        .join("<br>");
    });

    const isDispatched = computed<boolean>(() => {
      return cleoCallInternal.DispatchVehicle.length > 0;
    });

    const currentW3wWords = computed<string>(() => {
      return props.what3WordsResponse.value.words;
    });

    const currentW3wLatLong = computed<string>(() => {
      if (props.what3WordsResponse.value.words.length === 0) {
        return "";
      }
      const coordinates = props.what3WordsResponse.value.coordinates;
      return (
        coordinates.lat.toString().slice(0, 9) +
        " / " +
        coordinates.lng.toString().slice(0, 9)
      );
    });

    const isDispatchedButtonDisabled = computed<boolean>(() => {
      return (
        vehicleSelected.value.id === 0 ||
        dispatchHttpResponseController.state.isLoading
      );
    });

    function findNearestVehicles() {
      const rawCall = toRaw(cleoCallInternal);
      context.emit("findNearestVehicles", {
        cleoCallSummary: rawCall,
        nearestCount: nearestCars.value
      });
    }

    function askUserForDispatchConfirm() {
      showDispatchConfirm.value = true;
    }

    function dispatchToVehicle() {
      // context.emit("dispatchToVehicle", {
      //   cleoCall: props.cleoCall,
      //   vehicle: vehicleSelected.value
      // });
      const coordinates = props.what3WordsResponse.value.coordinates;
      const lat = coordinates.lat;
      const lng = coordinates.lng;
      const vehicleUnid = vehicleSelected.value.unid;

      dispatchHttpResponseController
        .getData(
          new VehiclesData().dispatchToVehicle(
            props.cleoCall.CallNo,
            vehicleUnid,
            lat.toString(),
            lng.toString()
          )
        )
        .then(() => {
          if (dispatchHttpResponseController.state.data?.RESULT === "SUCCESS") {
            // context.emit("close");
            cleoCallInternal.DispatchVehicle = cleoCommonService.formatUserDominoName(
              vehicleSelected.value.name
            );

            //  we need to let Cleo Legacy this was a success.
            context.emit("dispatched", {
              cleoCall: props.cleoCall,
              vehicle: vehicleSelected.value.name
            });
          }
        });
    }

    function retrieveFromVehicle() {
      retrieveHttpResponseController
        .getData(new VehiclesData().retrieveFromVehicle(props.cleoCall.CallNo))
        .then(() => {
          if (dispatchHttpResponseController.state.data?.RESULT === "SUCCESS") {
            // context.emit("close");
            cleoCallInternal.DispatchVehicle = "";
          }
        });
    }

    const currentVehicleName = computed<string>(() => {
      return cleoCommonService.formatUserDominoName(
        cleoCallInternal.DispatchVehicle
      );
    });

    const getDispatchConfirmMessage = computed<string>(() => {
      let message = "";
      if (currentW3wWords.value.length === 0) {
        message = "<strong>You have not specified a Lat/Long.</strong> ";
      }
      return (
        (message.length > 0 ? message + " " : "") +
        " Are you sure you would like to dispatch?"
      );
    });

    return {
      cleoCallInternal,
      handleClick,
      getAddress,
      isDispatched,
      nearestCars,
      findNearestVehicles,
      vehicleSelected,
      vehicleService,
      currentW3wWords,
      currentW3wLatLong,
      getVehicles,
      askUserForDispatchConfirm,
      showDispatchConfirm,
      dispatchToVehicle,
      getDispatchConfirmMessage,
      retrieveFromVehicle,
      isDispatchedButtonDisabled,
      dispatchHttpResponseController,
      retrieveHttpResponseController,
      currentVehicleName
    };
  }
});
</script>

<style scoped>
.map-base--title {
  font-weight: 700;
  padding: 0 0 5px 0;
}
/*.map-base--title-link {*/
/*  text-decoration: none;*/
/*}*/
.cleo-map--marker-header {
  font-weight: 600;
  /*margin-bottom: 10px;*/
}

.cleo-map--marker-body {
  margin-top: 10px;
}

.cleo-map--marker-section {
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #e8e8e8;
}

.cleo-map--marker-line {
  margin-top: 5px;
}

.cleo-map--marker-label {
  width: 100px;
  display: inline-block;
  vertical-align: top;
}

.cleo-map--marker-data {
  width: 300px;
  display: inline-block;
}

.cleo-map--marker-data-large {
  max-height: 100px;
  overflow: auto;
}

.cleo-map--marker-button-dispatch {
  float: right;
}

.cleo-map--marker-call-urgent {
  margin-left: 5px;
  font-weight: 600;
  color: #fb3939;
}
.cleo-map--marker-call-non-urgent {
  margin-left: 5px;
  font-weight: 600;
  color: #1a6e00;
}
</style>
