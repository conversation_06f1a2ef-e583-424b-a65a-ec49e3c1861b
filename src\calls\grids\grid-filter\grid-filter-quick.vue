<template>
  <div>
    Quick Filter
    <input
      v-model="selectedValue"
      v-on:keyup="debounceOnChange"
      :class="
        selectedValue === '' ? '' : 'grid-route-toolbar--filters-warn-value'
      "
    />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { debounce } from "@/common/debounce";
import {loggerInstance} from "@/common/Logger";

@Component({
  name: "grid-filter-quick"
})
export default class GridFilterQuick extends Vue {
  public selectedValue = "";

  public debounceOnChange: any;

  public created() {
    this.debounceOnChange = debounce(() => {
      loggerInstance.log("GridFilterQuick.debounceOnChange()...selectedValue: " + this.selectedValue);
      this.onChange();
    }, 500);
  }

  public onChange() {
    this.$emit("change", this.selectedValue);
  }
}
</script>
