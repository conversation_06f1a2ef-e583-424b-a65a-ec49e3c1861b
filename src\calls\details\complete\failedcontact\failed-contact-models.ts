import { ISimpleButtonInputValue } from "../SimpleButtonSelecter.vue";
import { IBaseControllerState, IStep } from "../complete-models";
import { FailedContactReason } from "./failed-contact-reasons/failed-contact-models";
import { HowManaged, HowManagedValue } from "./how-managed/how-managed-models";
import { FaildContactStepName } from "./useFailedContactController";

export interface IFailedContactSafeguarding {
  risk: "" | "LOW" | "HIGH";
  furtherAction: null | boolean;
  welfareAction: "";
}

export interface IFailedContactControllerState
  extends IBaseControllerState<FaildContactStepName> {
  count: number;
  steps: Record<FaildContactStepName, IStep<FaildContactStepName>>;
  userResponse: {
    howManaged:
      | ISimpleButtonInputValue<HowManaged, HowManagedValue>
      | ISimpleButtonInputValue<"", "">;
    contactMade: boolean | null;
    failedContactReason:
      | ISimpleButtonInputValue<FailedContactReason, FailedContactReason>
      | ISimpleButtonInputValue<"", "">;
    safeguarding: IFailedContactSafeguarding;
  };
}
