import { CLEO_CONFIG, defaultErrorToastOptions } from "./config/config-";
import Axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse
} from "axios";
import Vue from "vue";

import { LoginService } from "@/login/login-service";

const loginService: LoginService = new LoginService();

const https: AxiosInstance = Axios.create({
  baseURL: CLEO_CONFIG.CLEO.BASE_URL
});

https.defaults.transformResponse = [
  (data: any) => {
    //  Leaving here as example, N.B. transforms happen before intercepts.

    //  IE...!
    // if (typeof data === "string") {
    //   return JSON.parse(data);
    // }
    return data;
  }
];

// add a request interceptor
https.interceptors.request.use(
  function(config: AxiosRequestConfig) {
    // do something before request is sent
    //  console.warn("https.interceptors.request AxiosRequestConfig", config);

    const userToken = loginService.getJwtAccessToken();
    if (userToken.length > 0) {
      config.headers.Authorization = "Bearer " + userToken;
    }

    // const tokenResponseStorage: string | null = localStorage.getItem("user");
    // if (tokenResponseStorage) {
    //   const tokenResponse: ITokenResponse = JSON.parse(tokenResponseStorage);
    //   config.headers.Authorization = "Bearer " + tokenResponse.access_token;
    // }
    return config;
  },
  function(error: AxiosError) {
    // do something with request error
    console.error("https.interceptors.request AxiosError", error);
    return Promise.reject(error);
  }
);

// add a response interceptor
https.interceptors.response.use(
  function(response: AxiosResponse) {
    //  console.warn("https.interceptors.response AxiosResponse", response);
    //  If we send back AxiosResponse, we are tightly coupling App + Axios.
    //  Any http\ status errors, etc. handle them here.
    return response.data as any;
  },
  function(error: AxiosError) {
    const status: number =
      error.response && error.response.status ? error.response.status : 0;
    const statusText: string =
      error.response && error.response.statusText
        ? error.response.statusText
        : "";

    if (error.response) {
      // client received an error response (5xx, 4xx)
      if (error.response.status === 401) {
        console.log("401: Authentication error");
      }
    } else if (error.request) {
      // client never received a response, or request never left
    } else {
      // anything else
    }

    // console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!reposnse" + error.response?.headers);

    //  TODO Need JS Error endpoint.
    Vue.toasted.show(
      "Http Error: " +
        error.message +
        (status > 0 ? ", status: " + status : "") +
        (statusText.length > 0 ? ", text: " + statusText : ""),
      defaultErrorToastOptions
    );
    console.error("https.interceptors.response AxiosError>>>", error);

    /**
     * If client goes into some error loop, don't DOS the server sending in error logs.
     */
    //  TODO not working, arguments getting parsed in opposite order.
    // const moreInfo: Record<string, string> = {};
    // if (error.config.url) {
    //   moreInfo["url"] = error.config.url;
    // }
    // const errorPayload = JSON.parse(JSON.stringify(error));
    // debounceError(errorPayload, moreInfo, "");

    return Promise.reject(error);
  }
);

export default https;
