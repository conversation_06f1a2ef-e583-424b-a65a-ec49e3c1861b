import {
  CompleteStepName,
  CompleteStepNameCas,
  ICompleteControllerState,
  ICompleteUserStepsConfig,
  IStep
} from "@/calls/details/complete/complete-models";
import { ICompleteControllerInput } from "@/calls/details/complete/useCompleteController";
import * as CompleteService from "@/calls/details/complete/complete-service";

export function factoryUserStepsEndAssessmentCas(
  state: ICompleteControllerState,
  completeControllerInput: ICompleteControllerInput
): ICompleteUserStepsConfig<CompleteStepNameCas> {
  const stepsDefault: Record<
    CompleteStepName,
    IStep<CompleteStepName>
  > = CompleteService.factorySteps();

  const steps: Record<CompleteStepNameCas, IStep<CompleteStepName>> = {
    END_ASSESSMENT_CONFIRMATION: stepsDefault.END_ASSESSMENT_CONFIRMATION,
    HOW_WAS_CASE_MANAGED: stepsDefault.HOW_WAS_CASE_MANAGED,
    CONTACT_MADE: stepsDefault.CONTACT_MADE,
    PATIENT_REFERRED_TO: stepsDefault.PATIENT_REFERRED_TO,
    OUTCOMES: stepsDefault.OUTCOMES,
    READ_CODES: stepsDefault.READ_CODES,
    FAILED_CONTACT_REASON: stepsDefault.FAILED_CONTACT_REASON,
    FAILED_CONTACT_SAFEGUARDING: stepsDefault.FAILED_CONTACT_SAFEGUARDING,
    FAILED_CONTACT_RISK_ASSESSMENT: stepsDefault.FAILED_CONTACT_RISK_ASSESSMENT,
    PATIENT_RISK_ASSESSMENT: stepsDefault.PATIENT_RISK_ASSESSMENT,
    INSUFFICIENT_CONTACT_ATTEMPTS: stepsDefault.INSUFFICIENT_CONTACT_ATTEMPTS,
    TAXI: stepsDefault.TAXI,
    VULNERABILITY: stepsDefault.VULNERABILITY,
    UNKNOWN: stepsDefault.UNKNOWN
  };

  const gotoNextMap: Record<CompleteStepNameCas, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "HOW_WAS_CASE_MANAGED";
    },
    HOW_WAS_CASE_MANAGED: () => {
      state.currentStep = "CONTACT_MADE";
    },
    CONTACT_MADE: () => {
      if (state.userResponse.contactMade.id === "CONTACT_MADE") {
        state.currentStep = "PATIENT_REFERRED_TO";
        return;
      }
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    PATIENT_REFERRED_TO: () => {
      state.currentStep = "READ_CODES";
    },
    READ_CODES: () => {
      if (
        completeControllerInput.callDetail.Service.id === 14 ||
        completeControllerInput.callDetail.Contract.Id === 14
      ) {
        state.currentStep = "TAXI";
        return;
      }
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    OUTCOMES: () => {
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    FAILED_CONTACT_REASON: () => {
      // state.currentStep = "PATIENT_RISK_ASSESSMENT";
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
      state.ui.disableNext = true;
      state.ui.disableComplete = true;
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_RISK_ASSESSMENT";
    },
    PATIENT_RISK_ASSESSMENT: () => {
      //  TODO remove this step, left here in case of rollback.
      //  @deprecated
      if (
        !CompleteService.doesPassFailedContactValidation(
          state,
          completeControllerInput.callDetail.failedContacts,
          state.failedContactConfig.config.seedTime
        )
      ) {
        state.currentStep = "INSUFFICIENT_CONTACT_ATTEMPTS";
        state.ui.disableNext = true;
        state.ui.disableComplete = true;
        return;
      }
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      //  TODO remove this step, left here in case of rollback.
      //  @deprecated
      state.isProcessComplete = state.steps[state.currentStep].isValid;
      state.finalAction =
        state.userResponse.insufficientContactAttempts.id ===
        "OVERRIDE_COMPLETE"
          ? "COMPLETE"
          : "RETURN_TO_OPEN_CASE";
    },
    FAILED_CONTACT_SAFEGUARDING: () => {
      state.currentStep = "UNKNOWN";
    },
    TAXI: () => {
      //  Should only have got here from READ_CODES when service is FCMS (14)
      state.currentStep = "VULNERABILITY";
    },
    VULNERABILITY: () => {
      //  Should only have got here from READ_CODES when service is FCMS (14)
      state.isProcessComplete = state.steps[state.currentStep].isValid;
    },
    UNKNOWN: () => {
      state.currentStep = "UNKNOWN";
    }
  };

  const gotoBackMap: Record<CompleteStepNameCas, () => void> = {
    END_ASSESSMENT_CONFIRMATION: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    HOW_WAS_CASE_MANAGED: () => {
      state.currentStep = "END_ASSESSMENT_CONFIRMATION";
    },
    CONTACT_MADE: () => {
      //  TODO  but if user changes answer here...you need to clear data from the next steps!!!????
      state.currentStep = "HOW_WAS_CASE_MANAGED";
    },
    PATIENT_REFERRED_TO: () => {
      state.currentStep = "CONTACT_MADE";
    },
    READ_CODES: () => {
      state.currentStep = "PATIENT_REFERRED_TO";
    },
    OUTCOMES: () => {
      state.currentStep = "CONTACT_MADE";
    },
    FAILED_CONTACT_REASON: () => {
      state.currentStep = "CONTACT_MADE";
    },
    FAILED_CONTACT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    PATIENT_RISK_ASSESSMENT: () => {
      state.currentStep = "FAILED_CONTACT_REASON";
    },
    INSUFFICIENT_CONTACT_ATTEMPTS: () => {
      //  user can not move off this step.
      state.currentStep = "PATIENT_RISK_ASSESSMENT";
    },
    FAILED_CONTACT_SAFEGUARDING: () => {
      console.error("useCompleteController.cancel under construction.");
    },
    TAXI: () => {
      state.currentStep = "READ_CODES";
    },
    VULNERABILITY: () => {
      state.currentStep = "TAXI";
    },
    UNKNOWN: () => {
      console.error("useCompleteController.cancel under construction!");
    }
  };

  return {
    steps: steps,
    validateMap: CompleteService.validationMapDefault(
      state,
      completeControllerInput
    ),
    gotoNextMap: gotoNextMap,
    gotoBackMap: gotoBackMap
  } as ICompleteUserStepsConfig<CompleteStepNameCas>;
}
