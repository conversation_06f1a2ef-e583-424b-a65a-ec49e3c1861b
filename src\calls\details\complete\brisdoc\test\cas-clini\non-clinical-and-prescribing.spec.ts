import {
  ICompleteControllerInput,
  useCompleteController
} from "@/calls/details/complete/useCompleteController";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import { ICallDetail } from "@/calls/details/call-details-models";

const callDetail: ICallDetail = factoryCallDetail();

describe("onBrisDocNonClinicalAndPrescribingSelected", () => {
  it("CAS", () => {
    callDetail.Service.serviceType = "CAS";

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };
    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    controller.state.currentStep = "BRISDOC_NON_CLINICAL_AND_PRESCRIBING";

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: null,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
    );
    expect(controller.state.validationMessages[0].id).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__NON_CLINICAL_SUPPORT_REQUIRED"
    );

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: true,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.currentStep).toBe(
      "BRISDOC_NON_CLINICAL_AND_PRESCRIBING"
    );

    expect(
      controller.state.validationMessages.find(
        m =>
          m.id === "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__SUPPORT_TYPE_REQUIRED"
      )
    ).toBeDefined();

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: false,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.validationMessages.length).toBe(0);
  });

  /**
   *
   */
  it("OOH Base", () => {
    callDetail.Service.serviceType = "OOH";
    callDetail.Classification.Description = "Base";

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };
    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    controller.state.currentStep = "BRISDOC_NON_CLINICAL_AND_PRESCRIBING";

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: null,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.validationMessages).toEqual([
      {
        id:
          "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__NON_CLINICAL_SUPPORT_REQUIRED",
        message: "Please select an option for Non Clinical Support Required"
      },
      {
        id:
          "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MEDICATION_ISSUED_FROM_STOCK",
        message: "Medication Issued From Stock"
      }
    ]);

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: false,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    expect(controller.state.validationMessages).toEqual([
      {
        id:
          "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MEDICATION_ISSUED_FROM_STOCK",
        message: "Medication Issued From Stock"
      }
    ]);

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: false,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: false
    });
    controller.goto("NEXT");

    expect(controller.state.validationMessages).toEqual([]);
  });

  /**
   *
   */
  it("OOH Visit", () => {
    callDetail.Service.serviceType = "OOH";
    callDetail.Classification.Description = "Visit";

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_BRISDOC: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_BRISDOC"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };
    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    controller.state.currentStep = "BRISDOC_NON_CLINICAL_AND_PRESCRIBING";

    controller.onBrisDocNonClinicalAndPrescribingSelected({
      nonClinicalSupportToCompleteCaseRequired: null,
      supportTypeRequired: "",
      supportTypeRequiredComments: "",
      mhClinicianSignOffRequired: null,
      medicationIssuedFromStock: null
    });
    controller.goto("NEXT");

    // expect(controller.state.validationMessages).toEqual([
    //   {
    //     id:
    //       "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MEDICATION_ISSUED_FROM_STOCK",
    //     message: "Medication Issued From Stock"
    //   },
    //   {
    //     id:
    //       "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__NON_CLINICAL_SUPPORT_REQUIRED",
    //     message: "Please select an option for Non Clinical Support Required"
    //   },
    //   {
    //     id:
    //       "BRISDOC_NON_CLINICAL_AND_PRESCRIBING__MEDICATION_ISSUED_FROM_STOCK",
    //     message: "Medication Issued From Stock"
    //   }
    // ]);

    // controller.onBrisDocNonClinicalAndPrescribingSelected({
    //   nonClinicalSupportToCompleteCaseRequired: null,
    //   supportTypeRequired: "",
    //   supportTypeRequiredComments: "",
    //   mhClinicianSignOffRequired: null,
    //   medicationIssuedFromStock: false
    // });
    // controller.goto("NEXT");
    //
    // expect(controller.state.validationMessages).toEqual([]);
  });
});
